package com.poizon.scm.wms.service.delivery.operate.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.service.delivery.operate.IDeliveryPageFetchRemoteService;
import com.poizon.scm.wms.service.frame.DataWrapper;
import com.poizon.scm.wms.service.frame.FetchService;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeliveryDetailFetchService implements FetchService<DeliveryDetailDo> {

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Resource
    private List<IDeliveryPageFetchRemoteService> deliveryPageFetchRemoteServices;


    private final static Map<WmsOutBoundTypeEnum, IDeliveryPageFetchRemoteService> deliveryFetchRemoteServiceMap = Maps.newConcurrentMap();


    @Value("${delivery.detail.fetch.partition.size:200}")
    private int partitionSize = 200;

    @Override
    public int calculateNextPageNumber(String referenceNo, int pageSize) {
        int count = deliveryDetailRepository.count(referenceNo);
        int currentPageNumber = count / pageSize;
        return currentPageNumber + 1;
    }

    @Override
    public DataWrapper<DeliveryDetailDo> pageFetch(String referenceNo, int pageSize, int pageNumber) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(referenceNo);
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }
        List<DeliveryDetailDo> deliveryDetailList = pageFetchFromRemote(deliveryHeaderDo, pageSize, pageNumber);
        return new DataWrapper().setTotal(deliveryHeaderDo.getTotalSku()).setData(deliveryDetailList);
    }

    @Override
    public void verifyDataOverflow(String referenceNo, int size, int total) {
        int count = deliveryDetailRepository.count(referenceNo);
        if (count + size > total) {
            throw new WmsException("获取的出库单明细数量已经超过计划数量，单据号: " + referenceNo);
        }
    }

    /**
     * 从远程获取数据
     *
     * @param deliveryHeader
     * @param pageSize
     * @param pageNumber
     * @return
     */
    private List<DeliveryDetailDo> pageFetchFromRemote(DeliveryHeaderDo deliveryHeader, int pageSize, int pageNumber) {
        IDeliveryPageFetchRemoteService service = findTargetService(deliveryHeader.getType());
        return service.pageFetchFromRemote(deliveryHeader,pageNumber,pageSize);
    }

    private IDeliveryPageFetchRemoteService findTargetService(String type) {
        IDeliveryPageFetchRemoteService service = deliveryFetchRemoteServiceMap.get(WmsOutBoundTypeEnum.getByType(type));
        if (Objects.nonNull(service)){
            return service;
        }
        for (IDeliveryPageFetchRemoteService deliveryPageFetchRemoteService : deliveryPageFetchRemoteServices) {
            boolean match = deliveryPageFetchRemoteService.match(type);
            if (match){
                deliveryFetchRemoteServiceMap.put(WmsOutBoundTypeEnum.getByType(type),deliveryPageFetchRemoteService);
                return deliveryPageFetchRemoteService;
            }
        }
        throw new WmsException("未找到出库单明细远程拉取对应执行器, type:"+ type);
    }


    @Override
    public void afterFetchAll(String referenceNo) {
        int planQty = deliveryDetailRepository.sumPlanQty(referenceNo);
        int count = deliveryHeaderRepository.updatePlanQty(referenceNo, planQty);
        if (count != 1) {
            throw new WmsException("更新出库单计划数量失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void process(DataWrapper<DeliveryDetailDo> dataWrapper) {
        int count = deliveryDetailRepository.batchSave(dataWrapper.data());
        if (count != dataWrapper.data().size()) {
            throw new WmsException("出库单明细保存失败");
        }
    }

    @Override
    public boolean hadFetchAll(String referenceNo, int total) {
        if (total == 0) {
            throw new WmsException("出库单明细数量为0，单据号: " + referenceNo);
        }
        int count = deliveryDetailRepository.count(referenceNo);
        if (count > total) {
            throw new WmsException("出库单明细数量大于预计数量，单据号: " + referenceNo);
        }
        return total == count;
    }

    @Override
    public List<DeliveryDetailDo> filterNotProcessed(String referenceNo, List<DeliveryDetailDo> originalList) {
        List<String> deliveryDetailNoList = originalList.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList());
        /*切割一下保证走索引*/
        List<List<String>> partitionList = Lists.partition(deliveryDetailNoList, partitionSize);
        List<DeliveryDetailDo> detailsInDb = new ArrayList<>();
        for (List<String> partition : partitionList) {
            List<DeliveryDetailDo> partitionInDb = deliveryDetailRepository.listByDetailNoList(partition);
            detailsInDb.addAll(partitionInDb);
        }
        if (CollectionUtils.isEmpty(detailsInDb)) {
            /*没有查到，说明全部需要保存*/
            return originalList;
        }
        List<String> detailNosInDb = detailsInDb.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList());
        /*过滤出没有在数据库里面的数据*/
        return originalList.stream().filter(detail -> !detailNosInDb.contains(detail.getDetailNo())).collect(Collectors.toList());
    }
}
