package com.poizon.scm.wms.third.receive.biz.out.executor;

import com.dewu.executor.annotation.Delay;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.ofc.sdk.model.wms.AllotBillCreateRequest;
import com.poizon.scm.wms.adapter.apply.model.HandoverApplyDetailDo;
import com.poizon.scm.wms.adapter.apply.model.HandoverApplyHeaderDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.domain.apply.HandoverApplyService;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.pojo.third.notify.request.out.NotifyOfcDeductSciRequest;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 交接给ofc
 *
 * <AUTHOR>
 * @data 2021/5/14
 */
@Slf4j
@Component
public class HandoverToOfcExecutor {

    @Autowired
    private HandoverApplyService handoverApplyService;

    @Autowired
    private SendMessageHandler sendMessageHandler;

    @Autowired
    private ShipTaskDetailQueryRepository taskDetailRepository;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @EventualConsistency(label = "xhjjgHandover", referenceNo = "#handoverApplyHeaderDo.referenceNo", delay = @Delay(delay = 30))
    public void xhjjgHandover(HandoverApplyHeaderDo handoverApplyHeaderDo, InventoryDo inventoryDo) {
        log.info("现货加价购单据{}准备交接发货通知", handoverApplyHeaderDo.getReferenceNo());
        List<HandoverApplyDetailDo> handoverApplyDetailDos = handoverApplyService.listByReferenceNo(handoverApplyHeaderDo.getReferenceNo());
        AllotBillCreateRequest request = xhjjgBuildAllotBill(handoverApplyHeaderDo,handoverApplyDetailDos,inventoryDo);
        SendMessage<AllotBillCreateRequest> message = new SendMessage<>();
        message.setTopic(RocketMqConstants.SCP_OFC_ALLOT_TOPIC);
        message.setTag(RocketMqConstants.OFC_ALLOT_APPOINTMENT_TAG);
        message.setMessageKey(handoverApplyHeaderDo.getAllotBillCode());
        message.setMessageContent(request);
        log.info("现货加价购交接发货通知ofc:{}", JsonUtils.serialize(message));
        String messageId = sendMessageHandler.process(message);
        if (StringUtils.isBlank(messageId)) {
            throw new WmsException("现货加价购交接请求ofc报错");
        }
    }

    @EventualConsistency(label = "handover", referenceNo = "#handoverApplyHeaderDo.referenceNo", delay = @Delay(delay = 30))
    public void handover(HandoverApplyHeaderDo handoverApplyHeaderDo, NotifyOfcDeductSciRequest originalMessage) {
        try {
            log.info("单据{}准备交接发货通知", handoverApplyHeaderDo.getReferenceNo());
            List<HandoverApplyDetailDo> handoverApplyDetailDos = handoverApplyService.listByReferenceNo(handoverApplyHeaderDo.getReferenceNo());
            if (CollectionUtils.isEmpty(handoverApplyDetailDos)) {
                throw new WmsException(WmsExceptionCode.HANDOVER_INFO_NOT_EXIST);
            }
            /*查询单据信息*/
            DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(handoverApplyHeaderDo.getReferenceNo());
            if (Objects.isNull(deliveryHeader)) {
                throw new WmsException(WmsExceptionCode.DELIVERY_EMPTY_ERROR);
            }
            /*原始单据的任务明细*/
            List<ShipTaskDetailDo> shipTaskDetailDos = taskDetailRepository.queryByReferenceNo(handoverApplyHeaderDo.getReferenceNo(),
                    deliveryHeader.getWarehouseCode(), deliveryHeader.getTenantCode());
            AllotBillCreateRequest request = build(handoverApplyHeaderDo, handoverApplyDetailDos, originalMessage, shipTaskDetailDos);
            SendMessage<AllotBillCreateRequest> message = new SendMessage<>();
            message.setTopic(RocketMqConstants.HandoverTopic.TOPIC);
            message.setTag(RocketMqConstants.HandoverTopic.TAG);
            message.setMessageKey(handoverApplyHeaderDo.getAllotBillCode());
            message.setMessageContent(request);
            log.info("合并发货交接发货通知ofc:{}", JsonUtils.serialize(message));
            String messageId = sendMessageHandler.process(message);
            if (StringUtils.isBlank(messageId)) {
                throw new WmsException("交接请求ofc报错");
            }
        } catch (Exception e) {
            log.error("交接请求ofc报错", e);
            throw e;
        }
    }

    /**
     * 现货加价购构造交接消息
     * @param handoverApplyHeaderDo
     * @param handoverApplyDetailDos
     * @param inventoryDo
     * @return
     */
    private AllotBillCreateRequest xhjjgBuildAllotBill(HandoverApplyHeaderDo handoverApplyHeaderDo,
                                                       List<HandoverApplyDetailDo> handoverApplyDetailDos,
                                                       InventoryDo inventoryDo) {
        AllotBillCreateRequest request = new AllotBillCreateRequest();
        AllotBillCreateRequest.AllotBill allotBill = new AllotBillCreateRequest.AllotBill();
        allotBill.setAllotBillCode(handoverApplyHeaderDo.getReferenceNo());
        allotBill.setRelatedCode(handoverApplyHeaderDo.getReferenceNo());
        allotBill.setWarehouseCode(handoverApplyHeaderDo.getWarehouseCode());
        allotBill.setSourceWarehouseCode(handoverApplyHeaderDo.getSourceWarehouseCode());
        allotBill.setRemark(handoverApplyHeaderDo.getRemark());
        allotBill.setPlateNumbers(handoverApplyHeaderDo.getPlateNumbers());
        allotBill.setSeal(handoverApplyHeaderDo.getSeal());
        allotBill.setOrderType(handoverApplyHeaderDo.getOrderType());
        request.setAllotBill(allotBill);
        List<AllotBillCreateRequest.OrderLine> orderLineList = new ArrayList<>();
        handoverApplyDetailDos.forEach(applyDetail -> {
            AllotBillCreateRequest.OrderLine line = new AllotBillCreateRequest.OrderLine();
            line.setTenantCode(applyDetail.getTenantId());
            line.setOwnerCode(applyDetail.getOwnerCode());
            line.setSkuId(applyDetail.getSkuId());
            line.setUniqueCode(applyDetail.getUniqueCode());
            line.setPlanQty(applyDetail.getPlanQty());
            line.setAppointmentCode(applyDetail.getAppointmentCode());
            line.setBizType(Integer.valueOf(applyDetail.getBizType()));
            line.setQualityLevel(applyDetail.getQualityLevel());
            line.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
            line.setOriginalOrderCode(inventoryDo.getOriginalOrderCode());
            line.setExpireDate(inventoryDo.getExpTime());
            line.setProductDate(inventoryDo.getMfgTime());
            line.setProduceCode(inventoryDo.getProductionBatchNo());
            line.setBatchCode(inventoryDo.getBatchNo());
            orderLineList.add(line);
        });
        request.setOrderLines(orderLineList);
        return request;
    }

    /**
     * 构造效期体
     *
     * @param handoverApplyHeaderDo
     * @param handoverApplyDetailDos
     * @param originalMessage
     * @param taskDetailList
     * @return
     */
    private AllotBillCreateRequest build(HandoverApplyHeaderDo handoverApplyHeaderDo, List<HandoverApplyDetailDo> handoverApplyDetailDos,
                                         NotifyOfcDeductSciRequest originalMessage, List<ShipTaskDetailDo> taskDetailList) {
        AllotBillCreateRequest request = new AllotBillCreateRequest();
        AllotBillCreateRequest.AllotBill allotBill = new AllotBillCreateRequest.AllotBill();
        allotBill.setAllotBillCode(handoverApplyHeaderDo.getReferenceNo());
        allotBill.setRelatedCode(handoverApplyHeaderDo.getReferenceNo());
        allotBill.setWarehouseCode(handoverApplyHeaderDo.getWarehouseCode());
        allotBill.setSourceWarehouseCode(handoverApplyHeaderDo.getSourceWarehouseCode());
        allotBill.setRemark(handoverApplyHeaderDo.getRemark());
        allotBill.setPlateNumbers(handoverApplyHeaderDo.getPlateNumbers());
        allotBill.setSeal(handoverApplyHeaderDo.getSeal());
        allotBill.setOrderType(handoverApplyHeaderDo.getOrderType());
        request.setAllotBill(allotBill);

        List<AllotBillCreateRequest.OrderLine> orderLines = create(handoverApplyDetailDos, originalMessage.getOrderLines(), taskDetailList);
        request.setOrderLines(orderLines);
        return request;
    }

    private List<AllotBillCreateRequest.OrderLine> create(List<HandoverApplyDetailDo> handoverApplyDetailDos,
                                                          List<NotifyOfcDeductSciRequest.OrderLine> orderLines,
                                                          List<ShipTaskDetailDo> taskDetailList) {
        List<AllotBillCreateRequest.OrderLine> orderLineList = new ArrayList<>();
        Map<String, ShipTaskDetailDo> taskMapping = taskDetailList.stream().collect(Collectors.toMap(detail -> String.join("_", detail.getSkuId(), detail.getOwnerCode(), String.valueOf(detail.getBizType())), Function.identity()));
        Map<String, NotifyOfcDeductSciRequest.OrderLine> mapping = orderLines.stream().collect(Collectors.toMap(detail -> String.join("_", detail.getSkuId(), detail.getOwnerCode(), String.valueOf(detail.getBizType())), Function.identity()));
        handoverApplyDetailDos.forEach(applyDetail -> {
            String key = String.join("_", applyDetail.getSkuId(), applyDetail.getOwnerCode(), String.valueOf(applyDetail.getBizType()));
            NotifyOfcDeductSciRequest.OrderLine orderLine = mapping.get(key);
            if (Objects.isNull(orderLine)) {
                throw new WmsException(WmsExceptionCode.HANDOVER_INFO_NOT_EXIST);
            }
            ShipTaskDetailDo taskDetail = taskMapping.get(key);
            if (Objects.isNull(taskDetail)) {
                throw new WmsException(WmsExceptionCode.SHIP_TASK_NOT_EXIST);
            }
            AllotBillCreateRequest.OrderLine line = new AllotBillCreateRequest.OrderLine();
            line.setTenantCode(applyDetail.getTenantId());
            line.setOwnerCode(orderLine.getOwnerCode());
            line.setSkuId(applyDetail.getSkuId());
            line.setUniqueCode(applyDetail.getUniqueCode());
            line.setPlanQty(applyDetail.getPlanQty());

            line.setAppointmentCode(applyDetail.getAppointmentCode());
            line.setBizType(Integer.valueOf(applyDetail.getBizType()));
            line.setQualityLevel(applyDetail.getQualityLevel());

            line.setFirstReceivedTime(orderLine.getFirstReceivedTime());

            line.setOriginalOrderCode(taskDetail.getOriginalOrderCode());
            line.setExpireDate(taskDetail.getExpTime());
            line.setProductDate(taskDetail.getMfgTime());
            line.setProduceCode(taskDetail.getProductionBatchNo());
            line.setBatchCode(taskDetail.getBatchNo());
            orderLineList.add(line);
        });
        return orderLineList;
    }
}
