package com.poizon.scm.wms.third.executor;

import com.alibaba.fastjson.JSON;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.scm.cis.api.client.InventoryOperateClient;
import com.poizon.scm.cis.api.inventory.CisDimensionDetailRequest;
import com.poizon.scm.cis.api.inventory.CisOrderDimensionDto;
import com.poizon.scm.cis.api.inventory.CisSubtractRequest;
import com.poizon.scm.cis.api.inventory.WmsAllocatedDetailDto;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.pojo.third.notify.request.out.NotifyOfcDeductSciRequest;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 通知ofc扣减SCI库存
 * @Date 2020/11/13 3:10 下午
 */
@Component
@Slf4j
public class NotifyOfcDeductSciExecutor {

    @Autowired
    private SendMessageHandler sendMessageHandler;
    @Autowired
    private InventoryOperateClient inventoryOperateClient;
    @Resource
    private CisInventoryOperation cisInventoryOperation;

    @EventualConsistency(label = "NotifyOfcDeductSciExecutor", referenceNo = "#request.orderCode")
    public Boolean execute(NotifyOfcDeductSciRequest request) {
        log.info("发送给ofc通知sci扣库存 [{}]", JSON.toJSONString(request));
        SendMessage<NotifyOfcDeductSciRequest> sendMessage = new SendMessage<>();
        sendMessage.setMessageContent(request);
        sendMessage.setTag(RocketMqConstants.OfcTopic.GID_WMS_OUT_STOCK_TAG);
        sendMessage.setTopic(RocketMqConstants.OfcTopic.TOPIC_NAME);
        sendMessage.setMessageKey(request.getOrderCode());
        String messageId = sendMessageHandler.process(sendMessage);
        if (StringUtils.isBlank(messageId)) {
            log.error("NotifyOfcDeductSciExecutor fail send message:[{}]", JSON.toJSONString(sendMessage));
            throw new WmsException("发送给ofc通知sci扣库存失败");
        }
        return true;
    }

    /**
     * // todo需要做一些事情
     *
     * @param request
     * @return
     */
    private CisSubtractRequest buildCisSubRequest(NotifyOfcDeductSciRequest request) {
        CisSubtractRequest cisSubtractRequest = new CisSubtractRequest();
        cisSubtractRequest.setOrderCode(request.getOrderCode());
        cisSubtractRequest.setOrderType(request.getOrderType());
        cisSubtractRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
//        cisSubtractRequest.setRequestId(request.getOrderCode());
        cisSubtractRequest.setRequestId(buildIdempotentKey(request.getOrderType(),request.getOrderCode()));
        if (WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(request.getBizType())) {
            cisSubtractRequest.setWmsAllocatedDetailDtoList(buildForPersonalDeposit(request));
        } else {
            cisSubtractRequest.setWmsAllocatedDetailDtoList(buildForEnterpriseDeposit(request));
        }
        cisSubtractRequest.setFromChannel("WMS");
        return cisSubtractRequest;
    }

    public static String buildIdempotentKey(String orderType, String orderCode) {
        return orderType + "-" + orderCode;
    }


    private List<WmsAllocatedDetailDto> buildForEnterpriseDeposit(NotifyOfcDeductSciRequest request) {
        List<WmsAllocatedDetailDto> allocatedDetails = new ArrayList<>();
        Map<String, List<NotifyOfcDeductSciRequest.OrderLine>> mapping = request.getOrderLines().stream().collect(Collectors.groupingBy(orderLine -> {
            StringBuilder builder = new StringBuilder();
            return builder.append(orderLine.getSkuId())
                    .append(orderLine.getOwnerCode())
                    .append(orderLine.getBizType())
                    .append(orderLine.getWarehouseCode())
                    .append(orderLine.getQualityLevel()).toString();

        }));
        mapping.forEach((key,list)->{
            WmsAllocatedDetailDto detailDto = new WmsAllocatedDetailDto();
            detailDto.setOrderDetailNo(StringUtils.EMPTY);
            CisOrderDimensionDto dimensionDto = createCisOrderDimension(list.get(0));
            detailDto.setOrderInventoryDimensionRequest(dimensionDto);
            List<CisDimensionDetailRequest> detailRequestList = new ArrayList();
            for (NotifyOfcDeductSciRequest.OrderLine orderLine : list) {
                CisDimensionDetailRequest detailRequest = buildDetailRequest(orderLine);
                detailRequestList.add(detailRequest);
            }
            detailDto.setCisDimensionDetailRequestList(detailRequestList);
            detailDto.setShouldQty(detailRequestList.stream().mapToInt(CisDimensionDetailRequest::getActualQty).sum());
            allocatedDetails.add(detailDto);
        });
        return allocatedDetails;
    }

    private List<WmsAllocatedDetailDto> buildForPersonalDeposit(NotifyOfcDeductSciRequest request) {
        List<WmsAllocatedDetailDto> allocatedDetails = new ArrayList<>();
        List<NotifyOfcDeductSciRequest.OrderLine> orderLines = request.getOrderLines();
        orderLines.forEach(orderLine -> {
            WmsAllocatedDetailDto detailDto = new WmsAllocatedDetailDto();
            detailDto.setOrderDetailNo(StringUtils.EMPTY);
            CisOrderDimensionDto dimensionDto = createCisOrderDimension(orderLine);
            detailDto.setOrderInventoryDimensionRequest(dimensionDto);
            CisDimensionDetailRequest detailRequest = buildDetailRequest(orderLine);
            detailDto.setCisDimensionDetailRequestList(Arrays.asList(detailRequest));
            detailDto.setShouldQty(detailRequest.getActualQty());
            allocatedDetails.add(detailDto);
        });
        return allocatedDetails;
    }

    private CisDimensionDetailRequest buildDetailRequest(NotifyOfcDeductSciRequest.OrderLine orderLine) {
        CisDimensionDetailRequest detailRequest = new CisDimensionDetailRequest();
        detailRequest.setActualQty(orderLine.getQty());
        detailRequest.setWarehouseCode(orderLine.getWarehouseCode());
        detailRequest.setBizType(orderLine.getBizType());
        detailRequest.setExpTime(orderLine.getExpTime());
        detailRequest.setFirstReceivedTime(orderLine.getFirstReceivedTime());
        detailRequest.setLotsNo(StringUtils.EMPTY);
        detailRequest.setQualityLevel(orderLine.getQualityLevel());
        detailRequest.setOriginalOrderCode(orderLine.getOriginalOrderCode());
        detailRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        if (WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(orderLine.getBizType())){
            detailRequest.setUniqueCode(orderLine.getUniqueCode());
        }
        detailRequest.setOwnerCode(orderLine.getOwnerCode());
        detailRequest.setSkuId(orderLine.getSkuId());
        return detailRequest;
    }

    private CisOrderDimensionDto createCisOrderDimension(NotifyOfcDeductSciRequest.OrderLine orderLine) {
        CisOrderDimensionDto dimensionDto = new CisOrderDimensionDto();
        dimensionDto.setBizType(orderLine.getBizType());
        dimensionDto.setWarehouseCode(orderLine.getWarehouseCode());
        dimensionDto.setTenantCode(OperationUserContextHolder.getTenantCode());
        dimensionDto.setQualityLevel(orderLine.getQualityLevel());
        if (WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(orderLine.getBizType())){
            dimensionDto.setUniqueCode(orderLine.getUniqueCode());
        }
        dimensionDto.setOwnerCode(orderLine.getOwnerCode());
        dimensionDto.setSkuId(orderLine.getSkuId());
        return dimensionDto;
    }
}
