package com.poizon.scm.wms.service.outbound.handover.producer;

import com.alibaba.fastjson.JSON;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.scm.wms.adapter.container.model.ContainerDetailDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBill;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.service.delivery.operate.async.JcAllotDeliveryParam;
import com.shizhuang.athena.api.request.inventory.handover.HandOverOutboundDetail;
import com.shizhuang.athena.api.request.inventory.handover.HandOverOutboundRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 交接单通知wms扣库存 生产者
 * <AUTHOR>
 * date 2021-01-14
 */
@Slf4j
@Component
public class HandoverMessageToInvProducer {

    @Autowired
    private SendMessageHandler sendMessageHandler;

    @EventualConsistency(label = "HandoverMessageToInv",
            referenceNo = "#jcAllotDeliveryParam.deliveryBill.deliveryOrderCode")
    public void sendHandoverMessageToInv(JcAllotDeliveryParam jcAllotDeliveryParam) {
        SendMessage<HandOverOutboundRequest> sendMessage = new SendMessage<>();
        HandOverOutboundRequest request = buildMessageContent(jcAllotDeliveryParam);
        sendMessage.setMessageContent(request);
        sendMessage.setMessageKey(request.getRequestId());
        sendMessage.setTopic(RocketMqConstants.HandoverInvokeWmsInvProducer.TOPIC_NAME);
        sendMessage.setTag(RocketMqConstants.HandoverInvokeWmsInvProducer.TAG);
        String messageId = sendMessageHandler.process(sendMessage);
        if (StringUtils.isBlank(messageId)) {
            log.error("execute fail send message={}", JSON.toJSONString(sendMessage));
            throw new WmsException("execute fail send message");
        }
    }


    private HandOverOutboundRequest buildMessageContent(JcAllotDeliveryParam deliveryParam) {
        WmsDeliveryBill deliveryBill = deliveryParam.getDeliveryBill();
        List<ContainerDetailDo> containerDetailDos = deliveryParam.getContainerDetailDoList();
        List<InventoryDo> inventoryDos = deliveryParam.getInventoryDos();

        HandOverOutboundRequest request = new HandOverOutboundRequest();
        request.setRequestId(deliveryBill.getOrderType() + "_" + deliveryBill.getDeliveryOrderCode());
        request.setBillNo(deliveryBill.getDeliveryOrderCode());
        request.setBillType(deliveryBill.getOrderType());
        request.setTenantId(OperationUserContextHolder.getTenantCode());
        request.setSourceWarehouseCode(deliveryBill.getWarehouseCode());
        request.setTargetWarehouseCode(deliveryParam.getTargetWarehouseCode());
//        request.setUserName(null != OperationUserContextHolder.get() ?
//                OperationUserContextHolder.get().getUserName() : "TEST");
//        request.setUserId(null != OperationUserContextHolder.get() ?
//                String.valueOf(OperationUserContextHolder.get().getUserId()) : "0");
        request.setUserId(StringUtils.isNotEmpty(deliveryParam.getHandoverUserId()) ? deliveryParam.getHandoverUserId() : "0");
        request.setUserName(StringUtils.isNotEmpty(deliveryParam.getHandoverUserName()) ? deliveryParam.getHandoverUserName() : "TEST");
        request.setOperateTime(deliveryParam.getHandoverTime());
        List<HandOverOutboundDetail> outboundDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(containerDetailDos)) {
            containerDetailDos.forEach(containerDetailDo -> {
                HandOverOutboundDetail outboundDetail = new HandOverOutboundDetail();
                outboundDetail.setInventoryNo(containerDetailDo.getInventoryNo());
                outboundDetail.setUniqueCode(containerDetailDo.getUniqueCode());
                outboundDetail.setContainerCode(containerDetailDo.getContainerCode());
                outboundDetail.setQty(1);
                outboundDetails.add(outboundDetail);
            });
        }else {
            inventoryDos.forEach(containerDetailDo -> {
                HandOverOutboundDetail outboundDetail = new HandOverOutboundDetail();
                outboundDetail.setInventoryNo(containerDetailDo.getInventoryNo());
                outboundDetail.setUniqueCode(containerDetailDo.getUniqueCode());
                outboundDetail.setQty(1);
                outboundDetails.add(outboundDetail);
            });
        }
        request.setHandOverList(outboundDetails);
        return request;
    }
}
