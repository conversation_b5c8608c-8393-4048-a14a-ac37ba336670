package com.poizon.scm.wms.service.shipping;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.container.model.ContainerInstanceDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailResultQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailResultCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.utils.BeanUtil;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.enums.WmsDeliveryDetailCancelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合并发货单发货任务服务
 *
 * <AUTHOR>
 * @data 2021/6/18
 */
@Service
@Slf4j
public class CombineDeliveryShipTaskService {

    @Autowired
    private ShipTaskQueryRepository shipTaskQueryRepository;

    @Autowired
    private ShipTaskCommandRepository shipTaskCommandRepository;

    @Autowired
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Autowired
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private PickTaskDetailQueryRepository pickTaskDetailQueryRepository;

    @Autowired
    private PickTaskDetailResultQueryRepository pickTaskDetailResultQueryRepository;

    @Autowired
    private ShipTaskDetailResultCommandRepository shipTaskDetailResultCommandRepository;

    @Autowired
    private ContainerShipTaskGenerateHelper containerShipTaskGenerateHelper;

    /**
     * 根据出库单获取发货任务
     *
     * @param deliveryHeader
     * @param containerInstances
     * @return 当前单据的所有发货任务明细
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ShipTaskDetailDo> getOrCreateShipTask(DeliveryHeaderDo deliveryHeader) {
        List<ShipTaskDo> existTask = shipTaskQueryRepository.findByReferenceNo(deliveryHeader.getDeliveryOrderCode());
        if (CollectionUtils.isNotEmpty(existTask)) {
            /*当前单据的发货任务已经存在,则执行查询当前单据的发货任务明细即可*/
            return shipTaskDetailQueryRepository.queryByReferenceNo(deliveryHeader.getDeliveryOrderCode(), deliveryHeader.getWarehouseCode(), deliveryHeader.getTenantCode());
        }
        /*获取出库单明细，排除取消的，取消的明细不生成发货任务*/
        List<DeliveryDetailDo> deliveryDetailList = deliveryDetailRepository.queryDeliveryDetailExcludeCancelByDeliveryCode(deliveryHeader.getDeliveryOrderCode());
        /*过滤掉取消的出库单明细,取消的明细不应该发货*/
        List<DeliveryDetailDo> notCancelDetails = deliveryDetailList.stream().filter(detail -> !WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(detail.getCancelFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notCancelDetails)) {
            log.error("出库单{}所有单据明细已经取消,不能发货", deliveryHeader.getDeliveryOrderCode());
            throw new WmsException(WmsExceptionCode.BOUND_CANCELED);
        }
        /*根据未取消的单据明细创建发货任务*/
        List<String> deliveryDetailNoList = notCancelDetails.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList());
        log.info("单据{}的箱实例为空,将通过拣货任务创建发货任务", deliveryHeader.getDeliveryOrderCode());
        return createShipTaskFromPickTaskResult(deliveryHeader, deliveryDetailNoList);
    }

    /**
     * 根据箱明细生成发货任务
     *
     * @param deliveryHeader       单据头
     * @param deliveryDetailNoList 出库单未取消的明细编号
     * @param containerInstances   箱实例编号
     * @return
     */
    private List<ShipTaskDetailDo> createShipTaskFromContainerDetails(DeliveryHeaderDo deliveryHeader, List<String> deliveryDetailNoList, List<ContainerInstanceDo> containerInstances) {
        return containerShipTaskGenerateHelper.createShipTaskFromContainerDetails(deliveryHeader.getDeliveryOrderCode(),deliveryDetailNoList,containerInstances);
    }

    /**
     * 根据拣货任务结果生成发货任务
     *
     * @param deliveryHeader
     * @param deliveryDetailNoList
     * @return
     */
    private List<ShipTaskDetailDo> createShipTaskFromPickTaskResult(DeliveryHeaderDo deliveryHeader, List<String> deliveryDetailNoList) {
        /*根据出库单明细获取完成的拣货任务明细*/
        List<PickTaskDetailDo> pickTaskDetails = pickTaskDetailQueryRepository.queryByBatchDeliveryDetailNoInStatus(deliveryHeader.getWarehouseCode(), deliveryDetailNoList, TaskStatusEnum.finishList);
        if (CollectionUtils.isEmpty(pickTaskDetails)) {
            log.error("出库单{}的完成状态的拣货任务明细为空", deliveryHeader.getDeliveryOrderCode());
            throw new WmsException(WmsExceptionCode.PICK_TASK_DETAIL_NOT_FOUND);
        }
        /*构建发货任务*/
        ShipTaskDo shipTask = buildShipTaskHeader(pickTaskDetails);
        int thResult = shipTaskCommandRepository.save(shipTask);
        Preconditions.checkBiz(thResult == 1, WmsExceptionCode.TASK_HEADER_SAVE_FAIL);
        /*构建发货明细*/
        List<ShipTaskDetailDo> shipTaskDetails = buildShipTaskDetails(shipTask, pickTaskDetails);
        int tdListResult = shipTaskDetailCommandRepository.batchSave(shipTaskDetails);
        Preconditions.checkBiz(tdListResult > 0, WmsExceptionCode.TASK_DETAIL_SAVE_FAIL);
        return shipTaskDetails;
    }


    /**
     * 创建发货任务明细
     *
     * @param shipTask
     * @param pickTaskDetails
     * @return
     */
    private List<ShipTaskDetailDo> buildShipTaskDetails(ShipTaskDo shipTask, List<PickTaskDetailDo> pickTaskDetails) {
        List<ShipTaskDetailDo> originalShipDetailData = new ArrayList<>();
        OperationUserContext userContext = OperationUserContextHolder.get();
        List<String> pickTaskDetailNoList = pickTaskDetails.stream().map(PickTaskDetailDo::getDetailNo).distinct().collect(Collectors.toList());
        Map<String, PickTaskDetailDo> pickTaskDetailNoMapping = pickTaskDetails.stream().collect(Collectors.toMap(PickTaskDetailDo::getDetailNo, Function.identity()));
        List<PickTaskDetailResultDo> pickTaskDetailResultList = pickTaskDetailResultQueryRepository.queryByTaskDetailNoList(pickTaskDetailNoList);
        pickTaskDetailResultList.forEach(pickTaskResult -> {
            PickTaskDetailDo pickTaskDetail = pickTaskDetailNoMapping.get(pickTaskResult.getTaskDetailNo());
            ShipTaskDetailDo shipTaskDetail = new ShipTaskDetailDo();
            shipTaskDetail.setTaskNo(shipTask.getTaskNo());
            shipTaskDetail.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            shipTaskDetail.setBizType(pickTaskDetail.getBizType().byteValue());
            shipTaskDetail.setStatus(NumberUtils.INTEGER_ZERO.byteValue());
            shipTaskDetail.setReferenceNo(pickTaskDetail.getReferenceNo());
            shipTaskDetail.setReferenceType(pickTaskDetail.getReferenceType());
            shipTaskDetail.setReferenceDetailNo(pickTaskDetail.getReferenceDetailNo());
            /*库存编号从任务结果中获取*/
            shipTaskDetail.setInventoryNo(pickTaskResult.getInventoryNo());
            shipTaskDetail.setContainerCode(pickTaskResult.getContainerCode());
            shipTaskDetail.setQty(pickTaskResult.getOperationQty());
            shipTaskDetail.setLocationCode(pickTaskResult.getLocationCode());

            shipTaskDetail.setOperationQty(NumberUtils.INTEGER_ZERO);
            shipTaskDetail.setPoNo(pickTaskDetail.getPoNo());
            shipTaskDetail.setOriginalDetailNo(pickTaskDetail.getOriginalDetailNo());
            shipTaskDetail.setWarehouseCode(pickTaskDetail.getWarehouseCode());
            shipTaskDetail.setTenantCode(pickTaskDetail.getTenantCode());
            shipTaskDetail.setOwnerCode(pickTaskDetail.getOwnerCode());
            shipTaskDetail.setVendorCode(pickTaskDetail.getVendorCode());
            shipTaskDetail.setBarcode(pickTaskDetail.getBarcode());
            shipTaskDetail.setUniqueCode(pickTaskDetail.getUniqueCode());
            shipTaskDetail.setSkuId(pickTaskDetail.getSkuId());
            shipTaskDetail.setGoodsTitle(pickTaskDetail.getGoodsTitle());
            shipTaskDetail.setGoodsPic(pickTaskDetail.getGoodsPic());
            shipTaskDetail.setGoodsArticleNumber(pickTaskDetail.getGoodsArticleNumber());
            shipTaskDetail.setQualityLevel(pickTaskDetail.getQualityLevel());
            shipTaskDetail.setSpecs(pickTaskDetail.getSpecs());
            shipTaskDetail.setUom(pickTaskDetail.getUom());
            shipTaskDetail.setAllocatedNo(pickTaskDetail.getAllocatedNo());
            shipTaskDetail.setFlowCode(pickTaskDetail.getFlowCode());
            shipTaskDetail.setCreatedUserId(userContext.getUserId());
            shipTaskDetail.setCreatedUserName(userContext.getUserName());
            shipTaskDetail.setCreatedRealName(userContext.getRealName());
            shipTaskDetail.setCreatedTime(new Date());
            shipTaskDetail.setUpdatedUserId(userContext.getUserId());
            shipTaskDetail.setUpdatedUserName(userContext.getUserName());
            shipTaskDetail.setUpdatedRealName(userContext.getRealName());
            shipTaskDetail.setUpdatedTime(new Date());
            shipTaskDetail.setVersion(NumberUtils.INTEGER_ZERO);
            shipTaskDetail.setDeleted(NumberUtils.INTEGER_ZERO);
            shipTaskDetail.setMfgTime(pickTaskDetail.getMfgTime());
            shipTaskDetail.setExpTime(pickTaskDetail.getExpTime());
            shipTaskDetail.setOriginalOrderCode(pickTaskDetail.getOriginalOrderCode());
            shipTaskDetail.setBatchNo(pickTaskDetail.getInvBatchNo());
            shipTaskDetail.setProductionBatchNo(pickTaskDetail.getProductionBatchNo());
            shipTaskDetail.setEntryOrderCode(pickTaskDetail.getEntryOrderCode());
            originalShipDetailData.add(shipTaskDetail);
        });
        return merge(originalShipDetailData);
    }

    /**
     * @param originalShipDetailData 原始数据,没有发货任务明细编号
     * @return
     */
    private List<ShipTaskDetailDo> merge(List<ShipTaskDetailDo> originalShipDetailData) {
        List<ShipTaskDetailDo> shipTaskDetailList = new ArrayList<>();
        Map<String, List<ShipTaskDetailDo>> shipDetailDataMapping = originalShipDetailData.stream().collect(Collectors.groupingBy(item -> String.join("_", item.getReferenceDetailNo(), item.getInventoryNo())));
        shipDetailDataMapping.forEach((key, list) -> {
            ShipTaskDetailDo detailData = list.get(0);
            ShipTaskDetailDo shipTaskDetail = new ShipTaskDetailDo();
            BeanUtil.copyProperties(detailData, shipTaskDetail);
            int qty = list.stream().mapToInt(ShipTaskDetailDo::getQty).sum();
            shipTaskDetail.setQty(qty);
            shipTaskDetail.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            shipTaskDetailList.add(shipTaskDetail);
        });
        return shipTaskDetailList;
    }


    /**
     * 创建发货任务头
     *
     * @param pickTaskDetails
     * @return
     */
    private ShipTaskDo buildShipTaskHeader(List<PickTaskDetailDo> pickTaskDetails) {
        OperationUserContext user = OperationUserContextHolder.get();
        Date now = new Date();
        String flowCode = pickTaskDetails.get(0).getFlowCode();
        String referenceType = pickTaskDetails.get(0).getReferenceType();
        String referenceNo = pickTaskDetails.get(0).getReferenceNo();
        String warehouseCode = pickTaskDetails.get(0).getWarehouseCode();
        int totalQty = pickTaskDetails.stream().mapToInt(PickTaskDetailDo::getOperationQty).sum();

        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        taskDo.setFlowCode(flowCode);
        taskDo.setReferenceType(referenceType);
        taskDo.setReferenceNo(referenceNo);
        taskDo.setStatus(TaskStatusEnum.INIT.getStatus());
        taskDo.setWarehouseCode(warehouseCode);
        taskDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setOperationUserId(user.getUserId());
        taskDo.setOperationRealName(user.getRealName());
        taskDo.setOperationUserName(user.getUserName());
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        taskDo.setCreatedUserId(user.getUserId());
        taskDo.setCreatedUserName(user.getUserName());
        taskDo.setCreatedRealName(user.getRealName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        taskDo.setVersion(NumberUtils.INTEGER_ZERO);
        taskDo.setDeleted(NumberUtils.INTEGER_ZERO);
        return taskDo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTaskHeadIfNecessary(String warehouseCode, String taskNo) {
        ShipTaskDo task = shipTaskQueryRepository.findByTaskNo(taskNo, warehouseCode, OperationUserContextHolder.getTenantCode());
        List<ShipTaskDetailDo> taskDetails = shipTaskDetailQueryRepository.queryByTaskNo(taskNo, warehouseCode, OperationUserContextHolder.getTenantCode());
        /*按照任务明细状态分组*/
        Map<Byte, List<ShipTaskDetailDo>> statusMapping = taskDetails.stream().collect(Collectors.groupingBy(ShipTaskDetailDo::getStatus));
        List<ShipTaskDetailDo> completeTaskDetails = statusMapping.getOrDefault(TaskStatusEnum.COMPLETE.getStatus().byteValue(), Lists.newArrayList());
        List<ShipTaskDetailDo> cancelTaskDetails = statusMapping.getOrDefault(TaskStatusEnum.CANCEL.getStatus().byteValue(), Lists.newArrayList());

        //未取消的任务执行数
        Integer notCancelNumber = taskDetails.stream().mapToInt(ShipTaskDetailDo::getQty).sum()
                - cancelTaskDetails.stream().mapToInt(ShipTaskDetailDo::getQty).sum();

        if (!task.getTotalQty().equals(notCancelNumber)) {
            ShipTaskDo update = buildTaskDo(taskNo, task, task.getStatus());
            update.setTotalQty(notCancelNumber);
            shipTaskCommandRepository.updateWithExpectStatus(update, String.valueOf(task.getStatus()));
        }

        if (completeTaskDetails.size() + cancelTaskDetails.size() != taskDetails.size()) {
            log.info("当前任务{}还没结束，无需修改状态", taskNo);
            return;
        }
        //如果到达了发货终态则不修改
        if (TaskStatusEnum.shipFinishedList.contains(task.getStatus())) {
            return;
        }
        /*要更新任务的状态*/
        /*取消的发货任务明细为空*/
        log.info("当前任务{}的所以发货任务明细全部完成，单据状态也应该是完成", taskNo);

        ShipTaskDo update;
        if (notCancelNumber == 0) {
            update = buildTaskDo(taskNo, task, TaskStatusEnum.CANCEL.getStatus());
        } else {
            update = buildTaskDo(taskNo, task, TaskStatusEnum.COMPLETE.getStatus());
        }
        shipTaskCommandRepository.updateWithExpectStatus(update, String.valueOf(task.getStatus()));
    }

    private ShipTaskDo buildTaskDo(String taskNo, ShipTaskDo task, Integer status) {
        ShipTaskDo update = new ShipTaskDo();
        update.setTaskNo(taskNo);
        update.setStatus(status);
        update.setType(task.getType());
        update.setUpdatedTime(new Date());
        return update;
    }


    /**
     * 写发货结果
     *
     * @param inventoryNo
     * @param taskDetail
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveShipTaskResult(String inventoryNo, ShipTaskDetailDo taskDetail) {
        OperationUserContext context = OperationUserContextHolder.get();
        ShipTaskDetailResultDo resultDo = new ShipTaskDetailResultDo();
        // 实例化对象
        resultDo.setDeleted(NumberUtils.INTEGER_ZERO);
        resultDo.setTaskNo(taskDetail.getTaskNo());
        if (StringUtils.isBlank(taskDetail.getTaskType()) || null == TaskTypeEnum.getTaskType(taskDetail.getTaskType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        resultDo.setTaskType(taskDetail.getTaskType());
        resultDo.setTaskDetailNo(taskDetail.getDetailNo());

        //原始单号&效期
        resultDo.setOriginalOrderCode(taskDetail.getOriginalOrderCode());
        resultDo.setExpTime(taskDetail.getExpTime());
        resultDo.setMfgTime(taskDetail.getMfgTime());


        // 商品基础信息
        resultDo.setBarcode(taskDetail.getBarcode());
        resultDo.setUniqueCode(taskDetail.getUniqueCode());
        resultDo.setSkuId(taskDetail.getSkuId());
        resultDo.setQualityLevel(taskDetail.getQualityLevel());
        resultDo.setOwnerCode(taskDetail.getOwnerCode());
        resultDo.setVendorCode(taskDetail.getVendorCode());
        resultDo.setTenantCode(taskDetail.getTenantCode());
        resultDo.setWarehouseCode(taskDetail.getWarehouseCode());

        resultDo.setInventoryNo(inventoryNo);

        //原始单号
        resultDo.setOriginalOrderCode(taskDetail.getOriginalOrderCode());
        resultDo.setExpTime(taskDetail.getExpTime());
        resultDo.setMfgTime(taskDetail.getMfgTime());


        // 操作结果信息
        resultDo.setContainerCode(taskDetail.getContainerCode());
        resultDo.setLocationCode(taskDetail.getLocationCode());

        /*第一次执行*/
        String taskDetailResultNo = IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL_RESULT.getSequenceType());
        resultDo.setResultNo(taskDetailResultNo);
        resultDo.setPlanQty(taskDetail.getQty());
        resultDo.setOperationQty(taskDetail.getQty());

        // 操作人信息
        Date currentDate = new Date();
        resultDo.setVersion(0);
        resultDo.setCreatedTime(currentDate);
        resultDo.setUpdatedTime(currentDate);
        if (Objects.nonNull(context)) {
            resultDo.setCreatedUserId(context.getUserId());
            resultDo.setCreatedUserName(context.getUserName());
            resultDo.setCreatedRealName(context.getRealName());
            resultDo.setUpdatedUserId(context.getUserId());
            resultDo.setUpdatedUserName(context.getUserName());
            resultDo.setUpdatedRealName(context.getRealName());
            resultDo.setOperationUserId(context.getUserId());
            resultDo.setOperationUserName(context.getUserName());
            resultDo.setOperationRealName(context.getRealName());
        }
        int count = shipTaskDetailResultCommandRepository.insertSelective(resultDo);
        if (count != 1) {
            log.error("保存发货任务结果失败");
            throw new WmsException("保存发货任务结果失败");
        }
    }

    /**
     * 更新发货任务明细和数量
     *
     * @param taskDetail
     * @param operationQty
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateShipTaskDetail(ShipTaskDetailDo taskDetail, int operationQty) {
        OperationUserContext context = OperationUserContextHolder.get();
        ShipTaskDetailDo update = new ShipTaskDetailDo();
        update.setDetailNo(taskDetail.getDetailNo());
        update.setTaskType(taskDetail.getTaskType());
        update.setVersion(taskDetail.getVersion());
        update.setTenantCode(taskDetail.getTenantCode());
        update.setOperationQty(operationQty);
        update.setUpdatedTime(new Date());
        update.setStatus(TaskStatusEnum.COMPLETE.getStatus().byteValue());
        if (Objects.nonNull(context)) {
            update.setUpdatedUserId(context.getUserId());
            update.setUpdatedUserName(context.getUserName());
            update.setUpdatedRealName(context.getRealName());
        }
        shipTaskDetailCommandRepository.modifyTaskDetail(update);
    }
}
