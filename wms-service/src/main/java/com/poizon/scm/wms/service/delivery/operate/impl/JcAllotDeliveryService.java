package com.poizon.scm.wms.service.delivery.operate.impl;


import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.container.model.ContainerDetailDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.handover.model.WmsHandoverBillDetailDo;
import com.poizon.scm.wms.adapter.outbound.handover.model.WmsHandoverBillDo;
import com.poizon.scm.wms.adapter.outbound.handover.model.param.HandoverDetailListQueryParam;
import com.poizon.scm.wms.adapter.outbound.handover.repository.WmsHandoverBillDetailRepository;
import com.poizon.scm.wms.adapter.outbound.handover.repository.WmsHandoverBillRepository;
import com.poizon.scm.wms.api.enums.NotifyPinkOperationTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.domain.inner.container.instance.ContainerOperateService;
import com.poizon.scm.wms.domain.inner.container.instance.ContainerQueryService;
import com.poizon.scm.wms.domain.inner.container.instance.entity.param.TakeContainerItemParam;
import com.poizon.scm.wms.domain.inner.container.instance.processor.ContainerReleaseMessageProcessor;
import com.poizon.scm.wms.domain.inner.container.instance.processor.entity.ContainerReleasePostMessageContent;
import com.poizon.scm.wms.domain.notify.notifyPinkLog.IPinkLogService;
import com.poizon.scm.wms.domain.notify.notifyPinkLog.param.WmsLogRequest;
import com.poizon.scm.wms.domain.outbound.outbound.entity.RelatedOrder;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBill;
import com.poizon.scm.wms.domain.ship.executor.LpnUnpackExecutor;
import com.poizon.scm.wms.lpn.api.container.enums.ContainerUnpackTypeEnum;
import com.poizon.scm.wms.lpn.api.container.enums.LpnContainerStepEnum;
import com.poizon.scm.wms.lpn.api.container.request.UnpackRequest;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipDetailPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipHeaderPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipPojo;
import com.poizon.scm.wms.service.delivery.operate.async.JcAllotAsyncExecutor;
import com.poizon.scm.wms.service.delivery.operate.async.JcAllotDeliveryParam;
import com.poizon.scm.wms.service.outbound.handover.producer.HandoverMessageToInvProducer;
import com.poizon.scm.wms.service.shipping.producer.DBAllocateMessageToOfcProducer;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 寄存调拨出库(寄存交接/合并退货交接出库类型)
 * 寄存交接之后通知ofc，ofc下发调拨出库单。
 * 1、更新交接单上的调拨出库单号。
 * 2、处理容器，箱明细出箱，释放容器。
 * 3、操作sci库存，扣减源仓库存，加目标仓在途。
 * 4、给wms库存服务发个消息，等wms库存扣完以后做后续流程(更新出库单为已出库状态，通知ofc已发货。)。
 *
 *
 * <AUTHOR>
 * @date 2021/11/23 6:42 下午
 */
@Slf4j
@Component
public class JcAllotDeliveryService {

    @Resource
    private WmsHandoverBillRepository wmsHandoverBillRepository;
    @Resource
    private WmsHandoverBillDetailRepository wmsHandoverBillDetailRepository;
    @Resource
    private ContainerOperateService containerOperateService;
    @Resource
    private InventoryRepository inventoryRepository;
    @Resource
    private ContainerQueryService containerQueryService;
    @Resource
    private ContainerReleaseMessageProcessor containerReleaseMessageProcessor;
    @Resource
    private JcAllotAsyncExecutor jcAllotAsyncExecutor;
    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Resource
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    DBAllocateMessageToOfcProducer product;
    @Autowired
    private HandoverMessageToInvProducer handoverMessageToInvProducer;
    @Resource
    private LpnUnpackExecutor lpnUnpackExecutor;
    @Resource
    private IPinkLogService pinkLogService;


    public void execute(WmsDeliveryBill deliveryBill) {
        JcAllotDeliveryParam deliveryParam = new JcAllotDeliveryParam();

        //拿到交接单号
        String handoverBillNo = getHandoverBillNo(deliveryBill);
        //更新交接单头的出库单号
        WmsHandoverBillDo handoverBillDo = updateHandoverBill(deliveryBill.getDeliveryOrderCode(), handoverBillNo, deliveryParam);
        List<WmsHandoverBillDetailDo> detailDos = wmsHandoverBillDetailRepository.selectListByCondition(HandoverDetailListQueryParam.builder().handoverBillNo(handoverBillNo).build());
        if (CollectionUtils.isEmpty(detailDos)) {
            log.error("未查询到原始交接单明细，param:{}", JSON.toJSONString(deliveryBill));
            throw new WmsException("未查询到原始交接单明细。");
        }
        List<String> uniqueCodes = detailDos.stream().map(WmsHandoverBillDetailDo::getUniqueCode).collect(Collectors.toList());
        List<InventoryDo> inventoryDos =
                inventoryRepository.queryInventoryDoByUniqueCode(uniqueCodes, OperationUserContextHolder.getTenantCode(), null);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(inventoryDos), "根据唯一码列表【" + JSON.toJSONString(uniqueCodes) + "】未查询到库存");

        if (StringUtils.isNotBlank(detailDos.get(0).getContainerCode())) {
            //如果使用了容器，则处理箱明细
            List<ContainerDetailDo> containerDetailDos = containerQueryService.findDetailsByUniqueCodes(uniqueCodes);
            //容器交接,箱明细出箱。
            for (ContainerDetailDo detailDo : containerDetailDos) {
                containerOperateService.takeContainerItem(TakeContainerItemParam.builder()
                        .containerCode(detailDo.getContainerCode())
                        .inventoryNo(detailDo.getInventoryNo())
                        .qty(1)
                        .build());
                takeContainerOperateLogNotify(detailDo, handoverBillDo.getSourceWarehouseCode());
                //如果容器空箱了就释放。
                if (containerOperateService.hasEmptyContainer(detailDo.getInstanceNo())) {
                    releaseContainer(detailDo);
                }
            }
            deliveryParam.setDeliveryBill(deliveryBill);
            deliveryParam.setContainerDetailDoList(containerDetailDos);
            deliveryParam.setInventoryDos(inventoryDos);

            //异步操作sci库存
            jcAllotAsyncExecutor.executeByContainer(deliveryParam);
        } else {
            deliveryParam.setDeliveryBill(deliveryBill);
            deliveryParam.setInventoryDos(inventoryDos);
            //异步操作sci库存
            jcAllotAsyncExecutor.executeByUniCode(deliveryParam);
            //异步出箱
            lpnUnpackExecutor.asyncExecute(
                    buildUnpackRequest(deliveryBill,handoverBillDo,uniqueCodes)
            );
        }
        // 寄存交接扣减wms库存
        handoverMessageToInvProducer.sendHandoverMessageToInv(deliveryParam);
    }

    /**
     * 发送出箱操作日志消息
     * @param detailDo
     */
    public void takeContainerOperateLogNotify(ContainerDetailDo detailDo, String warehouseCode) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        pinkLogService.sendSingleOperateLog(
                WmsLogRequest.builder()
                        .operatorId(userContext.getUserId())
                        .operateRealName(userContext.getRealName())
                        .operateTime(new Date())
                        .operateType(NotifyPinkOperationTypeEnum.UNBOUND.getPinkType())
                        .operateRemark(NotifyPinkOperationTypeEnum.UNBOUND.getDesc())
                        .repositoryCode(warehouseCode)
                        .uniqueCode(detailDo.getUniqueCode())
                        .containerCode(detailDo.getContainerCode())
                        .build()
        );
    }

    private UnpackRequest buildUnpackRequest(WmsDeliveryBill deliveryBill, WmsHandoverBillDo handoverBillDo, List<String> uniqueCodes) {
        UnpackRequest unpackRequest = new UnpackRequest();
        unpackRequest.setContainerUnpackType(ContainerUnpackTypeEnum.UNIQUE_CODE.getType());
        unpackRequest.setCurrentBusinessStep(LpnContainerStepEnum.HANDOVER.getStep());
        unpackRequest.setUniqueCodes(uniqueCodes);
        unpackRequest.setRequestId(handoverBillDo.getHandoverBillNo());
        unpackRequest.setWarehouseCode(deliveryBill.getWarehouseCode());
        unpackRequest.setOperationUserId(handoverBillDo.getUpdatedUserId());
        unpackRequest.setOperationUserName(handoverBillDo.getUpdatedUserName());
        // 交接单提交时间才是实际出库时间。而不是当前消息的接收时间
        unpackRequest.setOperationTime(Objects.isNull(handoverBillDo.getSubmitTime()) ? new Date() : handoverBillDo.getSubmitTime());
        unpackRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        return unpackRequest;
    }

    private String getHandoverBillNo(WmsDeliveryBill deliveryBill) {
        List<RelatedOrder> relatedOrders = deliveryBill.getRelatedOrders();
        if (CollectionUtils.isEmpty(relatedOrders)) {
            throw new WmsOperationException("寄存调拨出库单交接单号为空");
        }
        String handoverBillNo = relatedOrders.get(0).getOrderCode();
        return handoverBillNo;
    }


    @Transactional(rollbackFor = Exception.class)
    public void handoverInventorySubEnd(String billNo, String tenantId) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(billNo, tenantId);
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }
        if (WmsOutBoundStatusEnum.OUTED.getStatus().equals(deliveryHeaderDo.getStatus())){
            log.info("重复消费，库存给我们重复发了消息，不管。billNo:{},tenantId:{}",billNo,tenantId);
            return;
        }
        updateDeliveryStatus(deliveryHeaderDo);
        shipNotifyOfc(deliveryHeaderDo);
    }

    /**
     * 更新出库单号
     *
     * @param deliveryHeader
     */
    public void updateDeliveryStatus(DeliveryHeaderDo deliveryHeader) {
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setId(deliveryHeader.getId());
        updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());
        updateDeliveryHeaderDo.setOutTime(new Date());
        updateDeliveryHeaderDo.setTotalActualQty(deliveryHeader.getTotalPlanQty());
        deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
        deliveryDetailRepository.updateActualQtyByDeliveryOrderCodeExcludeCancel(deliveryHeader.getTenantCode(), deliveryHeader.getDeliveryOrderCode());
    }

    private void shipNotifyOfc(DeliveryShipPojo shipPojo) {
        log.info("通知单据进行出库,deliveryShipPojo  -> [{}]", JSON.toJSONString(shipPojo));
        product.sendJCDBAllocateMessageToOfc(shipPojo);
    }

    /**
     * 通知ofc
     *
     * @param deliveryHeader
     */
    public void shipNotifyOfc(DeliveryHeaderDo deliveryHeader) {
        DeliveryShipPojo shipPojo = buildDeliveryShipPojo(deliveryHeader);
        shipNotifyOfc(shipPojo);
    }

    /**
     * 构建通知ofc的参数
     *
     * @param deliveryHeader
     * @return
     */
    private DeliveryShipPojo buildDeliveryShipPojo(DeliveryHeaderDo deliveryHeader) {
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryHeader.getDeliveryOrderCode(), deliveryHeader.getTenantCode());
        if (CollectionUtils.isEmpty(deliveryDetailDos)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY);
        }
        Set<String> uniqueCodeSet = deliveryDetailDos.stream().map(DeliveryDetailDo::getUniqueCode).collect(Collectors.toSet());
        List<InventoryDo> inventoryDos = inventoryRepository.findByUniqueCodes(uniqueCodeSet, OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isEmpty(inventoryDos) || uniqueCodeSet.size() != inventoryDos.size()) {
            throw new WmsException("没有查到库存");
        }
        return buildDeliveryShipPojoByInventory(deliveryHeader, deliveryDetailDos, inventoryDos);
    }

    private DeliveryShipPojo buildDeliveryShipPojoByInventory(DeliveryHeaderDo deliveryHeader, List<DeliveryDetailDo> deliveryDetailDos, List<InventoryDo> inventoryDos) {
        /*出库单头*/
        DeliveryShipHeaderPojo shipHeaderPojo = new DeliveryShipHeaderPojo();
        shipHeaderPojo.setDeliveryOrderCode(deliveryHeader.getDeliveryOrderCode());
        shipHeaderPojo.setTotalActualQty(inventoryDos.size());
        shipHeaderPojo.setDeliveryOrderType(deliveryHeader.getType());
        shipHeaderPojo.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());

        Map<String, InventoryDo> inventoryDoMap = inventoryDos.stream().collect(Collectors.toMap(InventoryDo::getUniqueCode, Function.identity(), (t1, t2) -> t1));
        /*出库单明细*/
        List<DeliveryShipDetailPojo> shipDetailPojoList = new ArrayList<>();
        for (DeliveryDetailDo entity : deliveryDetailDos) {
            DeliveryShipDetailPojo shipDetailPojo = new DeliveryShipDetailPojo();
            shipDetailPojo.setWarehouseCode(deliveryHeader.getWarehouseCode());
            shipDetailPojo.setDetailNo(entity.getDetailNo());
            shipDetailPojo.setUniqueCode(entity.getUniqueCode());
            shipDetailPojo.setBizType(entity.getBizType());
            shipDetailPojo.setSkuId(entity.getSkuId());
            shipDetailPojo.setOwnerCode(entity.getOwnerCode());
            shipDetailPojo.setQualityLevel(entity.getQualityLevel());

            InventoryDo inventoryDo = inventoryDoMap.get(entity.getUniqueCode());
            if (Objects.isNull(inventoryDo)) {
                continue;
            }
            shipDetailPojo.setBatchNo(inventoryDo.getBatchNo());
            shipDetailPojo.setOriginalRelatedOrderCode(inventoryDo.getOriginalOrderCode());
            shipDetailPojo.setFirstReceiveTime(inventoryDo.getFirstReceivedTime());
            shipDetailPojo.setExpire(inventoryDo.getExpTime());
            shipDetailPojo.setMfgTime(inventoryDo.getMfgTime());
            // 唯一码场景,出库数量都是 1
            shipDetailPojo.setActualQty(NumberUtils.INTEGER_ONE);
            shipDetailPojo.setProductionBatchNo(inventoryDo.getProductionBatchNo());
            shipDetailPojoList.add(shipDetailPojo);
        }

        /*出库单对象*/
        DeliveryShipPojo deliveryShipPojo = new DeliveryShipPojo();
        deliveryShipPojo.setDeliveryModifyHeader(shipHeaderPojo);
        deliveryShipPojo.setDeliveryShipDetailPojoList(shipDetailPojoList);
        return deliveryShipPojo;
    }


    private WmsHandoverBillDo updateHandoverBill(String deliveryOrderCode,
                                                 String handoverBillNo,
                                                 JcAllotDeliveryParam deliveryParam) {
        WmsHandoverBillDo updateDo = wmsHandoverBillRepository.selectByBillNo(handoverBillNo);
        if (Objects.isNull(updateDo)) {
            log.error("未查询到原始交接单，deliveryOrderCode:{},handoverBillNo:{}", deliveryOrderCode, handoverBillNo);
            throw new WmsException("未查询到原始交接单。");
        }
        //为了支持库存唯一码找货项目，这里要提前拿到交接人和交接时间再去做更新
        deliveryParam.setHandoverTime(updateDo.getSubmitTime());
        deliveryParam.setHandoverUserId(String.valueOf(updateDo.getUpdatedUserId()));
        deliveryParam.setHandoverUserName(updateDo.getUpdatedUserName());
        deliveryParam.setTargetWarehouseCode(updateDo.getTargetWarehouseCode());

        updateDo.setAllotOutNo(deliveryOrderCode);
        OperationUserContext userContext = OperationUserContextHolder.get();
        updateDo.setUpdatedUserId(userContext.getUserId());
        updateDo.setUpdatedRealName(userContext.getRealName());
        updateDo.setUpdatedUserName(userContext.getUserName());
        wmsHandoverBillRepository.updateBillWithLock(updateDo);
        return updateDo;
    }


    private void releaseContainer(ContainerDetailDo detailDo) {
        containerReleaseMessageProcessor.sendMessage(
                ContainerReleasePostMessageContent
                        .builder()
                        .containerCode(detailDo.getContainerCode())
                        .instanceNo(detailDo.getInstanceNo())
                        .operateUserId(OperationUserContextHolder.get().getUserId())
                        .operateUserName(OperationUserContextHolder.get().getUserName())
                        .tenantId(OperationUserContextHolder.getTenantCode())
                        .warehouseCode(OperationUserContextHolder.get().getWarehouseCode())
                        .build()
        );
    }

}
