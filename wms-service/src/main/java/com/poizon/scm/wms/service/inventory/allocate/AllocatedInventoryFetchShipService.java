package com.poizon.scm.wms.service.inventory.allocate;

import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailResultCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.BaseOperationUserContext;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.dao.entitys.task.TaskEntity;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.task.biz.ship.ShipCommonHandler;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipPojo;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.frame.DataWrapper;
import com.poizon.scm.wms.service.frame.FetchService;
import com.poizon.scm.wms.third.build.CisSubtractRequestBuilder;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.athena.api.inventory.WmsInventoryApi;
import com.shizhuang.athena.api.request.inventory.QueryAllocationCommandDetailRequest;
import com.shizhuang.athena.api.response.inventory.AllocationCommandDetailResponse;
import com.shizhuang.avatar.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AllocatedInventoryFetchShipService implements FetchService<ShipTaskDetailDo> {

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Resource
    private DeliveryDetailRepository deliveryDetailRepository;
    @Resource
    private DistributeLockUtil distributeLockUtil;
    @Resource
    private ICommodityQueryV2Service commodityQueryV2Service;
    @Resource
    private WmsInventoryApi newWmsInventoryApi;
    @Resource
    private ShipTaskQueryRepository shipTaskQueryRepository;
    @Resource
    private ShipTaskCommandRepository shipTaskCommandRepository;
    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;
    @Resource
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;
    @Resource
    private ShipTaskDetailResultCommandRepository shipTaskDetailResultCommandRepository;
    @Resource
    private ShipCommonHandler shipCommonHandler;
    @Resource
    private CisInventoryOperation cisInventoryOperation;
    @Resource
    private CisSubtractRequestBuilder cisSubtractRequestBuilder;
    @Resource
    private OutboundMessageProducer outboundMessageProducer;


    @Override
    public int calculateNextPageNumber(String referenceNo, int pageSize) {
        int count =  shipTaskDetailQueryRepository.count(referenceNo);
        return count / pageSize + 1;
    }

    @Override
    public DataWrapper<ShipTaskDetailDo> pageFetch(String referenceNo, int pageSize, int pageNumber) {
        QueryAllocationCommandDetailRequest request = new QueryAllocationCommandDetailRequest();
        request.setOrderCode(referenceNo);
        request.setOrderType(WmsOutBoundTypeEnum.XNCK.getType());
        request.setPageNum(pageNumber);
        request.setPageSize(pageSize);
        log.info("请求获取库存分配记录的参数:{}", JsonUtils.serialize(request));
        Result<AllocationCommandDetailResponse> result = newWmsInventoryApi.queryAllocationCommandDetail(request);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.error("请求库存分配记录的响应结果为" + JsonUtils.serialize(result));
            throw new WmsException("请求库存分配记录的响应报文为" + JsonUtils.serialize(result));
        }
        if (!Result.SUCCESS_CODE.equals(result.getCode())) {
            String errorMsg = JsonUtils.serialize(result.getErrors());
            log.error("请求库存分配记录的响应报错:{}", errorMsg);
            throw new WmsException("请求库存分配记录的报错" + errorMsg);
        }
        log.info("请求库存分配记录的响应报文:{}", JsonUtils.serialize(result));
        return convertFrom(result.getData());
    }

    @Override
    public void verifyDataOverflow(String referenceNo, int size, int total) {
        int count = shipTaskDetailQueryRepository.count(referenceNo);
        if (count == total) {
            return;
        }
        /*已经存在的加上当前处理的超过了要处理的，说明数据溢出了*/
        if (count + size > total) {
            throw new WmsException("拉取的库存分配明细总量已经大于预计总量，单据号: " + referenceNo);
        }
    }

    /**
     * 将对象转成所需的结果
     *
     * @param response
     * @return
     */
    private DataWrapper<ShipTaskDetailDo> convertFrom(AllocationCommandDetailResponse response) {
        if (Objects.isNull(response.getTotalCommandResultLines())) {
            throw new WmsException("获取出库单库存分配记录的响应结果未给总行数，无法处理");
        }
        List<ShipTaskDetailDo> expectList = new ArrayList<>();
        List<AllocationCommandDetailResponse.AllocationCommandDetail> dataList = response.getAllocationCommandDetailList();
        if (Objects.isNull(dataList)) {
            /*为空场景：1、零出；2、正好拉取到最后一满页后，又获取了一页*/
            return new DataWrapper<ShipTaskDetailDo>().setData(expectList).setTotal(response.getTotalCommandResultLines());
        }
        String orderCode = dataList.get(0).getOrderCode();
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(orderCode);
        if (Objects.isNull(deliveryHeaderDo)){
            throw new WmsException("出库单未查到");
        }
        Set<String> skuIdSet = dataList.stream().map(AllocationCommandDetailResponse.AllocationCommandDetail::getSkuId).collect(Collectors.toSet());
        Map<String, SkuCommonRspDomain> skuMap = commodityQueryV2Service.querySkuCommonMapBySkuIds(OperationUserContextHolder.getTenantCode(), skuIdSet);
        for (AllocationCommandDetailResponse.AllocationCommandDetail detail : dataList) {
            ShipTaskDetailDo shipTaskDetail = new ShipTaskDetailDo();
            shipTaskDetail.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            shipTaskDetail.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            shipTaskDetail.setBizType(detail.getBizType().byteValue());
            shipTaskDetail.setReferenceNo(detail.getOrderCode());
            shipTaskDetail.setReferenceType(deliveryHeaderDo.getType());
            shipTaskDetail.setReferenceDetailNo(detail.getDetailCode());
            /*库存编号从任务结果中获取*/
            shipTaskDetail.setInventoryNo(detail.getInventoryNo());
            shipTaskDetail.setContainerCode(detail.getContainerCode());
            shipTaskDetail.setQty(detail.getQty());
            shipTaskDetail.setLocationCode(detail.getLocationCode());
            //后边会自动执行，所以这里直接塞成完成吧 
            shipTaskDetail.setStatus(TaskStatusEnum.COMPLETE.getStatus().byteValue());
            shipTaskDetail.setOperationQty(detail.getQty());
            shipTaskDetail.setPoNo(detail.getOriginalOrderCode());
            shipTaskDetail.setOriginalDetailNo(detail.getOriginalOrderCode());
            shipTaskDetail.setWarehouseCode(detail.getWarehouseCode());
            shipTaskDetail.setTenantCode(OperationUserContextHolder.getTenantCode());
            shipTaskDetail.setOwnerCode(detail.getOwnerCode());
            shipTaskDetail.setVendorCode(detail.getVendorCode());
            shipTaskDetail.setBarcode(StringUtils.EMPTY);
            shipTaskDetail.setUniqueCode(detail.getUniqueCode());
            shipTaskDetail.setSkuId(detail.getSkuId());
            shipTaskDetail.setQualityLevel(detail.getQualityLevel());
            shipTaskDetail.setUom(StringUtils.EMPTY);
            shipTaskDetail.setAllocatedNo(StringUtils.EMPTY);
            shipTaskDetail.setFlowCode(StringUtils.EMPTY);
            shipTaskDetail.setCreatedUserId(BaseOperationUserContext.userId);
            shipTaskDetail.setCreatedUserName(BaseOperationUserContext.userName);
            shipTaskDetail.setCreatedRealName(BaseOperationUserContext.realName);
            shipTaskDetail.setMfgTime(detail.getMfgTime());
            shipTaskDetail.setExpTime(detail.getExpTime());
            shipTaskDetail.setOriginalOrderCode(detail.getOriginalOrderCode());
            shipTaskDetail.setBatchNo(detail.getBatchNo());
            shipTaskDetail.setFirstReceivedTime(detail.getFirstReceivedTime());
            shipTaskDetail.setEntryOrderCode(detail.getEntryOrderCode());
            fillSkuInfo(skuMap, shipTaskDetail);
            expectList.add(shipTaskDetail);
        }
        return new DataWrapper<ShipTaskDetailDo>().setData(expectList).setTotal(response.getTotalCommandResultLines());
    }

    private void fillSkuInfo(Map<String, SkuCommonRspDomain> skuMap, ShipTaskDetailDo shipTaskDetail) {
        SkuCommonRspDomain skuCommonRspDomain = skuMap.get(shipTaskDetail.getSkuId());
        if (skuCommonRspDomain == null) {
            log.error("sku不存在:{}", shipTaskDetail.getSkuId());
            throw new WmsException("sku不存在");
        }
        shipTaskDetail.setSpecs(skuCommonRspDomain.getPropertyText());
        shipTaskDetail.setGoodsTitle(skuCommonRspDomain.getSpuName());
        shipTaskDetail.setGoodsPic(skuCommonRspDomain.getSkuLogoUrl());
        shipTaskDetail.setGoodsArticleNumber(skuCommonRspDomain.getArtNoMain());
        shipTaskDetail.setBarcode(skuCommonRspDomain.getBarcode());
    }

    @Transactional
    @Override
    public void process(DataWrapper<ShipTaskDetailDo> dataWrapper) {
        /*
         * 插入发货任务头/明细/结果
         * 修改出库单明细的分配数量
         * 修改出库单状态
         * */
        List<ShipTaskDetailDo> dataList = dataWrapper.data();
        String deliveryOrderCode = dataList.get(0).getReferenceNo();
        if (hadFetchAll(deliveryOrderCode,dataWrapper.total())){
            return;
        }
        boolean locked = false;
        try {
            locked = distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_HEADER, deliveryOrderCode);
            if (locked) {
                //获取任务头
                ShipTaskDo taskHeader = getTaskHeader(dataWrapper, dataList);
                //保存明细
                saveTaskDetail(dataList, taskHeader);
                //保存结果
                saveTaskDetailResult(dataList);
                //更新出库单分配数量
                updateDeliveryAllocateQty(dataList);
            }
        } catch (InterruptedException e) {
            log.warn("处理库存分配记录时获取锁失败");
            Thread.currentThread().interrupt();
        } finally {
            if (locked) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_HEADER, deliveryOrderCode);
            }
        }
    }

    public void updateDeliveryAllocateQty(List<ShipTaskDetailDo> dataList) {
        Map<String, Integer> detailMappingAllocatedQty = dataList.stream().collect(Collectors.groupingBy(ShipTaskDetailDo::getReferenceDetailNo, Collectors.summingInt(ShipTaskDetailDo::getQty)));
        List<DeliveryDetailDo> updateList = buildFrom(detailMappingAllocatedQty);
        deliveryDetailRepository.batchAddActualAllocatedQty(updateList);
    }

    public void saveTaskDetailResult(List<ShipTaskDetailDo> dataList) {
        List<ShipTaskDetailResultDo> resultDos = buildTaskDetailResults(dataList);
        shipTaskDetailResultCommandRepository.batchSave(resultDos);
    }

    public void saveTaskDetail(List<ShipTaskDetailDo> dataList, ShipTaskDo taskHeader) {
        //填充任务明细上的任务号
        dataList.forEach(x ->
                x.setTaskNo(taskHeader.getTaskNo()));
        /*按照出库单明细分组获取当前明细分配的数量*/
        int count = shipTaskDetailCommandRepository.batchSave(dataList);
        if (count != dataList.size()) {
            throw new WmsException("插入发货任务明细报错");
        }
    }

    private List<ShipTaskDetailResultDo> buildTaskDetailResults(List<ShipTaskDetailDo> detailDos) {
        List<ShipTaskDetailResultDo> resultDos = new ArrayList<>(detailDos.size());
        for (ShipTaskDetailDo detailDo : detailDos) {
            ShipTaskDetailResultDo detailResultDo = new ShipTaskDetailResultDo();
            detailResultDo.setDeleted(NumberUtils.INTEGER_ZERO);
            detailResultDo.setTaskNo(detailDo.getTaskNo());
            if (org.apache.commons.lang3.StringUtils.isBlank(detailDo.getTaskType()) || null == TaskTypeEnum.getTaskType(detailDo.getTaskType())) {
                throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
            }
            detailResultDo.setTaskType(detailDo.getTaskType());
            detailResultDo.setTaskDetailNo(detailDo.getDetailNo());

            // 商品基础信息
            detailResultDo.setBarcode(detailDo.getBarcode());
            detailResultDo.setUniqueCode(detailDo.getUniqueCode());
            detailResultDo.setSkuId(detailDo.getSkuId());
            detailResultDo.setQualityLevel(detailDo.getQualityLevel());
            detailResultDo.setOwnerCode(detailDo.getOwnerCode());
            detailResultDo.setVendorCode(detailDo.getVendorCode());
            detailResultDo.setTenantCode(detailDo.getTenantCode());
            detailResultDo.setWarehouseCode(detailDo.getWarehouseCode());

            detailResultDo.setInventoryNo(detailDo.getInventoryNo());

            //原始单号&效期
            detailResultDo.setMfgTime(detailDo.getMfgTime());
            detailResultDo.setExpTime(detailDo.getExpTime());
            detailResultDo.setOriginalOrderCode(detailDo.getOriginalOrderCode());

            /*第一次执行*/
            String taskDetailResultNo = IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL_RESULT.getSequenceType());
            detailResultDo.setResultNo(taskDetailResultNo);
            detailResultDo.setOperationQty(detailDo.getQty());

            // 操作人信息
            Date currentDate = new Date();
            detailResultDo.setVersion(0);
            detailResultDo.setCreatedTime(currentDate);
            detailResultDo.setUpdatedTime(currentDate);
            detailResultDo.setCreatedUserId(detailDo.getCreatedUserId());
            detailResultDo.setCreatedUserName(detailDo.getCreatedUserName());
            detailResultDo.setCreatedRealName(detailDo.getCreatedRealName());
            detailResultDo.setUpdatedUserId(detailDo.getCreatedUserId());
            detailResultDo.setUpdatedUserName(detailDo.getCreatedUserName());
            detailResultDo.setUpdatedRealName(detailDo.getUpdatedRealName());
            detailResultDo.setOperationUserId(detailDo.getCreatedUserId());
            detailResultDo.setOperationUserName(detailDo.getCreatedUserName());
            detailResultDo.setOperationRealName(detailDo.getCreatedRealName());
            resultDos.add(detailResultDo);
        }
        return resultDos;
    }

    private ShipTaskDo getTaskHeader(DataWrapper<ShipTaskDetailDo> dataWrapper, List<ShipTaskDetailDo> dataList) {
        List<ShipTaskDo> existTask = shipTaskQueryRepository.findByReferenceNo(dataList.get(0).getReferenceNo());
        if (CollectionUtils.isNotEmpty(existTask)) {
            return existTask.iterator().next();
        }
        //第一页，还没生成发货任务，所以去生成一个发货任务头。
        ShipTaskDo taskDo = buildTaskHeader(dataWrapper, dataList);
        int saveHeaderResult = shipTaskCommandRepository.save(taskDo);
        if (saveHeaderResult != 1){
            throw new WmsException("插入发货任务头报错");
        }
        return taskDo;
    }

    private ShipTaskDo buildTaskHeader(DataWrapper<ShipTaskDetailDo> dataWrapper, List<ShipTaskDetailDo> dataList) {
        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        ShipTaskDetailDo detailDo = dataList.get(0);
        String warehouseCode = detailDo.getWarehouseCode();
        int totalQty = dataWrapper.total();
        taskDo.setReferenceType(detailDo.getReferenceType());
        taskDo.setReferenceNo(detailDo.getReferenceNo());
        taskDo.setStatus(TaskStatusEnum.EXECUTING.getStatus());
        taskDo.setWarehouseCode(warehouseCode);
        taskDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        Date now = new Date();
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setCreatedUserId(detailDo.getCreatedUserId());
        taskDo.setCreatedUserName(detailDo.getCreatedUserName());
        taskDo.setCreatedRealName(detailDo.getCreatedRealName());
        taskDo.setOperationUserId(detailDo.getCreatedUserId());
        taskDo.setOperationRealName(detailDo.getCreatedRealName());
        taskDo.setOperationUserName(detailDo.getCreatedUserName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        return taskDo;
    }

    /**
     * 通过分配记录构造出库单明细的更新对象
     *
     * @param detailMappingAllocatedQty
     * @return
     */
    private List<DeliveryDetailDo> buildFrom(Map<String, Integer> detailMappingAllocatedQty) {
        List<DeliveryDetailDo> deliveryDetailDos = new ArrayList<>();
        detailMappingAllocatedQty.forEach((detailNo, allocatedQty) -> {
            DeliveryDetailDo deliveryDetailDo = new DeliveryDetailDo();
            deliveryDetailDo.setDetailNo(detailNo);
            deliveryDetailDo.setAllocatedQty(allocatedQty);
            deliveryDetailDos.add(deliveryDetailDo);
        });
        return deliveryDetailDos;
    }

    @Override
    public boolean hadFetchAll(String referenceNo, int total) {
        int count = shipTaskDetailQueryRepository.count(referenceNo);
        if (count > total) {
            throw new WmsException("库存分配明细总量大于预计总量，单据号: " + referenceNo);
        }
        return count == total;
    }

    @Override
    public List<ShipTaskDetailDo> filterNotProcessed(String referenceNo, List<ShipTaskDetailDo> originalList) {
        /*库存服务没有回传库存分配编号，所以没有办法过滤已经拉取的数据，这里只能抛出异常了*/
        throw new WmsException("库存分配暂不支持兜底拉取策略");
    }

    @Transactional
    @Override
    public void afterFetchAll(String referenceNo) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(referenceNo);
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }
        //更新单据分配状态
//        updateDeliveryHeaderStatus(deliveryHeaderDo);
        //更新任务头
        ShipTaskDo shipTaskDo = updateTaskHeader(deliveryHeaderDo);
        //通知单据
        TaskEntity taskEntity = BeanUtil.deepCopy(shipTaskDo, TaskEntity.class);
        DeliveryShipPojo deliveryShipPojo = shipCommonHandler.notifyDeliveryOrder(taskEntity, TaskStatusEnum.COMPLETE);
        //扣wms库存
        shipCommonHandler.executeShipInventory(taskEntity);
        //扣sci库存
        cisInventoryOperation.sendSubtractSciInventoryMsg(cisSubtractRequestBuilder.buildCisSubRequest3(deliveryShipPojo));
        //通知ofc
        outboundMessageProducer.xnckDeliveryShip2Ofc(referenceNo);

    }

    private ShipTaskDo updateTaskHeader(DeliveryHeaderDo deliveryHeaderDo) {
        List<ShipTaskDo> headerList = shipTaskQueryRepository.findByReferenceNo(deliveryHeaderDo.getDeliveryOrderCode());
        if (CollectionUtils.isEmpty(headerList)){
            throw new WmsException("没找到发货任务头，有问题。");
        }
        ShipTaskDo taskDo = headerList.iterator().next();
        taskDo.setStatus(TaskStatusEnum.COMPLETE.getStatus());
        taskDo.setEndTime(new Date());
        taskDo.setUpdatedTime(new Date());
        shipTaskCommandRepository.updateSelectiveUseVersionLock(taskDo);
        return taskDo;
    }


    /**
     * 更新单据的分配状态
     *
     * @param shipmentNo
     */
    private void updateDeliveryHeaderStatus(DeliveryHeaderDo deliveryHeaderDo) {
        int totalAllocatedQty = shipTaskDetailQueryRepository.sumQty(deliveryHeaderDo.getDeliveryOrderCode());
        WmsOutBoundStatusEnum status;
        if (totalAllocatedQty <= 0) {
            status = WmsOutBoundStatusEnum.FAIL_ALLOCATE;
        } else {
            status = totalAllocatedQty == deliveryHeaderDo.getTotalPlanQty() ? WmsOutBoundStatusEnum.WHOLE_ALLOCATE : WmsOutBoundStatusEnum.PART_ALLOCATE;
        }
        deliveryHeaderRepository.updateStatusByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(), status.getStatus());
    }

}
