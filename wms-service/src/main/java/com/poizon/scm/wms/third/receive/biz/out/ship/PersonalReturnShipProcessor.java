package com.poizon.scm.wms.third.receive.biz.out.ship;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.domain.inner.container.instance.processor.TakeContainerItemByUnicodeProcessor;
import com.poizon.scm.wms.pojo.third.receive.request.TaskReceiveRequest;
import com.poizon.scm.wms.service.shipping.ShippingService;
import com.poizon.scm.wms.third.receive.biz.out.ship.base.AbstractShipProcessor;
import com.poizon.scm.wms.util.DeliveryHeaderUtils;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/20 10:03
 * @Description 直接出库的
 * 目前直接出库的场景有
 * 1.个人寄存退货
 */
@Component
@Slf4j
public class PersonalReturnShipProcessor extends AbstractShipProcessor {

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private ShippingService shippingService;

    @Resource
    private CisInventoryOperation cisInventoryOperation;

    @Resource
    private TakeContainerItemByUnicodeProcessor takeContainerItemByUnicodeProcessor;

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Override
    public List<String> getTenantIdList() {
        return Lists.newArrayList(TenantEnum.dwAllTenantList);
    }

    @Override
    public Boolean matchProcessor(DeliveryHeaderDo deliveryHeaderDo) {
        String deliveryType = deliveryHeaderDo.getType();
        Integer deliveryBizType = deliveryHeaderDo.getBizType();
        // 个人寄存退
        if (WmsOutboundBusinessUtils.personalDepositReturn(deliveryType, deliveryBizType)) {
            if (DeliveryHeaderUtils.ifGoodsFlow(deliveryHeaderDo.getContainsGoodsFlow())) {
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public void handle(TaskReceiveRequest receiveRequest, DeliveryHeaderDo deliveryHeaderDo) {
        // 1.lpn出箱
        unPackLpnForShip(receiveRequest);
        reSetWarehouseCodeAndSignId(deliveryHeaderDo.getWarehouseCode(), receiveRequest.getSignId());
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(),
                OperationUserContextHolder.getTenantCode());
        String uniqueCode = deliveryDetailDos.get(0).getUniqueCode();
        // 2.个人寄存退, 容器出箱
        takeContainerItemByUnicodeProcessor.process(uniqueCode);
        InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(uniqueCode);
        if (null == inventoryDo) {
            log.info("出库单{}没有库存", receiveRequest.getOrderCode());
            updateDeliveryStatus(deliveryHeaderDo);
            return;
        }
        //现货&个人寄存, 库存类型等真或询问, 直接扣wms库存并更新对应单据状态
        log.info("开始处理操作仓现货、个人寄存发货, 单据[{}], 唯一码[{}]", receiveRequest.getOrderCode(), uniqueCode);
        shippingService.operationWarehouseShip(receiveRequest);
        // 个人寄存退货需要去扣sci库存
        cisInventoryOperation.sendSubtractSciInventoryMsg(cisInventoryOperation.buildCisSubRequest(
                deliveryHeaderDo, deliveryDetailDos.get(0), inventoryDo));
    }

    private void updateDeliveryStatus(DeliveryHeaderDo deliveryHeaderDo) {
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setId(deliveryHeaderDo.getId());
        updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());
        updateDeliveryHeaderDo.setOutTime(new Date());
        updateDeliveryHeaderDo.setTotalActualQty(1);
        int deliveryHeaderRows = deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
        if (deliveryHeaderRows <= NumberUtils.INTEGER_ZERO) {
            throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
        }

    }
}
