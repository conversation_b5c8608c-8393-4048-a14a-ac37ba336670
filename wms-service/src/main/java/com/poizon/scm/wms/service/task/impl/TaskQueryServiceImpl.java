package com.poizon.scm.wms.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.TaskDetailRepository;
import com.poizon.scm.wms.adapter.common.TaskRepository;
import com.poizon.scm.wms.adapter.common.model.TaskDo;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.BeanUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.dao.entitys.BaseEntity;
import com.poizon.scm.wms.dao.entitys.task.TaskDetailEntity;
import com.poizon.scm.wms.dao.entitys.task.TaskDetailResultEntity;
import com.poizon.scm.wms.dao.entitys.task.TaskEntity;
import com.poizon.scm.wms.dao.mappers.task.TaskDetailMapper;
import com.poizon.scm.wms.dao.mappers.task.TaskDetailResultMapper;
import com.poizon.scm.wms.dao.mappers.task.TaskMapper;
import com.poizon.scm.wms.dao.utils.MybatisPlusPageUtils;
import com.poizon.scm.wms.domain.task.TaskCacheHelper;
import com.poizon.scm.wms.pojo.task.*;
import com.poizon.scm.wms.pojo.task.param.*;
import com.poizon.scm.wms.service.base.BaseServiceImpl;
import com.poizon.scm.wms.service.task.TaskBaseService;
import com.poizon.scm.wms.service.task.TaskDetailBaseService;
import com.poizon.scm.wms.service.task.TaskDetailResultBasService;
import com.poizon.scm.wms.service.task.TaskQueryService;
import com.poizon.scm.wms.util.enums.TaskDetailStatusEnum;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.dao.entitys.task.TaskDetailResultEntity.COL_TASK_DETAIL_NO;

/**
 * 任务查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/21 3:09 下午
 */
@Slf4j
@Service(value = "taskQueryService")
public class TaskQueryServiceImpl extends BaseServiceImpl implements TaskQueryService {

    @Autowired
    private TaskBaseService taskBaseService;

    @Autowired
    private TaskDetailBaseService taskDetailBaseService;

    @Autowired
    private TaskDetailResultBasService taskDetailResultBasService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private TaskDetailMapper taskDetailMapper;

    @Autowired
    private TaskDetailResultMapper taskDetailResultMapper;

    @Autowired
    private TaskRepository taskRepository;

    @Value("${basePage.maxPageSize:200}")
    private Integer maxPageSize;

    @Autowired
    private TaskDetailRepository taskDetailRepository;

    @Autowired
    private TaskCacheHelper taskCacheHelper;


    @Override
    public PagingObject<BaseTaskPojo> findTaskByPage(@NotNull TaskQueryPojo taskQueryPojo) {
        /*参数为空校验*/
        if (StringUtils.isBlank(taskQueryPojo.getType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        if (StringUtils.isBlank(taskQueryPojo.getWarehouseCode())) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_IS_NULL);
        }
        if (StringUtils.isBlank(taskQueryPojo.getType()) || null == TaskTypeEnum.getTaskType(taskQueryPojo.getType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        if (maxPageSize != null && taskQueryPojo.getPageSize() > maxPageSize) {
            taskQueryPojo.setPageSize(maxPageSize);
        }
        /*封装查询参数*/
        String tenantCode = OperationUserContextHolder.getTenantCode();
        TaskEntity taskParam = new TaskEntity();
        taskParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskParam.setType(taskQueryPojo.getType());
        taskParam.setWarehouseCode(taskQueryPojo.getWarehouseCode());
        taskParam.setReferenceNo(taskQueryPojo.getReferenceNo());
        taskParam.setReferenceType(taskQueryPojo.getReferenceType());
        taskParam.setTenantCode(tenantCode);
        Page<TaskEntity> taskEntityPage = new Page(taskQueryPojo.getPageNum(), taskQueryPojo.getPageSize());
        QueryWrapper<TaskEntity> wrapper = new QueryWrapper<>(taskParam);
        wrapper.in(NumberUtils.INTEGER_ONE.equals(taskQueryPojo.getStatus()), TaskEntity.COL_STATUS,
                TaskStatusEnum.EXECUTING.getStatus(), TaskStatusEnum.INIT.getStatus());
        // 添加查询字段
        wrapper.select(TaskEntity.COL_TASK_NO, TaskEntity.COL_REFERENCE_TYPE,
                TaskEntity.COL_REFERENCE_NO, TaskEntity.COL_LAUNCH_NO, TaskEntity.COL_TYPE,
                TaskEntity.COL_STATUS, TaskEntity.COL_TOTAL_QTY, TaskEntity.COL_CREATED_TIME);
        /*查询任务头*/
        if (StringUtils.equals(TaskTypeEnum.PICK.getTaskType(), taskQueryPojo.getType())) {
            /*如果是捡货任务,需要按照日期升序*/
            wrapper.orderByAsc(TaskEntity.COL_CREATED_TIME);
        } else {
            wrapper.orderByDesc(TaskEntity.COL_ID);
            //wrapper.orderByDesc(TaskEntity.COL_CREATED_TIME);
        }
        taskEntityPage = this.taskBaseService.page(taskEntityPage, wrapper);
        if (null == taskEntityPage || CollectionUtils.isEmpty(taskEntityPage.getRecords())) {
            return null;
        } else {
            /*封装返回值*/
            PagingObject pagingObject = new PagingObject();
            pagingObject.setPageNum(taskQueryPojo.getPageNum());
            pagingObject.setPageSize(taskQueryPojo.getPageSize());
            pagingObject.setTotal(taskEntityPage.getTotal());

            Map<String, TaskDetailEntity> taskOperationCountMap = null;
            if (taskQueryPojo.isNeedOperationNum()) {
                Set<String> taskNoSet = taskEntityPage.getRecords().stream()
                        .map(TaskEntity::getTaskNo).collect(Collectors.toSet());
                taskOperationCountMap = this.taskDetailBaseService.countOperationQtyByTaskNos(taskQueryPojo.getType(), taskNoSet, tenantCode, taskQueryPojo.getWarehouseCode());
            } else {
                taskOperationCountMap = new HashMap<>(NumberUtils.INTEGER_ZERO);
            }
            List<BaseTaskPojo> taskPojoList = new ArrayList<>(taskEntityPage.getRecords().size());
            /*遍历执行对象转化*/
            for (TaskEntity entity : taskEntityPage.getRecords()) {
                BaseTaskPojo baseTaskPojo = buildBaseTaskPojo(entity);
                if (taskQueryPojo.isNeedOperationNum()) {
                    TaskDetailEntity temp = taskOperationCountMap.get(baseTaskPojo.getTaskNo());
                    if (null != temp && null != temp.getOperationQty()) {
                        baseTaskPojo.setOperationQty(temp.getOperationQty());
                    }
                }
                taskPojoList.add(baseTaskPojo);
            }
            pagingObject.setContents(taskPojoList);
            return pagingObject;
        }
    }

    /**
     * 构建任务头
     *
     * @param entity
     * @return
     */
    private TaskHeaderPojo buildTaskHeaderPojo(TaskEntity entity) {
        TaskHeaderPojo baseTaskPojo = new TaskHeaderPojo();
        baseTaskPojo.setTaskNo(entity.getTaskNo());
        baseTaskPojo.setStatus(entity.getStatus());
        baseTaskPojo.setWarehouseCode(entity.getWarehouseCode());
        baseTaskPojo.setTotalQty(entity.getTotalQty());
        return baseTaskPojo;
    }

    /**
     * 构建任务头
     * 已废弃使用，请使用buildBaseTaskPojoByTaskDo方法
     *
     * @param entity
     * @return
     */
    @Deprecated
    private BaseTaskPojo buildBaseTaskPojo(TaskEntity entity) {
        BaseTaskPojo baseTaskPojo = new BaseTaskPojo();
        baseTaskPojo.setTaskNo(entity.getTaskNo());
        baseTaskPojo.setReferenceType(entity.getReferenceType());
        baseTaskPojo.setReferenceNo(entity.getReferenceNo());
        baseTaskPojo.setType(entity.getType());
        baseTaskPojo.setStatus(entity.getStatus());
        baseTaskPojo.setTotalQty(entity.getTotalQty());
        baseTaskPojo.setWarehouseCode(entity.getWarehouseCode());
        baseTaskPojo.setLaunchNo(entity.getLaunchNo());
        return baseTaskPojo;
    }

    private BaseTaskPojo buildBaseTaskPojoByTaskDo(TaskDo taskDo) {
        BaseTaskPojo baseTaskPojo = new BaseTaskPojo();
        baseTaskPojo.setTaskNo(taskDo.getTaskNo());
        baseTaskPojo.setReferenceType(taskDo.getReferenceType());
        baseTaskPojo.setReferenceNo(taskDo.getReferenceNo());
        baseTaskPojo.setType(taskDo.getType());
        baseTaskPojo.setStatus(taskDo.getStatus());
        baseTaskPojo.setTotalQty(taskDo.getTotalQty());
        baseTaskPojo.setWarehouseCode(taskDo.getWarehouseCode());
        baseTaskPojo.setLaunchNo(taskDo.getLaunchNo());
        return baseTaskPojo;
    }

    /**
     * 构建拣货任务
     *
     * @param taskDo
     * @return
     */
    private PickTaskPojo buildPickTaskPojo(TaskDo taskDo) {
        BaseTaskPojo baseTaskPojo = buildBaseTaskPojoByTaskDo(taskDo);
        PickTaskPojo pickTaskPojo = new PickTaskPojo();
        BeanUtil.copyProperties(baseTaskPojo, pickTaskPojo);
        pickTaskPojo.setUpdatedTime(taskDo.getUpdatedTime());
        return pickTaskPojo;
    }

    @Override
    public List<BaseTaskDetailPojo> findTaskDetail(@NotNull TaskDetailQueryPojo taskDetailQueryPojo) {
        /*参数为空校验*/
        if (StringUtils.isBlank(taskDetailQueryPojo.getTaskNo())
                && StringUtils.isBlank(taskDetailQueryPojo.getReferenceNo())
                && StringUtils.isBlank(taskDetailQueryPojo.getTaskDetailNo())) {
            throw new WmsException(WmsExceptionCode.TASK_NO_NULL);
        }
        if (StringUtils.isBlank(taskDetailQueryPojo.getWarehouseCode())) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_IS_NULL);
        }
        if (StringUtils.isBlank(taskDetailQueryPojo.getTaskType())
                || null == TaskTypeEnum.getTaskType(taskDetailQueryPojo.getTaskType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        OperationUserContext userContext = OperationUserContextHolder.get();
        /*封装查询参数*/
        TaskDetailEntity taskDetailParam = new TaskDetailEntity();
        taskDetailParam.setDeleted(NumberUtils.INTEGER_ZERO);
        if (StringUtils.isNotEmpty(taskDetailQueryPojo.getTaskNo())) {
            taskDetailParam.setTaskNo(taskDetailQueryPojo.getTaskNo());
        }
        if (StringUtils.isNotBlank(taskDetailQueryPojo.getTaskDetailNo())) {
            taskDetailParam.setDetailNo(taskDetailQueryPojo.getTaskDetailNo());
        }
        if (StringUtils.isNotBlank(taskDetailQueryPojo.getReferenceNo())) {
            taskDetailParam.setReferenceNo(taskDetailQueryPojo.getReferenceNo());
        }
        if (StringUtils.isNotBlank(taskDetailQueryPojo.getReferenceDetailNo())) {
            taskDetailParam.setReferenceDetailNo(taskDetailQueryPojo.getReferenceDetailNo());
        }
        taskDetailParam.setTaskType(taskDetailQueryPojo.getTaskType());
        taskDetailParam.setWarehouseCode(taskDetailQueryPojo.getWarehouseCode());
        taskDetailParam.setTenantCode(userContext.getTenantCode());
        QueryWrapper queryWrapper = new QueryWrapper<>(taskDetailParam).orderByDesc(TaskDetailResultEntity.COL_CREATED_TIME);
        if (CollectionUtils.isNotEmpty(taskDetailQueryPojo.getStatusList())) {
            queryWrapper.in(TaskDetailEntity.COL_STATUS, taskDetailQueryPojo.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(taskDetailQueryPojo.getSkuIds())) {
            queryWrapper.in(TaskDetailEntity.COL_SKU_ID, taskDetailQueryPojo.getSkuIds());
        }
        /*执行查询*/
        return doQueryTaskDetail(taskDetailQueryPojo.getTaskType(), queryWrapper);
    }

    /**
     * 公共查询
     *
     * @param taskType
     * @param queryWrapper
     * @return
     */
    private List<BaseTaskDetailPojo> doQueryTaskDetail(String taskType, QueryWrapper queryWrapper) {
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        List<TaskDetailEntity> detailEntityList = this.taskDetailBaseService.list(queryWrapper);
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return null;
        }
        /*封装返回值.遍历执行对象转化*/
        List<BaseTaskDetailPojo> detailPojoList = new ArrayList<>(detailEntityList.size());

        /*查询任务执行结果*/
        Set<String> taskNos = detailEntityList.stream()
                .map(TaskDetailEntity::getTaskNo).collect(Collectors.toSet());

        Map<String, List<TaskDetailResultEntity>> taskDetailResultEntityMap = this.taskDetailResultBasService.queryByTaskNos(taskType, taskNos);

        for (TaskDetailEntity entity : detailEntityList) {
            /*构建任务明细*/
            BaseTaskDetailPojo taskDetailPojo = buildTaskDetailPojo(entity);

            /*构建任务结果*/
            buildTaskDetailResult(taskDetailResultEntityMap, entity, taskDetailPojo);

            // 添加到返回值中
            detailPojoList.add(taskDetailPojo);
        }

        return detailPojoList;

    }

    /**
     * 构建任务结果
     *
     * @param taskDetailResultEntityMap
     * @param entity
     * @param taskDetailPojo
     */
    private void buildTaskDetailResult(Map<String, List<TaskDetailResultEntity>> taskDetailResultEntityMap,
                                       TaskDetailEntity entity, BaseTaskDetailPojo taskDetailPojo) {
        /*构建任务结果*/
        if (taskDetailResultEntityMap.containsKey(entity.getDetailNo())) {
            List<TaskDetailResultEntity> tempResultEntityList = taskDetailResultEntityMap.get(entity.getDetailNo());
            List<BaseDetailResultPojo> detailResultPojoList = new ArrayList<>(tempResultEntityList.size());
            for (TaskDetailResultEntity resultEntity : tempResultEntityList) {
                // 构建任务结果
                BaseDetailResultPojo detailResultPojo = new BaseDetailResultPojo();
                BeanUtils.copyProperties(resultEntity, detailResultPojo);
                detailResultPojoList.add(detailResultPojo);
            }
            taskDetailPojo.setDetailResultPojoList(detailResultPojoList);
        }
    }

    /**
     * 构建任务明细
     *
     * @param entity
     * @return
     */
    private BaseTaskDetailPojo buildTaskDetailPojo(TaskDetailEntity entity) {
        BaseTaskDetailPojo taskDetailPojo = new BaseTaskDetailPojo();
        BeanUtils.copyProperties(entity, taskDetailPojo);
        return taskDetailPojo;
    }

    @Override
    public List<TaskPojo> findTasks(@NotNull CodeQueryPojo codeQueryPojo) {
        /*参数为空校验*/
        if (StringUtils.isBlank(codeQueryPojo.getType())) {
            throw new WmsException(WmsExceptionCode.TASK_NO_NULL);
        }
        if (StringUtils.isBlank(codeQueryPojo.getWarehouseCode())) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_IS_NULL);
        }
        /*封装查询参数*/
        if (StringUtils.isBlank(codeQueryPojo.getUniqueCode()) &&
                StringUtils.isBlank(codeQueryPojo.getBarcode())
                && StringUtils.isBlank(codeQueryPojo.getDetailNo())
                && CollectionUtils.isEmpty(codeQueryPojo.getSkuIds())) {
            log.warn("唯一码或商品编码或任务详情单号不能为空");
            throw new WmsOperationException(WmsExceptionCode.PARAMS_IS_EMPTY);
        }
        /*构建查询参数*/
        OperationUserContext userContext = OperationUserContextHolder.get();
        TaskDetailEntity taskDetailParam = new TaskDetailEntity();
        if (StringUtils.isNotEmpty(codeQueryPojo.getTaskNo())) {
            taskDetailParam.setTaskNo(codeQueryPojo.getTaskNo());
        }
        taskDetailParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskDetailParam.setTaskType(codeQueryPojo.getType());
        taskDetailParam.setWarehouseCode(codeQueryPojo.getWarehouseCode());
        taskDetailParam.setTenantCode(userContext.getTenantCode());
        if (StringUtils.isNotBlank(codeQueryPojo.getUniqueCode())) {
            taskDetailParam.setUniqueCode(codeQueryPojo.getUniqueCode());
        }
        if (StringUtils.isNotBlank(codeQueryPojo.getDetailNo())) {
            taskDetailParam.setDetailNo(codeQueryPojo.getDetailNo());
        }
        if (StringUtils.isNotBlank(codeQueryPojo.getBarcode())) {
            taskDetailParam.setBarcode(codeQueryPojo.getBarcode());
        }
        if (StringUtils.isNotEmpty(codeQueryPojo.getContainerCode())) {
            taskDetailParam.setContainerCode(codeQueryPojo.getContainerCode());
        }
        if (StringUtils.isNotEmpty(codeQueryPojo.getLocationCode())) {
            taskDetailParam.setLocationCode(codeQueryPojo.getLocationCode());
        }
        if (StringUtils.isNotEmpty(codeQueryPojo.getReferenceNo())) {
            taskDetailParam.setReferenceNo(codeQueryPojo.getReferenceNo());
        }
        if (StringUtils.isNotEmpty(codeQueryPojo.getReferenceType())) {
            taskDetailParam.setReferenceNo(codeQueryPojo.getReferenceType());
        }
        if (StringUtils.isNotBlank(codeQueryPojo.getDetailNo())) {
            taskDetailParam.setDetailNo(codeQueryPojo.getDetailNo());
        }
        /*查询任务明细*/
        QueryWrapper queryWrapper = new QueryWrapper<>(taskDetailParam);
        if (codeQueryPojo.getStatus() != null) {
            if (NumberUtils.INTEGER_ONE.equals(codeQueryPojo.getStatus())) {
                queryWrapper.in(TaskDetailEntity.COL_STATUS, TaskStatusEnum.EXECUTING.getStatus()
                        , TaskStatusEnum.INIT.getStatus());
            }else if(NumberUtils.INTEGER_ZERO.equals(codeQueryPojo.getStatus())){
                queryWrapper.in(TaskDetailEntity.COL_STATUS, TaskStatusEnum.EXECUTING.getStatus()
                        , TaskStatusEnum.INIT.getStatus(),TaskStatusEnum.COMPLETE.getStatus());
            } else {
                queryWrapper.eq(TaskDetailEntity.COL_STATUS, codeQueryPojo.getStatus());
            }
        }
        if (CollectionUtils.isNotEmpty(codeQueryPojo.getSkuIds())) {
            queryWrapper.in(TaskDetailEntity.COL_SKU_ID, codeQueryPojo.getSkuIds());
        }
        List<TaskDetailEntity> detailEntityList = this.taskDetailBaseService.list(queryWrapper);
        if (CollectionUtils.isEmpty(detailEntityList)) {
            log.info("查询结果为空,taskDetailPara -> [{}]", JSON.toJSONString(taskDetailParam));
            return null;
        }
        Map<String, List<TaskDetailEntity>> taskDetailMap = detailEntityList.stream()
                .collect(Collectors.groupingBy(TaskDetailEntity::getTaskNo));

        /*查询任务头*/
        List<TaskEntity> taskEntityList = this.taskMapper.findTaskByNos(taskDetailMap.keySet(),
                taskDetailParam.getWarehouseCode(), taskDetailParam.getTenantCode(), taskDetailParam.getTaskType());
        if (CollectionUtils.isEmpty(taskEntityList) || taskEntityList.size() != taskDetailMap.keySet().size()) {
            log.error("任务查询失败,taskDetailMap.keySet -> [{}] , taskEntityList -> [{}]",
                    JSON.toJSONString(taskDetailMap.keySet()), JSON.toJSONString(taskEntityList));
        }

        /*遍历数据,封装返回结果*/
        List<TaskPojo> taskPojoList = new ArrayList<>(taskEntityList.size());
        taskEntityList.stream().forEach(entity -> {
            TaskPojo taskPojo = new TaskPojo();
            taskPojo.setTaskHeaderPojo(buildBaseTaskPojo(entity));

            /*构建任务明细*/
            List<TaskDetailEntity> tempDetailEntityList = taskDetailMap.get(entity.getTaskNo());
            if (CollectionUtils.isEmpty(tempDetailEntityList)) {
                log.error("任务查询失败,entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.TASK_QUERY_FAIL);
            }
            List<BaseTaskDetailPojo> taskDetailPojoList = new ArrayList<>(tempDetailEntityList.size());
            tempDetailEntityList.stream().forEach(detailEntity -> {
                taskDetailPojoList.add(buildTaskDetailPojo(detailEntity));
            });
            taskPojo.setTaskDetailPojoList(taskDetailPojoList);

            taskPojoList.add(taskPojo);
        });
        return taskPojoList;
    }


    @Override
    public Page<TaskDetailPageRspPojo> queryTaskDetailByPage(@Valid TaskDetailPageQueryPojo pojo) {
        Page<TaskDetailPageRspPojo> page = MybatisPlusPageUtils.convert2MpPage(pojo);
        if (StringUtils.isBlank(pojo.getType()) || null == TaskTypeEnum.getTaskType(pojo.getType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        pojo.setTenantCode(OperationUserContextHolder.getTenantCode());
        page = taskDetailMapper.selectTaskDetailByPage(page, pojo);
        return page;
    }


    @Override
    public List<BaseTaskDetailPojo> taskDetailList(TaskDetailListQueryPojo queryPojo) {
        TaskDetailEntity taskDetailParam = new TaskDetailEntity();
        if (StringUtils.isBlank(queryPojo.getTaskType()) || null == TaskTypeEnum.getTaskType(queryPojo.getTaskType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        String taskType = queryPojo.getTaskType();
        BeanUtils.copyProperties(queryPojo, taskDetailParam);
        taskDetailParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskDetailParam.setTaskType(taskType);
        //租户和仓库
        taskDetailParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        taskDetailParam.setWarehouseCode(queryPojo.getWarehouseCode());
        QueryWrapper queryWrapper = new QueryWrapper<>(taskDetailParam).orderByDesc(TaskDetailResultEntity.COL_CREATED_TIME);
        if (CollectionUtils.isNotEmpty(queryPojo.getTaskDetailStatusList())) {
            queryWrapper.in(TaskDetailEntity.COL_STATUS, queryPojo.getTaskDetailStatusList());
        }
        if (CollectionUtils.isNotEmpty(queryPojo.getSkuIds())) {
            queryWrapper.in(TaskDetailEntity.COL_SKU_ID, queryPojo.getSkuIds());
        }
        return doQueryTaskDetail(taskType, queryWrapper);
    }

    @Override
    public BaseTaskPojo getTaskHeader(String referenceNo, String taskType) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        BaseTaskPojo queryPojo = new BaseTaskPojo();
        queryPojo.setReferenceNo(referenceNo);
        queryPojo.setType(taskType);
        // 设置仓库&&货主属性
        queryPojo.setWarehouseCode(userContext.getWarehouseCode());
        TaskHeaderPojo taskHeaderPojo = getTaskHeader(queryPojo);
        if (null != taskHeaderPojo) {
            BaseTaskPojo baseTaskPojo = new BaseTaskPojo(taskHeaderPojo);
            // 查询商品操作
            baseTaskPojo.setOperationQty(taskDetailMapper.countOperationQtyByTaskNo(baseTaskPojo.getTaskNo(),
                    taskType, userContext.getTenantCode(),
                    userContext.getWarehouseCode()));
            return baseTaskPojo;
        }
        return null;
    }

    @Nullable
    @Override
    public BaseTaskDetailPojo getTaskDetail(String uniqueCode, String taskType, Integer status) {
        BaseTaskDetailPojo pojo = new BaseTaskDetailPojo();
        QueryWrapper<TaskDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(TaskDetailEntity.COL_DELETED, NumberUtils.INTEGER_ZERO);
        queryWrapper.eq(TaskDetailEntity.COL_UNIQUE_CODE, uniqueCode);
        queryWrapper.eq(TaskDetailEntity.COL_TASK_TYPE, taskType);
        queryWrapper.eq(TaskDetailEntity.COL_STATUS, status);
        queryWrapper.eq(TaskDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        queryWrapper.select(TaskDetailEntity.COL_DETAIL_NO,
                TaskDetailEntity.COL_STATUS,
                TaskDetailEntity.COL_REFERENCE_NO,
                TaskDetailEntity.COL_REFERENCE_DETAIL_NO,
                TaskDetailEntity.COL_REFERENCE_TYPE,
                TaskDetailEntity.COL_TASK_NO,
                TaskDetailEntity.COL_BIZ_TYPE,
                TaskDetailEntity.COL_WAREHOUSE_CODE,
                TaskDetailEntity.COL_QUALITY_LEVEL);
        TaskDetailEntity taskDetailEntity = taskDetailMapper.selectOne(queryWrapper);
        if (taskDetailEntity != null) {
            pojo.setDetailNo(taskDetailEntity.getDetailNo());
            pojo.setStatus(taskDetailEntity.getStatus());
            pojo.setReferenceNo(taskDetailEntity.getReferenceNo());
            pojo.setReferenceDetailNo(taskDetailEntity.getReferenceDetailNo());
            pojo.setReferenceType(taskDetailEntity.getReferenceType());
            pojo.setTaskNo(taskDetailEntity.getTaskNo());
            pojo.setQualityLevel(taskDetailEntity.getQualityLevel());
            pojo.setBizType(taskDetailEntity.getBizType());
            pojo.setWarehouseCode(taskDetailEntity.getWarehouseCode());
            return pojo;
        } else {
            return null;
        }

    }

    @Override
    public List<BaseTaskDetailPojo> getTaskDetailList(String uniqueCode, String taskType) {
        List<BaseTaskDetailPojo> taskDetailPojoList = new ArrayList<>();
        QueryWrapper<TaskDetailEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(TaskDetailEntity.COL_DELETED, NumberUtils.INTEGER_ZERO);
        queryWrapper.eq(TaskDetailEntity.COL_UNIQUE_CODE, uniqueCode);
        queryWrapper.eq(TaskDetailEntity.COL_TASK_TYPE, taskType);
        queryWrapper.select(TaskDetailEntity.COL_DETAIL_NO,
                TaskDetailEntity.COL_STATUS,
                TaskDetailEntity.COL_REFERENCE_NO,
                TaskDetailEntity.COL_REFERENCE_DETAIL_NO,
                TaskDetailEntity.COL_TASK_NO,
                TaskDetailEntity.COL_QUALITY_LEVEL,
                TaskDetailEntity.COL_BIZ_TYPE,
                TaskDetailEntity.COL_REFERENCE_TYPE);
        List<TaskDetailEntity> taskDetailEntityList = taskDetailMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(taskDetailEntityList)) {
            taskDetailEntityList.forEach(taskDetailEntity -> {
                BaseTaskDetailPojo pojo = new BaseTaskDetailPojo();
                pojo.setDetailNo(taskDetailEntity.getDetailNo());
                pojo.setStatus(taskDetailEntity.getStatus());
                pojo.setReferenceNo(taskDetailEntity.getReferenceNo());
                pojo.setReferenceDetailNo(taskDetailEntity.getReferenceDetailNo());
                pojo.setTaskNo(taskDetailEntity.getTaskNo());
                pojo.setQualityLevel(taskDetailEntity.getQualityLevel());
                pojo.setBizType(taskDetailEntity.getBizType());
                pojo.setReferenceType(taskDetailEntity.getReferenceType());
                taskDetailPojoList.add(pojo);
            });
        }
        return taskDetailPojoList;
    }

    @Override
    public TaskHeaderPojo getTaskHeader(BaseTaskPojo queryPojo) {
        TaskEntity taskParam = new TaskEntity();
        taskParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskParam.setReferenceNo(queryPojo.getReferenceNo());
        taskParam.setReferenceType(queryPojo.getReferenceType());
        taskParam.setTaskNo(queryPojo.getTaskNo());
        if (StringUtils.isBlank(queryPojo.getType())
                || null == TaskTypeEnum.getTaskType(queryPojo.getType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        taskParam.setType(queryPojo.getType());
        if (StringUtils.isBlank(queryPojo.getWarehouseCode())) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_IS_NULL);
        }
        taskParam.setWarehouseCode(queryPojo.getWarehouseCode());
        taskParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        QueryWrapper queryWrapper = new QueryWrapper<>(taskParam).select(findExistTaskHeaderCol());
        TaskEntity taskEntity = taskMapper.selectOne(queryWrapper);
        if (taskEntity != null) {
            TaskHeaderPojo taskHeaderPojo = buildTaskHeaderPojo(taskEntity);
            return taskHeaderPojo;
        }
        return null;
    }

    @Override
    public List<BaseTaskPojo> getTaskHeaders(@NotNull List<String> referenceNos, @NotNull TaskTypeEnum taskType) {
        QueryWrapper queryWrapper = createQueryWrapper();
        queryWrapper.eq(BaseEntity.COL_WAREHOUSE_CODE, OperationUserContextHolder.get().getWarehouseCode());
        queryWrapper.eq(TaskEntity.COL_TYPE, taskType.getTaskType());
        queryWrapper.in(TaskEntity.COL_REFERENCE_NO, referenceNos);
        queryWrapper.eq(TaskEntity.COL_DELETED, NumberUtils.INTEGER_ZERO);
        List<TaskEntity> taskEntityList = taskMapper.selectList(queryWrapper);
        List<BaseTaskPojo> taskPojoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(taskEntityList)) {
            OperationUserContext userContext = OperationUserContextHolder.get();
            taskEntityList.forEach(e -> {
                BaseTaskPojo baseTaskPojo = buildBaseTaskPojo(e);
                baseTaskPojo.setOperationQty(0);
                if (!e.getStatus().equals(TaskStatusEnum.INIT.getStatus())) {
                    baseTaskPojo.setOperationQty(taskDetailMapper.countOperationQtyByTaskNo(e.getTaskNo(), e.getType(), userContext.getTenantCode(), userContext.getWarehouseCode()));
                }
                taskPojoList.add(baseTaskPojo);
            });
        }
        return taskPojoList;
    }

    @Override
    public List<TaskDetailResultPojo> getTaskDetailResult(String taskDetailNo, String taskType) {
        List<TaskDetailResultPojo> resultPojos = new ArrayList<>();
        TaskDetailResultEntity taskResultParam = new TaskDetailResultEntity();
        taskResultParam.setTaskDetailNo(taskDetailNo);
        taskResultParam.setDeleted(NumberUtils.INTEGER_ZERO);
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        taskResultParam.setTaskType(taskType);
        taskResultParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        QueryWrapper queryWrapper = new QueryWrapper<>(taskResultParam);
        List<TaskDetailResultEntity> entitys = taskDetailResultMapper.selectList(queryWrapper);
        entitys.stream().forEach(entity -> {
            TaskDetailResultPojo resultPojo = new TaskDetailResultPojo();
            BeanUtils.copyProperties(entity, resultPojo);
            resultPojos.add(resultPojo);
        });
        return resultPojos;
    }

    @Override
    public List<TaskDetailResultPojo> getTaskDetailResult(List<String> taskDetailList, String taskType) {
        List<TaskDetailResultPojo> resultPojos = new ArrayList<>();
        TaskDetailResultEntity resultEntity = new TaskDetailResultEntity();
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        resultEntity.setTaskType(taskType);
        resultEntity.setDeleted(NumberUtils.INTEGER_ZERO);
        QueryWrapper<TaskDetailResultEntity> queryWrapper = new QueryWrapper<>(resultEntity);
        queryWrapper.in(COL_TASK_DETAIL_NO, taskDetailList);
        queryWrapper.eq(TaskDetailResultEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        List<TaskDetailResultEntity> entitys = taskDetailResultMapper.selectList(queryWrapper);
        entitys.stream().forEach(entity -> {
            TaskDetailResultPojo resultPojo = new TaskDetailResultPojo();
            BeanUtils.copyProperties(entity, resultPojo);
            resultPojos.add(resultPojo);
        });
        return resultPojos;
    }

    @Override
    public Page<TaskDetailResultPojo> queryTaskDetailResultByPage(String warehouseCode, String referenceNo, String taskType, int pageNum, int pageSize) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        Page<TaskDetailResultPojo> page = new Page(pageNum, pageSize);
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        return taskDetailResultMapper.selectTaskDetailResultByPage(page, userContext.getTenantCode(), taskType, warehouseCode, referenceNo);
    }

    @Override
    public TaskDetailResultQtyPojo queryTaskDetailResultSumQty(String taskDetailNo, String taskType) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        return taskDetailResultMapper.selectSumQty(userContext.getTenantCode(), userContext.getWarehouseCode(), taskDetailNo, taskType);
    }

    @Override
    public TaskCountPojo getTaskCount(TaskCountQueryPojo queryPojo) {
        TaskCountPojo countPojo = new TaskCountPojo();
        queryPojo.setTenantCode(OperationUserContextHolder.getTenantCode());
        List<TaskDetailCount> countList = taskDetailMapper.getTaskDetailCount(queryPojo);
        if (CollectionUtils.isEmpty(countList)) {
            return countPojo;
        }
        int planTotal = 0;
        for (TaskDetailCount p : countList) {
            Integer status = p.getStatus();
            planTotal += p.getTotalQty();
            Integer totalOperationQty = p.getTotalOperationQty();
            if (status.equals(TaskDetailStatusEnum.COMPLETE.getStatus())) {
                countPojo.setFinishTotal(totalOperationQty);
            }
        }
        countPojo.setPlanTotal(planTotal);
        return countPojo;
    }

    /**
     * 检验查询参数
     *
     * @param taskDetailQueryPojo
     */
    private void validateFindTaskPojo(PickTaskDetailQueryPojo taskDetailQueryPojo) {
        if (StringUtils.isEmpty(taskDetailQueryPojo.getTaskNo())) {
            throw new WmsException(WmsExceptionCode.TASK_NO_NULL);
        }
        if (StringUtils.isBlank(taskDetailQueryPojo.getWarehouseCode())) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_IS_NULL);
        }
    }

    @Override
    public List<PickTaskPojo> queryPickTaskList(String launchBatchNo, String taskType, String tenantCode) {
        List<TaskDo> taskDoList = taskRepository.findByReferenceNo(launchBatchNo, taskType, tenantCode);

        List<PickTaskPojo> taskPojoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(taskDoList)) {
            taskDoList.forEach(e -> {
                PickTaskPojo pickTaskPojo = buildPickTaskPojo(e);
                taskPojoList.add(pickTaskPojo);
            });
        }
        return taskPojoList;
    }

    private String[] findExistTaskHeaderCol() {
        return new String[]{TaskEntity.COL_TASK_NO, TaskEntity.COL_STATUS, TaskEntity.COL_TOTAL_QTY};
    }

    @Override
    public BaseTaskDetailPojo getNextStockTakingTaskDetail(TaskDetailListQueryPojo queryPojo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        if (StringUtils.isBlank(queryPojo.getTaskType()) || null == TaskTypeEnum.getTaskType(queryPojo.getTaskType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        //租户和仓库
        queryPojo.setTenantCode(userContext.getTenantCode());
        queryPojo.setWarehouseCode(userContext.getWarehouseCode());

        /*
        QueryWrapper queryWrapper = new QueryWrapper<>(taskDetailParam);
        if (queryPojo.getSortType() == SortType.ASC) {
            queryWrapper.gt(TaskDetailEntity.COL_ID, queryPojo.getCurrentTaskDetailId());
            queryWrapper.orderByAsc(TaskDetailResultEntity.COL_ID);
        } else if (queryPojo.getSortType() == SortType.DESC) {
            queryWrapper.lt(TaskDetailEntity.COL_ID, queryPojo.getCurrentTaskDetailId());
            queryWrapper.orderByDesc(TaskDetailResultEntity.COL_ID);
        }
        queryWrapper.in(TaskDetailEntity.COL_STATUS, queryPojo.getTaskDetailStatusList());
        queryWrapper.last(" limit 1");*/
        List<TaskDetailEntity> detailEntityList = taskDetailMapper.queryNextStockTakingTaskDetail(queryPojo);
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return null;
        }
        /*封装返回值.遍历执行对象转化*/
        List<BaseTaskDetailPojo> detailPojoList = taskDetailEntityToBaseTaskDetailPojo(detailEntityList);
        return detailPojoList.get(0);
    }

    @Override
    public BaseTaskPojo getTaskHeader(String taskNo, String referenceNo, String taskType) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        BaseTaskPojo queryPojo = new BaseTaskPojo();
        queryPojo.setReferenceNo(referenceNo);
        queryPojo.setType(taskType);
        queryPojo.setTaskNo(taskNo);
        // 设置仓库&&货主属性
        queryPojo.setWarehouseCode(userContext.getWarehouseCode());
//        UserUtils.buildQueryCommonParams(queryPojo);
        TaskHeaderPojo taskHeaderPojo = getTaskHeader(queryPojo);
        if (null != taskHeaderPojo) {
            BaseTaskPojo baseTaskPojo = new BaseTaskPojo(taskHeaderPojo);
            // 查询商品操作
            baseTaskPojo.setOperationQty(taskDetailMapper.countOperationQtyByTaskNo(baseTaskPojo.getTaskNo(),
                    taskType, userContext.getTenantCode(),
                    userContext.getWarehouseCode()));
            return baseTaskPojo;
        }
        return null;
    }

    @Override
    public List<BaseTaskDetailPojo> getTaskDetailList(Set<String> taskDetailNos, String taskType) {
        if (CollectionUtils.isNotEmpty(taskDetailNos)) {
            List<BaseTaskDetailPojo> detailPojoList = new ArrayList<>(taskDetailNos.size());

            QueryWrapper queryWrapper = new QueryWrapper<>();
            queryWrapper.in(TaskDetailEntity.COL_DETAIL_NO, taskDetailNos);
            queryWrapper.eq(TaskDetailEntity.COL_TASK_TYPE, taskType);
            queryWrapper.eq(TaskDetailEntity.COL_DELETED, NumberUtils.INTEGER_ZERO);

            queryWrapper.select(new String[]{
                    TaskDetailEntity.COL_DETAIL_NO,
                    TaskDetailEntity.COL_OPERATION_QTY,
                    TaskDetailEntity.COL_UNIQUE_CODE,
                    TaskDetailEntity.COL_INVENTORY_NO,
                    TaskDetailEntity.COL_SKU_ID,
                    TaskDetailEntity.COL_STATUS,
                    TaskDetailEntity.COL_QUALITY_LEVEL,
                    TaskDetailEntity.COL_OWNER_CODE,
                    TaskDetailEntity.COL_MFG_TIME,
                    TaskDetailEntity.COL_EXP_TIME,
                    TaskDetailEntity.COL_ORIGINAL_ORDER_CODE
            });

            List<TaskDetailEntity> detailEntityList = this.taskDetailBaseService.list(queryWrapper);
            for (TaskDetailEntity entity : detailEntityList) {
                /*构建任务明细*/
                BaseTaskDetailPojo taskDetailPojo = buildTaskDetailPojo(entity);
                // 添加到返回值中
                detailPojoList.add(taskDetailPojo);
            }
            return detailPojoList;
        }
        return null;
    }

    @Override
    public TaskQty queryTaskQty(String referenceNo, String taskType) {
        // 任务类型为空
        Preconditions.checkStringNotBlank(referenceNo, WmsExceptionCode.PARAMS_IS_EMPTY);
        Preconditions.checkStringNotBlank(taskType, WmsExceptionCode.PARAMS_IS_EMPTY);

        TaskQty taskQty = new TaskQty(referenceNo, taskType);
        TaskDetailEntity taskDetailEntity = taskDetailMapper.queryTaskOptQty(referenceNo, taskType, OperationUserContextHolder.getTenantCode());
        if (null != taskDetailEntity) {
            taskQty.setOperationQty(taskDetailEntity.getOperationQty());
            Integer forceFinishQty = taskDetailEntity.getQty() - taskDetailEntity.getOperationQty();
            taskQty.setForceFinishQty(forceFinishQty);
        }

        return taskQty;
    }

    @Override
    public List<TaskDetailEntity> queryTaskDetailEntityByInventoryNo(String taskType, String inventoryNo, List<Integer> statusList) {
        TaskDetailEntity taskDetailParam = new TaskDetailEntity();
        taskDetailParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskDetailParam.setTaskType(taskType);
        taskDetailParam.setInventoryNo(inventoryNo);
        QueryWrapper queryWrapper = new QueryWrapper<>(taskDetailParam).orderByDesc(TaskDetailResultEntity.COL_CREATED_TIME);
        queryWrapper.in(TaskDetailEntity.COL_STATUS, statusList);
        return this.taskDetailBaseService.list(queryWrapper);
    }

    @Override
    public List<BaseTaskDetailPojo> querySimpleTaskDetailList(TaskDetailListQueryPojo queryPojo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        TaskDetailEntity taskDetailParam = new TaskDetailEntity();
        if (StringUtils.isBlank(queryPojo.getTaskType()) || null == TaskTypeEnum.getTaskType(queryPojo.getTaskType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        String taskType = queryPojo.getTaskType();
        BeanUtils.copyProperties(queryPojo, taskDetailParam);
        taskDetailParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskDetailParam.setTaskType(taskType);
        taskDetailParam.setTenantCode(queryPojo.getTenantCode());
        //租户和仓库
        if (userContext != null && !StringUtils.isEmpty(userContext.getTenantCode())) {
            taskDetailParam.setTenantCode(userContext.getTenantCode());
        }
        taskDetailParam.setWarehouseCode(queryPojo.getWarehouseCode());
        QueryWrapper queryWrapper = new QueryWrapper<>(taskDetailParam).orderByDesc(TaskDetailResultEntity.COL_CREATED_TIME);
        if (CollectionUtils.isNotEmpty(queryPojo.getTaskDetailStatusList())) {
            queryWrapper.in(TaskDetailEntity.COL_STATUS, queryPojo.getTaskDetailStatusList());
        }
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        List<TaskDetailEntity> detailEntityList = this.taskDetailBaseService.list(queryWrapper);
        return taskDetailEntityToBaseTaskDetailPojo(detailEntityList);
    }

    List<BaseTaskDetailPojo> taskDetailEntityToBaseTaskDetailPojo(List<TaskDetailEntity> detailEntityList) {
        if (CollectionUtils.isEmpty(detailEntityList)) {
            return null;
        }
        /*封装返回值.遍历执行对象转化*/
        List<BaseTaskDetailPojo> detailPojoList = new ArrayList<>(detailEntityList.size());
        for (TaskDetailEntity entity : detailEntityList) {
            /*构建任务明细*/
            BaseTaskDetailPojo taskDetailPojo = buildTaskDetailPojo(entity);
            // 添加到返回值中
            detailPojoList.add(taskDetailPojo);
        }
        return detailPojoList;
    }

    @Override
    public List<TaskDetailResultPojo> queryTaskDetailResult(TaskDetailResultQueryPojo pojo) {
        List<TaskDetailResultPojo> resultPojos = new ArrayList<>();
        pojo.setTenantCode(OperationUserContextHolder.getTenantCode());
        List<TaskDetailResultEntity> list = taskDetailResultMapper.queryList(pojo);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list.stream().forEach(entity -> {
            TaskDetailResultPojo resultPojo = new TaskDetailResultPojo();
            BeanUtils.copyProperties(entity, resultPojo);
            resultPojos.add(resultPojo);
        });
        return resultPojos;
    }

    @Override
    public Map<String, List<String>> queryAreaCodeListAsMap(Set<String> taskNoSet) {
        List<PickTaskExtraInfo> pickTaskExtraInfos = taskMapper.queryAreaCodes(taskNoSet);
        Map<String, List<PickTaskExtraInfo>> listMap = pickTaskExtraInfos.stream().collect(Collectors.groupingBy(PickTaskExtraInfo::getTaskNo));
        Map<String, List<String>> areaCodesMap = new HashMap<>();
        for (Map.Entry<String, List<PickTaskExtraInfo>> entry : listMap.entrySet()) {
            areaCodesMap.put(entry.getKey(), entry.getValue().stream().map(item -> Optional.of(item.getAreaCodes()).orElse("")).collect(Collectors.toList()));
        }
        return areaCodesMap;
    }

    @Override
    public Map<String, BaseTaskPojo> queryTaskMapByReferenceNo(List<String> referenceNoList, String taskType) {
        Map<String, BaseTaskPojo> taskPojoMap = new HashMap<>();
        List<TaskDo> list = taskRepository.findByReferenceNos(referenceNoList, taskType);
        if (CollectionUtils.isEmpty(list)) {
            return taskPojoMap;
        }
        List<String> taskNos = list.stream().map(taskDo -> taskDo.getTaskNo()).collect(Collectors.toList());
        Map<String, Integer> result = getOptQtyByTaskNos(taskNos, taskType);
        for (TaskDo taskDo : list) {
            Integer optQty = result.get(taskDo.getTaskNo());
            BaseTaskPojo baseTaskPojo = new BaseTaskPojo();
            baseTaskPojo.setWarehouseCode(taskDo.getWarehouseCode());
            baseTaskPojo.setTaskNo(taskDo.getTaskNo());
            baseTaskPojo.setStatus(taskDo.getStatus());
            baseTaskPojo.setTotalQty(taskDo.getTotalQty());
            baseTaskPojo.setOperationQty(optQty == null ? 0 : optQty);
            taskPojoMap.put(taskDo.getReferenceNo(), baseTaskPojo);
        }
        return taskPojoMap;
    }

    @Override
    public List<TaskDo> findByNos(List<String> taskNos, String taskType) {
        return taskRepository.findByNos(taskNos,taskType);
    }

    /**
     * 从缓存中获取执行数
     *
     * @param taskNos
     * @param taskType
     * @return
     */
    private Map<String, Integer> getOptQtyByTaskNos(List<String> taskNos, String taskType) {
        Map<String, Integer> result = new HashMap<>();
        for (String taskNo : taskNos) {
            Long optQty = taskCacheHelper.getTaskOptQty(taskNo, taskType);
            if (optQty == null) {
                optQty = 0L;
            }
            result.put(taskNo, optQty.intValue());
        }
        return result;
    }
}
