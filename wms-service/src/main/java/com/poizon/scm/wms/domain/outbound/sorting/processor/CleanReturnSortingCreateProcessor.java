package com.poizon.scm.wms.domain.outbound.sorting.processor;

import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDo;
import com.poizon.scm.wms.adapter.sorting.model.SortingTaskDetailDo;
import com.poizon.scm.wms.adapter.sorting.model.SortingTaskHeaderDo;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 清退出库 二次分拣任务生成
 * <AUTHOR>
 */
@Slf4j
@Component
public class CleanReturnSortingCreateProcessor extends AbstractSortingCreateProcessor{

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;

    /**
     * 生成并保存二次分拣任务
     *
     * @param taskDo 任务Do
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void execute(PickTaskDo taskDo) {
        //查询批次下所有的任务明细
        List<PickTaskDetailDo> taskDetails = getPickTaskDetails(taskDo);

        /*过滤掉取消的拣货任务明细*/
        List<PickTaskDetailDo> taskDetailList = taskDetails.stream().filter(detail -> !TaskStatusEnum.CANCEL.getStatus().equals(detail.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskDetailList)) {
            throw new WmsException(WmsExceptionCode.TASK_DETAIL_NOT_EXIST);
        }

        // 清退出库的出库明细可能会存在多条拣货明细
        Map<String, List<PickTaskDetailDo>> taskDetailMap = taskDetailList.stream()
                .collect(Collectors.groupingBy(PickTaskDetailDo::getReferenceDetailNo));

        //根据任务明细号查询任务结果, 拿到拣货移位以后的库存号
        Map<String, List<PickTaskDetailResultDo>> taskDetailInventoryMap = getTaskDetailResultMapByDetailNos(taskDetails);

        /*获取订单编号和明细的分组关系*/
        Map<String, List<DeliveryDetailDo>> deliveryCodeMappingDetails = getDeliveryDetailMapByDetailCode(taskDetailMap.keySet());

        // 根据出库单号集合查合并订单信息表，按合并单号归类
        Map<String, List<DeliveryHeaderDo>> combineMap = getCombineMap(deliveryCodeMappingDetails.keySet());

        saveSortingTask(taskDo,
                taskDetailInventoryMap,
                taskDetailMap,
                deliveryCodeMappingDetails,
                combineMap);
    }

    private Map<String, List<DeliveryHeaderDo>> getCombineMap(Set<String> deliveryDetailCodes) {
        List<DeliveryHeaderDo> originalDeliveryHeaderList = deliveryHeaderRepository.queryBatchDeliveryInfo(deliveryDetailCodes);
        if (CollectionUtils.isEmpty(originalDeliveryHeaderList)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_COMBINE_NULL);
        }
        /*只处理清退出库的单据(按道理当前批次下也只有清退出库的单据)*/
//        List<DeliveryHeaderDo> deliveryHeaderList = originalDeliveryHeaderList.stream().filter(deliveryHeader ->
//                WmsOutBoundTypeEnum.QLCK.getType().equals(deliveryHeader.getType())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(deliveryHeaderList)) {
//            throw new WmsException(WmsExceptionCode.DELIVERY_COMBINE_NULL);
//        }
        Map<String, List<DeliveryHeaderDo>> combineMap = originalDeliveryHeaderList.stream().collect(Collectors.groupingBy(DeliveryHeaderDo::getDeliveryOrderCode));
        if (MapUtils.isEmpty(combineMap)) {
            log.error("未找到清退出库订单明细 param -> {}", JsonUtils.serialize(deliveryDetailCodes));
            throw new WmsException(WmsExceptionCode.DELIVERY_COMBINE_NULL);
        }
        return combineMap;
    }

    private void saveSortingTask(PickTaskDo taskDo, Map<String, List<PickTaskDetailResultDo>> taskDetailInventoryMap,
                                 Map<String, List<PickTaskDetailDo>> taskDetailMap,
                                 Map<String, List<DeliveryDetailDo>> deliveryDetailMap,
                                 Map<String, List<DeliveryHeaderDo>> combineMap) {
        //设置初始分拣号
        int sortingCode = 1;
        for (Map.Entry<String, List<DeliveryHeaderDo>> entry : combineMap.entrySet()) {
            // 构建二次分拣任务头(以合并单号为维度)
            SortingTaskHeaderDo sortingTaskHeaderDo = buildSortingTaskHeaderDo(taskDo, sortingCode, entry.getKey());

            // 构建二次分拣任务明细(以出库单明细编号为维度)
            List<SortingTaskDetailDo> detailDoList = buildSortingDetails(taskDo, taskDetailInventoryMap,
                    taskDetailMap, deliveryDetailMap, entry.getValue(), sortingTaskHeaderDo);

            // 为空说明拣货任务已取消，没拣货不生成分拣任务
            if (detailDoList.isEmpty()) {
                log.info("订单:" + entry.getKey() + "已取消,未拣货无需生成拣货任务");
                continue;
            }

            doSave(sortingTaskHeaderDo, detailDoList);

            //一次循环代表一个作业单，循环结束分拣号加一
            sortingCode = sortingCode + 1;
        }
    }

    /**
     * 构建二次分拣任务头(以合并单号为维度)
     *
     * @return
     */
    private SortingTaskHeaderDo buildSortingTaskHeaderDo(PickTaskDo taskDo, Integer sortingCode, String combineNo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        //构建二次分拣任务头(以合并单号为维度)
        SortingTaskHeaderDo sortingTaskHeaderDo = new SortingTaskHeaderDo();
        sortingTaskHeaderDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        sortingTaskHeaderDo.setCombineNo(combineNo);
        sortingTaskHeaderDo.setStatus(TaskStatusEnum.INIT.getStatus());
        sortingTaskHeaderDo.setSortingCode(String.valueOf(sortingCode));
        sortingTaskHeaderDo.setBatchNo(taskDo.getReferenceNo());
        sortingTaskHeaderDo.setLaunchNo(taskDo.getLaunchNo());
        //通用属性
        sortingTaskHeaderDo.setWarehouseCode(taskDo.getWarehouseCode());
        sortingTaskHeaderDo.setTenantId(OperationUserContextHolder.getTenantCode());
        sortingTaskHeaderDo.setCreatedUserId(userContext.getUserId());
        sortingTaskHeaderDo.setCreatedUserName(userContext.getUserName());
        sortingTaskHeaderDo.setCreatedRealName(userContext.getRealName());
        sortingTaskHeaderDo.setCreatedTime(new Date());
        return sortingTaskHeaderDo;
    }

    /**
     * 构造品牌多件的二分明细
     *
     * @param taskDo
     * @param taskDetailInventoryMap 批次下所有的拣货结果
     * @param taskDetailMap 批次下所有的拣货明细
     * @param deliveryDetailMap 批次下所有的出库单明细
     * @param deliveryCombineDos 所有的出库单
     * @param sortingTaskHeaderDo 二次分拣头
     * @return
     */
    private List<SortingTaskDetailDo> buildSortingDetails(PickTaskDo taskDo,
                                                          Map<String, List<PickTaskDetailResultDo>> taskDetailInventoryMap,
                                                          Map<String, List<PickTaskDetailDo>> taskDetailMap,
                                                          Map<String, List<DeliveryDetailDo>> deliveryDetailMap,
                                                          List<DeliveryHeaderDo> deliveryCombineDos,
                                                          SortingTaskHeaderDo sortingTaskHeaderDo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        List<SortingTaskDetailDo> detailDoList = new ArrayList<>();
        List<PickTaskDetailResultDo> totalPickTaskResultList = taskDetailInventoryMap.values().
                stream().flatMap(Collection::stream).filter(item -> sortingTaskHeaderDo.getCombineNo().equals(item.getReferenceNo()))
                .collect(Collectors.toList());
        // 拣货结果已经最细颗粒度，需要根据"容器号+库存编码+出库单明细编号+任务编号"将拣货结果分组
        Map<String, List<PickTaskDetailResultDo>> listMap = totalPickTaskResultList.stream().collect(Collectors.
                groupingBy(p -> p.getContainerCode() + p.getInventoryNo() + p.getReferenceDetailNo() + p.getTaskNo()));
        listMap.values().forEach(item -> {
            PickTaskDetailResultDo pickTaskDetailResultDo = item.get(0);
            String deliveryDetailNo = pickTaskDetailResultDo.getReferenceDetailNo();
            SortingTaskDetailDo sortingTaskDetailDo = new SortingTaskDetailDo();
            sortingTaskDetailDo.setTaskNo(sortingTaskHeaderDo.getTaskNo());
            sortingTaskDetailDo.setTaskDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            sortingTaskDetailDo.setCombineNo(sortingTaskHeaderDo.getCombineNo());                     //合并单号
            sortingTaskDetailDo.setDeliveryOrderCode(sortingTaskHeaderDo.getCombineNo());
            sortingTaskDetailDo.setDeliveryOrderDetailNo(deliveryDetailNo);
            sortingTaskDetailDo.setQualityLevel(pickTaskDetailResultDo.getQualityLevel());

            List<PickTaskDetailDo> pickTaskDetailDos = taskDetailMap.get(deliveryDetailNo);
            if (CollectionUtils.isEmpty(pickTaskDetailDos)) {
                log.error("出库单明细{}查询不到拣货拣货明细", deliveryDetailNo);
                throw new WmsOperationException("出库单明细查询不到拣货拣货明细");
            }
            Integer bizType = pickTaskDetailDos.get(0).getBizType();
            sortingTaskDetailDo.setBizType(bizType);
            sortingTaskDetailDo.setOwnerCode(pickTaskDetailResultDo.getOwnerCode());
            sortingTaskDetailDo.setSkuId(pickTaskDetailResultDo.getSkuId());
            sortingTaskDetailDo.setBatchNo(sortingTaskHeaderDo.getBatchNo());
            sortingTaskDetailDo.setLaunchNo(sortingTaskHeaderDo.getLaunchNo());
            sortingTaskDetailDo.setTenantId(OperationUserContextHolder.getTenantCode());
            //通用属性
            sortingTaskDetailDo.setWarehouseCode(taskDo.getWarehouseCode());
            sortingTaskDetailDo.setCreatedUserId(userContext.getUserId());
            sortingTaskDetailDo.setCreatedUserName(userContext.getUserName());
            sortingTaskDetailDo.setCreatedRealName(userContext.getRealName());
            sortingTaskDetailDo.setCreatedTime(new Date());
            sortingTaskDetailDo.setPickTaskNo(pickTaskDetailResultDo.getTaskNo());
            sortingTaskDetailDo.setUniqueCode(StringUtils.isNotEmpty(pickTaskDetailResultDo.getUniqueCode())
                    ? pickTaskDetailResultDo.getUniqueCode() : StringUtils.EMPTY);
            Integer planQty = item.stream().collect(Collectors.summingInt(PickTaskDetailResultDo::getOperationQty));
            sortingTaskDetailDo.setPlanQty(planQty);
            sortingTaskDetailDo.setInventoryNo(pickTaskDetailResultDo.getInventoryNo());
            sortingTaskDetailDo.setContainerCode(pickTaskDetailResultDo.getContainerCode());
            sortingTaskDetailDo.setContainerInstanceNo(pickTaskDetailResultDo.getContainerInstanceNo());
            detailDoList.add(sortingTaskDetailDo);
        });
        return detailDoList;
    }
}
