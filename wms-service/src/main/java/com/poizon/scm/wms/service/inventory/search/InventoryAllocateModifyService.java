package com.poizon.scm.wms.service.inventory.search;

import com.dewu.executor.annotation.Delay;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.query.SciInventoryAllocateDetail;
import com.poizon.scm.wms.adapter.inventory.query.SciInventoryAllocatedRecord;
import com.poizon.scm.wms.adapter.inventory.repository.InvInventoryAllocatedRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryStatusDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryPermissionRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryStatusRepository;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.pojo.delivery.DeliveryModifyStatusPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedCancelPojo;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateServiceImpl;
import com.poizon.scm.wms.service.inventory.search.modifier.AllocatedModifierService;
import com.poizon.scm.wms.util.enums.DeliveryPermissionAllocatedStatusEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * wms库存占用修改服务
 *
 * <AUTHOR>
 * @data 2021/3/14
 */
@Component
@Slf4j
public class InventoryAllocateModifyService {

    @Autowired
    private DistributeLockUtil distributeLockUtil;

    @Autowired
    private DeliveryOrderModifyService deliveryOrderModifyService;

    @Autowired
    private InvInventoryAllocatedRepository invInventoryAllocatedRepository;

    @Autowired
    private DeliveryPermissionRepository deliveryPermissionRepository;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private DeliveryStatusRepository deliveryStatusRepository;

    @Autowired
    private InventoryOperateServiceImpl inventoryOperateServiceImpl;

    @Autowired
    private AllocatedModifierService allocatedModifier;

    @EventualConsistency(label = "modifyWmsInventoryAllocate", delay = @Delay(delay = 30), referenceNo = "#record.referenceNo", rollbackFor = Exception.class)
    public void modify(SciInventoryAllocatedRecord record) {
        try {
            /*
             * 1、更新库存占用数量
             * 2、删除库存分配记录
             * 3、修改单据明细的分配数量
             * 4、修改单据的状态
             * */
            log.info("开始修改单据{}的库存分配", record.getReferenceNo());
            int sciTotalActualQty = record.getDetails().stream().mapToInt(SciInventoryAllocateDetail::getQty).sum();
            /*获取单据在WMS的库存占用*/
            List<InvInventoryAllocatedDo> invInventoryAllocatedDoList = invInventoryAllocatedRepository.queryAllocatedRecordsByShipmentNo(record.getReferenceNo());
            int wmsTotalQty = invInventoryAllocatedDoList.stream().mapToInt(InvInventoryAllocatedDo::getQty).sum();
            log.info("单据{}在sci的库存占用数:{},在wms的库存占用数:{}", record.getReferenceNo(), sciTotalActualQty, wmsTotalQty);
            if (sciTotalActualQty > wmsTotalQty) {
                log.error("wms monitor : 单据{}sci库存占用超过wms库存占用", record.getReferenceNo());
                throw new WmsException(WmsExceptionCode.SCI_INVENTORY_ALLOCATED_ERROR);
            }
            int status = DeliveryPermissionAllocatedStatusEnum.FINISHED.getStatus();
            ;
            if (sciTotalActualQty == NumberUtils.INTEGER_ZERO) {
                /*没有分配到SCI库存,清理掉WMS库存分配*/
                log.info("单据{}取消库存分配", record.getReferenceNo());
                clearAllocate(record.getReferenceNo());
                status = DeliveryPermissionAllocatedStatusEnum.WAITING_CANCEL.getStatus();
            } else if (sciTotalActualQty != wmsTotalQty) {
                log.info("单据{}修改库存分配", record.getReferenceNo());
                modifyAllocate(record.getReferenceNo(), record.getDetails(), invInventoryAllocatedDoList);
            }
            /*更新发货准许表分配完成标记为true*/
            int count = deliveryPermissionRepository.updateAllocatedStatus(record.getReferenceNo(), status);
            if (count != NumberUtils.INTEGER_ONE) {
                throw new WmsException(WmsExceptionCode.MODIFY_DELIVERY_PERMISSION_ERROR);
            }
        } catch (Exception e) {
            log.error("根据sci库存占用记录更新wms库存占用失败", e);
            throw e;
        }
    }

    /**
     * 更新库存分配
     *
     * @param referenceNo
     * @param cisInventoryAllocatedRecordList
     * @param invInventoryAllocatedRecordList
     */
    private void modifyAllocate(String referenceNo, List<SciInventoryAllocateDetail> cisInventoryAllocatedRecordList, List<InvInventoryAllocatedDo> invInventoryAllocatedRecordList) {
        allocatedModifier.modify(cisInventoryAllocatedRecordList, invInventoryAllocatedRecordList);
        /*修改单据状态*/
        log.info("单据{}修改状态为:{}", referenceNo, WmsOutBoundStatusEnum.PART_ALLOCATE.getStatus());
        int count = deliveryHeaderRepository.updateStatusByDeliveryCode(referenceNo, WmsOutBoundStatusEnum.PART_ALLOCATE.getStatus());
        if (count != NumberUtils.INTEGER_ONE) {
            log.error("单据{}状态修改失败", referenceNo);
            throw new WmsException(WmsExceptionCode.UPDATE_DELIVERY_STATUS_ERROR);
        }
        DeliveryStatusDo deliveryStatusDo = create(referenceNo, WmsOutBoundStatusEnum.PART_ALLOCATE.getStatus());
        deliveryStatusRepository.save(deliveryStatusDo);
    }

    private DeliveryStatusDo create(String referenceNo, Integer status) {
        DeliveryStatusDo deliveryStatus = new DeliveryStatusDo();
        deliveryStatus.setDeliveryOrderCode(referenceNo);
        deliveryStatus.setStatus(status);
        return deliveryStatus;
    }

    /**
     * 清理库存分配
     *
     * @param deliveryOrderCode
     */
    private void clearAllocate(String deliveryOrderCode) {
        /*走取消分配流程*/
        boolean tryLockFlag = false;
        try {
            tryLockFlag = distributeLockUtil.tryLockForBiz(LockEnum.CANCEL_ALLOCATED_LOCK, deliveryOrderCode);
            if (!tryLockFlag) {
                log.info("取消库存分配并发:", deliveryOrderCode);
                throw new WmsException(WmsExceptionCode.REPEAT_OPERATION);
            }
            InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo = new InventoryAllocatedCancelPojo();
            inventoryAllocatedCancelPojo.setTenantCode(OperationUserContextHolder.getTenantCode());
            inventoryAllocatedCancelPojo.setShipmentNo(deliveryOrderCode);
            Boolean flag = inventoryOperateServiceImpl.cancelAllocatedInventory(inventoryAllocatedCancelPojo);
            if (flag) {
                /*取消所有详情*/
                deliveryDetailRepository.updateDeliveryDetailAllocateQty(deliveryOrderCode);
                DeliveryModifyStatusPojo pojo = new DeliveryModifyStatusPojo();
                pojo.setDeliveryOrderCode(deliveryOrderCode);
                pojo.setDeliveryStatus(WmsOutBoundStatusEnum.CANCEL);
                deliveryOrderModifyService.modifyDeliveryOrderStatusAndLock(pojo);
            } else {
                throw new WmsException("库存取消失败");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new WmsException("Interrupted", e);
        } catch (Exception e) {
            throw new WmsException("取消库存分配获取锁失败", e);
        } finally {
            if (tryLockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.CANCEL_ALLOCATED_LOCK, deliveryOrderCode);
            }
        }
    }
}
