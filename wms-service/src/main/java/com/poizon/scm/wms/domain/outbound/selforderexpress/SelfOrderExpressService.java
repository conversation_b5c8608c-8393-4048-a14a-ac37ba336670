package com.poizon.scm.wms.domain.outbound.selforderexpress;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.wms.ManualTransaction;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsLogisticsBillRepository;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.model.ExpressOperationRecordDo;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.model.SelfOrderExpressDo;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.model.SelfOrderOriginalInfoDo;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.params.SelfOrderExpressListQueryParams;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.repository.ExpressOperationRecordRepository;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.repository.SelfOrderExpressRepository;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.repository.SelfOrderOriginalInfoRepository;
import com.poizon.scm.wms.adapter.outbound.waybill.dto.WaybillAddress;
import com.poizon.scm.wms.adapter.outbound.waybill.dto.WaybillRelation;
import com.poizon.scm.wms.adapter.outbound.waybill.param.MatchAndCustomMakeParam;
import com.poizon.scm.wms.adapter.outbound.waybill.result.MatchAndCustomMakeResult;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.api.enums.ExpressOperationEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.common.utils.NewNoGenUtil;
import com.poizon.scm.wms.common.utils.enums.NewNoGenEnum;
import com.poizon.scm.wms.domain.outbound.returngoods.entity.LogisticsInfo;
import com.poizon.scm.wms.domain.outbound.returngoods.service.TmsPlaceOrderFacade;
import com.poizon.scm.wms.domain.outbound.returngoods.service.TmsWayBillServiceFacade;
import com.poizon.scm.wms.domain.outbound.returngoods.service.handler.ExpressPrintHandler;
import com.poizon.scm.wms.domain.outbound.selforderexpress.dto.param.PlaceOrderParam;
import com.poizon.scm.wms.domain.outbound.selforderexpress.entity.StdTmsExpressInfo;
import com.poizon.scm.wms.domain.outbound.selforderexpress.producer.SelfOrderShipProducer;
import com.poizon.scm.wms.domain.outbound.selforderexpress.resp.SelfOrderExpressListQueryResp;
import com.poizon.scm.wms.service.base.BasWarehouseService;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.NumberUtils;
import com.shizhuang.duapp.tms.carrier.api.request.query.H5QueryExpressSheetRequest;
import com.shizhuang.duapp.tms.carrier.api.request.waybill.WaybillCancelRequest;
import com.shizhuang.duapp.tms.carrier.api.response.query.H5QueryExpressSheetResponse;
import com.shizhuang.duapp.tms.common.enums.BizBillTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.common.exceptions.WmsExceptionCode.EXPRESS_TMS_REQUEST_EXCEPTION;

/**
 * <AUTHOR>
 * @date 2021/12/20
 */
@Component
public class SelfOrderExpressService  {

    @Resource
    private TmsPlaceOrderFacade tmsPlaceOrderFacade;

    @Autowired
    private TmsWayBillServiceFacade tmsWayBillServiceFacade;

    @Resource
    private ScpWarehouseRepository scpWarehouseRepository;

    @Autowired
    private BasWarehouseService basWarehouseService;
    @Resource
    private SelfOrderExpressRepository selfOrderExpressRepository;

    @Resource
    private WmsLogisticsBillRepository logisticsBillRepository;

    @Resource
    private WmsLogisticsBillRepository wmsLogisticsBillRepository;

    @Resource
    private ExpressOperationRecordRepository expressOperationRecordRepository;

    @Resource
    private SelfOrderOriginalInfoRepository selfOrderOriginalInfoRepository;

    @Value("${tms.expressCode.switch:true}")
    private Boolean tmsExpressCodeSwitch;

    @Value("${self.order.express.switch:false}")
    private Boolean selfOrderSwitchFlag;
    //用来接受下单异常
    public static ThreadLocal<Exception> exceptionThreadLocal = new ThreadLocal<>();

    @Autowired
    private ExpressPrintHandler expressPrintHandler;

    @Resource
    private SelfOrderShipProducer selfOrderShipProducer;

    @ManualTransaction
    public LogisticsInfo placeOrder(PlaceOrderParam placeOrderParam,String originalSelfOrderNo) {
        //TMS下单
        String selfOrderNo = originalSelfOrderNo;
        if (StringUtils.isBlank(selfOrderNo)){
            selfOrderNo = NewNoGenUtil.generateNewNo(NewNoGenEnum.SELF_ORDER_NO);
        }
        MatchAndCustomMakeParam matchAndCustomMakeParam = buildMatchAndCustomMakeRequest(placeOrderParam, selfOrderNo);
        MatchAndCustomMakeResult response = new MatchAndCustomMakeResult();
        try{
            response = tmsPlaceOrderFacade.matchAndCustomMake(matchAndCustomMakeParam);
        }catch (Exception exception){
            //下单返回失败，保存自主下单信息
            expressFailedSaveSelfExpressOrderInfo(originalSelfOrderNo,placeOrderParam, response , selfOrderNo);
            exceptionThreadLocal.set(exception);
            return null;
        }

        List<WmsLogisticsBillDo> wmsLogisticsBillDoList = buildWmsLogisticsBillDoList(matchAndCustomMakeParam.getMatchAndCustomMakeRequest(), response, placeOrderParam);
        if (!checkIfExpressExist(response.getWaybillNo())) {
            //插入下单数据
            logisticsBillRepository.batchInsert(wmsLogisticsBillDoList);
            /*原始自主下单信息为空，则直接插入*/
            if (StringUtils.isBlank(originalSelfOrderNo)){
                //插入自主下单记录
                selfOrderExpressRepository.insertSelective(buildSelfOrderExpressDo(placeOrderParam, response, selfOrderNo,
                        SelfOrderExpressStatusEnum.ordered.name()));
            }else {
                /*否则更新*/
                SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryBySelfOrderNo(originalSelfOrderNo);
                if (Objects.isNull(selfOrderExpressDo)){
                    throw new WmsException("原始自主下单信息不存在");
                }
                updateSelfOrderInfo(selfOrderExpressDo,response,SelfOrderExpressStatusEnum.ordered.name());
            }
        }
        if (StringUtils.isBlank(originalSelfOrderNo)&&CollectionUtils.isNotEmpty(placeOrderParam.getOriginalInfo())){
            /*APP端下单且原始下单信息不为空才保存原始下单信息*/
            selfOrderOriginalInfoRepository.batchSave(buildSelfOrderOriginalInfos(selfOrderNo,placeOrderParam));
        }
        ExpressOperationRecordDo expressOperationRecordDo = buildExpressOperation(response.getWaybillNo(), StringUtils.isBlank(originalSelfOrderNo) ? ExpressOperationEnum.APP_PRINT_EXPRESS.getCode() : ExpressOperationEnum.SCM_PRINT_EXPRESS.getCode());
        expressOperationRecordRepository.save(expressOperationRecordDo);
        LogisticsInfo logisticsInfo = expressPrintHandler.buildLogisticsInfo(wmsLogisticsBillDoList, null, selfOrderNo, null);
        /*增加打印的备注信息*/
        logisticsInfo.setRemark(placeOrderParam.getRemark());
        return logisticsInfo;
    }

    /**
     * 检查下单是否有异常，有异常直接抛出
     * @param logisticsInfo
     */
    public void checkException(LogisticsInfo logisticsInfo) {
        if(logisticsInfo == null){
            //des下单失败
            Exception exception = this.exceptionThreadLocal.get();
            if(WmsOperationException.class.isAssignableFrom(exception.getClass())){
                throw new WmsOperationException(WmsExceptionCode.EXPRESS_TMS_REQUEST_FAILED.getCode(),
                        exception.getMessage()) ;
            }else {
                throw new WmsException(EXPRESS_TMS_REQUEST_EXCEPTION, exception) ;
            }

        }
    }

    private void expressFailedSaveSelfExpressOrderInfo(String originalSelfOrderNo,PlaceOrderParam placeOrderParam,
                                                       MatchAndCustomMakeResult response ,String selfOrderNo){

        if(StringUtils.isBlank(originalSelfOrderNo)){
            //插入自主下单记录
            selfOrderExpressRepository.insertSelective(buildSelfOrderExpressDo(placeOrderParam, response, selfOrderNo,
                    SelfOrderExpressStatusEnum.failed.name()));
        }else {
            //先导入，后打印的情况下 ，更新下单状态为失败
            SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryBySelfOrderNo(originalSelfOrderNo);
            if (Objects.isNull(selfOrderExpressDo)){
                throw new WmsException("原始自主下单信息不存在");
            }
            updateSelfOrderInfo(selfOrderExpressDo,response,SelfOrderExpressStatusEnum.failed.name());
        }

        if (StringUtils.isBlank(originalSelfOrderNo)&&CollectionUtils.isNotEmpty(placeOrderParam.getOriginalInfo())){
            /*APP端下单且原始下单信息不为空才保存原始下单信息*/
            selfOrderOriginalInfoRepository.batchSave(buildSelfOrderOriginalInfos(selfOrderNo,placeOrderParam));
        }

    }

    /**
     * 保存原始下单信息
     *
     * @param selfOrderNo
     * @param placeOrderParam
     * @return
     */
    private List<SelfOrderOriginalInfoDo> buildSelfOrderOriginalInfos(String selfOrderNo, PlaceOrderParam placeOrderParam) {
        List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos = new ArrayList<>();
        Set<String> originalInfoCodes = Sets.newHashSet(placeOrderParam.getOriginalInfo());
        for (String originalInfo : originalInfoCodes) {
            SelfOrderOriginalInfoDo selfOrderOriginalInfoDo = new SelfOrderOriginalInfoDo();
            selfOrderOriginalInfoDo.setSelfOrderNo(selfOrderNo);
            selfOrderOriginalInfoDo.setOriginalCode(originalInfo);
            selfOrderOriginalInfoDo.setWarehouseCode(OperationUserContextHolder.get().getWarehouseCode());
            selfOrderOriginalInfoDo.setTenantId(OperationUserContextHolder.getTenantCode());
            selfOrderOriginalInfoDo.setCreatedTime(new Date());
            selfOrderOriginalInfoDos.add(selfOrderOriginalInfoDo);
        }
        return selfOrderOriginalInfoDos;
    }

    private void updateSelfOrderInfo(SelfOrderExpressDo selfOrderExpressDo, MatchAndCustomMakeResult response,String status) {
        SelfOrderExpressDo update = new SelfOrderExpressDo();
        update.setId(selfOrderExpressDo.getId());
        update.setLogisticsCode(response.getLogisticsCode());
        update.setLogisticsName(response.getLogisticsName());
        update.setExpressCode(response.getWaybillNo());
        update.setStationNo(OperationUserContextHolder.get().getStationNo());
        update.setStatus(status);
        update.setUpdatedTime(new Date());
        OperationUserContext operationUserContext = OperationUserContextHolder.get();
        update.setUpdatedUserId(operationUserContext.getUserId());
        update.setUpdatedUserName(operationUserContext.getUserName());
        update.setUpdatedRealName(operationUserContext.getRealName());
        selfOrderExpressRepository.updateSelective(update);
    }

    private ExpressOperationRecordDo buildExpressOperation(String expressCode,String operation) {
        ExpressOperationRecordDo operationRecord = new ExpressOperationRecordDo();
        operationRecord.setExpressCode(expressCode);
        operationRecord.setOperation(operation);
        operationRecord.setCreatedTime(new Date());
        OperationUserContext operationUserContext = OperationUserContextHolder.get();
        operationRecord.setWarehouseCode(operationUserContext.getWarehouseCode());
        operationRecord.setTenantCode(OperationUserContextHolder.getTenantCode());
        operationRecord.setOperatorUserId(operationUserContext.getUserId());
        operationRecord.setOperatorRealName(operationUserContext.getRealName());
        operationRecord.setOperatorUserName(operationUserContext.getUserName());
        operationRecord.setStationNo(operationUserContext.getStationNo());
        return operationRecord;
    }


    /**
     * 重新打印
     * @param expressCode
     * @return
     */
    public LogisticsInfo reprint(String expressCode){
        SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryByExpressCode(expressCode);
        if (Objects.isNull(selfOrderExpressDo)){
            throw new WmsOperationException(WmsExceptionCode.SELF_ORDER_EXPRESS_NOT_EXIST);
        }
        List<WmsLogisticsBillDo> logisticsBillDoList = wmsLogisticsBillRepository.selectByExpressCode(expressCode);
        ExpressOperationRecordDo expressOperationRecordDo = buildExpressOperation(expressCode, ExpressOperationEnum.REPRINT.getCode());
        expressOperationRecordRepository.save(expressOperationRecordDo);
        LogisticsInfo logisticsInfo = expressPrintHandler.buildLogisticsInfo(logisticsBillDoList, null, selfOrderExpressDo.getSelfOrderNo(), null);
        logisticsInfo.setRemark(selfOrderExpressDo.getRemark());
        return logisticsInfo;
    }


    /**
     * 分页查询
     * @param params
     * @return
     */
    public PagingObject<SelfOrderExpressListQueryResp> pageBySelfOrderExpressListQueryParams(SelfOrderExpressListQueryParams params) {
        PagingObject<SelfOrderExpressDo> page = selfOrderExpressRepository.pageBySelfOrderExpressListQueryParams(params);
        PagingObject<SelfOrderExpressListQueryResp> resultPage = new PagingObject<>();
        resultPage.setTotal(page.getTotal());
        resultPage.setPageSize(page.getPageSize());
        resultPage.setPageNum(page.getPageNum());
        List<SelfOrderExpressDo> list = page.getContents();
        List<SelfOrderExpressListQueryResp> respList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            Set<String> warehouseCodes = list.stream().map(SelfOrderExpressDo::getWarehouseCode).collect(Collectors.toSet());
            List<String> selfOrderNoList = list.stream().map(SelfOrderExpressDo::getSelfOrderNo).collect(Collectors.toList());
            /*查询自主下单原始下单信息*/
            List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos = selfOrderOriginalInfoRepository.batchQueryBySelfOrderNo(OperationUserContextHolder.getTenantCode(), selfOrderNoList);
            Map<String, List<SelfOrderOriginalInfoDo>> selfOrderMap = selfOrderOriginalInfoDos.stream().collect(Collectors.groupingBy(SelfOrderOriginalInfoDo::getSelfOrderNo));
            Map<String,String> warehouseMap = basWarehouseService.findWarehouseName(warehouseCodes);
            list.forEach(e->{
                SelfOrderExpressListQueryResp resp = new SelfOrderExpressListQueryResp();
                BeanUtils.copyProperties(e,resp);
                resp.setWeight((double) e.getWeight() / 1000);
                resp.setKeepValue(e.getKeepValue());
                resp.setInsureValue((double) e.getInsureValue() / 100);
                String warehouseName = warehouseMap.get(e.getWarehouseCode());
                resp.setWarehouseName(warehouseName);
                resp.setExpressPayTypeDesc(SelfOrderPayMethodEnum.getDescByCode(e.getExpressPayType()));
                resp.setStatusDesc(SelfOrderExpressStatusEnum.getDescByCode(e.getStatus()));
                resp.setCreatedTime(DateUtils.formatDate(e.getCreatedTime(),DateUtils.FORMAT_TIME));
                resp.setOutboundSceneName(SelfOrderOutBoundSceneEnum.getDescByCode(e.getOutboundScene()));
                if (null==e.getUpdatedTime()){
                    resp.setLastUpdatedTime("");
                }else {
                    resp.setLastUpdatedTime(DateUtils.formatDate(e.getUpdatedTime(),DateUtils.FORMAT_TIME));
                }
                List<SelfOrderOriginalInfoDo> orderOriginalInfoDoList = selfOrderMap.get(e.getSelfOrderNo());
                if (CollectionUtils.isEmpty(orderOriginalInfoDoList)){
                    resp.setOriginalInfo(StringUtils.EMPTY);
                }else {
                    List<String> originalCodeList = orderOriginalInfoDoList.stream().map(SelfOrderOriginalInfoDo::getOriginalCode).collect(Collectors.toList());
                    String originalInfo = org.springframework.util.StringUtils.collectionToCommaDelimitedString(originalCodeList);
                    resp.setOriginalInfo(originalInfo);
                }
                respList.add(resp);
            });
        }
        resultPage.setContents(respList);
        return resultPage;
    }

    /**
     * 导出查询
     * @param params
     * @return
     */
    public List<Object[]> exportBySelfOrderExpressListQueryParams(SelfOrderExpressListQueryParams params) {
        List<Object[]> resultList = new ArrayList<>();
        for (; ; ) {
            List<SelfOrderExpressDo> list = selfOrderExpressRepository.exportBySelfOrderExpressListQueryParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                Set<String> expressCodeSet = list.stream().map(SelfOrderExpressDo::getExpressCode).collect(Collectors.toSet());
                List<WmsLogisticsBillDo> wmsLogisticsBillDos = wmsLogisticsBillRepository.selectByExpressCodeList(expressCodeSet);
                List<ExpressOperationRecordDo> expressOperationRecordDos = expressOperationRecordRepository.selectByExpressCodeList(expressCodeSet);
                buildExportData(list,wmsLogisticsBillDos,expressOperationRecordDos, resultList);
                params.setId(list.get(list.size() - 1).getId());
            }
            if (list.size() != NumberUtils.EXPORT_MAX_LIMIT||resultList.size()>NumberUtils.EXPORT_MAX_NUM) {
                break;
            }
        }
        return resultList;
    }

    /**
     * 构建导出数据
     * @param list
     * @param wmsLogisticsBillDos
     * @param expressOperationRecordDos
     * @param resultList
     */
    private void buildExportData(List<SelfOrderExpressDo> list, List<WmsLogisticsBillDo> wmsLogisticsBillDos, List<ExpressOperationRecordDo> expressOperationRecordDos, List<Object[]> resultList) {
        Set<String> warehouseCodeSet = list.stream().map(SelfOrderExpressDo::getWarehouseCode).collect(Collectors.toSet());
        Map<String,String> warehouseMap = basWarehouseService.findWarehouseName(warehouseCodeSet);
        Map<String, WmsLogisticsBillDo> logisticsBillDoMap = wmsLogisticsBillDos.stream().collect(Collectors.toMap(WmsLogisticsBillDo::getExpressCode, Function.identity(), (o1, o2) -> o1));
        List<String> selfOrderNoList = list.stream().map(SelfOrderExpressDo::getSelfOrderNo).collect(Collectors.toList());
        List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos = selfOrderOriginalInfoRepository.batchQueryBySelfOrderNo(OperationUserContextHolder.getTenantCode(), selfOrderNoList);
        Map<String, List<SelfOrderOriginalInfoDo>> selfOrderNoMap = selfOrderOriginalInfoDos.stream().collect(Collectors.groupingBy(SelfOrderOriginalInfoDo::getSelfOrderNo));
        /*过滤掉关单的操作*/
        Map<String, List<ExpressOperationRecordDo>> operationRecordGroup = expressOperationRecordDos.stream().filter(s -> !ExpressOperationEnum.CLOSE_EXPRESS.getCode().equals(s.getOperation())).collect(Collectors.groupingBy(ExpressOperationRecordDo::getExpressCode));
        list.forEach(e->{
            WmsLogisticsBillDo wmsLogisticsBillDo = logisticsBillDoMap.get(e.getExpressCode());
            List<ExpressOperationRecordDo> groupOperations = operationRecordGroup.get(e.getExpressCode());
            List<Object> obj = new ArrayList<>();
            obj.add(e.getOriginExpressCode());
            obj.add(e.getUniqueCode());
            List<SelfOrderOriginalInfoDo> originalInfoDos = selfOrderNoMap.get(e.getSelfOrderNo());
            if (CollectionUtils.isEmpty(originalInfoDos)){
                obj.add(StringUtils.EMPTY);
            }else {
                List<String> originalCodeList = originalInfoDos.stream().map(SelfOrderOriginalInfoDo::getOriginalCode).collect(Collectors.toList());
                String originalInfo = org.springframework.util.StringUtils.collectionToCommaDelimitedString(originalCodeList);
                obj.add(originalInfo);
            }
            obj.add(SelfOrderOutBoundSceneEnum.getDescByCode(e.getOutboundScene()));
            obj.add(e.getBuyerName());
            obj.add(e.getSellerId());
            obj.add(e.getSellerName());
            obj.add(warehouseMap.get(e.getWarehouseCode()));
            obj.add(SelfOrderPayMethodEnum.getDescByCode(e.getExpressPayType()));
            obj.add(e.getExpressCode());
            obj.add(e.getGoodsQty());
            obj.add(e.getPackQty());
            obj.add((double)e.getWeight()/1000);/*kg转换成g*/
            obj.add(com.poizon.scm.wms.util.common.StringUtils.booleanToChineseCharacter(e.getKeepValue()));
            obj.add((double) e.getInsureValue() / 100);/*金额转换，分转成元*/
            obj.add(e.getSelfOrderNo());
            obj.add(e.getCategoryName());
            obj.add(e.getLogisticsName());
            String productName = Objects.isNull(wmsLogisticsBillDo)?"":wmsLogisticsBillDo.getLogisticsProductName();
            obj.add(productName);
            obj.add(SelfOrderExpressStatusEnum.getDescByCode(e.getStatus()));
            obj.add(e.getRemark());
            obj.add(e.getUpdatedRealName());
            obj.add(e.getStationNo());
            obj.add(DateUtils.dateToString(e.getCreatedTime()));
            String sendAddress = Objects.isNull(wmsLogisticsBillDo)?"":wmsLogisticsBillDo.getSenderCity()+wmsLogisticsBillDo.getSenderArea()+wmsLogisticsBillDo.getSenderAddres();
            obj.add(sendAddress);
            String receiveAddress = Objects.isNull(wmsLogisticsBillDo)?"":wmsLogisticsBillDo.getReceiverCity()+wmsLogisticsBillDo.getReceiverArea()+wmsLogisticsBillDo.getReceiverAddress();
            obj.add(receiveAddress);
            /*打印次数*/
            int count = CollectionUtils.isEmpty(groupOperations)?0:groupOperations.size();
            obj.add(count);
            String operators = CollectionUtils.isEmpty(groupOperations)?"":groupOperations.stream().map(ExpressOperationRecordDo::getOperatorRealName).collect(Collectors.joining(","));
            obj.add(operators);
            resultList.add(obj.toArray());
        });
    }

    private boolean checkIfExpressExist(String expressCode) {
        return selfOrderExpressRepository.queryByExpressCode(expressCode) != null;
    }

    private List<WmsLogisticsBillDo> buildWmsLogisticsBillDoList(MatchAndCustomMakeParam.MatchAndCustomMakeRequest request,
                                                                 MatchAndCustomMakeResult response, PlaceOrderParam placeOrderParam) {
        List<WmsLogisticsBillDo> list = new ArrayList<>();
        ExpressChannelEnum expressChannel = ExpressChannelEnum.getByCode(response.getLogisticsCode());
        int sortNum = 1;
        if (CollectionUtils.isEmpty(response.getSubWaybillNoList())) {
            response.setSubWaybillNoList(Collections.singletonList(response.getWaybillNo()));
        }
        for (String subExpressCode : response.getSubWaybillNoList()) {
            WmsLogisticsBillDo wmsLogisticsBillDo = new WmsLogisticsBillDo();
            wmsLogisticsBillDo.setDeliveryOrderCode(StringUtils.isNotBlank(placeOrderParam.getDeliveryOrderCode()) ? "ZDY" + placeOrderParam.getDeliveryOrderCode() : "");
            wmsLogisticsBillDo.setLogisticsCode(expressChannel.getCode());
            wmsLogisticsBillDo.setLogisticsName(expressChannel.getName());
            wmsLogisticsBillDo.setExpressCode(response.getWaybillNo());
            wmsLogisticsBillDo.setSubExpressCode(subExpressCode);
            wmsLogisticsBillDo.setExpressType(ExpressProductEnum.getTypeByCode(response.getLogisticsProductCode()));
            wmsLogisticsBillDo.setDestCode(response.getDestCode());
            wmsLogisticsBillDo.setReceiverName(placeOrderParam.getReceiverName());
            wmsLogisticsBillDo.setReceiverMobile(placeOrderParam.getReceiverMobile());
            wmsLogisticsBillDo.setReceiverTel(placeOrderParam.getReceiverMobile());
            WaybillAddress waybillAddress = response.getDestAddress();
            if(tmsPlaceOrderFacade.useDesAddress&&Objects.nonNull(waybillAddress)){
                //使用des返回的地址信息
                wmsLogisticsBillDo.setReceiverCountry(waybillAddress.getCountry());
                wmsLogisticsBillDo.setReceiverProvince(waybillAddress.getProvince());
                wmsLogisticsBillDo.setReceiverProvinceCode(waybillAddress.getProvinceCode());
                wmsLogisticsBillDo.setReceiverCity(waybillAddress.getCity());
                wmsLogisticsBillDo.setReceiverCityCode(waybillAddress.getCityCode());
                wmsLogisticsBillDo.setReceiverArea(waybillAddress.getRegion());
                wmsLogisticsBillDo.setReceiverAreaCode(waybillAddress.getRegionCode());
                wmsLogisticsBillDo.setReceiverTown(waybillAddress.getTown());
                wmsLogisticsBillDo.setReceiverTownCode(waybillAddress.getTownCode());
                wmsLogisticsBillDo.setReceiverAddress(waybillAddress.getDetailedAddress());
            }else{
                wmsLogisticsBillDo.setReceiverProvince(placeOrderParam.getProvince());
                wmsLogisticsBillDo.setReceiverProvinceCode(placeOrderParam.getProvinceCode());
                wmsLogisticsBillDo.setReceiverCity(placeOrderParam.getCity());
                wmsLogisticsBillDo.setReceiverCityCode(placeOrderParam.getRegionCode());
                wmsLogisticsBillDo.setReceiverArea(placeOrderParam.getRegion());
                wmsLogisticsBillDo.setReceiverAreaCode(placeOrderParam.getRegionCode());
                wmsLogisticsBillDo.setReceiverTownCode(placeOrderParam.getTownCode());
                wmsLogisticsBillDo.setReceiverTown(placeOrderParam.getTown());
                wmsLogisticsBillDo.setReceiverAddress(placeOrderParam.getDetailAddress());
            }
            wmsLogisticsBillDo.setSenderAddres(request.getOriginAddress().getDetailedAddress());
            wmsLogisticsBillDo.setSenderName(request.getOriginName());
            wmsLogisticsBillDo.setSenderMobile(request.getOriginMobile());
            wmsLogisticsBillDo.setSenderTel(request.getOriginTel());
            wmsLogisticsBillDo.setSenderProvince(request.getOriginAddress().getProvince());
            wmsLogisticsBillDo.setSenderCity(request.getOriginAddress().getCity());
            wmsLogisticsBillDo.setSenderArea(request.getOriginAddress().getRegion());
            wmsLogisticsBillDo.setSendDate(new Date());
            wmsLogisticsBillDo.setPayMethod(ExpressPayMethodEnum.getByCode(request.getPayType()).getType());
            wmsLogisticsBillDo.setPackQty(request.getQuantity());
            wmsLogisticsBillDo.setInsureValue(request.getInsureValue().intValue());
            wmsLogisticsBillDo.setSiteInfo(Objects.nonNull(response.getSiteInfo()) ? JSON.toJSONString(response.getSiteInfo()) : "");
            wmsLogisticsBillDo.setZoneCode(response.getZoneCode());
            wmsLogisticsBillDo.setGatheringPlace(response.getGatheringPlace());
            wmsLogisticsBillDo.setWarehouseCode(request.getRepositoryCode());
            wmsLogisticsBillDo.setCategoryName(placeOrderParam.getCategoryName());
            wmsLogisticsBillDo.setSort(sortNum++);
            wmsLogisticsBillDo.setPackNo("");
            Boolean airEmbargo = Optional.ofNullable(response.getExtendMap())
                    .map(item -> item.get("airEmbargo"))
                    .map(object -> (boolean) object)
                    .orElse(false);
            wmsLogisticsBillDo.setAirProhibitedItems(airEmbargo ? 1 : 0);
            //危险品
            wmsLogisticsBillDo.setDangerousItems(response.getDangerous() ? 1 : 0);

            wmsLogisticsBillDo.setWeight(StringUtils.EMPTY);
            wmsLogisticsBillDo.setOriginName(request.getOriginName());
            if (CollectionUtils.isNotEmpty(response.getWaybillRelations())) {
                WaybillRelation waybillRelation = response.getWaybillRelations().get(0);
                if (waybillRelation != null) {
                    wmsLogisticsBillDo.setDuExpressCode(waybillRelation.getDwWaybillNo());
                    wmsLogisticsBillDo.setDuSubExpressCode(waybillRelation.getDwSubWaybillNo());
                }
            }
            wmsLogisticsBillDo.setRouteCode(response.getRouteCode());
            wmsLogisticsBillDo.setDowngradeExpressCode(response.getHitFallback() ? 1 : 0);
            wmsLogisticsBillDo.setGoodsQty(placeOrderParam.getGoodsQty());
            wmsLogisticsBillDo.setTenantCode(OperationUserContextHolder.getTenantCode());
            wmsLogisticsBillDo.setLogisticsProductCode(response.getLogisticsProductCode());
            wmsLogisticsBillDo.setLogisticsProductName(response.getLogisticsProductName());
            wmsLogisticsBillDo.setPackageWarnings(JsonUtils.serialize(response.getPackageWarnings()));
            list.add(wmsLogisticsBillDo);
        }
        return list;
    }

    private SelfOrderExpressDo buildSelfOrderExpressDo(PlaceOrderParam placeOrderParam, MatchAndCustomMakeResult response,
                                                       String selfOrderNo,String status) {
        SelfOrderExpressDo selfOrderExpressDo = new SelfOrderExpressDo();
        selfOrderExpressDo.setSelfOrderNo(selfOrderNo);
        selfOrderExpressDo.setOriginExpressCode(placeOrderParam.getOriginExpressCode());
        selfOrderExpressDo.setUniqueCode(placeOrderParam.getUniqueCode());
        selfOrderExpressDo.setOutboundScene(placeOrderParam.getOutboundScene());
        selfOrderExpressDo.setBuyerName(placeOrderParam.getBuyerName());
        selfOrderExpressDo.setSellerName(placeOrderParam.getSellerName());
        selfOrderExpressDo.setSellerId(placeOrderParam.getSellerId());
        selfOrderExpressDo.setExpressPayType(placeOrderParam.getExpressPayType());
        selfOrderExpressDo.setStatus(status);
        selfOrderExpressDo.setLogisticsCode(response.getLogisticsCode());
        selfOrderExpressDo.setLogisticsName(response.getLogisticsName());
        selfOrderExpressDo.setExpressCode(response.getWaybillNo());
        selfOrderExpressDo.setRemark(placeOrderParam.getRemark());
        selfOrderExpressDo.setStationNo(OperationUserContextHolder.get().getStationNo());
        selfOrderExpressDo.setWarehouseCode(OperationUserContextHolder.get().getWarehouseCode());
        selfOrderExpressDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        selfOrderExpressDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        selfOrderExpressDo.setUpdatedTime(new Date());
        selfOrderExpressDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        selfOrderExpressDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        selfOrderExpressDo.setCategoryId(placeOrderParam.getCategoryId());
        selfOrderExpressDo.setCategoryName(placeOrderParam.getCategoryName());
        selfOrderExpressDo.setReceiverName(placeOrderParam.getReceiverName());
        selfOrderExpressDo.setReceiverMobile(placeOrderParam.getReceiverMobile());
        selfOrderExpressDo.setReceiverProvince(placeOrderParam.getProvince());
        selfOrderExpressDo.setReceiverCity(placeOrderParam.getCity());
        selfOrderExpressDo.setReceiverArea(placeOrderParam.getRegion());
        selfOrderExpressDo.setReceiverAddress(placeOrderParam.getDetailAddress());
        selfOrderExpressDo.setPackQty(placeOrderParam.getPackQty());
        selfOrderExpressDo.setGoodsQty(placeOrderParam.getGoodsQty());
        selfOrderExpressDo.setWeight(placeOrderParam.getWeight());
        selfOrderExpressDo.setKeepValue(placeOrderParam.isKeepValue());
        selfOrderExpressDo.setInsureValue(placeOrderParam.getInsureValue());
        return selfOrderExpressDo;
    }

    private MatchAndCustomMakeParam buildMatchAndCustomMakeRequest(PlaceOrderParam placeOrderParam, String orderCode) {
        //自定义下单
        MatchAndCustomMakeParam matchAndCustomMakeParam = new MatchAndCustomMakeParam();
        //运配升级
        MatchAndCustomMakeParam.MatchAndCustomMakeRequest matchAndCustomMakeRequest = new MatchAndCustomMakeParam.MatchAndCustomMakeRequest();
        matchAndCustomMakeParam.setOfcInnerCmd(null);
        matchAndCustomMakeParam.setMatchAndCustomMakeRequest(matchAndCustomMakeRequest);
        // 订单no
        matchAndCustomMakeRequest.setBizOrderNo(orderCode);
        //matchAndCustomMakeRequest.setBizOrderId(deliveryHeaderDo.getOrderBizId());
        // 业务类型
        if (!selfOrderSwitchFlag) {
            //灰度开关
            matchAndCustomMakeRequest.setBizType(ExpressBizTypeEnum.DW_ZIZHU.getCode());
        } else if (SelfOrderPayMethodEnum.DAO_FU_NORMAL.getCode().equals(placeOrderParam.getExpressPayType())) {
            matchAndCustomMakeRequest.setBizType(ExpressBizTypeEnum.DW_ZIZHU_DF.getCode());
        } else {
            matchAndCustomMakeRequest.setBizType(ExpressBizTypeEnum.DW_ZIZHU_JF.getCode());
        }
        // 业务场景
        matchAndCustomMakeRequest.setBizScene(ExpressScenesEnum.ZIZHU.getCode());
        // 单据类型
        matchAndCustomMakeRequest.setBizBillType(BizBillTypeEnum.ZIZHU.getCode());
        // 仓库code
        matchAndCustomMakeRequest.setRepositoryCode(OperationUserContextHolder.get().getWarehouseCode());
        ScpWarehouseDo scpWarehouseDo = scpWarehouseRepository.queryByCodeAndTenant(OperationUserContextHolder.get().getWarehouseCode());
        if (scpWarehouseDo == null) {
            throw new WmsOperationException("仓库不存在或已被禁用");
        }
        setSenderAndReceiverInfo(matchAndCustomMakeRequest, scpWarehouseDo, placeOrderParam);
        // 包裹数量
        matchAndCustomMakeRequest.setQuantity(placeOrderParam.getPackQty());
        // 付款方式
        matchAndCustomMakeRequest.setPayType(placeOrderParam.getExpressPayType());
        // 保价金额（分）
        matchAndCustomMakeRequest.setInsureValue(placeOrderParam.getInsureValue());
        // 托寄物
        matchAndCustomMakeRequest.setConsignments(buildConsignments(placeOrderParam));
        // 重量
        matchAndCustomMakeRequest.setTotalWeight(placeOrderParam.getWeight());
        /*总体积为0*/
        matchAndCustomMakeRequest.setTotalVolume(0L);
        // 是否支持降级
        matchAndCustomMakeRequest.setSupportFallback(tmsExpressCodeSwitch);
        // 园区
        matchAndCustomMakeRequest.setOriginWarehouseZoneCode(scpWarehouseDo.getParkCode() != null ? scpWarehouseDo.getParkCode() : "YQSH01");

        matchAndCustomMakeRequest.setUnalterableLogistics(StringUtils.isBlank(placeOrderParam.getLogisticsProductCode()) ? false : true);
        matchAndCustomMakeRequest.setLogisticsCode(placeOrderParam.getLogisticsCode());
        matchAndCustomMakeRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        //运输产品
        matchAndCustomMakeRequest.setLogisticsProductCode(placeOrderParam.getLogisticsProductCode());
        return matchAndCustomMakeParam;
    }

    private void setSenderAndReceiverInfo(MatchAndCustomMakeParam.MatchAndCustomMakeRequest matchAndCustomMakeRequest, ScpWarehouseDo scpWarehouseDo, PlaceOrderParam placeOrderParam) {
        // 发件地址
        matchAndCustomMakeRequest.setOriginAddress(buildSenderAddress(scpWarehouseDo));
        // 公司名
        matchAndCustomMakeRequest.setOriginCompany(scpWarehouseDo.getWarehouseOutDeliveryContact());
        // 发件人姓名
        matchAndCustomMakeRequest.setOriginName(scpWarehouseDo.getWarehouseOutDeliveryContact());
        // 发件人手机
        matchAndCustomMakeRequest.setOriginMobile(scpWarehouseDo.getWarehouseOutDeliveryTel());
        // 发件人电话
        matchAndCustomMakeRequest.setOriginTel(scpWarehouseDo.getWarehouseOutDeliveryTel());
        // 收件地址
        matchAndCustomMakeRequest.setDestAddress(buildReceiverAddress(placeOrderParam));
        // 收件人姓名
        matchAndCustomMakeRequest.setDestName(placeOrderParam.getReceiverName());
        // 收件人手机
        matchAndCustomMakeRequest.setDestMobile(placeOrderParam.getReceiverMobile());
        // 收件人电话
        matchAndCustomMakeRequest.setDestTel("");
    }

    private WaybillAddress buildSenderAddress(ScpWarehouseDo scpWarehouseDo) {
        WaybillAddress waybillAddress = new WaybillAddress();
        waybillAddress.setProvince(scpWarehouseDo.getWarehouseOutDeliveryProvince());
        waybillAddress.setCity(scpWarehouseDo.getWarehouseOutDeliveryCity());
        waybillAddress.setRegion(scpWarehouseDo.getWarehouseOutDeliveryRegion());
        waybillAddress.setDetailedAddress(scpWarehouseDo.getWarehouseOutDeliveryAddressDetail());
        return waybillAddress;
    }

    private WaybillAddress buildReceiverAddress(PlaceOrderParam placeOrderParam) {
        WaybillAddress destAddress = new WaybillAddress();
        destAddress.setProvince(placeOrderParam.getProvince());
        destAddress.setProvinceCode(placeOrderParam.getProvinceCode());
        destAddress.setCityCode(placeOrderParam.getCityCode());
        destAddress.setCity(placeOrderParam.getCity());
        destAddress.setRegionCode(placeOrderParam.getRegionCode());
        destAddress.setRegion(placeOrderParam.getRegion());
        destAddress.setTown(placeOrderParam.getTown());
        destAddress.setTownCode(placeOrderParam.getTownCode());
        destAddress.setDetailedAddress(placeOrderParam.getDetailAddress());
        return destAddress;
    }

    private List<MatchAndCustomMakeParam.MatchAndCustomMakeConsignment> buildConsignments(PlaceOrderParam placeOrderParam) {
        MatchAndCustomMakeParam.MatchAndCustomMakeConsignment consignment = new MatchAndCustomMakeParam.MatchAndCustomMakeConsignment();
        consignment.setCategoryId(placeOrderParam.getCategoryId());
        consignment.setName(placeOrderParam.getCategoryName());
        //consignment.setSkuId();
        consignment.setQuantity(placeOrderParam.getGoodsQty()); //商品数量
        return Collections.singletonList(consignment);
    }

    public void closeOrder(String selfOrderNo) {
        SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryBySelfOrderNo(selfOrderNo);
        if (selfOrderExpressDo == null) {
            throw new WmsOperationException("订单不存在");
        }
        if (selfOrderExpressDo.getStatus().equals(SelfOrderExpressStatusEnum.shipped.name())) {
            throw new WmsOperationException("订单状态已发货");
        }
        WaybillCancelRequest cancelRequest = new WaybillCancelRequest();
        cancelRequest.setLogisticsCode(selfOrderExpressDo.getLogisticsCode());
        cancelRequest.setWaybillNo(selfOrderExpressDo.getExpressCode());
        tmsPlaceOrderFacade.cancel(cancelRequest);
        selfOrderExpressRepository.updateStatusBySelfOrderNo(selfOrderNo, SelfOrderExpressStatusEnum.canceled.name());
        ExpressOperationRecordDo expressOperationRecordDo = buildExpressOperation(selfOrderExpressDo.getExpressCode(), ExpressOperationEnum.CLOSE_EXPRESS.getCode());
        expressOperationRecordRepository.save(expressOperationRecordDo);
    }

    @ManualTransaction
    public void shipOrder(String selfOrderNo) {
        selfOrderExpressRepository.updateStatusBySelfOrderNo(selfOrderNo, SelfOrderExpressStatusEnum.shipped.name());
        selfOrderShipProducer.sendMessage(selfOrderNo);
    }

    /**
     * 查询运单信息
     *
     * @param selfOrderNo
     */
    public StdTmsExpressInfo queryExpressInfo(String selfOrderNo) {
        SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryBySelfOrderNo(selfOrderNo);
        if (Objects.isNull(selfOrderExpressDo)){
            throw new WmsOperationException(WmsExceptionCode.SELF_ORDER_EXPRESS_NOT_EXIST);
        }
        H5QueryExpressSheetRequest request = new H5QueryExpressSheetRequest();
        request.setOrderNo(selfOrderExpressDo.getSelfOrderNo());
        request.setBizScene(ExpressScenesEnum.ZIZHU.getCode());
        request.setConsignmentName(selfOrderExpressDo.getCategoryName());
        H5QueryExpressSheetResponse expressInfo = tmsWayBillServiceFacade.getExpressInfo(request);
        StdTmsExpressInfo info = new StdTmsExpressInfo();
        BeanUtil.copy(expressInfo,info);
        return info;
    }

}
