package com.poizon.scm.wms.service.inventory.operate;

import com.poizon.scm.wms.api.dto.request.inventory.DeliveryHeaderRedistributeRequest;
import com.poizon.scm.wms.api.dto.request.inventory.InventoryRedistributeRequest;
import com.poizon.scm.wms.api.dto.request.inventory.InventoryUnfreezeRequest;
import com.poizon.scm.wms.pojo.inventory.*;

import java.util.List;
import java.util.Map;

/**
 * @category <AUTHOR>
 * @since 2020/4/15
 */
public interface InventoryOperateService {

    /***
     * 批量收货
     * @category
     * <AUTHOR>
     * @since
     * @param inventoryAddOperatePojos
     * @return key:任务明细编号，value:库存编号
     */
    Map<String, String> receive(List<InventoryAddOperatePojo> inventoryAddOperatePojos);

    /***
     * 直接加库存
     * @category
     * <AUTHOR>
     * @since
     * @param inventoryAddOperatePojo
     * @return 库存编号
     */
    String add(InventoryAddOperatePojo inventoryAddOperatePojo);

    /***
     * 收货
     * @category
     * <AUTHOR>
     * @since 2020年05月07日 18:34:13
     * @param inventoryAddOperatePojo
     * @return java.lang.String
     */
    String receive(InventoryAddOperatePojo inventoryAddOperatePojo);

    /**
     * 获取库存批次号
     *
     * @param inventoryDimensionPojo
     * @param activityType
     * @return
     */
    String getInventoryBatchNo(InventoryDimensionPojo inventoryDimensionPojo, String activityType);

    String getInventoryBatchNo(InventoryDimensionPojo inventoryDimensionPojo, String activityType, Integer expiryDateRecordMode);

    /**
     * 更新批次属性信息
     * @param batchNo
     * @param activityType
     * @param expiryDateRecordMode
     * @return
     */
    String modifyBatchAttrInfo(String batchNo, String activityType, Integer expiryDateRecordMode);

    /***
     * 上架
     * @category
     * <AUTHOR>
     * @since 2020年04月15日 20:38:29
     * @param inventoryMoveOperatePojo
     * @return 库存编号
     */
    String onShelves(InventoryShelvesOperatePojo inventoryMoveOperatePojo);

    /***
     * 拣货
     * @category
     * <AUTHOR>
     * @since 2020年05月06日 17:22:28
     * @param inventoryMoveOperatePojo
     * @return java.lang.String
     */
    String pick(InventoryMoveOperatePojo inventoryMoveOperatePojo);

    /***
     * 预占库存
     * @category
     * <AUTHOR>
     * @since 2020年04月15日 20:41:38
     * @param inventoryAllocatedOrderPojo
     * @param allocateAsync
     * @return java.lang.Boolean
     */
    void allocatedInventory(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, Boolean allocateAsync);

    /**
     * 校验单据能否做库存分配
     *
     * @param deliveryType
     * @param bizType
     * @param deliveryOrderCode
     * @return false：不能，true 能
     */
    boolean checkInvAllocated(String deliveryType, Integer bizType, String deliveryOrderCode);

    /***
     * 异步预占库存
     * @category
     * <AUTHOR>
     * @since 2020年04月15日 20:41:38
     * @param inventoryAllocatedOrderPojo
     * @param allocateAsync
     * @return java.lang.Boolean
     */
    void asyncAllocatedInventory(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, Boolean allocateAsync);

    /**
     * 为job分配
     *
     * @param deliveryOrderCode
     */
    void allocatedInventoryForJob(String deliveryOrderCode);

    /***
     * 取消分配库存
     * @category
     * <AUTHOR>
     * @since 2020年05月12日 18:21:19
     * @param inventoryAllocatedCancelPojo
     * @return void
     */
    Boolean cancelAllocatedInventory(InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo);

    /***
     * 更新库存属性
     * @category
     * <AUTHOR>
     * @since 2020年04月15日 20:43:14
     * @param inventoryOperatePojo
     * @return java.lang.Boolean
     */
    String modifyInventoryAttr(InventoryModifyAttrOperatePojo inventoryOperatePojo);

    /***
     * 库存移位
     * @category
     * <AUTHOR>
     * @since 2020年04月26日 22:38:53
     * @param inventoryOperatePojo
     * @return void
     */
    String moveInventory(InventoryMoveOperatePojo inventoryOperatePojo);

    /***
     * 发货
     * @category
     * <AUTHOR>
     * @since 2020年05月07日 18:36:49
     * @param inventoryBasePojo
     * @return java.lang.String
     */
    String ship(InventoryBasePojo inventoryBasePojo);

    /**
     * 返架
     *
     * @param inventoryOperatePojo
     * @return
     */
    String returnShelfInventory(InventoryMoveOperatePojo inventoryOperatePojo);


    /**
     * 返架v2
     *
     * @param inventoryOperatePojo
     * @return
     */
    String returnShelfInventoryV2(InventoryMoveOperatePojo inventoryOperatePojo);

    /**
     * 补货下架
     *
     * @param inventoryMoveOperatePojo
     * @param returnQty
     * @return
     */
    String replenishOff(InventoryMoveOperatePojo inventoryMoveOperatePojo, Integer returnQty);

    /**
     * 补货上架
     *
     * @param inventoryMoveOperatePojo
     * @return
     */
    String replenishOnShelf(InventoryMoveOperatePojo inventoryMoveOperatePojo);

    /**
     * 库存再次分配
     *
     * @param inventoryRedistributeRequest
     * @return
     */
    void redistributeInventory(InventoryRedistributeRequest inventoryRedistributeRequest);

    void redistributeInventory(DeliveryHeaderRedistributeRequest request);

    /**
     * 解锁库存
     */
    int unfreezeQty(InventoryUnfreezeRequest inventoryUnfreezeRequest);

    /**
     * 更新库存行容器号，库存KC号不变
     *
     * @param inventoryNo
     * @param tenantCode
     * @param containerCode
     */
    void modifyContainerCode(String inventoryNo, String tenantCode, String containerCode);

    /**
     * 通过唯一码扣减库存(当前用户处理等真寄存交接合并退交接扣减库存)
     *  @param uniqueCodes
     * @param tenantCode
     * @param warehouseCode
     * @param orderType
     */
    void detectInventory(List<String> uniqueCodes, String tenantCode, String warehouseCode, String orderCode, String orderType);

    /***
     * 取消分配库存
     * @category
     * <AUTHOR>
     * @since 2020年05月12日 18:21:19
     * @param inventoryAllocatedCancelByInventoryNoPojo
     * @return void
     */
    Boolean cancelAllocatedByInventoryNo(InventoryAllocatedCancelByInventoryNoPojo inventoryAllocatedCancelByInventoryNoPojo);
}
