package com.poizon.scm.wms.domain.outbound.selforderexpress.producer;

import com.dewu.executor.annotation.Delay;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsLogisticsBillRepository;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.model.SelfOrderExpressDo;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.model.SelfOrderOriginalInfoDo;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.repository.SelfOrderExpressRepository;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.repository.SelfOrderOriginalInfoRepository;
import com.poizon.scm.wms.api.constants.WmsMessageQueueConstant;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.Consts;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.domain.notify.notifyLms.NotifyLmsMessage;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.DeliveryOutNotifyDesParam;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.util.common.StringUtils;
import com.poizon.scm.wms.util.enums.SelfOrderOutBoundSceneEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.common.constants.Consts.DYNAMIC_DATASOURCE_LISTENERS;

/**
 * <AUTHOR>
 * @date 2022/4/20
 */
@Component
public class SelfOrderShipProducer {

    @Resource
    private SelfOrderExpressRepository selfOrderExpressRepository;

    @Resource
    private WmsLogisticsBillRepository logisticsBillRepository;

    @Resource
    private SelfOrderOriginalInfoRepository selfOrderOriginalInfoRepository;

    @Resource
    private SendMessageHandler sendMessageHandler;

    @EventualConsistency(delay = @Delay(delay = 30), referenceNo = "#selfOrderNo",label = Consts.OUTBOUND_TRANSFER,listeners = {DYNAMIC_DATASOURCE_LISTENERS})
    public void sendMessage(String selfOrderNo) {
        SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryBySelfOrderNo(selfOrderNo);
        if (Objects.isNull(selfOrderExpressDo)) {
            throw new WmsException("自主下单信息不存在");
        }
        List<WmsLogisticsBillDo> logisticsBillDoList = logisticsBillRepository.selectByExpressCode(selfOrderExpressDo.getExpressCode());
        if (CollectionUtils.isEmpty(logisticsBillDoList)) {
            throw new WmsException("自主下单面单信息不存在");
        }
        // 通知mes自主下单消息
        List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos = selfOrderOriginalInfoRepository.batchQueryBySelfOrderNo(selfOrderExpressDo.getTenantCode(), Collections.singletonList(selfOrderExpressDo.getSelfOrderNo()));
        sendMessageHandler.process(buildMessage(selfOrderExpressDo, logisticsBillDoList, selfOrderOriginalInfoDos));
        sendMessageHandler.sendToNewCluster(buildScpMessage(selfOrderExpressDo, logisticsBillDoList, selfOrderOriginalInfoDos));
        // 多件退回
        if (SelfOrderOutBoundSceneEnum.DJTH.getCode().equals(selfOrderExpressDo.getOutboundScene())) {
            // 发送计时计件消息
            sendMessageHandler.process(buildLmsPieceCountMsg(selfOrderExpressDo));
            // 通知des订单运单关系
            notifyDesBandExpressRelation(logisticsBillDoList, selfOrderExpressDo);
        }
    }

    private void notifyDesBandExpressRelation(List<WmsLogisticsBillDo> logisticsBillDoList, SelfOrderExpressDo selfOrderExpressDo) {
        for (WmsLogisticsBillDo wmsLogisticsBillDo : logisticsBillDoList) {
            DeliveryOutNotifyDesParam orderExpressCodeDto = new DeliveryOutNotifyDesParam();
            DeliveryOutNotifyDesParam.DeliveryOutNotifyDesItem expressCodeDESDto = new DeliveryOutNotifyDesParam.DeliveryOutNotifyDesItem();
            expressCodeDESDto.setParentBillNo(selfOrderExpressDo.getSelfOrderNo());
            expressCodeDESDto.setOrderNo(selfOrderExpressDo.getSelfOrderNo());
            expressCodeDESDto.setWaybillNo(selfOrderExpressDo.getExpressCode());
            expressCodeDESDto.setSubWaybillNo(wmsLogisticsBillDo.getSubExpressCode());
            expressCodeDESDto.setLogisticsCode(selfOrderExpressDo.getLogisticsCode());
            expressCodeDESDto.setDeliveryTime(new Date());
            orderExpressCodeDto.setList(Collections.singletonList(expressCodeDESDto));
            orderExpressCodeDto.setTenantId(selfOrderExpressDo.getTenantCode());
            SendMessage<DeliveryOutNotifyDesParam> message = new SendMessage<>();
            message.setMessageKey(selfOrderExpressDo.getExpressCode());
            message.setMessageContent(orderExpressCodeDto);
            message.setTopic(RocketMqConstants.ReplaceExpressPinkTopic.TOPIC_NAME);
            message.setTag(RocketMqConstants.ReplaceExpressPinkTopic.TAG);
            sendMessageHandler.process(message);
        }
    }

    private SendMessage<NotifyLmsMessage> buildLmsPieceCountMsg(SelfOrderExpressDo selfOrderExpressDo) {
        NotifyLmsMessage notifyLmsMessage = new NotifyLmsMessage();
        notifyLmsMessage.setUniqueKey(selfOrderExpressDo.getExpressCode());
        notifyLmsMessage.setCount(selfOrderExpressDo.getGoodsQty());
        notifyLmsMessage.setStatisticsPoint("MULTIPLE_RETURN");
        notifyLmsMessage.setOperationTime(DateUtils.dateToString(new Date(),DateUtils.FORMAT_TIME));
        notifyLmsMessage.setSignId(OperationUserContextHolder.get().getSignId());
        notifyLmsMessage.setUserId(OperationUserContextHolder.get().getUserId());
        notifyLmsMessage.setTenantId(OperationUserContextHolder.getTenantCode());
        notifyLmsMessage.setOperateWarehouseCode(OperationUserContextHolder.get().getWarehouseCode());
//            notifyLmsMessage.setReferenceType();
//            notifyLmsMessage.setReferenceNo();
//            notifyLmsMessage.setReferenceDetailNo();
//            notifyLmsMessage.setSkuId();
        notifyLmsMessage.setAppId("WMS");
        notifyLmsMessage.setCategoryId(0L);
        notifyLmsMessage.setCategoryName("无商品类目");
        notifyLmsMessage.setAssistMasterUserId(OperationUserContextHolder.get().getUserId());
        NotifyLmsMessage.LmsPieceCountDetail lmsPieceCountDetail = new NotifyLmsMessage.LmsPieceCountDetail();
//            lmsPieceCountDetail.setUniqueCode();
//            lmsPieceCountDetail.setSkuId();
//            lmsPieceCountDetail.setWmsTaskNo();
        lmsPieceCountDetail.setExpressCode(selfOrderExpressDo.getExpressCode());

        notifyLmsMessage.setDetailContents(Collections.singletonList(lmsPieceCountDetail));
        SendMessage<NotifyLmsMessage> message = new SendMessage<>();
        message.setMessageKey(selfOrderExpressDo.getExpressCode());
        message.setMessageContent(notifyLmsMessage);
        message.setTopic(RocketMqConstants.ScmLmsTopic.TOPIC_NAME);
        message.setTag(RocketMqConstants.ScmLmsTopic.TAG);
        return message;
    }

    /**
     * 给pink发保税二销的运单信息
     * @param expressCode
     */
    @EventualConsistency(delay = @Delay(delay = 30), referenceNo = "#expressCode")
    public void sendBSEXShipMessage(String expressCode) {
        List<WmsLogisticsBillDo> logisticsBillDoList = logisticsBillRepository.selectByExpressCode(expressCode);
        if (CollectionUtils.isEmpty(logisticsBillDoList)) {
            throw new WmsException("自主下单面单信息不存在");
        }
        sendMessageHandler.process(buildMessage(null, logisticsBillDoList, null));
    }

    private SendMessage<SelfOrderExpressInfo> buildMessage(SelfOrderExpressDo selfOrderExpressDo,
                                                           List<WmsLogisticsBillDo> logisticsBillDoList,
                                                           List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos) {
        SelfOrderExpressInfo selfOrderExpressInfo = buildSelfOrderExpressInfo(selfOrderExpressDo, logisticsBillDoList, selfOrderOriginalInfoDos);
        SendMessage<SelfOrderExpressInfo> message = new SendMessage<>();
        message.setMessageKey(selfOrderExpressInfo.getExpressCode());
        message.setMessageContent(selfOrderExpressInfo);
        message.setTag(RocketMqConstants.WmsOutBoundTopic.TAG_SELF_ORDER_EXPRESS_SHIP);
        message.setTopic(RocketMqConstants.WmsOutBoundTopic.TOPIC_NAME);
        return message;
    }
    private SendMessage<SelfOrderExpressInfo> buildScpMessage(SelfOrderExpressDo selfOrderExpressDo,
                                                           List<WmsLogisticsBillDo> logisticsBillDoList,
                                                           List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos) {
        SelfOrderExpressInfo selfOrderExpressInfo = buildSelfOrderExpressInfo(selfOrderExpressDo, logisticsBillDoList, selfOrderOriginalInfoDos);
        SendMessage<SelfOrderExpressInfo> message = new SendMessage<>();
        message.setMessageKey(selfOrderExpressInfo.getExpressCode());
        message.setMessageContent(selfOrderExpressInfo);
        message.setTag(WmsMessageQueueConstant.TAG_SELF_ORDER_EXPRESS_SHIP);
        message.setTopic(WmsMessageQueueConstant.TOPIC_WMS_OUTBOUND);
        return message;
    }

    private SelfOrderExpressInfo buildSelfOrderExpressInfo(SelfOrderExpressDo selfOrderExpressDo, List<WmsLogisticsBillDo> logisticsBillDoList, List<SelfOrderOriginalInfoDo> selfOrderOriginalInfoDos) {
        WmsLogisticsBillDo wmsLogisticsBillDo = logisticsBillDoList.get(0);
        SelfOrderExpressInfo selfOrderExpressInfo = new SelfOrderExpressInfo();
        selfOrderExpressInfo.setRepositoryCode(OperationUserContextHolder.get().getWarehouseCode());
        selfOrderExpressInfo.setUserId(OperationUserContextHolder.get().getUserId());
        selfOrderExpressInfo.setLogisticsCode(wmsLogisticsBillDo.getLogisticsCode());
        selfOrderExpressInfo.setLogisticsName(wmsLogisticsBillDo.getLogisticsName());
        selfOrderExpressInfo.setExpressCode(wmsLogisticsBillDo.getExpressCode());
        selfOrderExpressInfo.setSubExpressCodes(logisticsBillDoList.stream().map(WmsLogisticsBillDo::getSubExpressCode).collect(Collectors.toList()));
        //得物运单号&子运单号
        selfOrderExpressInfo.setDwExpressCode(wmsLogisticsBillDo.getDuExpressCode());
        List<SelfOrderExpressInfo.SubExpressInfo> subExpressInfos = new ArrayList<>();
        for (WmsLogisticsBillDo logisticsBillDo : logisticsBillDoList) {
            SelfOrderExpressInfo.SubExpressInfo subExpressInfo = new SelfOrderExpressInfo.SubExpressInfo();
            subExpressInfo.setSubExpressCode(logisticsBillDo.getSubExpressCode());
            subExpressInfo.setDwSubExpressCode(logisticsBillDo.getDuSubExpressCode());
            subExpressInfos.add(subExpressInfo);
        }
        selfOrderExpressInfo.setSubExpressInfos(subExpressInfos);
        List<String> originalInfo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(selfOrderOriginalInfoDos)) {
            Set<String> originalCode = selfOrderOriginalInfoDos.stream().map(SelfOrderOriginalInfoDo::getOriginalCode).collect(Collectors.toSet());
            originalInfo.addAll(originalCode);
        }
        if (selfOrderExpressDo != null) {
            if (StringUtils.isNotEmpty(selfOrderExpressDo.getUniqueCode())) {
                originalInfo.add(selfOrderExpressDo.getUniqueCode());
            }
            if (StringUtils.isNotEmpty(selfOrderExpressDo.getOriginExpressCode())) {
                originalInfo.add(selfOrderExpressDo.getOriginExpressCode());
            }
            selfOrderExpressInfo.setReferenceNo(selfOrderExpressDo.getReferenceNo());
            selfOrderExpressInfo.setSelfOrderNo(selfOrderExpressDo.getSelfOrderNo());
            SelfOrderExpressInfo.ReceiverInfo receiverInfo = new SelfOrderExpressInfo.ReceiverInfo();
            receiverInfo.setMobile(selfOrderExpressDo.getReceiverMobile());
            receiverInfo.setReceiverName(selfOrderExpressDo.getReceiverName());
            receiverInfo.setProvince(selfOrderExpressDo.getReceiverProvince());
            receiverInfo.setProvinceCode(selfOrderExpressDo.getReceiverProvinceCode());
            receiverInfo.setCity(selfOrderExpressDo.getReceiverCity());
            receiverInfo.setCityCode(selfOrderExpressDo.getReceiverCityCode());
            receiverInfo.setArea(selfOrderExpressDo.getReceiverArea());
            receiverInfo.setAreaCode(selfOrderExpressDo.getReceiverAreaCode());
            receiverInfo.setTown(selfOrderExpressDo.getReceiverTown());
            receiverInfo.setTownCode(selfOrderExpressDo.getReceiverTownCode());
            receiverInfo.setAddress(selfOrderExpressDo.getReceiverAddress());
            selfOrderExpressInfo.setScene(selfOrderExpressDo.getOutboundScene());
            selfOrderExpressInfo.setProductTitle(selfOrderExpressDo.getCategoryName());
            selfOrderExpressInfo.setExpressPayType(selfOrderExpressDo.getExpressPayType());
            selfOrderExpressInfo.setDeliverTime(new Date());
            selfOrderExpressInfo.setGoodsQty(selfOrderExpressDo.getGoodsQty());
            selfOrderExpressInfo.setRemark(selfOrderExpressDo.getRemark());
            selfOrderExpressInfo.setOperateUserName(selfOrderExpressDo.getUpdatedUserName());
            selfOrderExpressInfo.setReceiverInfo(receiverInfo);

            selfOrderExpressInfo.setTenantCode(selfOrderExpressDo.getTenantCode());
            selfOrderExpressInfo.setSellerId(selfOrderExpressDo.getSellerId());
            selfOrderExpressInfo.setSellerName(selfOrderExpressDo.getReceiverName());
            selfOrderExpressInfo.setStationNo(selfOrderExpressDo.getStationNo());
            selfOrderExpressInfo.setPackQty(selfOrderExpressDo.getPackQty());
            selfOrderExpressInfo.setOriginExpressCode(selfOrderExpressDo.getOriginExpressCode());
        }

        selfOrderExpressInfo.setOriginalInfo(originalInfo);
        return selfOrderExpressInfo;
    }

    @Data
    public static class SelfOrderExpressInfo {
        //仓库编码
        private String repositoryCode;
        //操作人
        private Long userId;
        //操作人
        private String operateUserName;
        //物流公司code
        private String logisticsCode;
        //物流公司name
        private String logisticsName;
        //运单号
        private String expressCode;
        //子单号
        private List<String> subExpressCodes;
        //唯一码和原始单据信息
        private List<String> originalInfo;
        //自主下单号
        private String selfOrderNo;
        //自主下单场景
        private Integer scene;
        /**
         * 备注
         */
        private String remark;

        /**
         * 工位号
         */
        private String stationNo;

        /**
         * 卖家名称
         */
        private String sellerName;

        /**
         * 卖家id
         */
        private String sellerId;

        /**
         * 快递支付方式(DF：到付现结，JFYJ：寄付月结)
         */
        private String expressPayType;

        /**
         * 托寄物
         */
        private String productTitle;

        /**
         * 发货时间
         */
        private Date deliverTime;

        /**
         * 商品数量
         */
        private Integer goodsQty;

        /**
         * 包裹数量
         */
        private Integer packQty;

        /**
         * 租户
         */
        private String tenantCode;

        /**
         * 原运单号
         */
        private String originExpressCode;

        /**
         * 收件人信息
         */
        private ReceiverInfo receiverInfo;

        /**
         * 得物单号
         */
        private String dwExpressCode;


        /**
         * 关联单号
         */
        private String referenceNo;

        /**
         * 子单号  得物单号
         */
        private List<SubExpressInfo> subExpressInfos;

        @Data
        public static class SubExpressInfo {
            /**
             * 包裹面单号(子单号) 三方单号
             */
            private String subExpressCode;
            /**
             * 得物自有面单子单号
             */
            private String dwSubExpressCode;
        }

        @Data
        public static class ReceiverInfo {
            /**
             * 收件人姓名
             */
            private String receiverName;
            /**
             * 电话号
             */
            private String mobile;


            //省
            private String province;
            //市
            private String city;
            //区
            private String area;
            //镇
            private String town;
            //详细地址
            private String address;

            private String provinceCode;
            /**
             * 收件市
             */
            private String cityCode;

            /**
             * 收件区
             */
            private String areaCode;

            /**
             * 收件人乡/镇
             */
            private String townCode;

        }

    }

}
