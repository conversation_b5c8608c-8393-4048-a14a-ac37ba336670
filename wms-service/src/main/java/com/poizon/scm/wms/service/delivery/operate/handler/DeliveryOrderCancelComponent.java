package com.poizon.scm.wms.service.delivery.operate.handler;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.domain.outbound.config.DeliverySwitch;
import com.poizon.scm.wms.domain.outbound.pick.service.param.cancel.PickTaskCancelParam;
import com.poizon.scm.wms.domain.outbound.pick.service.processor.opearte.PickTaskCancel;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.task.TaskFactory;
import com.poizon.scm.wms.pojo.task.operation.modify.CancelTaskPojo;
import com.poizon.scm.wms.service.delivery.executor.DeliveryCancelConsistencyExecutor;
import com.poizon.scm.wms.util.enums.PickTaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.enums.WmsDeliveryDetailCancelEnum;
import com.poizon.scm.wms.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DeliveryOrderCancelComponent {

    @Resource
    private DeliveryDetailRepository deliveryDetailRepository;

    @Resource
    private PickTaskDetailQueryRepository pickTaskDetailQueryRepository;

    @Autowired
    private OutboundMessageProducer outboundMessageProducer;

    @Resource
    private DeliverySwitch deliverySwitch;


    /**
     * 正常拣货中的取消
     * 1. 如果当前发货单明细还未开始拣货，整个发货单明细取消
     * -- 取消库存分配
     * -- 根据发货单明细取消拣货任务
     * 2. 如果当前发货单明细开始拣货
     * -- 取消还未拣货的拣货任务明细
     * -- 取消还未拣货的拣货任务明细的库存分配
     * -- 进行中的拣货任务明细继续拣货
     *
     * @param pickTaskDetailNo
     */
    @Transactional
    public void outboundPickingCancelIfNecessary(String pickTaskDetailNo) {
        log.info("通用能力取消拣货中的出库单 {} {}", JsonUtils.serialize(pickTaskDetailNo));



        PickTaskDetailDo pickTaskDetailDo = pickTaskDetailQueryRepository.findByNo(pickTaskDetailNo);
        if (null == pickTaskDetailNo) {
            return;
        }

        boolean openCommonPickCancel = deliverySwitch.isOpenCommonPickCancel(pickTaskDetailDo.getWarehouseCode());
        if (!openCommonPickCancel) {
            log.info("当前仓不支持通用取消能力，不处理");
            return;
        }

        if (!PickTaskStatusEnum.COMPLETE.getCode().equals(pickTaskDetailDo.getStatus())) {
            log.info("当前拣货明细未完成，不生成返架任务");
            return;
        }

        String referenceDetailNo = pickTaskDetailDo.getReferenceDetailNo();

        DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDetail(referenceDetailNo);
        if (null == deliveryDetailDo) {
            log.info("当前拣货任务明细对应的发货单明细未空");
            return;
        }

        boolean detailIsCancel = WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(deliveryDetailDo.getCancelFlag());
        if (!detailIsCancel) {
            return;
        }

        // 生成返架任务
        outboundMessageProducer.sendReturnShelfMessage(deliveryDetailDo.getDeliveryOrderCode(), deliveryDetailDo.getDetailNo());
    }
}
