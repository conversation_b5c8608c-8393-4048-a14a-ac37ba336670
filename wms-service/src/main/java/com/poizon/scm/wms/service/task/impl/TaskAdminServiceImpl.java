package com.poizon.scm.wms.service.task.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.SkuRepository;
import com.poizon.scm.wms.adapter.common.TaskDetailRepository;
import com.poizon.scm.wms.adapter.common.TaskRepository;
import com.poizon.scm.wms.adapter.common.model.*;
import com.poizon.scm.wms.adapter.inbound.entry.model.EntryDetailExtensionDo;
import com.poizon.scm.wms.adapter.inbound.entry.repository.db.query.EntryDetailExtensionRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryBatchPropertiesDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryBatchPropertiesRepository;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.param.ScmTaskPageParam;
import com.poizon.scm.wms.adapter.outbound.pick.model.param.ScmTaskRequestParam;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.adapter.task.model.BasWorkZoneDo;
import com.poizon.scm.wms.adapter.task.model.ModifyAttrTaskExtensionDo;
import com.poizon.scm.wms.adapter.task.model.TaskBillReportDo;
import com.poizon.scm.wms.adapter.task.model.TaskBillReportParam;
import com.poizon.scm.wms.adapter.task.repository.BasWorkZoneRepository;
import com.poizon.scm.wms.adapter.third.taskcenter.TaskCenterRepository;
import com.poizon.scm.wms.adapter.third.taskcenter.request.TaskCenterTaskQueryRequest;
import com.poizon.scm.wms.adapter.third.taskcenter.request.TaskGroupPageQueryRequest;
import com.poizon.scm.wms.adapter.third.taskcenter.request.TaskPageQueryRequest;
import com.poizon.scm.wms.adapter.third.taskcenter.response.TaskCenterTaskGroupQueryResponse;
import com.poizon.scm.wms.adapter.third.taskcenter.response.TaskCenterTaskQueryResponse;
import com.poizon.scm.wms.adapter.upper.model.UpperResultDo;
import com.poizon.scm.wms.api.dto.request.task.TaskDetailRequest;
import com.poizon.scm.wms.api.dto.request.task.TaskPageRequest;
import com.poizon.scm.wms.api.dto.response.base.BasTypeResponse;
import com.poizon.scm.wms.api.dto.response.task.*;
import com.poizon.scm.wms.api.enums.ActivityTypeEnum;
import com.poizon.scm.wms.api.enums.DictTypeEnum;
import com.poizon.scm.wms.api.enums.TaskTypeAdminEnum;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.BeanUtil;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.common.utils.GetPropertyHelper;
import com.poizon.scm.wms.dao.entitys.task.TaskDetailEntity;
import com.poizon.scm.wms.dao.entitys.task.TaskDetailResultEntity;
import com.poizon.scm.wms.dao.entitys.task.TaskEntity;
import com.poizon.scm.wms.dao.mappers.task.TaskDetailMapper;
import com.poizon.scm.wms.domain.outbound.pick.service.PickCommandService;
import com.poizon.scm.wms.domain.outbound.pick.service.PickQueryService;
import com.poizon.scm.wms.domain.outbound.pick.service.param.forcefinish.PickForceFinishParam;
import com.poizon.scm.wms.domain.outbound.pick.service.processor.opearte.PickTaskForceFinish;
import com.poizon.scm.wms.domain.print.PrintStatisticalService;
import com.poizon.scm.wms.domain.task.TaskFactory;
import com.poizon.scm.wms.domain.upper.UpperCommandService;
import com.poizon.scm.wms.domain.upper.param.forcefinish.UpperForceFinishParam;
import com.poizon.scm.wms.infra.upper.mapper.UpperResultExtMapper;
import com.poizon.scm.wms.pojo.commodity.mdm.rsp.SkuCommonPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryAdjustDetailPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryAdjustDetailQueryPojo;
import com.poizon.scm.wms.pojo.log.OperateLogPojo;
import com.poizon.scm.wms.pojo.task.operation.TaskOperationPojo;
import com.poizon.scm.wms.pojo.task.operation.modify.ForceFinishTaskPojo;
import com.poizon.scm.wms.service.base.BasAreaService;
import com.poizon.scm.wms.service.base.BasLocationV2Service;
import com.poizon.scm.wms.service.base.BasWarehouseService;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryService;
import com.poizon.scm.wms.service.config.WmsInboundTransferConfig;
import com.poizon.scm.wms.service.inventory.adjust.InventoryAdjustService;
import com.poizon.scm.wms.service.log.OperateLogService;
import com.poizon.scm.wms.service.merchant.ScpMerchantService;
import com.poizon.scm.wms.service.modify.repository.ModifyTaskRemarkRepository;
import com.poizon.scm.wms.service.sys.SysDictService;
import com.poizon.scm.wms.service.task.TaskAdminService;
import com.poizon.scm.wms.service.task.TaskBaseService;
import com.poizon.scm.wms.service.task.TaskDetailBaseService;
import com.poizon.scm.wms.util.common.ItemCode;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.wms.task.center.enums.task.PhaseType;
import com.poizon.wms.task.center.enums.task.TaskGroupStatus;
import com.poizon.wms.task.center.enums.task.TaskStatus;
import com.poizon.wms.task.center.request.task.TaskGroupPageQueryOrderBy;
import io.netty.util.internal.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 任务后端操作Impl
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/14 11:18 上午
 */
@Slf4j
@Service(value = "taskAdminService")
public class TaskAdminServiceImpl implements TaskAdminService {

    @Autowired
    private TaskBaseService taskBaseService;

    @Autowired
    private TaskDetailBaseService taskDetailBaseService;

    @Autowired
    private TaskDetailMapper taskDetailMapper;

    @Autowired
    private BasWarehouseService basWarehouseService;


    @Autowired
    private ICommodityQueryService commodityQueryService;

    @Autowired
    private TaskFactory<TaskOperationPojo> taskFactory;

    @Autowired
    private SysDictService sysDictService;

    @Autowired
    private OperateLogService operateLogService;

    @Autowired
    private ScpMerchantService scpMerchantService;

    @Autowired
    private InventoryAdjustService inventoryAdjustService;

    @Autowired
    private TaskDetailRepository taskDetailRepository;

    @Autowired
    private SkuRepository skuRepository;
    @Autowired
    private PrintStatisticalService printStatisticalService;
    @Autowired
    private UpperCommandService upperCommandService;

    @Value(value = "${switch.taskBillReportFlag.new:true}")
    private boolean isNewTaskBillReport;


    @Resource
    private PickTaskForceFinish pickTaskForceFinish;
    @Resource
    private PickTaskDetailQueryRepository pickTaskDetailQueryRepository;

    @Resource
    private BasLocationV2Service basLocationV2Service;
    @Resource
    private BasAreaService basAreaService;
    @Resource
    private InventoryRepository inventoryRepository;
    @Resource
    private PickCommandService pickCommandService;

    @Resource
    private EntryDetailExtensionRepository entryDetailExtensionRepository;
    @Resource
    private InventoryBatchPropertiesRepository inventoryBatchPropertiesRepository;

    @Resource
    private PickQueryService pickQueryService;
    @Resource
    private BasWorkZoneRepository basWorkZoneRepository;
    @Resource
    private ScpWarehouseRepository warehouseRepository;

    @Resource
    private PickTaskQueryRepository pickTaskQueryRepository;

    @Resource
    private ShipTaskQueryRepository shipTaskQueryRepository;

    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Resource
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;

    /**
     * 任务明细导出数据源
     */
    @Value(value = "${taskAdminExportDatasourceName:readonly}")
    private String taskAdminExportDatasourceName;

    @Value("${switch.outbound.datasource:true}")
    private Boolean switchOutboundDataSource;

    @Resource
    private ModifyTaskRemarkRepository modifyTaskRemarkRepository;

    @Resource
    private TaskCenterRepository taskCenterRepository;
    @Resource
    private UpperResultExtMapper upperResultExtMapper;
    @Resource
    private WmsInboundTransferConfig wmsInboundTransferConfig;

    @Override
    public PagingObject<TaskAdminResponse> page(TaskPageRequest queryRequest) {
        /*封装返回值*/
        PagingObject pagingObject = new PagingObject();
        pagingObject.setPageNum(queryRequest.getPageNum());
        pagingObject.setPageSize(queryRequest.getPageSize());
        String taskType = queryRequest.getType();

        /*构建查询参数*/
        QueryWrapper<TaskEntity> queryWrapper = buildTaskEntityQueryWrapper(queryRequest);
        // 唯一码查询结果为空
        if (queryWrapper == null) {
            pagingObject.setContents(new ArrayList());
            return pagingObject;
        }
        Page<TaskEntity> taskEntityPage = new Page(queryRequest.getPageNum(), queryRequest.getPageSize());

        /*查询任务头*/
        taskEntityPage = taskBaseService.page(taskEntityPage, queryWrapper);
        if (null == taskEntityPage || CollectionUtils.isEmpty(taskEntityPage.getRecords())) {
            // 数量为空
            pagingObject.setContents(new ArrayList());
            return pagingObject;
        }
        List<TaskAdminResponse> taskPojoList = buildTaskAdmin(taskEntityPage.getRecords(), taskType);

        pagingObject.setTotal(taskEntityPage.getTotal());
        pagingObject.setContents(taskPojoList);

        return pagingObject;
    }


    @Override
    public PagingObject<TaskAdminResponse> rollPage(TaskPageRequest queryRequest) {
        log.info("打印入参对象:queryRequest {}",JSON.toJSONString(queryRequest));
        /*封装返回值*/
        PagingObject pagingObject = new PagingObject();
        pagingObject.setPageNum(queryRequest.getPageNum());
        pagingObject.setPageSize(queryRequest.getPageSize());
        pagingObject.setContents(new ArrayList<>());
        String taskType = queryRequest.getType();
        if(wmsInboundTransferConfig.isTransfer("TaskAdminServiceImpl.rollPage") && (TaskTypeEnum.RECEIVED.getTaskType().equals(queryRequest.getType())) ||TaskTypeEnum.UPPER.getTaskType().equals(queryRequest.getType())) {
            TaskGroupPageQueryRequest pageQueryRequest = buildTaskQueryParam(queryRequest);
            if (Objects.isNull(pageQueryRequest)) {
                return pagingObject;
            }
            List<TaskCenterTaskGroupQueryResponse> taskCenterTaskGroupQueryResponses = taskCenterRepository.taskGroupPageQueryInAll(pageQueryRequest);
            if (CollectionUtils.isEmpty(taskCenterTaskGroupQueryResponses)) {
                return pagingObject;
            }
            if (queryRequest.getFirstId() != null) {
                Collections.reverse(taskCenterTaskGroupQueryResponses);
            }
            List<TaskAdminResponse> taskPojoList = buildGroupTaskAdmin(taskCenterTaskGroupQueryResponses, taskType);
//        pagingObject.setTotal(taskEntityPage.getTotal());
            pagingObject.setContents(taskPojoList);
            return pagingObject;
        }

        // 处理 ship/pick 类型走 XML 查询
        if ((TaskTypeEnum.SHIP.getTaskType().equals(taskType)
                || TaskTypeEnum.PICK.getTaskType().equals(taskType)) && switchOutboundDataSource) {
            //单组走各表的xml查询
            pagingObject = getPagingObjectNew(queryRequest, pagingObject, taskType);
        } else {
            //走sharding分表查询
            pagingObject = getPagingObject(queryRequest, pagingObject, taskType);
        }

        return pagingObject;
    }

    private PagingObject getPagingObjectNew(TaskPageRequest queryRequest, PagingObject pagingObject, String taskType) {
        int offset = (queryRequest.getPageNum() - 1) * queryRequest.getPageSize();
        ScmTaskPageParam param = new ScmTaskPageParam();

        BeanUtil.copyProperties(queryRequest,param);
        param.setTenantCode(OperationUserContextHolder.getTenantCode());

        List<String> barCodeList = null;
        if (StringUtils.isNotBlank(queryRequest.getBarcode())) {
            ItemCode itemCode = skuRepository.parse(queryRequest.getBarcode());
            //唯一码不算在barcode里面
            if (null != itemCode && !itemCode.isUniqueCode()) {
                barCodeList = itemCode.getCode();
            }
        }

        if (CollectionUtils.isNotEmpty(barCodeList) || StringUtils.isNotBlank(queryRequest.getUniqueCode()) || StringUtils.isNotBlank(queryRequest.getReferenceNo())) {
            if (TaskTypeEnum.SHIP.getTaskType().equals(taskType)) {
                //查询相关的拣货明细
                List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository
                        .queryForScmTaskDetail(new ScmTaskRequestParam(queryRequest.getWareHouse(), barCodeList, queryRequest.getUniqueCode(), OperationUserContextHolder.getTenantCode(), queryRequest.getReferenceNo()));
                if (CollectionUtils.isEmpty(shipTaskDetailDos)) {
                    throw new WmsOperationException("未查询到信息！");
                }
                param.setTaskNoList(shipTaskDetailDos.stream().map(x->x.getTaskNo()).collect(Collectors.toList()));
            } else if (TaskTypeEnum.PICK.getTaskType().equals(taskType)) {
                //查询相关的拣货明细
                List<PickTaskDetailDo> pickTaskDetailDos = pickTaskDetailQueryRepository
                        .queryForScmTaskDetail(new ScmTaskRequestParam(queryRequest.getWareHouse(), barCodeList, queryRequest.getUniqueCode(), OperationUserContextHolder.getTenantCode(), queryRequest.getReferenceNo()));
                if (CollectionUtils.isEmpty(pickTaskDetailDos)) {
                    throw new WmsOperationException("未查询到信息！");
                }
                param.setTaskNoList(pickTaskDetailDos.stream().map(x->x.getTaskNo()).collect(Collectors.toList()));
            }
        }


        List<TaskEntity> taskEntities = null;
        // 处理 ship/pick 类型走 XML 查询
        if (TaskTypeEnum.SHIP.getTaskType().equals(taskType)) {

            List<ShipTaskDo> shipTaskDoList = shipTaskQueryRepository.queryListForScmTask(
                    param,
                    offset,
                    queryRequest.getPageSize());
            taskEntities = BeanUtil.copyListProperties(shipTaskDoList, TaskEntity.class);
        } else if (TaskTypeEnum.PICK.getTaskType().equals(taskType)) {
            List<PickTaskDo> shipTaskDoList = pickTaskQueryRepository.queryListForScmTask(
                    param,
                    offset,
                    queryRequest.getPageSize());
            taskEntities = BeanUtil.copyListProperties(shipTaskDoList, TaskEntity.class);
        }

        if (CollectionUtils.isEmpty(taskEntities)) {
            return pagingObject;
        }

        // 处理滚动分页的特殊排序
        if (queryRequest.getFirstId() != null && !taskEntities.isEmpty()) {
            Collections.reverse(taskEntities); // 保证正向滚动时数据顺序正确
        }

        List<TaskAdminResponse> responses = buildTaskAdmin(taskEntities, taskType);
        pagingObject.setContents(responses);
        pagingObject.setTotal(responses.size());
        return pagingObject;
    }


    private PagingObject getPagingObject(TaskPageRequest queryRequest, PagingObject pagingObject, String taskType) {
        /*构建查询参数*/
        QueryWrapper<TaskEntity> queryWrapper = buildTaskEntityQueryWrapper(queryRequest);

        // 唯一码查询结果为空
        if (queryWrapper == null) {
            return pagingObject;
        }

        if (queryRequest.getLastId() != null) {
            queryWrapper.lt(TaskEntity.COL_ID, queryRequest.getLastId())
                    .orderByDesc(TaskEntity.COL_ID);
        } else if (queryRequest.getFirstId() != null) {
            queryWrapper.gt(TaskEntity.COL_ID, queryRequest.getFirstId())
                    .orderByAsc(TaskEntity.COL_ID);
        } else {
            queryWrapper.orderByDesc(TaskEntity.COL_ID);
        }
        queryWrapper.last("limit " + queryRequest.getPageSize());

        // 唯一码查询结果为空
//        Page<TaskEntity> taskEntityPage = new Page(queryRequest.getPageNum(), queryRequest.getPageSize());
        log.info("打印入参对象:queryWrapper {}",JSON.toJSONString(queryWrapper));
        /*查询任务头*/
        List<TaskEntity> list = taskBaseService.list(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            // 数量为空
            return pagingObject;
        }
        if (queryRequest.getFirstId() != null) {
            Collections.reverse(list);
        }

        List<TaskAdminResponse> taskPojoList = buildTaskAdmin(list, taskType);

//        pagingObject.setTotal(taskEntityPage.getTotal());
        pagingObject.setContents(taskPojoList);
        return pagingObject;
    }


    private List<TaskAdminResponse> buildGroupTaskAdmin(List<TaskCenterTaskGroupQueryResponse> taskEntityList, String taskType){
        /*查询仓库名称*/
        Set<String> warehouseCodeSet = taskEntityList.stream()
                .map(TaskCenterTaskGroupQueryResponse::getWarehouseCode).collect(Collectors.toSet());
        Map<String, String> warehouseNameMap = new HashMap<>(warehouseCodeSet.size());
        List<BasTypeResponse<String>> basTypeResponseList = basWarehouseService.warehouseName(warehouseCodeSet);
        if (CollectionUtils.isNotEmpty(basTypeResponseList)) {
            warehouseNameMap = basTypeResponseList.stream().collect(Collectors.toMap(BasTypeResponse::getTypeCode, BasTypeResponse::getTypeDesc));
        }

        /*查询任务操作数*/
        Set<String> taskNoSet = taskEntityList.stream()
                // 过滤非完成的任务
                .filter(entity -> !TaskGroupStatus.FINISH.name().equals(entity.getGroupStatus()))
                .map(TaskCenterTaskGroupQueryResponse::getGroupNo).collect(Collectors.toSet());

        Map<String, Long> printStatisticalMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(taskNoSet)) {
            appendPrintStatisticalMap(taskType, taskNoSet, printStatisticalMap);
        }
        List<TaskAdminResponse> taskPojoList = new ArrayList<>(taskEntityList.size());
        for (TaskCenterTaskGroupQueryResponse entity : taskEntityList) {
            /*构建返回对象*/
            TaskAdminResponse taskAdmin = new TaskAdminResponse();
            taskAdmin.setId(entity.getId());
            String wareHouseName = warehouseNameMap.get(entity.getWarehouseCode());
            taskAdmin.setWareHouseName(StringUtils.isNotEmpty(wareHouseName) ? wareHouseName : "");
            taskAdmin.setTaskNo(entity.getGroupNo());
            taskAdmin.setReferenceNo(entity.getWorkBillNo());
            taskAdmin.setRemark("");
            TaskTypeEnum taskTypeEnum = TaskTypeEnum.convertType(entity.getPhaseType());
            if (null == taskTypeEnum) {
                log.error("任务报表查询失败,entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.TASK_QUERY_FAIL);
            }
            taskAdmin.setType(taskTypeEnum.getTaskType());
            taskAdmin.setTypeName(taskTypeEnum.getTaskName());

            TaskStatusEnum taskStatusEnum = TaskStatusEnum.convertGroupStatus(entity.getGroupStatus());
            if (null == taskStatusEnum) {
                log.error("任务报表查询失败,entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.TASK_QUERY_FAIL);
            }
            taskAdmin.setStatus(taskStatusEnum.getStatus());
            taskAdmin.setStatusName(taskStatusEnum.getStatusName());
            taskAdmin.setPlanQty(entity.getPlanQty());
            taskAdmin.setOperationQty(entity.getOperatedQty());
            if (entity.getCreateTime() != null) {
                taskAdmin.setCreateTime(DateUtils.formatTimeForAdmin(entity.getCreateTime()));
            }
            if (entity.getEndTime() != null) {
                taskAdmin.setFinishTime(DateUtils.formatTimeForAdmin(entity.getEndTime()));
            }
            taskAdmin.setPrintCounts(printStatisticalMap.getOrDefault(entity.getGroupNo(), 0L));
            taskPojoList.add(taskAdmin);
        }
        return taskPojoList;
    }

    /**
     * 构建任务返回对象
     *
     * @param taskEntityList
     * @return
     */
    private List<TaskAdminResponse> buildTaskAdmin(List<TaskEntity> taskEntityList, String taskType) {
        /*查询仓库名称*/
        Set<String> warehouseCodeSet = taskEntityList.stream()
                .map(TaskEntity::getWarehouseCode).collect(Collectors.toSet());
        Map<String, String> warehouseNameMap = new HashMap<>(warehouseCodeSet.size());
        List<BasTypeResponse<String>> basTypeResponseList = basWarehouseService.warehouseName(warehouseCodeSet);
        if (CollectionUtils.isNotEmpty(basTypeResponseList)) {
            warehouseNameMap = basTypeResponseList.stream().collect(Collectors.toMap(BasTypeResponse::getTypeCode, BasTypeResponse::getTypeDesc));
        }

        /*查询任务操作数*/
        Set<String> taskNoSet = taskEntityList.stream()
                // 过滤非完成的任务
                .filter(entity -> !TaskStatusEnum.COMPLETE.getStatus().equals(entity.getStatus()))
                .map(TaskEntity::getTaskNo).collect(Collectors.toSet());

        /*统计任务操作数量*/
        Map<String, TaskDetailEntity> taskOperationCountMap = Maps.newHashMap();
        Map<String, Long> printStatisticalMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(taskNoSet)) {
            // 分批查询数量
            List<List<String>> splitList = Lists.partition(new ArrayList<>(taskNoSet), QUERY_BATCH_SIZE);
            for (List<String> entity : splitList) {
                appendOperationCountMap(taskType, taskOperationCountMap, entity);
                appendPrintStatisticalMap(taskType, taskNoSet, printStatisticalMap);
            }
        }
        Map<String, String> taskNoRemarkMap = new HashMap<>(taskEntityList.size());
        if (taskType.equals(TaskTypeAdminEnum.MODIFY_ATTR.getTaskType())) {
            log.info("此次查询任务类型是属性转换任务，因此需要再去查备注信息!");
            List<String> taskNos = taskEntityList.stream().map(TaskEntity::getTaskNo).collect(Collectors.toList());
            List<ModifyAttrTaskExtensionDo> remarks = modifyTaskRemarkRepository.selectRemarksByTaskNos(taskNos);
            if (CollectionUtils.isNotEmpty(remarks)) {
                taskNoRemarkMap = remarks.stream().collect(Collectors.toMap(ModifyAttrTaskExtensionDo::getTaskNo, ModifyAttrTaskExtensionDo::getRemark));
            }
        }

        List<TaskAdminResponse> taskPojoList = new ArrayList<>(taskEntityList.size());
        for (TaskEntity entity : taskEntityList) {
            /*构建返回对象*/
            TaskAdminResponse taskAdmin = buildTaskAdminResponse(entity, warehouseNameMap, taskOperationCountMap, printStatisticalMap, taskNoRemarkMap);
            taskPojoList.add(taskAdmin);
        }
        return taskPojoList;
    }

    private void appendPrintStatisticalMap(String taskType, Set<String> taskNoSet, Map<String, Long> printStatisticalMap) {
        printStatisticalMap.putAll(printStatisticalService.queryMapByReferences(Lists.newArrayList(taskNoSet), taskType));
    }

    private void appendOperationCountMap(String taskType, Map<String, TaskDetailEntity> taskOperationCountMap, List<String> entity) {
        List<TaskDetailEntity> taskDetailEntityList = this.taskDetailMapper.countOperationQtyByTaskNos(entity, OperationUserContextHolder.getTenantCode(), null, taskType);
        taskOperationCountMap.putAll(taskDetailEntityList.stream()
                .collect(Collectors.toMap(TaskDetailEntity::getTaskNo, Function.identity())));
    }

    /**
     * 查询批次大小
     */
    private static final Integer QUERY_BATCH_SIZE = 1000;



    /**
     * 构建查询参数
     *
     * @param queryRequest
     * @return
     */
    private TaskGroupPageQueryRequest buildTaskQueryParam(TaskPageRequest queryRequest) {

        TaskGroupPageQueryRequest pageQueryRequest = new TaskGroupPageQueryRequest();

        pageQueryRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        pageQueryRequest.setPageNum(queryRequest.getPageNum());
        pageQueryRequest.setPageSize(queryRequest.getPageSize());
        if (CollectionUtils.isNotEmpty(queryRequest.getWareHouse())) {
            List<String> warehouse = queryRequest.getWareHouse().stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(warehouse)) {
                pageQueryRequest.setWarehouseCodeList(warehouse);
            }
        }
        if (StringUtils.isNotEmpty(queryRequest.getTaskNo())) {
            pageQueryRequest.setGroupNo(queryRequest.getTaskNo());
        }
        if (null != queryRequest.getStatus()) {
            if (null == TaskStatusEnum.getTaskStatus(queryRequest.getStatus())) {
                log.error("任务状态不存在, queryRequest -> [{}]", JSON.toJSONString(queryRequest));
                throw new WmsException(WmsExceptionCode.TASK_STATUS_ILLEGAL);
            }
            TaskStatusEnum taskStatus = TaskStatusEnum.getTaskStatus(queryRequest.getStatus());
            pageQueryRequest.setGroupStatus(TaskStatusEnum.convertGroupTaskStatus(taskStatus));
        }
        if (StringUtils.isBlank(queryRequest.getType())) {
            throw new WmsOperationException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        } else if (null == TaskTypeEnum.getTaskType(queryRequest.getType())) {
            log.error("任务类型不存在, queryRequest -> [{}]", JSON.toJSONString(queryRequest));
            throw new WmsException(WmsExceptionCode.PARAMS_ERROR);
        }
        TaskTypeEnum taskType = TaskTypeEnum.getTaskType(queryRequest.getType());
        pageQueryRequest.setPhaseType(TaskTypeEnum.convertPhaseType(taskType));
        if(Objects.equals(pageQueryRequest.getPhaseType(), PhaseType.RECEIVED.getPhaseCode())){
            pageQueryRequest.setTag("received");
        }
        if (StringUtils.isNotEmpty(queryRequest.getReferenceNo())) {
            pageQueryRequest.setWorkBillNo(queryRequest.getReferenceNo());
        }
        if (null != queryRequest.getCreateStartTime()) {
            pageQueryRequest.setCreateTimeLeft(queryRequest.getCreateStartTime());
        }
        if (null != queryRequest.getCreateEndTime()) {
            pageQueryRequest.setCreateTimeRight(queryRequest.getCreateEndTime());
        }
        if (null != queryRequest.getFinishStartTime()) {
            pageQueryRequest.setEndTimeLeft(queryRequest.getFinishStartTime());
        }
        if (null != queryRequest.getFinishEndTime()) {
            pageQueryRequest.setEndTimeRight(queryRequest.getFinishEndTime());

            if(StringUtils.isBlank(pageQueryRequest.getGroupStatus())){
                pageQueryRequest.setGroupStatusList(Lists.newArrayList(TaskGroupStatus.FINISH.name(), TaskGroupStatus.ABNORMAL_FINISH.name()));
            } else if (!TaskGroupStatus.FINISH.name().equals(pageQueryRequest.getGroupStatus())
                        && !TaskGroupStatus.ABNORMAL_FINISH.name().equals(pageQueryRequest.getGroupStatus())){
                    return null;
            }
        }
        // 上一页，下一页
        if(Objects.nonNull(queryRequest.getLastId())){
            pageQueryRequest.setMaxId(queryRequest.getLastId());
            pageQueryRequest.setOrderBy(TaskGroupPageQueryOrderBy.ID_DESC.getOrderBy());
        }else if(Objects.nonNull(queryRequest.getFirstId())){
            pageQueryRequest.setMinId(queryRequest.getFirstId());
            pageQueryRequest.setOrderBy(TaskGroupPageQueryOrderBy.ID.getOrderBy());
        }else {
            pageQueryRequest.setOrderBy(TaskGroupPageQueryOrderBy.ID_DESC.getOrderBy());
        }

        // 商品编码和barcode查询
        if(StringUtils.isNotBlank(queryRequest.getBarcode()) || StringUtils.isNotEmpty(queryRequest.getUniqueCode())){
            TaskCenterTaskQueryRequest taskQueryRequest = new TaskCenterTaskQueryRequest();
            if (StringUtils.isNotBlank(queryRequest.getBarcode())) {
                ItemCode itemCode = skuRepository.parse(queryRequest.getBarcode());
                if (itemCode.isUniqueCode()) {
                    return null;
                }
                if (CollectionUtils.isEmpty(itemCode.getCode())) {
                    return null;
                }
                taskQueryRequest.setSkuIdList(itemCode.getSkuIds());
            }
            taskQueryRequest.setUniqueCode(queryRequest.getUniqueCode());

            taskQueryRequest.setPhaseType(pageQueryRequest.getPhaseType());
            taskQueryRequest.setTenantCode(pageQueryRequest.getTenantCode());
            taskQueryRequest.setTag(pageQueryRequest.getTag());
            taskQueryRequest.setWarehouseCodeList(pageQueryRequest.getWarehouseCodeList());
            if(StringUtils.isEmpty(queryRequest.getUniqueCode())){
                taskQueryRequest.setTaskNoList(Lists.newArrayList(TaskStatus.NOT_OPERATE.name(), TaskStatus.PART_OPERATE.name()));
            }
            List<TaskCenterTaskQueryResponse> taskCenterTaskQueryResponses = taskCenterRepository.taskListInAll(taskQueryRequest);
            if(CollectionUtils.isEmpty(taskCenterTaskQueryResponses)){
                return null;
            }
            Set<String> groupNoList = taskCenterTaskQueryResponses.stream().map(TaskCenterTaskQueryResponse::getGroupNo).collect(Collectors.toSet());

            if(StringUtils.isNotBlank(queryRequest.getTaskNo())){
                if(!groupNoList.contains(queryRequest.getTaskNo())){
                    return null;
                }
            } else {
                pageQueryRequest.setGroupNoList(Lists.newArrayList(groupNoList));
            }

        }
        return pageQueryRequest;
    }

    /**
     * 构建查询参数
     *
     * @param queryRequest
     * @return
     */
    private QueryWrapper<TaskEntity> buildTaskEntityQueryWrapper(TaskPageRequest queryRequest) {
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>();
        TaskEntity taskEntityParam = new TaskEntity();
        taskEntityParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskEntityParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isNotEmpty(queryRequest.getWareHouse())) {
            List<String> warehouse = queryRequest.getWareHouse().stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(warehouse)) {
                queryWrapper.in(TaskEntity.COL_WAREHOUSE_CODE, warehouse);
            }
        }
        if (StringUtils.isNotEmpty(queryRequest.getTaskNo())) {
            taskEntityParam.setTaskNo(queryRequest.getTaskNo());
        }
        if (null != queryRequest.getStatus()) {
            if (null == TaskStatusEnum.getTaskStatus(queryRequest.getStatus())) {
                log.error("任务状态不存在, queryRequest -> [{}]", JSON.toJSONString(queryRequest));
                throw new WmsException(WmsExceptionCode.TASK_STATUS_ILLEGAL);
            }
            taskEntityParam.setStatus(queryRequest.getStatus());
        }
        if (StringUtils.isBlank(queryRequest.getType())) {
            throw new WmsOperationException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        } else if (null == TaskTypeEnum.getTaskType(queryRequest.getType())) {
            log.error("任务类型不存在, queryRequest -> [{}]", JSON.toJSONString(queryRequest));
            throw new WmsException(WmsExceptionCode.PARAMS_ERROR);
        }
        taskEntityParam.setType(queryRequest.getType());
        if (StringUtils.isNotEmpty(queryRequest.getReferenceNo())) {
            taskEntityParam.setReferenceNo(queryRequest.getReferenceNo());
        }
        if (null != queryRequest.getCreateStartTime()) {
            queryWrapper.ge(TaskEntity.COL_CREATED_TIME, queryRequest.getCreateStartTime());
        }
        if (null != queryRequest.getCreateEndTime()) {
            queryWrapper.le(TaskEntity.COL_CREATED_TIME, queryRequest.getCreateEndTime());
        }
        if (null != queryRequest.getFinishStartTime()) {
            queryWrapper.ge(TaskEntity.COL_END_TIME, queryRequest.getFinishStartTime());
        }
        if (null != queryRequest.getFinishEndTime()) {
            queryWrapper.le(TaskEntity.COL_END_TIME, queryRequest.getFinishEndTime());
        }
        /*上面已经判断type不能为空了，所以这段逻辑过时了*/
//        if (null == taskEntityParam.getType()) {
//            // 默认的任务类型为 上架、拣货、移位、属性调整
//            queryWrapper.in(TaskEntity.COL_TYPE,
//                    TaskTypeEnum.UPPER.getTaskType(),
//                    TaskTypeEnum.PICK.getTaskType(),
//                    TaskTypeEnum.MOVE.getTaskType(),
//                    TaskTypeEnum.MODIFY_ATTR.getTaskType(),
//                    TaskTypeEnum.SHIP.getTaskType(),
//                    TaskTypeEnum.RETURN_SHELF.getTaskType());
//        }
        if (queryRequest.getFirstId() == null || queryRequest.getFirstId() == 0L) {
            queryWrapper.orderByDesc(TaskEntity.COL_CREATED_TIME);
        }
        if (StringUtils.isNotBlank(queryRequest.getBarcode())) {
            ItemCode itemCode = skuRepository.parse(queryRequest.getBarcode());
            if (itemCode.isUniqueCode()) {
                return null;
            }
            if (CollectionUtils.isEmpty(itemCode.getCode())) {
                return null;
            }
            List<TaskDetailDo> detailDos = taskDetailRepository.findUnCompleteByItemCode(itemCode, null, queryRequest.getType());
            if (CollectionUtils.isEmpty(detailDos)) {
                return null;
            } else {
                Set<String> taskNoSet = detailDos.stream().map(TaskDetailDo::getTaskNo).collect(Collectors.toSet());
                queryWrapper.in(TaskEntity.COL_TASK_NO, taskNoSet);
            }
        }
        /*唯一码不为空，先查询任务明细*/
        if (StringUtils.isNotEmpty(queryRequest.getUniqueCode())) {
            TaskDetailEntity taskDetailEntityParam = new TaskDetailEntity();
            taskDetailEntityParam.setDeleted(NumberUtils.INTEGER_ZERO);
            taskDetailEntityParam.setUniqueCode(queryRequest.getUniqueCode());
            taskDetailEntityParam.setTaskType(queryRequest.getType());
            taskDetailEntityParam.setTenantCode(OperationUserContextHolder.getTenantCode());
            QueryWrapper detailWrapper = new QueryWrapper<>(taskDetailEntityParam);
            detailWrapper.select(TaskDetailEntity.COL_TASK_NO);
            List<TaskDetailEntity> taskDetailEntityList = this.taskDetailBaseService.list(detailWrapper);
            if (CollectionUtils.isEmpty(taskDetailEntityList)) {
                return null;
            } else {
                Set<String> taskNoSet = taskDetailEntityList.stream()
                        // 这里可能需要根据条件过滤
                        .map(TaskDetailEntity::getTaskNo)
                        .collect(Collectors.toSet());
                queryWrapper.in(TaskEntity.COL_TASK_NO, taskNoSet);
            }
        }

        queryWrapper.setEntity(taskEntityParam);
        return queryWrapper;
    }

    /**
     * 构建返回对象
     *
     * @param entity
     * @param warehouseNameMap
     * @param taskOperationCountMap
     * @param printStatisticalMap
     * @return
     */
    private TaskAdminResponse buildTaskAdminResponse(TaskEntity entity,
                                                     Map<String, String> warehouseNameMap,
                                                     Map<String, TaskDetailEntity> taskOperationCountMap,
                                                     Map<String, Long> printStatisticalMap,
                                                     Map<String, String> taskNoRemarkMap) {
        TaskAdminResponse taskAdmin = new TaskAdminResponse();
        taskAdmin.setId(entity.getId());
        String wareHouseName = warehouseNameMap.get(entity.getWarehouseCode());
        taskAdmin.setWareHouseName(StringUtils.isNotEmpty(wareHouseName) ? wareHouseName : "");
        taskAdmin.setTaskNo(entity.getTaskNo());
        taskAdmin.setReferenceNo(entity.getReferenceNo());
        taskAdmin.setType(entity.getType());
        taskAdmin.setRemark(taskNoRemarkMap.getOrDefault(entity.getTaskNo(), ""));
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getTaskType(entity.getType());
        if (null == taskTypeEnum) {
            log.error("任务报表查询失败,entity -> [{}]", JSON.toJSONString(entity));
            throw new WmsException(WmsExceptionCode.TASK_QUERY_FAIL);
        }
        taskAdmin.setTypeName(taskTypeEnum.getTaskName());
        taskAdmin.setStatus(entity.getStatus());
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.getTaskStatus(entity.getStatus());
        if (null == taskStatusEnum) {
            log.error("任务报表查询失败,entity -> [{}]", JSON.toJSONString(entity));
            throw new WmsException(WmsExceptionCode.TASK_QUERY_FAIL);
        }
        taskAdmin.setStatusName(taskStatusEnum.getStatusName());
        taskAdmin.setPlanQty(entity.getTotalQty());
        // 设置任务操作数量
        if (TaskStatusEnum.COMPLETE.getStatus().equals(entity.getStatus())) {
            taskAdmin.setOperationQty(entity.getTotalQty());
        } else {
            TaskDetailEntity taskDetailEntity = taskOperationCountMap.get(entity.getTaskNo());
            taskAdmin.setOperationQty(null != taskDetailEntity ? taskDetailEntity.getOperationQty() : 0);
        }
        if (entity.getCreatedTime() != null) {
            taskAdmin.setCreateTime(DateUtils.formatTimeForAdmin(entity.getCreatedTime()));
        }
        if (entity.getEndTime() != null) {
            taskAdmin.setFinishTime(DateUtils.formatTimeForAdmin(entity.getEndTime()));
        }
        taskAdmin.setPrintCounts(printStatisticalMap.getOrDefault(entity.getTaskNo(), 0L));
        return taskAdmin;
    }

    @Override
    public TaskDetailAdminResponse queryAllTaskDetailObj(TaskDetailRequest detailRequest){

        //参数异常抛出
        if (StringUtils.isBlank(detailRequest.getType()) || Objects.isNull(TaskTypeEnum.getTaskType(detailRequest.getType())) ) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }

        //前置的对象头
        TaskDetailAdminResponse adminResponse = new TaskDetailAdminResponse();
        TaskTypeEnum taskTypeEnum = getAndCheckAdminTaskType(detailRequest.getType(), "任务明细不可查询");
        PagingObject<CommodityDetail> pagingObject = new PagingObject();
        adminResponse.setHeader(getDetailHeader(taskTypeEnum));

        //内容条件参数
        if (StringUtils.isEmpty(detailRequest.getTaskNo())) {
            log.error("任务编号不能为空,detailRequest -> [{}]", JSON.toJSONString(detailRequest));
            adminResponse.setData(pagingObject);
            return adminResponse;
        }

        //查询任务明细
        TaskDetailDo taskDetailDoParam = new TaskDetailDo();
        taskDetailDoParam.setTaskNo(detailRequest.getTaskNo());
        taskDetailDoParam.setTaskType(detailRequest.getType());
        log.info("查询任务明细 入参明细:{}",JSON.toJSONString(taskDetailDoParam));
        Integer itemCount = this.taskDetailRepository.countPageTaskDetailAndResult(taskDetailDoParam);
        if (Objects.isNull(itemCount) || itemCount <= 0) {
            adminResponse.setData(pagingObject);
            return adminResponse;
        }

        //收集记录所有明细
        List<TaskDetailAndResultDo> allPageList = Lists.newArrayList();
        Map<String, String> areaMap = new HashMap<>(itemCount);
        Map<String, String> workZoneMap = new HashMap<>(itemCount);
        String warehouseName = "";
        String orderTags = "";
        String batchNo = "";

        //循环查询所有数据;(已和产品确认明细数据量不大)
        Integer  pageNum  = 1;
        Integer  pageSize = 100;
        while (true) {
            //查询对象的参数:{"taskNo":"ASN2401224145118745","taskType":"received","tenantCode":"1"}
            Integer pageStart = (pageNum-1) * pageSize;
            log.info("查询对象的参数:{},{},{},{}",JSON.toJSONString(taskDetailDoParam),pageNum,pageSize,pageStart);
            List<TaskDetailAndResultDo> resList = this.taskDetailRepository.pageTaskDetailAndResult(taskDetailDoParam,pageStart,pageSize);
            if (CollectionUtils.isEmpty(resList)){
                break;
            }
            allPageList.addAll(resList);
            if(resList.size() < pageSize) {
                break;
            }
            if (allPageList.size() >= 500) {
                log.info("明细总页数大于500条，不再继续往下查,任务单号为:{}",taskDetailDoParam.getTaskNo());
                break;
            }
            pageNum++;
        }

        log.info("打印出查询的到的记录:{}",JSON.toJSONString(allPageList));

        if (TaskTypeEnum.PICK.equals(taskTypeEnum)) {
            String warehouseCode = allPageList.get(0).getWarehouseCode();
            //仓库名称
            warehouseName = getWarehouseName(warehouseCode);
            //查询库区map
            Set<String> locationCodeSet = allPageList.stream().map(TaskDetailDo::getLocationCode).collect(Collectors.toSet());
            areaMap = getAreaMap(warehouseCode, locationCodeSet);
            //作业库区map
            Set<String> workZoneCodes = allPageList.stream().map(TaskDetailAndResultDo::getWorkZoneCode).collect(Collectors.toSet());
            workZoneMap = getWorkZoneMap(warehouseCode, workZoneCodes);
            //订单标记
            PickTaskDo pickTask = getPickTask(detailRequest.getTaskNo());
            orderTags = DeliveryTagV2Enum.getDescByBitVal(pickTask.getOrderTags());
            //批次单号
            batchNo = StringUtils.isBlank(pickTask.getReferenceNo()) ? StringUtils.EMPTY : pickTask.getReferenceNo();
        }

        //获取商品信息
        Map<String, SkuCommonPojo> commodityDetailPojoHashMap = new HashMap<>();
        Set<String> skuIdSet = allPageList.stream().map(TaskDetailAndResultDo::getSkuId).distinct().collect(Collectors.toSet());
        TaskEntity taskEntity = checkTaskIsExist(detailRequest.getTaskNo(), detailRequest.getType(), JSON.toJSONString(detailRequest));
        List<SkuCommonPojo> skuList = commodityQueryService.querySkuCommonInfoListBySkuIds(taskEntity.getTenantCode(), skuIdSet);
        if (CollectionUtils.isEmpty(skuList) || skuList.size() != skuIdSet.size()) {
            log.error("获取商品信息失败,异常分别为:[skuIdSet:{}, commodityBasicPojoList:{}]", JSON.toJSONString(skuIdSet), JSON.toJSONString(skuList));
        } else {
            commodityDetailPojoHashMap = skuList.stream().collect(Collectors.toMap(SkuCommonPojo::getSkuId, Function.identity()));
        }

        //查询唯一码
        String activityType = "";
        Map<String, Barcode> barcodeMap = skuRepository.parseSkuIdList(new ArrayList<>(skuIdSet));
        if (allPageList.get(0).getBizType().equals(WmsBizTypeEnum.GE_REN_JI_CUN.getBizType())) {
            InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(allPageList.get(0).getUniqueCode());
            if (inventoryDo != null && StringUtils.isNotBlank(inventoryDo.getBatchNo())) {
                List<InventoryBatchPropertiesDo> inventoryBatchPropertiesDos = inventoryBatchPropertiesRepository.listByBatchNo(Arrays.asList(inventoryDo.getBatchNo()));
                if (CollectionUtils.isNotEmpty(inventoryBatchPropertiesDos)) {
                    activityType = inventoryBatchPropertiesDos.get(0).getActivityType();
                }
            }
            if (StringUtils.isBlank(activityType)) {
                EntryDetailExtensionDo entryDetailExtensionDo = entryDetailExtensionRepository.selectExtensionByEntryDetailNo(
                        allPageList.get(0).getReferenceDetailNo(),
                        allPageList.get(0).getReferenceNo());
                if (entryDetailExtensionDo != null) {
                    activityType = entryDetailExtensionDo.getActivityType();
                }
            }
            if (StringUtils.isBlank(activityType) && StringUtils.isNotBlank(allPageList.get(0).getOriginalOrderCode())
                    && StringUtils.isNotBlank(allPageList.get(0).getSkuId())) {
                InventoryBatchPropertiesDo inventoryBatchPropertiesDo = new InventoryBatchPropertiesDo();
                inventoryBatchPropertiesDo.setOriginalOrderCode(allPageList.get(0).getOriginalOrderCode());
                inventoryBatchPropertiesDo.setSkuId(allPageList.get(0).getSkuId());
                List<InventoryBatchPropertiesDo> list = inventoryBatchPropertiesRepository.list(inventoryBatchPropertiesDo);
                if (CollectionUtils.isNotEmpty(list)) {
                    activityType = list.get(0).getActivityType();
                }
            }
        }

        //异常记录打印日志后跳过;
        List<CommodityDetail> commodityDetails = new ArrayList<>(allPageList.size());
        for (TaskDetailAndResultDo entity : allPageList) {
            //获取商品信息
            SkuCommonPojo sku = commodityDetailPojoHashMap.get(entity.getSkuId());
            if (Objects.isNull(sku)) {
                log.error("商品不存在,[entity:{}],commodityDetailPojoHashMap:[{}]", JSON.toJSONString(entity), JSON.toJSONString(commodityDetailPojoHashMap));
                continue;
            }

            TaskDetailEntity taskDetailEntity = new TaskDetailEntity();
            org.springframework.beans.BeanUtils.copyProperties(entity, taskDetailEntity);
            String uniqueCodeAlias = entity.getUniqueCodeAlias();
            String invBatchNo = entity.getInvBatchNo();
            String workZone = "";
            if (TaskTypeEnum.PICK.equals(taskTypeEnum) && StringUtils.isNotBlank(entity.getWorkZoneCode())){
                workZone = Optional.ofNullable(workZoneMap.get(entity.getWorkZoneCode())).orElse(StringUtils.EMPTY);
            }

            List<TaskDetailResultDo> taskDetailResultDoList = entity.getTaskDetailResultDoList();
            if (CollectionUtils.isNotEmpty(taskDetailResultDoList)) {
                for (TaskDetailResultDo taskDetailResultDo : taskDetailResultDoList) {
                    TaskDetailResultEntity resultEntity = new TaskDetailResultEntity();
                    org.springframework.beans.BeanUtils.copyProperties(taskDetailResultDo, resultEntity);
                    commodityDetails.add(buildDifferentTaskTypeDetail(taskTypeEnum, taskDetailEntity, resultEntity,
                            sku, barcodeMap,areaMap,warehouseName,orderTags, workZone,activityType, uniqueCodeAlias,batchNo,invBatchNo));
                }
            } else {
                commodityDetails.add(buildDifferentTaskTypeDetail(taskTypeEnum, taskDetailEntity,
                        null, sku, barcodeMap,areaMap,warehouseName,orderTags, workZone, activityType,
                        uniqueCodeAlias,batchNo,invBatchNo));
            }
        }

        //返回查询结果
        pagingObject.setContents(commodityDetails);
        adminResponse.setData(pagingObject);
        return adminResponse;
    }

    @Override
    public TaskDetailAdminResponse queryAdminTaskDetail(TaskDetailRequest detailRequest) {
        TaskDetailAdminResponse adminResponse = new TaskDetailAdminResponse();
        if (StringUtils.isEmpty(detailRequest.getTaskNo())) {
            log.error("任务编号不能为空,detailRequest -> [{}]", JSON.toJSONString(detailRequest));
            throw new WmsException(WmsExceptionCode.TASK_NO_NULL);
        }
        if (StringUtils.isBlank(detailRequest.getType()) || null == TaskTypeEnum.getTaskType(detailRequest.getType())) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        /*校验任务编号是否正确*/
        TaskEntity taskEntity = checkTaskIsExist(detailRequest.getTaskNo(), detailRequest.getType(), JSON.toJSONString(detailRequest));

        /*判断任务类型,来决定返回的表头*/
        TaskTypeEnum taskTypeEnum = getAndCheckAdminTaskType(detailRequest.getType(), "任务明细不可查询");

        TaskPageQueryRequest taskPageQueryRequest = new TaskPageQueryRequest();

        TaskDetailDo taskDetailDoParam = new TaskDetailDo();
        taskDetailDoParam.setTaskNo(detailRequest.getTaskNo());
        taskDetailDoParam.setTaskType(detailRequest.getType());
        Integer itemCount = 0;

        if (TaskTypeEnum.SHIP.getTaskType().equals(detailRequest.getType()) && switchOutboundDataSource) {
            itemCount = shipTaskDetailCommandRepository.countPageTaskDetailAndResult(taskDetailDoParam);
        } else if (TaskTypeEnum.PICK.getTaskType().equals(detailRequest.getType()) && switchOutboundDataSource) {
            itemCount = pickTaskDetailQueryRepository.countPageTaskDetailAndResult(taskDetailDoParam);
        }
        else {
            taskPageQueryRequest.setGroupNo(detailRequest.getTaskNo());
            taskPageQueryRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
            taskPageQueryRequest.setPageNum(detailRequest.getPageNum());
            taskPageQueryRequest.setPageSize(detailRequest.getPageSize());
            if(wmsInboundTransferConfig.isTransfer("TaskAdminServiceImpl.queryAdminTaskDetail") && (TaskTypeEnum.RECEIVED == taskTypeEnum || TaskTypeEnum.UPPER == taskTypeEnum)){
                Long count = taskCenterRepository.pageCountInAll(taskPageQueryRequest);
                if(Objects.nonNull(count)) {
                    itemCount = count.intValue();
                }
            } else {
                itemCount = this.taskDetailRepository.countPageTaskDetailAndResult(taskDetailDoParam);
            }

        }

        // 构建任务头数据
        adminResponse.setHeader(getDetailHeader(taskTypeEnum));
        /*封装返回值*/
        PagingObject<CommodityDetail> pagingObject = new PagingObject();
        pagingObject.setPageNum(detailRequest.getPageNum());
        if (null != itemCount && itemCount > 0) {

            pagingObject.setPageSize(detailRequest.getPageSize());
            pagingObject.setTotal(itemCount);

//            List<TaskDetailEntity> taskDetailEntityList = taskEntityPage.getRecords();
            Integer pageStart = (detailRequest.getPageNum() - 1) * detailRequest.getPageSize();
            log.info("打印出对应分页的查询条件:{},{},{}",JSON.toJSONString(taskDetailDoParam),pageStart,JSON.toJSONString(detailRequest));

            List<TaskDetailAndResultDo> taskDetailAndResultDoList = new ArrayList<>();
            if (TaskTypeEnum.SHIP.getTaskType().equals(detailRequest.getType())) {
                taskDetailAndResultDoList = shipTaskDetailCommandRepository.pageTaskDetailAndResult(taskDetailDoParam, pageStart, detailRequest.getPageSize());
            }
            else if (TaskTypeEnum.PICK.getTaskType().equals(detailRequest.getType())){
                taskDetailAndResultDoList = pickTaskDetailQueryRepository.pageTaskDetailAndResult(taskDetailDoParam, pageStart, detailRequest.getPageSize());
            }
            else {
                if(wmsInboundTransferConfig.isTransfer("TaskAdminServiceImpl.queryAdminTaskDetail") && (TaskTypeEnum.RECEIVED == taskTypeEnum || TaskTypeEnum.UPPER == taskTypeEnum)){
                    List<TaskCenterTaskQueryResponse> taskCenterTaskQueryResponses = taskCenterRepository.pageQueryInAll(taskPageQueryRequest);
                    taskDetailAndResultDoList = buildTaskDetailAndResultDo(taskCenterTaskQueryResponses, taskTypeEnum);
                } else {
                    taskDetailAndResultDoList = this.taskDetailRepository.pageTaskDetailAndResult(taskDetailDoParam, pageStart, detailRequest.getPageSize());
                }
            }


//            /*查询任务结果*/
//            Set<String> taskNoSet = taskDetailEntityList.stream()
//                    .map(TaskDetailEntity::getTaskNo)
//                    .collect(Collectors.toSet());
//
//            Map<String, List<TaskDetailResultEntity>> taskDetailResultMap = this.taskDetailResultBasService.queryByTaskNos(taskTypeEnum.getTaskType(), taskNoSet);
            Map<String, String> areaMap = new HashMap<>(itemCount);
            Map<String, String> workZoneMap = new HashMap<>(itemCount);
            String warehouseName = "";
            String orderTags = "";
            String batchNo = "";
            if (TaskTypeEnum.PICK.equals(taskTypeEnum)) {
                String warehouseCode = taskDetailAndResultDoList.get(0).getWarehouseCode();
                //仓库名称
                warehouseName = getWarehouseName(warehouseCode);
                //查询库区map
                Set<String> locationCodeSet = taskDetailAndResultDoList.stream().map(TaskDetailDo::getLocationCode).collect(Collectors.toSet());
                areaMap = getAreaMap(warehouseCode, locationCodeSet);
                //作业库区map
                Set<String> workZoneCodes = taskDetailAndResultDoList.stream().map(TaskDetailAndResultDo::getWorkZoneCode).collect(Collectors.toSet());
                workZoneMap = getWorkZoneMap(warehouseCode, workZoneCodes);
                //订单标记
                PickTaskDo pickTask = getPickTask(detailRequest.getTaskNo());
                orderTags = DeliveryTagV2Enum.getDescByBitVal(pickTask.getOrderTags());
                //批次单号
                batchNo = StringUtils.isBlank(pickTask.getReferenceNo()) ? StringUtils.EMPTY : pickTask.getReferenceNo();
            }
            /*查询商品信息*/
            Set<String> skuIdSet = taskDetailAndResultDoList.stream()
                    .map(TaskDetailAndResultDo::getSkuId)
                    .collect(Collectors.toSet());
            Map<String, SkuCommonPojo> commodityDetailPojoHashMap = new HashMap<>();
            List<SkuCommonPojo> skuList = commodityQueryService.querySkuCommonInfoListBySkuIds(OperationUserContextHolder.getTenantCode(), skuIdSet);
            if (CollectionUtils.isEmpty(skuList) || skuList.size() != skuIdSet.size()) {
                log.error("商品查询失败,skuIdSet -> [{}] , commodityBasicPojoList -> [{}]",
                        JSON.toJSONString(skuIdSet), JSON.toJSONString(skuList));
            } else {
                commodityDetailPojoHashMap = skuList.stream()
                        .collect(Collectors.toMap(SkuCommonPojo::getSkuId, Function.identity()));
            }

            Map<String, Barcode> barcodeMap = skuRepository.parseSkuIdList(new ArrayList<>(skuIdSet));
            /*查询活动标示*/
            String activityType = "";
            if (taskDetailAndResultDoList.get(0).getBizType().equals(WmsBizTypeEnum.GE_REN_JI_CUN.getBizType())) {
                InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(taskDetailAndResultDoList.get(0).getUniqueCode());
                if (inventoryDo != null && StringUtils.isNotBlank(inventoryDo.getBatchNo())) {
                    List<InventoryBatchPropertiesDo> inventoryBatchPropertiesDos = inventoryBatchPropertiesRepository.listByBatchNo(Arrays.asList(inventoryDo.getBatchNo()));
                    if (CollectionUtils.isNotEmpty(inventoryBatchPropertiesDos)) {
                        activityType = inventoryBatchPropertiesDos.get(0).getActivityType();
                    }
                }
                if (StringUtils.isBlank(activityType)) {
                    EntryDetailExtensionDo entryDetailExtensionDo = entryDetailExtensionRepository.selectExtensionByEntryDetailNo(
                            taskDetailAndResultDoList.get(0).getReferenceDetailNo(),
                            taskDetailAndResultDoList.get(0).getReferenceNo());
                    if (entryDetailExtensionDo != null) {
                        activityType = entryDetailExtensionDo.getActivityType();
                    }
                }
                if (StringUtils.isBlank(activityType) && StringUtils.isNotBlank(taskDetailAndResultDoList.get(0).getOriginalOrderCode())
                        && StringUtils.isNotBlank(taskDetailAndResultDoList.get(0).getSkuId())) {
                    InventoryBatchPropertiesDo inventoryBatchPropertiesDo = new InventoryBatchPropertiesDo();
                    inventoryBatchPropertiesDo.setOriginalOrderCode(taskDetailAndResultDoList.get(0).getOriginalOrderCode());
                    inventoryBatchPropertiesDo.setSkuId(taskDetailAndResultDoList.get(0).getSkuId());
                    List<InventoryBatchPropertiesDo> list = inventoryBatchPropertiesRepository.list(inventoryBatchPropertiesDo);
                    if (CollectionUtils.isNotEmpty(list)) {
                        activityType = list.get(0).getActivityType();
                    }
                }
            }
            /*遍历构建返回对象*/
            List<CommodityDetail> commodityDetails = new ArrayList<>(taskDetailAndResultDoList.size());
            for (TaskDetailAndResultDo entity : taskDetailAndResultDoList) {
                // 获取商品信息
                SkuCommonPojo sku = commodityDetailPojoHashMap.get(entity.getSkuId());
                if (null == sku) {
                    log.error("商品不存在,entity -> [{}] , commodityDetailPojoHashMap -> [{}]",
                            JSON.toJSONString(entity), JSON.toJSONString(commodityDetailPojoHashMap));
                    throw new WmsException(WmsExceptionCode.TASK_QUERY_FAIL);
                }
                TaskDetailEntity taskDetailEntity = new TaskDetailEntity();
                org.springframework.beans.BeanUtils.copyProperties(entity, taskDetailEntity);
                String uniqueCodeAlias = entity.getUniqueCodeAlias();
                String invBatchNo = entity.getInvBatchNo();
                String workZone = "";
                if (TaskTypeEnum.PICK.equals(taskTypeEnum) && StringUtils.isNotBlank(entity.getWorkZoneCode())){
                    workZone = Optional.ofNullable(workZoneMap.get(entity.getWorkZoneCode())).orElse(StringUtils.EMPTY);
                }
                List<TaskDetailResultDo> taskDetailResultDoList = entity.getTaskDetailResultDoList();
                if (CollectionUtils.isNotEmpty(taskDetailResultDoList)) {
                    for (TaskDetailResultDo taskDetailResultDo : taskDetailResultDoList) {
                        TaskDetailResultEntity resultEntity = new TaskDetailResultEntity();
                        org.springframework.beans.BeanUtils.copyProperties(taskDetailResultDo, resultEntity);
                        commodityDetails.add(buildDifferentTaskTypeDetail(taskTypeEnum, taskDetailEntity,
                                resultEntity, sku, barcodeMap,areaMap,warehouseName,orderTags, workZone,activityType,
                                uniqueCodeAlias,batchNo,invBatchNo));
                    }
                } else {
                    commodityDetails.add(buildDifferentTaskTypeDetail(taskTypeEnum, taskDetailEntity,
                            null, sku, barcodeMap,areaMap,warehouseName,orderTags, workZone, activityType,
                            uniqueCodeAlias,batchNo,invBatchNo));
                }
            }

            pagingObject.setContents(commodityDetails);
            adminResponse.setData(pagingObject);
        } else {
            pagingObject.setContents(new ArrayList<>());
            adminResponse.setData(pagingObject);
        }
        return adminResponse;
    }

    private List<TaskDetailAndResultDo> buildTaskDetailAndResultDo(List<TaskCenterTaskQueryResponse> taskCenterTaskQueryResponses, TaskTypeEnum taskTypeEnum){
        Map<String, String> upperDetailLocationMap = Maps.newHashMap();
        if(TaskTypeEnum.UPPER.getTaskType().equals(taskTypeEnum.getTaskType())) {
            List<String> taskNos = taskCenterTaskQueryResponses.stream().map(TaskCenterTaskQueryResponse::getTaskNo).collect(Collectors.toList());
            Lists.partition(taskNos, 50).forEach(s -> {
                List<UpperResultDo> upperResultDos = upperResultExtMapper.findByTaskDetailNos(OperationUserContextHolder.getTenantCode(), Lists.newArrayList(s));
                upperDetailLocationMap.putAll(upperResultDos.stream().collect(Collectors.toMap(UpperResultDo::getTaskDetailNo, UpperResultDo::getLocationCode,(v1,v2)->v1)));
            });
        }
        List<TaskDetailAndResultDo> taskDetailAndResultDoList = Lists.newArrayList();
        for(TaskCenterTaskQueryResponse taskQueryResponse : taskCenterTaskQueryResponses){
            TaskDetailAndResultDo taskDetailAndResultDo = new TaskDetailAndResultDo();
            taskDetailAndResultDo.setDetailNo(taskQueryResponse.getTaskNo());
            taskDetailAndResultDo.setTaskType(taskTypeEnum.getTaskType());
            taskDetailAndResultDo.setBizType(taskQueryResponse.getBizType());
            taskDetailAndResultDo.setStatus(TaskStatusEnum.convertStatus(taskQueryResponse.getTaskStatus()).getStatus());
            taskDetailAndResultDo.setReferenceNo(taskQueryResponse.getWorkBillNo());
            taskDetailAndResultDo.setReferenceType(taskQueryResponse.getWorkBillType());
            taskDetailAndResultDo.setReferenceDetailNo(taskQueryResponse.getWorkBillDetailNo());
            taskDetailAndResultDo.setWarehouseCode(taskQueryResponse.getWarehouseCode());
            taskDetailAndResultDo.setTenantCode(taskQueryResponse.getTenantCode());
            taskDetailAndResultDo.setOwnerCode(taskQueryResponse.getOwnerCode());
            taskDetailAndResultDo.setUniqueCode(taskQueryResponse.getUniqueCode());
            taskDetailAndResultDo.setSkuId(taskQueryResponse.getSkuId());
            taskDetailAndResultDo.setQualityLevel(taskQueryResponse.getQualityLevel());
            taskDetailAndResultDo.setQty(taskQueryResponse.getPlanQty());
            taskDetailAndResultDo.setOperationQty(taskQueryResponse.getOperatedQty());
            taskDetailAndResultDo.setLocationCode(taskQueryResponse.getLocationCode());
            if(StringUtils.isBlank(taskQueryResponse.getLocationCode())){
                taskDetailAndResultDo.setLocationCode("RECEIVE_DOCK");
            }
            taskDetailAndResultDo.setCreatedUserId(taskQueryResponse.getCreateUserId());
            taskDetailAndResultDo.setCreatedUserName(taskQueryResponse.getCreateUserName());
            taskDetailAndResultDo.setCreatedRealName(taskQueryResponse.getCreateUserName());
            taskDetailAndResultDo.setCreatedTime(taskQueryResponse.getCreateTime());
            taskDetailAndResultDo.setUpdatedUserId(taskQueryResponse.getModifyUserId());
            taskDetailAndResultDo.setUpdatedUserName(taskQueryResponse.getModifyUserName());
            taskDetailAndResultDo.setUpdatedRealName(taskQueryResponse.getModifyUserName());
            taskDetailAndResultDo.setUpdatedTime(taskQueryResponse.getModifyTime());
            taskDetailAndResultDo.setMfgTime(taskQueryResponse.getMfgTime());
            taskDetailAndResultDo.setExpTime(taskQueryResponse.getExpTime());
            taskDetailAndResultDoList.add(taskDetailAndResultDo);
            // 存在操作数量
            if(taskQueryResponse.getOperatedQty() > 0){
                TaskDetailResultDo taskDetailResultDo = new TaskDetailResultDo();
                taskDetailResultDo.setTaskDetailNo(taskQueryResponse.getTaskNo());
                taskDetailResultDo.setTaskType(taskTypeEnum.getTaskType());
                taskDetailResultDo.setReferenceNo(taskQueryResponse.getWorkBillNo());
                taskDetailResultDo.setReferenceType(taskQueryResponse.getWorkBillType());
                taskDetailResultDo.setReferenceDetailNo(taskQueryResponse.getWorkBillDetailNo());
                taskDetailResultDo.setWarehouseCode(taskQueryResponse.getWarehouseCode());
                taskDetailResultDo.setTenantCode(taskQueryResponse.getTenantCode());
                taskDetailResultDo.setOwnerCode(taskQueryResponse.getOwnerCode());
                taskDetailResultDo.setUniqueCode(taskQueryResponse.getUniqueCode());
                taskDetailResultDo.setSkuId(taskQueryResponse.getSkuId());
                taskDetailResultDo.setQualityLevel(taskQueryResponse.getQualityLevel());
                taskDetailResultDo.setPlanQty(taskQueryResponse.getPlanQty());
                taskDetailResultDo.setOperationQty(taskQueryResponse.getOperatedQty());
                taskDetailResultDo.setLocationCode(taskQueryResponse.getLocationCode());
                taskDetailResultDo.setCreatedUserId(taskQueryResponse.getCreateUserId());
                taskDetailResultDo.setCreatedUserName(taskQueryResponse.getCreateUserName());
                taskDetailResultDo.setCreatedRealName(taskQueryResponse.getCreateUserName());
                taskDetailResultDo.setCreatedTime(taskQueryResponse.getCreateTime());
                taskDetailResultDo.setUpdatedUserId(taskQueryResponse.getModifyUserId());
                taskDetailResultDo.setUpdatedUserName(taskQueryResponse.getModifyUserName());
                taskDetailResultDo.setUpdatedRealName(taskQueryResponse.getModifyUserName());
                taskDetailResultDo.setUpdatedTime(taskQueryResponse.getModifyTime());
                taskDetailResultDo.setMfgTime(taskQueryResponse.getMfgTime());
                taskDetailResultDo.setExpTime(taskQueryResponse.getExpTime());
                if(taskTypeEnum.getTaskType() == TaskTypeEnum.UPPER.getTaskType()) {
                    taskDetailResultDo.setLocationCode(upperDetailLocationMap.getOrDefault(taskQueryResponse.getTaskNo(),taskQueryResponse.getLocationCode()));
                }
                if(StringUtils.isBlank(taskDetailResultDo.getLocationCode())){
                    taskDetailAndResultDo.setLocationCode("RECEIVE_DOCK");
                }
                taskDetailAndResultDo.setTaskDetailResultDoList(Lists.newArrayList(taskDetailResultDo));
            }
        }
        return taskDetailAndResultDoList;
    }


    private String getWarehouseName(String warehouseCode) {
        ScpWarehouseDo scpWarehouseDo = warehouseRepository.queryByCodeAndTenant(warehouseCode);
        if (Objects.isNull(scpWarehouseDo)){
            throw new WmsException(WmsExceptionCode.WAREHOUSE_NOT_EXIST);
        }
        return scpWarehouseDo.getWarehouseName();
    }

    private PickTaskDo getPickTask(String taskNo) {
        PickTaskDo pickTaskDo = pickQueryService.queryPickTask(taskNo);
        if (Objects.isNull(pickTaskDo)){
            throw new WmsException(WmsExceptionCode.TASK_HEADER_NOT_EXIST);
        }
        return pickTaskDo;
    }

    private Map<String, String> getWorkZoneMap(String warehouseCode, Set<String> workZoneCodeSet) {
        Map<String, String> workZoneMap = new HashMap<>();
        if (CollectionUtils.isEmpty(workZoneCodeSet)){
            return workZoneMap;
        }
        List<BasWorkZoneDo> basWorkZoneDos = basWorkZoneRepository.selectByWorkZoneCodes(warehouseCode, workZoneCodeSet);
        if (CollectionUtils.isEmpty(basWorkZoneDos)){
            return workZoneMap;
        }
        return basWorkZoneDos.stream().collect(Collectors.toMap(BasWorkZoneDo::getWorkZoneCode,BasWorkZoneDo::getWorkZoneName));
    }

    private Map<String, String> getAreaMap(String warehouseCode,
                                           Set<String> locationCodeSet) {
        Map<String, String> areaMap = new HashMap<>();
        List<BasLocationDo> basLocationDos = basLocationV2Service.areaDetailByCodes(
                warehouseCode, locationCodeSet, OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isEmpty(basLocationDos)) {
            log.error("根据拣货明细库位set未查询到库位信息, locationCodeSet:{}", JSON.toJSONString(locationCodeSet));
            return areaMap;
        }
        Set<String> areaCodeSet = basLocationDos.stream().map(BasLocationDo::getAreaCode).collect(Collectors.toSet());
        List<BasAreaDo> basAreaDos = basAreaService.areaDetail(warehouseCode, areaCodeSet);
        if (CollectionUtils.isEmpty(basAreaDos)) {
            log.error("根据库位库区set未查询到库区信息,areaSet:{}, locationCodeSet:{}",
                    JSON.toJSONString(areaCodeSet), JSON.toJSONString(locationCodeSet));
            return areaMap;
        }
        Map<String, String> areaNameMap = basAreaDos.stream().collect(Collectors.toMap(BasAreaDo::getAreaCode, BasAreaDo::getAreaName));
        for (BasLocationDo basLocationDo : basLocationDos) {
            areaMap.put(basLocationDo.getLocationCode(),
                    Optional.ofNullable(areaNameMap.get(basLocationDo.getAreaCode())).orElse(StringUtils.EMPTY));
        }
        return areaMap;

    }

    /**
     * 构建不同任务类型的值
     *
     * @param taskTypeEnum
     * @param entity
     * @param detailResultEntity
     * @param sku
     * @return
     */
    @Override
    public CommodityDetail buildDifferentTaskTypeDetail(TaskTypeEnum taskTypeEnum, TaskDetailEntity entity,
                                                        TaskDetailResultEntity detailResultEntity, SkuCommonPojo sku,
                                                        Map<String, Barcode> barcodeMap,
                                                        Map<String, String> areaMap,
                                                        String warehouseName,
                                                        String orderTags,
                                                        String workZone,
                                                        String activityType,
                                                        String uniqueCodeAlias,
                                                        String batchNo,
                                                        String invBatchNo) {
        CommodityDetail commodityDetail = null;
        switch (taskTypeEnum) {
            case UPPER:
                // 上架任务
                commodityDetail = buildUpperDetail(entity, detailResultEntity, sku);
                break;
            case PICK:
                // 拣货任务
                commodityDetail = buildPickDetail(entity, detailResultEntity, sku, areaMap, warehouseName,orderTags, workZone,uniqueCodeAlias,batchNo,invBatchNo);
                break;
            case MOVE:
                // 移位任务
                commodityDetail = buildMoveDetail(entity, detailResultEntity, sku);
                break;
            case MODIFY_ATTR:
                // 属性调整任务
                commodityDetail = buildAttrTransformDetail(entity, detailResultEntity, sku);
                break;
            case RECEIVED:
                // 收货任务
                commodityDetail = buildReceivedDetail(entity, detailResultEntity, sku);
                break;
            case SHIP:
                // 发货任务
                commodityDetail = buildShipDetail(entity, detailResultEntity, sku);
                break;
            case RETURN_SHELF:
                //返架任务
                commodityDetail = buildReturnShelfDetail(entity, detailResultEntity, sku);
                break;
            case RECEIVED_QUALITY:
                // 入库质检任务
                commodityDetail = buildReceivedQualityDetail(entity, detailResultEntity, sku);
                break;
            default:
                throw new WmsException(WmsExceptionCode.TASK_TYPE_NOT_FIND);
        }
        if (sku != null) {
            commodityDetail.setExpiryDate(sku.getExpiryDate());
            commodityDetail.setExtraCode(barcodeMap.get(sku.getSkuId()) == null ?
                    StringUtil.EMPTY_STRING : barcodeMap.get(sku.getSkuId()).getExtraCodes());
            commodityDetail.setStandardCode(barcodeMap.get(sku.getSkuId()) == null ?
                    StringUtil.EMPTY_STRING : barcodeMap.get(sku.getSkuId()).getStandardCodes());
            commodityDetail.setSkuId(sku.getSkuId());
        }

        commodityDetail.setExpTime(DateUtils.dateToString(entity.getExpTime(), DateUtils.FROMAT_DATE));
        commodityDetail.setMfgTime(DateUtils.dateToString(entity.getMfgTime(), DateUtils.FROMAT_DATE));
        commodityDetail.setActivityTypeName(ActivityTypeEnum.getTypeName(activityType));
        return commodityDetail;
    }

    /**
     * 构建收货质检任务明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     * @return
     */
    private CommodityDetail buildReceivedQualityDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                                       SkuCommonPojo sku) {
        ReceivedQualityTaskDetail receivedQualityTaskDetail = new ReceivedQualityTaskDetail();
        receivedQualityTaskDetail.setTaskNo(entity.getTaskNo());
        receivedQualityTaskDetail.setReferenceNo(entity.getReferenceNo());

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, receivedQualityTaskDetail, detailResultEntity);
        receivedQualityTaskDetail.setQty(entity.getQty());

        return receivedQualityTaskDetail;
    }

    /**
     * 构建发货任务明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     * @return
     */
    private CommodityDetail buildShipDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                            SkuCommonPojo sku) {

        DeliveryTaskDetail deliveryTaskDetail = new DeliveryTaskDetail();
        deliveryTaskDetail.setReferenceNo(entity.getReferenceNo());

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, deliveryTaskDetail, detailResultEntity);
        deliveryTaskDetail.setPlanQty(entity.getQty());
        deliveryTaskDetail.setOperationQty(null != detailResultEntity ? detailResultEntity.getOperationQty() : NumberUtils.INTEGER_ZERO);

        return deliveryTaskDetail;
    }

    /**
     * 构建操作信息
     */
    private void buildOperation(TaskDetailResultEntity detailResultEntity, CommodityDetail commodityDetail) {
        if (null != detailResultEntity) {
            if (TaskTypeEnum.MOVE.getTaskType().equals(detailResultEntity.getTaskType())) {
                commodityDetail.setOperator(detailResultEntity.getOperationRealName());
            } else {
                commodityDetail.setOperator(detailResultEntity.getUpdatedRealName());
            }
            if (null != detailResultEntity.getUpdatedTime()) {
                commodityDetail.setOperatedTime(DateUtils.formatDate(detailResultEntity.getUpdatedTime(), DateUtils.FORMAT_TIME));
            } else if (null != detailResultEntity.getCreatedTime()) {
                commodityDetail.setOperatedTime(DateUtils.formatDate(detailResultEntity.getCreatedTime(), DateUtils.FORMAT_TIME));
            }
        }
    }


    /**
     * 构建收货任务明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     * @return
     */
    private CommodityDetail buildReceivedDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                                SkuCommonPojo sku) {

        ReceiveTaskDetail receiveTaskDetail = new ReceiveTaskDetail();
        receiveTaskDetail.setReferenceNo(entity.getReferenceNo());
        receiveTaskDetail.setLocationCode(null != detailResultEntity ? detailResultEntity.getLocationCode() : "");

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, receiveTaskDetail, detailResultEntity);

        receiveTaskDetail.setPlanQty(entity.getQty());
        receiveTaskDetail.setOperationQty(null != detailResultEntity ? detailResultEntity.getOperationQty() : NumberUtils.INTEGER_ZERO);
        receiveTaskDetail.setBizType(entity.getBizType());
        receiveTaskDetail.setReferenceType(entity.getReferenceType());
//        if (StringUtils.isNotBlank(detailResultEntity.getInventoryNo())) {
//            InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByInventoryNo(detailResultEntity.getInventoryNo());
//            if (inventoryDo != null) {
//                receiveTaskDetail.setBatchNo(inventoryDo.getBatchNo());
//            }
//        }
        return receiveTaskDetail;
    }

    /**
     * 获取 && 校验 任务类型
     *
     * @param taskType
     * @param errMsg
     * @return
     */
    private TaskTypeEnum getAndCheckAdminTaskType(String taskType, String errMsg) {
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getTaskType(taskType);
        if (taskTypeEnum == null) {
            throw new WmsOperationException("未知的任务类型，taskType:" + taskType);
        }
        if (Objects.nonNull(taskTypeEnum) &&
                !TaskTypeEnum.TASK_DETAIL_SCAN_LIST.contains(taskTypeEnum)) {
            log.error("任务明细不可查询,taskType -> [{}]", taskType);
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY, errMsg);
        }
        return taskTypeEnum;
    }

    /**
     * 构建属性调整明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     */
    private AttrTransformTaskDetail buildAttrTransformDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                                             SkuCommonPojo sku) {
        AttrTransformTaskDetail attrTransformTaskDetail = new AttrTransformTaskDetail();
        attrTransformTaskDetail.setId(entity.getId());
        attrTransformTaskDetail.setTaskDetailNo(entity.getDetailNo());
        attrTransformTaskDetail.setLocationCode(entity.getLocationCode());
        String qualityLevel = this.sysDictService.getDictName(entity.getTenantCode(), entity.getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL, entity.getQualityLevel());
        attrTransformTaskDetail.setQualityLevel(qualityLevel);


        /**
         * 2020.8.6 DQ仓业务添加日期调整
         */
        InventoryAdjustDetailQueryPojo inventoryAdjustDetailQueryPojo = new InventoryAdjustDetailQueryPojo();
        inventoryAdjustDetailQueryPojo.setAdjustNo(entity.getReferenceNo());
        inventoryAdjustDetailQueryPojo.setInventoryNo(entity.getInventoryNo());
        inventoryAdjustDetailQueryPojo.setUniqueCode(entity.getUniqueCode());
        inventoryAdjustDetailQueryPojo.setWarehouseCode(entity.getWarehouseCode());
        inventoryAdjustDetailQueryPojo.setTenantCode(entity.getTenantCode());
        InventoryAdjustDetailPojo adjustDetailPojo = inventoryAdjustService.queryDetail(inventoryAdjustDetailQueryPojo);

        attrTransformTaskDetail.setMfgTime(adjustDetailPojo.getMfgTime());
        attrTransformTaskDetail.setExpTime(adjustDetailPojo.getExpTime());

        attrTransformTaskDetail.setProductionBatchNo(adjustDetailPojo.getProductionBatchNo());
        attrTransformTaskDetail.setTargetProductionBatchNo(adjustDetailPojo.getToProductionBatchNo());

        String targetQualityLevel = "";
        Integer operationQty = NumberUtils.INTEGER_ZERO;
        if (null != detailResultEntity) {
            if (StringUtils.isNotEmpty(detailResultEntity.getQualityLevel())) {
                targetQualityLevel = this.sysDictService.getDictName(entity.getTenantCode(), entity.getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL, detailResultEntity.getQualityLevel());
            }
            operationQty = detailResultEntity.getOperationQty();

            attrTransformTaskDetail.setTargetMfgTime(DateUtils.formatDate(detailResultEntity.getMfgTime(), DateUtils.FORMAT_TIME));
            attrTransformTaskDetail.setTargetExpTime(DateUtils.formatDate(detailResultEntity.getExpTime(), DateUtils.FORMAT_TIME));
        }
        attrTransformTaskDetail.setTargetQualityLevel(targetQualityLevel);

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, attrTransformTaskDetail, detailResultEntity);

        attrTransformTaskDetail.setPlanQty(entity.getQty());
        attrTransformTaskDetail.setOperationQty(operationQty);

        //沈老饭非要在这样改  因为他说单据取消的时候没有操作人不知道怎么搞
        if (StringUtils.isBlank(attrTransformTaskDetail.getOperator())
                && entity.getStatus().equals(TaskStatusEnum.CANCEL.getStatus())) {
            attrTransformTaskDetail.setOperator(entity.getUpdatedRealName());
            attrTransformTaskDetail.setOperatedTime(DateUtils.formatDate(entity.getUpdatedTime(), DateUtils.FORMAT_TIME));
        }

        return attrTransformTaskDetail;
    }

    /**
     * 构建移位明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     */
    private MoveTaskDetail buildMoveDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                           SkuCommonPojo sku) {
        MoveTaskDetail moveTaskDetail = new MoveTaskDetail();
        moveTaskDetail.setId(entity.getId());
        moveTaskDetail.setTaskDetailNo(entity.getDetailNo());
        moveTaskDetail.setLocationCode(entity.getLocationCode());
        moveTaskDetail.setTargetLocationCode(null != detailResultEntity ? detailResultEntity.getLocationCode() : "");

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, moveTaskDetail, detailResultEntity);

        moveTaskDetail.setPlanQty(entity.getQty());
        moveTaskDetail.setOperationQty(detailResultEntity != null ? detailResultEntity.getOperationQty() : NumberUtils.INTEGER_ZERO);
        return moveTaskDetail;
    }

    /**
     * 构建拣货明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     */
    private PickTaskDetail buildPickDetail(TaskDetailEntity entity,
                                           TaskDetailResultEntity detailResultEntity,
                                           SkuCommonPojo sku,
                                           Map<String, String> areaMap,
                                           String warehouseName,
                                           String orderTags,
                                           String workZone,
                                           String uniqueCodeAlias,
                                           String batchNo,
                                           String invBatchNo) {

        PickTaskDetail pickTaskDetail = new PickTaskDetail();
        pickTaskDetail.setId(entity.getId());
        pickTaskDetail.setReferenceDetailNo(entity.getReferenceNo());
        pickTaskDetail.setTaskDetailNo(entity.getDetailNo());
        pickTaskDetail.setLocationCode(entity.getLocationCode());
        pickTaskDetail.setAreaCode(Optional.ofNullable(areaMap.get(entity.getLocationCode())).orElse(StringUtils.EMPTY));
        pickTaskDetail.setWorkZoneCode(workZone);
        pickTaskDetail.setWarehouseName(warehouseName);
        pickTaskDetail.setTag(orderTags);
        pickTaskDetail.setReferenceNo(batchNo);
        if (detailResultEntity != null) {
            pickTaskDetail.setContainerCode(detailResultEntity.getContainerCode());
        }

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, pickTaskDetail, detailResultEntity);
        //setQ码
        pickTaskDetail.setUniqueCode(StringUtils.isNotBlank(uniqueCodeAlias) ? uniqueCodeAlias : entity.getUniqueCode());
        pickTaskDetail.setPlanQty(entity.getQty());
        pickTaskDetail.setOperationQty(detailResultEntity != null ? detailResultEntity.getOperationQty() : NumberUtils.INTEGER_ZERO);
        pickTaskDetail.setInvBatchNo(invBatchNo);
        return pickTaskDetail;
    }

    /**
     * 构建上架明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     */
    private PutAwayTaskDetail buildUpperDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                               SkuCommonPojo sku) {
        PutAwayTaskDetail putAwayTaskDetail = new PutAwayTaskDetail();
        putAwayTaskDetail.setId(entity.getId());
        putAwayTaskDetail.setReferenceNo(entity.getReferenceNo());
        putAwayTaskDetail.setTaskDetailNo(entity.getDetailNo());
        putAwayTaskDetail.setLocationCode(null != detailResultEntity ? detailResultEntity.getLocationCode() : "");

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, putAwayTaskDetail, detailResultEntity);

        putAwayTaskDetail.setPlanQty(entity.getQty());
        putAwayTaskDetail.setOperationQty(null != detailResultEntity ? detailResultEntity.getOperationQty() : NumberUtils.INTEGER_ZERO);
        return putAwayTaskDetail;
    }

    /**
     * 构建任务明细的商品信息
     *
     * @param entity
     * @param sku
     * @param commodityDetail
     */
    private void buildTaskDetailCommodityInfo(TaskDetailEntity entity,
                                              SkuCommonPojo sku,
                                              CommodityDetail commodityDetail,
                                              TaskDetailResultEntity detailResultEntity) {
        if (sku != null) {
            commodityDetail.setGoodsArticleNumber(sku.getArtNoMain());
            commodityDetail.setGoodsTitle(sku.getSpuName());
            commodityDetail.setSpecs(sku.getSpecs());
            commodityDetail.setCategoryNameLevel1(sku.getCategoryNameLevel1());
        }
        String categoryName = null != sku ? GetPropertyHelper.getString(() -> sku.getCategoryNameLevel3()) : "";
        String brandName = null != sku ? GetPropertyHelper.getString(() -> sku.getBrandName()) : "";
        String seriesName = null != sku ? GetPropertyHelper.getString(() -> sku.getSeriesName()) : "";

        commodityDetail.setUniqueCode(entity.getUniqueCode());
        commodityDetail.setBrandName(brandName);

        commodityDetail.setCategoryName(categoryName);
        String targetQualityLevel = "";
        if (StringUtils.isNotEmpty(entity.getQualityLevel())) {
            targetQualityLevel = this.sysDictService.getDictName(entity.getTenantCode(), entity.getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL, entity.getQualityLevel());
        }
        commodityDetail.setQualityLevel(targetQualityLevel);

        commodityDetail.setSeriesName(seriesName);
        commodityDetail.setOwnerCode(entity.getOwnerCode());
        commodityDetail.setOwnerName(scpMerchantService.getNameByCode(entity.getOwnerCode()));
        buildOperation(detailResultEntity, commodityDetail);
    }

    /**
     * 构建任务头数据
     *
     * @param taskTypeEnum
     * @return
     */
    public List<TaskDetailHeader> getDetailHeader(TaskTypeEnum taskTypeEnum) {
        List<TaskDetailHeader> headerList = null;
        switch (taskTypeEnum) {
            case UPPER:
                // 上架任务
                headerList = buildTaskDetailHeader(PutAwayTaskDetail.class);
                break;
            case PICK:
                // 拣货任务
                headerList = buildTaskDetailHeader(PickTaskDetail.class);
                break;
            case MOVE:
                // 移位任务
                headerList = buildTaskDetailHeader(MoveTaskDetail.class);
                break;
            case MODIFY_ATTR:
                // 属性调整任务
                headerList = buildTaskDetailHeader(AttrTransformTaskDetail.class);
                break;
            case RECEIVED:
                // 收货任务头
                headerList = buildTaskDetailHeader(ReceiveTaskDetail.class);
                break;
            case SHIP:
                headerList = buildTaskDetailHeader(DeliveryTaskDetail.class);
                break;
            case RETURN_SHELF:
                headerList = buildTaskDetailHeader(ReturnShelfTaskDetail.class);
                break;
            case RECEIVED_QUALITY:
                headerList = buildTaskDetailHeader(ReceivedQualityTaskDetail.class);
                break;
            default:
                return null;
        }
        return headerList;
    }

    /**
     * 构建任务明细头
     *
     * @param clazz
     * @return
     */
    @Override
    public List<TaskDetailHeader> buildTaskDetailHeader(Class clazz) {

        Field[] fields = ReflectUtil.getFields(clazz);

        List<Field> fieldList = new ArrayList<>();
        // 父类定义属性
        fieldList.addAll(Arrays.asList(fields));
        // 该类定义属性
        if (CollectionUtils.isEmpty(fieldList)) {
            return null;
        }
        Map<String, TaskDetailHeader> headerMap = new HashMap<>(fieldList.size());
        for (Field field : fieldList) {
            TaskDetailHeader header = null;
            if (null == headerMap.get(field.getName())) {
                header = new TaskDetailHeader();
                header.setAttrName(field.getName());
            } else {
                // 子类覆盖父类的属性字段&&虽然有点不地道
                header = headerMap.get(field.getName());
            }
            /*获取ApiModelProperty注释*/
            Annotation[] annotations = field.getAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation instanceof ApiModelProperty) {
                    ApiModelProperty apiModelProperty = (ApiModelProperty) annotation;
                    header.setTitle(apiModelProperty.value());
                } else if (annotation instanceof FieldProperty) {
                    FieldProperty fieldProperty = (FieldProperty) annotation;
                    header.setSort(fieldProperty.sort());
                    header.setIsHide(fieldProperty.isHide());
                }
            }
            headerMap.put(header.getAttrName(), header);
        }

        /*获取对象 && 按照顺序进行排序*/
        List<TaskDetailHeader> headerList = headerMap.values()
                .stream()
                // 添加过滤
                .filter(entity -> StringUtils.isNotEmpty(entity.getTitle()) && null != entity.getSort())
                .sorted(Comparator.comparing(TaskDetailHeader::getSort))
                .collect(Collectors.toList());

        return headerList;
    }

    @Override
    public void forceFinish(String taskNo, String taskType) {
        if (StringUtils.isEmpty(taskNo)) {
            log.error("任务编号不能为空,detailRequest -> [{}]", taskNo);
            throw new WmsException(WmsExceptionCode.TASK_NO_NULL);
        }
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        /*校验任务编号是否正确*/
        TaskEntity taskEntity = checkTaskIsExist(taskNo, taskType, JSON.toJSONString(taskNo));

        /*判断任务类型,来决定返回的表头*/
        TaskTypeEnum taskTypeEnum = getAndCheckAdminTaskType(taskType, "任务不可强制结束");
        //判断任务类型是否能强制完成
        checkCanForceCompleteTask(taskTypeEnum, taskNo);

        if (taskTypeEnum.getTaskType().equals(TaskTypeEnum.PICK.getTaskType())) {
            pickCommandService.forceFinish(PickForceFinishParam.builder()
                    .taskNo(taskNo)
                    .build());
        } else if (taskTypeEnum.getTaskType().equals(TaskTypeEnum.UPPER.getTaskType())) {
            upperCommandService.forceFinish(
                    UpperForceFinishParam.builder().upperHeaderNo(taskNo).build()
            );
        } else {
            /*构建强制完成任务参数*/
            ForceFinishTaskPojo<Void> forceFinishTaskPojo = new ForceFinishTaskPojo();
            forceFinishTaskPojo.setTaskTypeEnum(taskTypeEnum);
            forceFinishTaskPojo.setTaskStatusEnum(TaskStatusEnum.FORCE_FINISH);
            forceFinishTaskPojo.setTaskNo(taskNo);
            forceFinishTaskPojo.setSupportForceFinish(true);
            this.taskFactory.process(forceFinishTaskPojo);
        }
        /*添加任务日志*/
        OperateLogPojo operateLogPojo = OperateLogPojo.builder()
                .referenceNo(taskEntity.getReferenceNo())
                .referenceType(taskEntity.getReferenceType())
                .taskNo(taskEntity.getTaskNo())
                .warehouseCode(taskEntity.getWarehouseCode())
                .message("强制完成任务").build();
        if (taskTypeEnum.equals(TaskTypeEnum.UPPER)) {
            operateLogPojo.setOperateTypeEnum(OperateTypeEnum.UPPER_TASK_FORCE_FINISH);
        } else if (taskTypeEnum.equals(TaskTypeEnum.PICK)) {
            operateLogPojo.setOperateTypeEnum(OperateTypeEnum.PICK_TASK_FORCE_FINISH);
        }
        operateLogService.addLog(operateLogPojo);
    }

    private void checkCanForceCompleteTask(TaskTypeEnum taskTypeEnum, String taskNo) {
        if (!(taskTypeEnum.equals(TaskTypeEnum.UPPER) || taskTypeEnum.equals(TaskTypeEnum.PICK))) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_NOT_SUPPORT_FINISH);
        }
        boolean forbiddenForceCompleteTask = (TaskTypeEnum.PICK.equals(taskTypeEnum) || TaskTypeEnum.UPPER.equals(taskTypeEnum)) &&
                TenantEnum.SCORE95.getType().equals(OperationUserContextHolder.getTenantCode());
        if (forbiddenForceCompleteTask) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_NOT_SUPPORT_FINISH);
        }
        if (TaskTypeEnum.PICK.equals(taskTypeEnum)) {
            //众筹业务类型，不支持强制完成拣货
            List<PickTaskDetailDo> pickTaskDetailDoList = pickTaskDetailQueryRepository.queryByTaskNo(taskNo);
            boolean huoDongBizType = pickTaskDetailDoList.stream().anyMatch(item -> WmsBizTypeEnum.HUO_DONG.getBizType().equals(item.getBizType()));
            if (huoDongBizType) {
                throw new WmsOperationException("活动入仓业务类型不支持强制完成拣货");
            }
        }
    }

    /**
     * 通过任务编号查询任务
     *
     * @param taskNo
     * @param detailStr
     * @return
     */
    private TaskEntity checkTaskIsExist(String taskNo, String taskType, String detailStr) {
        TaskEntity taskEntityParam = new TaskEntity();
        taskEntityParam.setTaskNo(taskNo);
        if (StringUtils.isBlank(taskType) || null == TaskTypeEnum.getTaskType(taskType)) {
            throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
        }
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getTaskType(taskType);
        if(wmsInboundTransferConfig.isTransfer("TaskAdminServiceImpl.checkTaskIsExist")) {
            if (TaskTypeEnum.RECEIVED == TaskTypeEnum.getTaskType(taskType) || TaskTypeEnum.UPPER == TaskTypeEnum.getTaskType(taskType)) {
                TaskCenterTaskGroupQueryResponse taskGroupQueryResponse = taskCenterRepository.findByGroupNoAll(taskNo, OperationUserContextHolder.getTenantCode());
                TaskEntity taskEntity = new TaskEntity();
                taskEntity.setId(taskGroupQueryResponse.getId());
                taskEntity.setTaskNo(taskGroupQueryResponse.getGroupNo());
                taskEntity.setReferenceType(taskGroupQueryResponse.getWorkBillType());
                taskEntity.setReferenceNo(taskGroupQueryResponse.getWorkBillNo());
                taskEntity.setType(TaskTypeEnum.RECEIVED == TaskTypeEnum.getTaskType(taskType) ? "UPPER" : "RECEIVED");
                taskEntity.setWarehouseCode(taskGroupQueryResponse.getWarehouseCode());
                return taskEntity;
            }
        }
        if (TaskTypeEnum.PICK.equals(taskTypeEnum)){
            PickTaskDo pickTaskDo = pickTaskQueryRepository.findByNo(taskNo);
            if (null == pickTaskDo) {
                log.error("任务不存在,detailRequest -> [{}]", detailStr);
                throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
            }
            TaskEntity taskEntity = new TaskEntity();
            BeanUtil.copyProperties(pickTaskDo, taskEntity);
            return taskEntity;
        }

        if (TaskTypeEnum.SHIP.equals(taskTypeEnum)){
            ShipTaskDo shipTaskDo = shipTaskQueryRepository.findByTaskNo(taskNo, null, OperationUserContextHolder.getTenantCode());
            if (null == shipTaskDo) {
                log.error("任务不存在,detailRequest -> [{}]", detailStr);
                throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
            }

            TaskEntity taskEntity = new TaskEntity();
            BeanUtil.copyProperties(shipTaskDo, taskEntity);
            return taskEntity;

        }

        taskEntityParam.setType(taskType);
        taskEntityParam.setDeleted(NumberUtils.INTEGER_ZERO);
        taskEntityParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        QueryWrapper<TaskEntity> queryWrapper = new QueryWrapper<>(taskEntityParam);
        queryWrapper.select(TaskEntity.COL_TASK_NO,
                TaskEntity.COL_TENANT_CODE,
                TaskEntity.COL_TYPE,
                TaskEntity.COL_REFERENCE_NO,
                TaskEntity.COL_REFERENCE_TYPE);
        TaskEntity taskEntity = this.taskBaseService.getOne(queryWrapper);
        if (null == taskEntity) {
            log.error("任务不存在,detailRequest -> [{}]", detailStr);
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
        } else if (null == taskEntity.getType()) {
            log.error("任务错误,taskEntity -> [{}]", JSON.toJSONString(taskEntity));
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY, "任务错误");
        }
        return taskEntity;
    }

    @Autowired
    private TaskRepository taskRepository;

    @Override
    public List<Object[]> taskList(TaskPageRequest queryRequest) {
        if (!isNewTaskBillReport) {
            log.info("zw任务单导出——旧");
            QueryWrapper<TaskEntity> queryWrapper = buildTaskEntityQueryWrapper(queryRequest);
            /*查询任务头*/
            List<TaskEntity> taskEntityList = taskBaseService.list(queryWrapper);
            if (CollectionUtils.isEmpty(taskEntityList)) {
                // 数量为空
                return null;
            }
            /*查询任务Response*/
            List<TaskAdminResponse> taskAdminResponseList = this.buildTaskAdmin(taskEntityList, queryRequest.getType());

            /*构建导出对象*/
            List<Object[]> rows = buildTaskReportExcel(taskAdminResponseList);

            return rows;
        } else {
            log.info("zw任务单导出——新");
            // 修复很多IN的那个逻辑。
            TaskBillReportParam param = new TaskBillReportParam();
            param.setTaskNo(queryRequest.getTaskNo());
            param.setStatus(queryRequest.getStatus());
            param.setTaskType(queryRequest.getType());
            param.setUniqueCode(queryRequest.getUniqueCode());
            param.setBarcode(queryRequest.getBarcode());
            param.setReferenceNo(queryRequest.getReferenceNo());
            param.setCreateStartTime(queryRequest.getCreateStartTime());
            param.setCreateEndTime(queryRequest.getCreateEndTime());
            param.setFinishStartTime(queryRequest.getFinishStartTime());
            param.setFinishEndTime(queryRequest.getFinishEndTime());
            param.setWarehouseCodes(queryRequest.getWareHouse());
            // 这里输入的是国标码/附加码
            if (StringUtils.isNotBlank(queryRequest.getBarcode())) {
                ItemCode itemCode = skuRepository.parse(queryRequest.getBarcode());
                if (!itemCode.isUniqueCode()) {
                    param.setSkuIds(itemCode.getSkuIds());
                }
            }
            List<TaskBillReportDo> rspDos = taskRepository.selectTaskReportSlidePage(param);
            List<Object[]> rows = new ArrayList<>(rspDos.size());
            Map<String, String> warehouseMap = basWarehouseService.warehouseNameMap();
            for (TaskBillReportDo taskAdmin : rspDos) {
                List<Object> listObjs = new ArrayList<>();
                listObjs.add(warehouseMap.get(taskAdmin.getWarehouseCode()));
                listObjs.add(taskAdmin.getTaskNo());
                listObjs.add(taskAdmin.getReferenceNo());
                listObjs.add(TaskTypeEnum.getTaskType(taskAdmin.getTaskType()).getTaskName());
                listObjs.add(TaskStatusEnum.getTaskStatus(taskAdmin.getStatus()).getStatusName());
                listObjs.add(formatQty(taskAdmin.getTotalQty()));
                listObjs.add(formatQty(taskAdmin.getTotalOperationQty()));
                listObjs.add(null != taskAdmin.getCreatedTime() ? taskAdmin.getCreatedTime() : "");
                listObjs.add(null != taskAdmin.getEndTime() ? taskAdmin.getEndTime() : "");
                rows.add(listObjs.toArray());
            }
            return rows;
        }
    }

    /**
     * 格式化数量
     *
     * @param qty
     * @return
     */
    private long formatQty(String qty) {
        if (StringUtils.isBlank(qty)) {
            return 0;
        }
        return Long.parseLong(qty);
    }

    @Override
    public List<Object[]> taskDetailList(TaskDetailRequest detailRequest) {
        TaskDetailAdminResponse taskDetailAdminResponse = this.queryAdminTaskDetail(detailRequest);
        return buildTaskDetailReportExcel(taskDetailAdminResponse);
    }

    @Override
    public String[] buildExcelDetailHeader(TaskDetailRequest detailRequest) {
        /*判断任务类型,来决定返回的表头*/
        TaskTypeEnum taskTypeEnum = getAndCheckAdminTaskType(detailRequest.getType(), "任务明细不可查询");
        List<TaskDetailHeader> detailHeaders = getDetailHeader(taskTypeEnum);
        List<String> titles = new ArrayList<>();
        for (TaskDetailHeader taskDetailHeader : detailHeaders) {
            if (NumberUtils.INTEGER_ZERO.equals(taskDetailHeader.getIsHide())) {
                titles.add(taskDetailHeader.getTitle());
            }
        }
        String[] headersStr = titles.toArray(new String[titles.size()]);
        return headersStr;
    }

    /**
     * 构建导出对象
     *
     * @param taskAdminResponseList
     * @return
     */
    private List<Object[]> buildTaskReportExcel(List<TaskAdminResponse> taskAdminResponseList) {
        List<Object[]> results = new ArrayList<>(taskAdminResponseList.size());
        for (TaskAdminResponse taskAdmin : taskAdminResponseList) {
            List<Object> listObjs = new ArrayList<>();
            listObjs.add(taskAdmin.getWareHouseName());
            listObjs.add(taskAdmin.getTaskNo());
            listObjs.add(taskAdmin.getReferenceNo());
            listObjs.add(taskAdmin.getTypeName());
            listObjs.add(taskAdmin.getStatusName());
            listObjs.add(taskAdmin.getPlanQty());
            listObjs.add(taskAdmin.getOperationQty());
            listObjs.add(null != taskAdmin.getCreateTime() ? taskAdmin.getCreateTime() : "");
            listObjs.add(null != taskAdmin.getFinishTime() ? taskAdmin.getFinishTime() : "");
            results.add(listObjs.toArray());
        }
        return results;
    }

    /**
     * 构建任务详情导出对象
     *
     * @param taskDetailAdminResponse
     * @return
     */
    private List<Object[]> buildTaskDetailReportExcel(TaskDetailAdminResponse taskDetailAdminResponse) {
        List<Object[]> results = new ArrayList<>(taskDetailAdminResponse.getData().getContents().size());
        try {
            for (CommodityDetail commodityDetail : taskDetailAdminResponse.getData().getContents()) {
                List<Object> listObjs = new ArrayList<>();
                for (TaskDetailHeader header : taskDetailAdminResponse.getHeader()) {
                    if (header.getIsHide().equals(0)) {
                        Object value = ReflectUtil.getFieldValue(commodityDetail, header.getAttrName());
                        //Object value = BeanUtils.getProperty(commodityDetail, header.getAttrName());
                        listObjs.add(value);
                    }
                }
                results.add(listObjs.toArray());
            }
        } catch (Exception ex) {
            log.error("任务明细导出异常：{}", ex);
        }
        return results;
    }


    /**
     * 构建返架明细
     *
     * @param entity
     * @param detailResultEntity
     * @param sku
     */
    private ReturnShelfTaskDetail buildReturnShelfDetail(TaskDetailEntity entity, TaskDetailResultEntity detailResultEntity,
                                                         SkuCommonPojo sku) {
        ReturnShelfTaskDetail returnShelfTaskDetail = new ReturnShelfTaskDetail();
        returnShelfTaskDetail.setId(entity.getId());
        returnShelfTaskDetail.setTaskDetailNo(entity.getDetailNo());
        returnShelfTaskDetail.setLocationCode(entity.getLocationCode());
        returnShelfTaskDetail.setTargetLocationCode(null != detailResultEntity ? detailResultEntity.getLocationCode() : "");

        /*构建任务明细的商品信息*/
        buildTaskDetailCommodityInfo(entity, sku, returnShelfTaskDetail, detailResultEntity);

        returnShelfTaskDetail.setPlanQty(entity.getQty());
        returnShelfTaskDetail.setOperationQty(detailResultEntity != null ? detailResultEntity.getOperationQty() : NumberUtils.INTEGER_ZERO);
        return returnShelfTaskDetail;
    }


}
