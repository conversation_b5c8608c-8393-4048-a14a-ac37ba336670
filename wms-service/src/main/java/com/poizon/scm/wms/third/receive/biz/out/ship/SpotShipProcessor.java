package com.poizon.scm.wms.third.receive.biz.out.ship;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.pojo.third.receive.request.TaskReceiveRequest;
import com.poizon.scm.wms.service.shipping.ShippingService;
import com.poizon.scm.wms.third.receive.biz.out.ship.base.AbstractShipProcessor;
import com.poizon.scm.wms.util.DeliveryHeaderUtils;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/21 10:12
 * @Description 现货发货
 * 1.现货出
 * 2.现货退货
 * 3.现货加价购交接
 */
@Slf4j
@Component
public class SpotShipProcessor extends AbstractShipProcessor {

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private ShippingService shippingService;

    @Override
    public List<String> getTenantIdList() {
        return Lists.newArrayList(TenantEnum.dwAllTenantList);
    }

    @Override
    public Boolean matchProcessor(DeliveryHeaderDo deliveryHeaderDo) {

        // 货品分流分层不能走这个执行器
        return WmsOutboundBusinessUtils.isXh(deliveryHeaderDo.getBizType(), deliveryHeaderDo.getOrderTags()) && !(
                WmsOutBoundTypeEnum.THCK.getType().equals(deliveryHeaderDo.getType()) && DeliveryHeaderUtils.ifGoodsFlow(deliveryHeaderDo.getContainsGoodsFlow()));
    }

    @Override
    public void handle(TaskReceiveRequest receiveRequest, DeliveryHeaderDo deliveryHeaderDo) {
        // 1.lpn出箱
        unPackLpnForShip(receiveRequest);
        // 2.
        reSetWarehouseCodeAndSignId(deliveryHeaderDo.getWarehouseCode(), receiveRequest.getSignId());
        String uniqueCode = receiveRequest.getTaskDetails().get(0).getUniqueCode();
        InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(uniqueCode);
        if (null == inventoryDo) {
            throw new WmsException(String.format("现货出库单库存不存在, 唯一码:%s, 出库单号:%s", uniqueCode, deliveryHeaderDo.getDeliveryOrderCode()));
        }
        //现货&个人寄存, 库存类型等真或询问, 直接扣wms库存并更新对应单据状态
        log.info("开始处理操作仓现货、个人寄存发货, 单据[{}], 唯一码[{}]", receiveRequest.getOrderCode(), uniqueCode);
        shippingService.operationWarehouseShip(receiveRequest);
    }
}
