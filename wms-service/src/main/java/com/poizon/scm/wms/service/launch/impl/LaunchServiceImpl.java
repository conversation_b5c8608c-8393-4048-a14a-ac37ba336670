package com.poizon.scm.wms.service.launch.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.TaskRepository;
import com.poizon.scm.wms.adapter.common.model.TaskDo;
import com.poizon.scm.wms.adapter.outbound.grey.repository.IGreyRepository;
import com.poizon.scm.wms.adapter.outbound.launch.model.LaunchBatchDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.LaunchDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.LaunchQueryParam;
import com.poizon.scm.wms.adapter.outbound.launch.repository.LaunchBatchRepository;
import com.poizon.scm.wms.adapter.outbound.launch.repository.LaunchRepository;
import com.poizon.scm.wms.api.dto.request.launch.*;
import com.poizon.scm.wms.api.dto.response.delivery.LaunchScm360Response;
import com.poizon.scm.wms.api.dto.response.launch.*;
import com.poizon.scm.wms.api.dto.response.task.TaskDetailAdminPrintResponse;
import com.poizon.scm.wms.api.enums.LaunchBatchStatusEnum;
import com.poizon.scm.wms.api.enums.LaunchSelectEnum;
import com.poizon.scm.wms.api.enums.LaunchStatusEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.Consts;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.dao.entitys.BaseEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryHeaderEntity;
import com.poizon.scm.wms.dao.transfer.LaunchDetailDeliveryTransfer;
import com.poizon.scm.wms.dao.utils.MybatisPlusPageUtils;
import com.poizon.scm.wms.domain.launch.create.LaunchCreationFactory;
import com.poizon.scm.wms.domain.launch.params.LaunchTailFlag;
import com.poizon.scm.wms.domain.outbound.pick.service.param.PickTaskCancelLaunchParam;
import com.poizon.scm.wms.domain.outbound.pick.service.processor.opearte.PickTaskCancelLaunch;
import com.poizon.scm.wms.domain.print.PrintStatisticalService;
import com.poizon.scm.wms.handler.launch.LaunchHandler;
import com.poizon.scm.wms.handler.launch.LaunchQueryHandler;
import com.poizon.scm.wms.pojo.launch.LaunchCreateRulePojo;
import com.poizon.scm.wms.pojo.launch.LaunchDeliveryQueryPojo;
import com.poizon.scm.wms.service.base.BaseServiceImpl;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.launch.ILaunchService;
import com.poizon.scm.wms.service.merchant.ScpMerchantService;
import com.poizon.scm.wms.service.pickup.PickupPrintService;
import com.poizon.scm.wms.util.RWDataSourceHolder;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 波次接口相关实现
 * @Date 2020/5/7 9:32 下午
 */
@Service
@Slf4j
public class LaunchServiceImpl extends BaseServiceImpl implements ILaunchService {

    @Resource
    private PickTaskCancelLaunch pickTaskCancelLaunch;

    @Autowired
    private LaunchHandler launchHandler;

    @Autowired
    private DistributeLockUtil distributeLockUtil;

    @Autowired
    private DeliveryOrderModifyService deliveryOrderModifyService;

    @Autowired
    private LaunchCreationFactory launchCreationFactory;

    @Autowired
    private ScpMerchantService scpMerchantService;

    @Autowired
    private LaunchQueryHandler launchQueryHandler;

    @Autowired
    TaskRepository taskRepository;

    @Autowired
    PickupPrintService pickupPrintService;

    @Autowired
    PrintStatisticalService printStatisticalService;

    @Autowired
    private LaunchRepository launchRepository;

    @Autowired
    private LaunchBatchRepository launchBatchRepository;

    @Autowired
    private IGreyRepository greyRepository;

    /**
     * 分页查询波次
     *
     * @param launchPageQueryRequest
     * @return
     */
    @Override
    public PagingObject<LaunchPageQueryResponse> list(LaunchPageQueryRequest launchPageQueryRequest) {
        // 校验必填参数
        validLaunchList(launchPageQueryRequest);
        PageHelper.startPage(launchPageQueryRequest.getPageNum(), launchPageQueryRequest.getPageSize(), true, false, null);
        String source = RWDataSourceHolder.get();
        // 不是归档库走根据灰度adb
        if (!Consts.DataSourceAbout.ARCHIVE.equals(source) && greyRepository.isLaunchQueryUseAdb(launchPageQueryRequest.getWarehouseCode())) {
            RWDataSourceHolder.set(Consts.DataSourceAbout.ADB);
        }
        Page<LaunchDo> launchDoPages = launchRepository.selectLaunchPages(this.buildLaunchQueryParam(launchPageQueryRequest));
        RWDataSourceHolder.set(source);
        PagingObject<LaunchPageQueryResponse> pagingObject = this.buildLaunchPagingObject(launchDoPages);
        List<String> launchNoList = pagingObject.getContents().stream().map(LaunchPageQueryResponse::getLaunchNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(launchNoList)) {
            Map<String, Long> printStatistical = printStatisticalService.queryMapByReferences(launchNoList, TaskTypeEnum.LAUNCH_PRINT.getCode());
            for (LaunchPageQueryResponse pageQueryResponse : pagingObject.getContents()) {
                pageQueryResponse.setPrintCounts(printStatistical.getOrDefault(pageQueryResponse.getLaunchNo(), 0L));
            }
        }
        return pagingObject;
    }


    private  PagingObject<LaunchPageQueryResponse> buildLaunchPagingObject(com.github.pagehelper.Page<LaunchDo> launchDoPages){
        PagingObject<LaunchPageQueryResponse> pagingObject = new PagingObject();
        List<LaunchPageQueryResponse> responseList = new ArrayList<>();
        launchDoPages.getResult().forEach( launchDo -> {
            responseList.add(convertLaunchPageResponse(launchDo));
        });
        pagingObject.setContents(responseList);
        pagingObject.setPageNum(launchDoPages.getPageNum());
        pagingObject.setPageSize(launchDoPages.getPageSize());
        pagingObject.setTotal(launchDoPages.getTotal());
        pagingObject.setPages(launchDoPages.getPages());
        return pagingObject;
    }
    private LaunchPageQueryResponse convertLaunchPageResponse(LaunchDo launchDo) {
        LaunchPageQueryResponse launchPageQueryResponse = new LaunchPageQueryResponse();
        BeanUtils.copyProperties(launchDo, launchPageQueryResponse);
        if(null != launchPageQueryResponse.getLaunchStatus() &&
                LaunchStatusEnum.PICKING_COMPLETED.getStatus() != launchPageQueryResponse.getLaunchStatus()) {
            launchPageQueryResponse.setLaunchFinishTime(null);
        }
        launchPageQueryResponse.setLaunchFinishTime(DateUtils.formatTimeForAdmin(launchDo.getLaunchFinishTime()));
        launchPageQueryResponse.setCreatedTime(DateUtils.formatTimeForAdmin(launchDo.getCreatedTime()));
        launchPageQueryResponse.setLaunchCreateType(LaunchAutomaticFlagEnum.getDescByType(launchDo.getAutomaticFlag()));
        launchPageQueryResponse.setLaunchModeStr(LaunchModeEnum.getDescByType(launchDo.getLaunchMode()));
        return launchPageQueryResponse;
    }
    /**
     * 波次列表查询，构造查询请求体
     *
     * @param launchPageQueryRequest
     * @return
     */
    private LaunchQueryParam buildLaunchQueryParam(LaunchPageQueryRequest launchPageQueryRequest) {
        LaunchQueryParam launchQueryParam = new LaunchQueryParam();
        launchQueryParam.setWarehouseCode(launchPageQueryRequest.getWarehouseCode());
        launchQueryParam.setCreatedRealName(launchPageQueryRequest.getCreatedRealName());
        launchQueryParam.setCreatedStartTime(launchPageQueryRequest.getCreatedStartTime());
        launchQueryParam.setCreatedEndTime(launchPageQueryRequest.getCreatedEndTime());
        launchQueryParam.setLaunchFinishStartTime(launchPageQueryRequest.getLaunchFinishStartTime());
        launchQueryParam.setLaunchFinishEndTime(launchPageQueryRequest.getLaunchFinishEndTime());
        launchQueryParam.setLaunchNo(launchPageQueryRequest.getLaunchNo());
        launchQueryParam.setLaunchStatus(launchPageQueryRequest.getLaunchStatus());
        launchQueryParam.setLaunchType(launchPageQueryRequest.getLaunchType());
        launchQueryParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        launchQueryParam.setAutomaticFlag(launchPageQueryRequest.getLaunchCreateType());
        launchQueryParam.setLaunchMode(launchPageQueryRequest.getLaunchMode());
        return launchQueryParam;

    }

    private Integer getBizType(Map<String, Object> bizTypeBatchMap, LaunchPageQueryResponse pageQueryResponse) {
        Integer bizType = 0;
        Object o = bizTypeBatchMap.get(pageQueryResponse.getLaunchNo());
        if (Objects.nonNull(o)) {
            try {
                bizType = (Integer) o;
            } catch (Exception e) {
                log.error("查询业务类型 数据类型转换错误。bizType类型:{}", o.getClass().getName());
            }
        }
        return bizType;
    }

    /**
     * 创建波次
     *
     * @param launchCreateRequest
     * @return
     */
    @Override
    public LaunchCreateResponse create(LaunchCreateRequest launchCreateRequest) {

        return launchCreationFactory.createLaunch(launchCreateRequest);
    }

    /**
     * 校验波次列表查询条件字段
     *
     * @param launchPageQueryRequest
     */
    private void validLaunchList(LaunchPageQueryRequest launchPageQueryRequest) {
        if (launchPageQueryRequest.getCreatedStartTime() == null
                || launchPageQueryRequest.getCreatedEndTime() == null) {
            throw new WmsOperationException(WmsExceptionCode.LAUNCH_CREATE_NOT_EMPTY);
        }

        if (launchPageQueryRequest.getCreatedStartTime() != null &&
                launchPageQueryRequest.getCreatedEndTime() != null && launchPageQueryRequest.getCreatedStartTime().
                compareTo(launchPageQueryRequest.getCreatedEndTime()) > 0) {
            throw new WmsOperationException(WmsExceptionCode.LAUNCH_CREATE_START_GT_END);
        }

        if (launchPageQueryRequest.getLaunchFinishStartTime() == null
                ^ launchPageQueryRequest.getLaunchFinishEndTime() == null) {
            throw new WmsOperationException(WmsExceptionCode.LAUNCH_FINISH_NOT_EMPTY);
        }

        if (launchPageQueryRequest.getLaunchFinishStartTime() != null &&
                launchPageQueryRequest.getLaunchFinishEndTime() != null &&
                launchPageQueryRequest.getLaunchFinishStartTime().compareTo(launchPageQueryRequest.getLaunchFinishEndTime()) > 0) {
            throw new WmsOperationException(WmsExceptionCode.LAUNCH_FINISH_START_GT_END);
        }

        int betweenDays = DateUtils.betweenDays(launchPageQueryRequest.getLaunchFinishStartTime(), launchPageQueryRequest.getLaunchFinishEndTime());
        if (betweenDays > 60) {
            throw new WmsOperationException("创建时间筛选时间间隔过长");
        }

    }

    /**
     * 波次明细分页查询
     *
     * @param launchDetailPageQueryRequest
     * @return
     */
    @Override
    public PagingObject<LaunchDetailPageQueryResponse> detailList(LaunchDetailPageQueryRequest launchDetailPageQueryRequest) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        // 日常校验波次
        LaunchDo launchEntity = launchHandler.checkLaunchExist(launchDetailPageQueryRequest.getLaunchNo(), userContext.getTenantCode());
        LaunchDeliveryQueryPojo queryPojo = LaunchDeliveryQueryPojo.builder().launchNo(launchDetailPageQueryRequest.getLaunchNo()).
                tenantCode(userContext.getTenantCode()).
                pageNum(launchDetailPageQueryRequest.getPageNum()).
                descOrder(BaseEntity.COL_CREATED_TIME).
                warehouseCode(launchEntity.getWarehouseCode()).
                pageSize(launchDetailPageQueryRequest.getPageSize()).build();
        PagingObject<LaunchDetailPageQueryResponse> wrapper =
                MybatisPlusPageUtils.wrapper(launchHandler.pageQueryDeliveryHeader(queryPojo), new LaunchDetailDeliveryTransfer());
        if (wrapper.getContents() != null) {
            Set<String> ownerCodeSet = wrapper.getContents().stream().map(LaunchDetailPageQueryResponse::getOwnerCode).collect(Collectors.toSet());
            Map<String, String> ownerMap = scpMerchantService.batchQueryName(ownerCodeSet);
            if (ownerMap != null) {
                wrapper.getContents().forEach(item -> {
                    item.setOwnerName(ownerMap.getOrDefault(item.getOwnerCode(), ""));
                });
            }
        }
        return wrapper;
    }

    /**
     * 波次查询发货单列表
     *
     * @param request
     * @return
     */
    @Override
    public PagingObject<LaunchDeliveryPageQueryResponse> deliveryList(LaunchDeliveryPageQueryRequest request) {
        return launchQueryHandler.deliveryList(request);
    }

    /**
     * 删除波次
     *
     * @param launchDeleteRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LaunchDeleteResponse delete(LaunchDeleteRequest launchDeleteRequest) {
        boolean lockFlag = false;
        try {
            if (!distributeLockUtil.tryLockForBiz(LockEnum.LAUNCH_DELETE, launchDeleteRequest.getLaunchNo())) {
                log.info("删除波次碰到拣货任务被执行的锁，参数是[{}]", JSON.toJSONString(launchDeleteRequest));
                throw new WmsException(WmsExceptionCode.LAUNCH_IS_EXECUTING_ERROR);
            }
            lockFlag = true;
            OperationUserContext userContext = OperationUserContextHolder.get();
            String launchNo = launchDeleteRequest.getLaunchNo();
            LaunchDo launchEntity = launchHandler.checkLaunchExist(launchNo,
                    userContext.getTenantCode());
            if (LaunchStatusEnum.DELETE.getStatus() == launchEntity.getLaunchStatus()
                    || launchHandler.checkPickExist(launchEntity.getLaunchNo())) {
                throw new WmsOperationException(WmsExceptionCode.LAUNCH_NOT_DELETE_ERROR);
            }

            if (LaunchModeEnum.FP_MODE.getType().equals(launchEntity.getLaunchMode())) {
                throw new WmsOperationException(WmsExceptionCode.FP_LAUNCH_NOT_DELETE_ERROR);
            }
            //等真下架类型波次不能删除
            if (OffShelvesTypeEnum.isOffShelvesType(launchEntity.getLaunchType())) {
                throw new WmsOperationException("等真询问下架类型的波次不能删除");
            }
            List<LaunchBatchDo> launchBatchEntities = launchHandler.checkLaunchBatchExist(launchNo, launchEntity.getWarehouseCode(),
                    userContext.getTenantCode());
            for (LaunchBatchDo launchBatchEntity : launchBatchEntities) {
                if (!LaunchBatchStatusEnum.cancelList.contains(launchBatchEntity.getBatchStatus())) {
                    throw new WmsException(WmsExceptionCode.LAUNCH_NOT_DELETE_ERROR);
                }
                /*CancelTaskPojo cancelTaskPojo = new CancelTaskPojo();
                cancelTaskPojo.setTaskStatusEnum(TaskStatusEnum.CANCEL);
                cancelTaskPojo.setTaskTypeEnum(TaskTypeEnum.LAUNCH_PICK);
                cancelTaskPojo.setReferenceNo(launchBatchEntity.getBatchNo());
                cancelTaskPojo.setReferenceType(LaunchTypeEnum.HBDD.getType().equals(launchBatchEntity.getBatchType()) ? LaunchTypeEnum.JYCK.getType() : launchBatchEntity.getBatchType());*/
                // 合并订单要做下特殊转换，不然后续无法找到任务。
                pickTaskCancelLaunch.cancel(PickTaskCancelLaunchParam.builder()
                        .referenceType(LaunchTypeEnum.HBDD.getType().equals(launchBatchEntity.getBatchType()) ? LaunchTypeEnum.JYCK.getType() : launchBatchEntity.getBatchType())
                        .referenceNo(launchBatchEntity.getBatchNo())
                        .build());
            }
            LaunchDo updateLaunchEntity = new LaunchDo();
            updateLaunchEntity.setLaunchNo(launchNo);
            updateLaunchEntity.setTenantCode(userContext.getTenantCode());
            updateLaunchEntity.setWarehouseCode(launchEntity.getWarehouseCode());
            updateLaunchEntity.setLaunchStatus(LaunchStatusEnum.DELETE.getStatus());
            updateLaunchEntity.setRecordVersion(launchEntity.getRecordVersion());
            launchRepository.updateByLaunchNo(updateLaunchEntity);
            LaunchBatchDo launchBatchEntity = new LaunchBatchDo();
            launchBatchEntity.setLaunchNo(launchNo);
            launchBatchEntity.setWarehouseCode(launchEntity.getWarehouseCode());
            launchBatchEntity.setTenantCode(userContext.getTenantCode());
            launchBatchEntity.setBatchStatus(LaunchBatchStatusEnum.DELETE.getStatus());
            launchBatchRepository.updateLunchBatchByLaunchNo(launchBatchEntity);
            deliveryOrderModifyService.cancelLaunchUpdateDeliveryHeader(launchNo);
        } catch (InterruptedException e) {
            log.error("interrupt, launch delete error, param is [{}]", JSON.toJSONString(launchDeleteRequest), e);
            Thread.currentThread().interrupt();
            throw new WmsException(WmsExceptionCode.LAUNCH_DELETE_ERROR);
        } catch (WmsOperationException e) {
            log.warn("launch delete error, param is [{}]", JSON.toJSONString(launchDeleteRequest), e);
            throw new WmsOperationException(WmsExceptionCode.LAUNCH_DELETE_ERROR);
        } catch (Exception e) {
            log.error("launch delete error, param is [{}]", JSON.toJSONString(launchDeleteRequest), e);
            throw new WmsException(WmsExceptionCode.LAUNCH_DELETE_ERROR);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.LAUNCH_DELETE, launchDeleteRequest.getLaunchNo());
            }
        }
        return new LaunchDeleteResponse();
    }

    /**
     * 波次查询的下拉框枚举值
     *
     * @param launchSelectRequest
     * @return
     */
    @Override
    public LaunchSelectResponse select(LaunchSelectRequest launchSelectRequest) {
        LaunchSelectResponse launchSelectResponse = new LaunchSelectResponse();
        launchSelectResponse.setParamMap(LaunchSelectEnum.queryTypeMap(launchSelectRequest.getParam()));
        return launchSelectResponse;
    }

    /**
     * 根据选择的单据判断是否需要尾单
     *
     * @param launchProcessTailOrderRequest
     * @return
     */
    @Override
    public LaunchProcessTailOrderResponse whetherProcessTailOrder(LaunchProcessTailOrderRequest
                                                                          launchProcessTailOrderRequest) {
        List<DeliveryHeaderEntity> deliveryHeaderEntities;
        if (launchHandler.whetherSelectAll(launchProcessTailOrderRequest.getSelectAll())) {
            if (null == launchProcessTailOrderRequest.getLaunchDeliveryPageQueryRequest()) {
                throw new WmsOperationException(WmsExceptionCode.WHOLE_LAUNCH_NO_PARAM_ERROR);
            }
            deliveryHeaderEntities = launchQueryHandler.queryNoPageLaunchDeliveryList(launchProcessTailOrderRequest.getLaunchDeliveryPageQueryRequest());
            if (CollectionUtils.isEmpty(deliveryHeaderEntities)) {
                throw new WmsOperationException(WmsExceptionCode.QUERY_DELIVERY_EMPTY_ERROR);
            }
            launchProcessTailOrderRequest.setDeliveryOrderCodeList(deliveryHeaderEntities.stream().map(DeliveryHeaderEntity::getDeliveryOrderCode).collect(Collectors.toList()));


        }
        if (CollectionUtils.isEmpty(launchProcessTailOrderRequest.getDeliveryOrderCodeList())) {
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_EMPTY_ERROR);
        }
        LaunchTailFlag tailFlag = launchHandler.judgeLaunchProcessTailOrder(launchProcessTailOrderRequest.getDeliveryOrderCodeList());
        Boolean tailOrderFlag = tailFlag.getWhetherProcessTailOrder();
        Boolean tailBatchFlag = tailFlag.getWhetherProcessTailBatch();
        String defaultRemark = tailFlag.getDefaultRemark();
        return LaunchProcessTailOrderResponse
                .builder()
                .whetherProcessTailOrder((byte) BooleanUtils.toInteger(tailOrderFlag))
                .whetherProcessTailBatch((byte) BooleanUtils.toInteger(tailBatchFlag))
                .defaultRemarkMsg(defaultRemark)
                .build();
    }

    @Override
    public LaunchDetailPrintResponse detailPrintList(String launchNo) {
        LaunchDetailPrintResponse response = new LaunchDetailPrintResponse();
        OperationUserContext userContext = OperationUserContextHolder.get();
        // 日常校验波次
        launchHandler.checkLaunchExist(launchNo, userContext.getTenantCode());
        List<TaskDo> pickTaskList = taskRepository.findByLaunchNo(launchNo);
        List<TaskDetailAdminPrintResponse> detailList = pickTaskList.parallelStream()
                .map(it -> pickupPrintService.detailPrintList(it.getTaskNo(), it.getType())).collect(Collectors.toList());
        response.setTaskDetails(detailList);
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean printLaunchStatistical(String launchNo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        // 日常校验波次
        launchHandler.checkLaunchExist(launchNo, userContext.getTenantCode());
        Boolean launch = printStatisticalService.savePrintStatical(launchNo, TaskTypeEnum.LAUNCH_PRINT.getCode());
        List<TaskDo> pickTaskList = taskRepository.findByLaunchNo(launchNo);
        Boolean tasks = printStatisticalService.batchSavePrintStatic(pickTaskList, TaskTypeEnum.PICK.getCode());
        return launch && tasks;
    }

    @Override
    public LaunchScm360Response queryLaunchByNo(String launchNo) {
        LaunchDo launchDo = launchRepository.findByNo(launchNo, OperationUserContextHolder.getTenantCode(), null);
        if(Objects.nonNull(launchDo)){
            return BeanUtil.copy(launchDo,LaunchScm360Response.class);
        }
        return null;
    }

    @Override
    public PagingObject<LaunchCriteriaAnalysisResponse> launchCriteriaAnalysis(LaunchCriteriaAnalysisRequest request) {
        List<LaunchDo> launchDos = launchRepository.queryForlaunchCriteriaAnalysis(request.getStartTime(), request.getEndTime(), request.getWarehouseCode());

        PagingObject<LaunchCriteriaAnalysisResponse> pagingObject = new PagingObject<>();
        if (CollectionUtils.isEmpty(launchDos)) {
            return pagingObject;
        }

        List<LaunchCriteriaAnalysisResponse> list = new ArrayList<>();
        //以仓库+创建时间年月日分组
        Map<String, List<LaunchDo>> launchMap = launchDos.stream().collect(Collectors.groupingBy(it -> it.getWarehouseCode() + DateUtils.dateToString(it.getCreatedTime(),DateUtils.FORMAT_TIME7)));

        for (Map.Entry<String, List<LaunchDo>> entry : launchMap.entrySet()) {
            list.add(buildLaunchCriteriaAnalysisResponse(entry.getValue()));
        }
        //创建时间倒序
        list.stream().sorted(Comparator.comparing(LaunchCriteriaAnalysisResponse::getLaunchCreateTime).reversed());
        //list进行内存分页
        pagingObject.setContents(list.stream().skip(request.getPageStart()).limit(request.getPageSize()).collect(Collectors.toList()));
        pagingObject.setPages(request.getPageStart());
        pagingObject.setTotal(list.size());
        pagingObject.setPageSize(request.getPageSize());
        return pagingObject;
    }

    private LaunchCriteriaAnalysisResponse buildLaunchCriteriaAnalysisResponse(List<LaunchDo> launchList) {
        LaunchCriteriaAnalysisResponse analysisResult = new LaunchCriteriaAnalysisResponse();

        int areaCodeUseTimes = 0;
        int workZoneFlagUseTimes = 0;
        int passNumUseTimes = 0;
        int levelNumUseTimes = 0;
        int rowNumUseTimes = 0;
        int columnNumUseTimes = 0;
        int ownerCodeUseTimes = 0;
        int bizTypeUseTimes = 0;
        int tagUseTimes = 0;
        int permutationTagUseTimes = 0;
        int categoryUseTimes = 0;
        int latestPickTimeUseTimes = 0;
        int latestDeliveryTimeUseTimes = 0;
        int receiverChannelUseTimes = 0;
        int salesChannelUseTimes = 0;
        int thirdLevelAddressUseTimes = 0;
        int orderCreateStartTimeUseTimes = 0;
        int goodsTagsUseTimes = 0;

        for (LaunchDo launchDo : launchList) {
            analysisResult.setWarehouseCode(launchDo.getWarehouseCode());
            analysisResult.setLaunchCreateTime(launchDo.getCreatedTime());
            LaunchCreateRulePojo createRulePojo = JsonUtils.deserialize(launchDo.getRuleInfo(), LaunchCreateRulePojo.class);
            if (CollectionUtils.isNotEmpty(createRulePojo.getAreaCodes())) {
                areaCodeUseTimes++;
            }
            if (createRulePojo.getWorkZoneFlag()) {
                workZoneFlagUseTimes++;
            }
            if (createRulePojo.getLpassNum() != null || createRulePojo.getHpassNum() != null) {
                passNumUseTimes++;
            }
            if (createRulePojo.getLlevelNum() != null || createRulePojo.getHlevelNum() != null) {
                levelNumUseTimes++;
            }
            if (createRulePojo.getLrowNum() != null || createRulePojo.getHrowsNum() != null) {
                rowNumUseTimes++;
            }
            if (createRulePojo.getHcolumnNum() != null || createRulePojo.getLcolumnNum() != null) {
                columnNumUseTimes++;
            }
            if (StringUtils.isNoneBlank(createRulePojo.getOwnerCode())) {
                ownerCodeUseTimes++;
            }
            if (StringUtils.isNoneBlank(createRulePojo.getBizTypes())) {
                bizTypeUseTimes++;
            }
            if (CollectionUtils.isNotEmpty(createRulePojo.getTagList())) {
                tagUseTimes++;
            }
            if (CollectionUtils.isNotEmpty(createRulePojo.getPermutationTagList())) {
                permutationTagUseTimes++;
            }
            if (CollectionUtils.isNotEmpty(createRulePojo.getFirstCategoryIdList())
                    ||CollectionUtils.isNotEmpty(createRulePojo.getSecondCategoryIdList())
                    || CollectionUtils.isNotEmpty(createRulePojo.getThirdCategoryIdList())) {
                categoryUseTimes++;
            }
            if (createRulePojo.getLatestPickStartTime() != null || createRulePojo.getLatestPickEndTime() != null) {
                latestPickTimeUseTimes++;
            }
            if (createRulePojo.getLatestDeliveryStartTime() != null || createRulePojo.getLatestDeliveryEndTime() != null) {
                latestDeliveryTimeUseTimes++;
            }
            if (StringUtils.isNoneBlank(createRulePojo.getReceiverChannel())) {
                receiverChannelUseTimes++;
            }
            if (StringUtils.isNoneBlank(createRulePojo.getSalesChannel())) {
                salesChannelUseTimes++;
            }
            if (CollectionUtils.isNotEmpty(createRulePojo.getThirdLevelAddress())) {
                thirdLevelAddressUseTimes++;
            }
            if (createRulePojo.getOrderCreateStartTime() != null || createRulePojo.getOrderCreateEndTime() != null) {
                orderCreateStartTimeUseTimes++;
            }
            if (CollectionUtils.isNotEmpty(createRulePojo.getGoodsTags())) {
                goodsTagsUseTimes++;
            }
        }

        analysisResult.setLaunchNum(launchList.size());
        analysisResult.setAreaCodesUseRate(new BigDecimal(areaCodeUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setWorkZoneFlagUseRate(new BigDecimal(workZoneFlagUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setPassNumUseRate(new BigDecimal(passNumUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setLevelNumUseRate(new BigDecimal(levelNumUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setRowNumUseRate(new BigDecimal(rowNumUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setColumnNumUseRate(new BigDecimal(columnNumUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setOwnerCodeUseRate(new BigDecimal(ownerCodeUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setBizTypeUseRate(new BigDecimal(bizTypeUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setTagUseRate(new BigDecimal(tagUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setPermutationTagUseRate(new BigDecimal(permutationTagUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setCategoryUseRate(new BigDecimal(categoryUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setLatestPickTimeUseRate(new BigDecimal(latestPickTimeUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setLatestDeliveryTimeUseRate(new BigDecimal(latestDeliveryTimeUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setReceiverChannelUseRate(new BigDecimal(receiverChannelUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setSalesChannelUseRate(new BigDecimal(salesChannelUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setThirdLevelAddressUseRate(new BigDecimal(thirdLevelAddressUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setOrderCreateStartTimeUseRate(new BigDecimal(orderCreateStartTimeUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        analysisResult.setGoodsTagsUseRate(new BigDecimal(goodsTagsUseTimes).divide(new BigDecimal(launchList.size()), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).toString());
        return analysisResult;
    }
}
