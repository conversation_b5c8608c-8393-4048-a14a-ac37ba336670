package com.poizon.scm.wms.service.delivery.query;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.SkuRepository;
import com.poizon.scm.wms.adapter.common.model.DeliveryTagRelationDo;
import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.repository.InvInventoryAllocatedRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.*;
import com.poizon.scm.wms.adapter.outbound.delivery.model.ext.DeliveryStatisticalDetailRspDo;
import com.poizon.scm.wms.adapter.outbound.delivery.params.DeliveryAbnormalQueryParams;
import com.poizon.scm.wms.adapter.outbound.delivery.query.DeliveryAllocatedListQueryParam;
import com.poizon.scm.wms.adapter.outbound.delivery.query.StatisticalDetailQueryParam;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryPermissionRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.query.DeliveryHeaderQueryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.resp.DeliveryAbnormalQueryResp;
import com.poizon.scm.wms.adapter.scp.model.ScpMerchantDo;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAbnormalDetailQueryRequest;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAbnormalQueryRequest;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAllocatedInventoryListRequest;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryQueryRequest;
import com.poizon.scm.wms.api.dto.response.delivery.*;
import com.poizon.scm.wms.api.enums.*;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.BeanUtil;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.common.utils.GetPropertyHelper;
import com.poizon.scm.wms.dao.entitys.DeliveryDetailEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryHeaderEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryRelatedOrdersEntity;
import com.poizon.scm.wms.dao.mappers.DeliveryDetailMapper;
import com.poizon.scm.wms.dao.mappers.DeliveryHeaderMapper;
import com.poizon.scm.wms.dao.mappers.DeliveryRelatedOrdersMapper;
import com.poizon.scm.wms.dao.utils.MybatisPlusPageUtils;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.infra.outbound.delivery.mapper.DeliveryRelatedOrdersDoExtMapper;
import com.poizon.scm.wms.pojo.delivery.DeliveryAbnormalQueryPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryAdminDetailPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryAdminQueryPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipListPojo;
import com.poizon.scm.wms.pojo.returnshelf.ReturnShelfTotalPojo;
import com.poizon.scm.wms.pojo.returnshelf.ReturnShelfTotalQueryPojo;
import com.poizon.scm.wms.pojo.task.BaseTaskPojo;
import com.poizon.scm.wms.pojo.task.TaskHeaderPojo;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.delivery.mapper.DeliveryDetailBaseService;
import com.poizon.scm.wms.service.merchant.ScpMerchantService;
import com.poizon.scm.wms.service.returnshelf.ReturnShelfService;
import com.poizon.scm.wms.service.sys.SysDictService;
import com.poizon.scm.wms.service.task.TaskQueryService;
import com.poizon.scm.wms.util.common.ItemCode;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.util.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发货后台Service
 *
 * <AUTHOR>
 * @date 2020/5/14 6:31 下午
 * @description
 */
@Service(value = "DeliveryOrderQueryService")
@Slf4j
public class DeliveryOrderQueryServiceImpl implements DeliveryOrderQueryService {
    @Autowired
    private DeliveryDetailMapper deliveryDetailMapper;
    @Autowired
    private DeliveryHeaderMapper deliveryHeaderMapper;
    @Autowired
    private DeliveryDetailBaseService deliveryDetailBaseService;
    @Autowired
    private DeliveryRelatedOrdersMapper deliveryRelatedOrdersMapper;
    @Autowired
    private TaskQueryService taskQueryService;

    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private ReturnShelfService returnShelfService;
    @Autowired
    private ScpMerchantService scpMerchantService;
    @Autowired
    private ICommodityQueryV2Service commodityQueryV2Service;
    @Autowired
    private SkuRepository skuRepository;
    @Autowired
    private DeliveryDetailRepository detailRepository;

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private IDeliveryTagRelationQueryService deliveryTagRelationQueryService;

    @Autowired
    private DeliveryPermissionRepository deliveryPermissionRepository;

    @Autowired
    private DeliveryRelatedOrdersDoExtMapper deliveryRelatedOrdersDoExtMapper;

    @Autowired
    private InvInventoryAllocatedRepository invInventoryAllocatedRepository;
    @Resource
    private DeliveryHeaderQueryRepository deliveryHeaderQueryRepository;

    @Override
    public PagingObject<DeliveryPageQueryResponse> queryDeliveryInfo(DeliveryAdminQueryPojo pojo) {
        PagingObject<DeliveryPageQueryResponse> responsePagingObject = new PagingObject<>();

        //Page格式结果查询 数量
        DeliveryInfoQueryDo queryDo = new DeliveryInfoQueryDo();
        BeanUtils.copyProperties(pojo, queryDo);
        Integer itemCount = deliveryHeaderRepository.countPageDeliveryInfo(queryDo);
        if (itemCount == null || itemCount == 0) {
            return responsePagingObject;
        }

        // 查询分页结果
        responsePagingObject.setPageNum(pojo.getPageNum());
        responsePagingObject.setPageSize(pojo.getPageSize());
        responsePagingObject.setTotal(itemCount);
        int pages = itemCount / pojo.getPageSize();
        responsePagingObject.setPages(itemCount % pojo.getPageSize() == 0 ? pages : pages + 1);
        Integer pageStart = (pojo.getPageNum() - 1) * pojo.getPageSize();
        List<DeliveryInfoResultDo> pageList = deliveryHeaderRepository.pageDeliveryInfo(queryDo, pageStart, pojo.getPageSize());

        if (CollectionUtils.isEmpty(pageList)) {
            return responsePagingObject;
        }

        //转换成H5需要的PagingObject
        List<DeliveryPageQueryResponse> responseList = BeanUtil.copyListProperties(pageList, DeliveryPageQueryResponse.class);
        responsePagingObject.setContents(responseList);

        boolean containUniqueCode = StringUtils.isNotEmpty(pojo.getUniqueCode());
        //属性值Copy不进去的字段单独复制
        List<DeliveryPageQueryResponse> contentList = copyProperties(pageList, responsePagingObject.getContents(), containUniqueCode);

        Set<String> deliveryOrderCodeSet = contentList.stream().
                map(DeliveryPageQueryResponse::getDeliveryOrderCode).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(deliveryOrderCodeSet)) {
            return new PagingObject<>();
        }
        //关联单号设置
        Map<String, DeliveryRelatedOrdersEntity> relatedOrdersEntityMap =
                queryDeliveryRelatedMapByOrderCode(deliveryOrderCodeSet);
        Map<String, Long> deliveryTagMap = deliveryHeaderRepository.queryDeliveryTagMap(deliveryOrderCodeSet);
        //其他属性值添加
        buildContentList(contentList, relatedOrdersEntityMap,deliveryTagMap);

        responsePagingObject.setContents(contentList);

        return responsePagingObject;
    }

    private Map<String, List<DeliveryTagRelationDo>> queryDeliveryTagMapByOrderCode(Set<String> deliveryOrderCodeSet) {
        if(CollectionUtils.isEmpty(deliveryOrderCodeSet)) {
            return Collections.emptyMap();
        }
        Map<String, List<DeliveryTagRelationDo>> deliveryTagMap = deliveryTagRelationQueryService.queryByOrderCodeList2Map(new ArrayList<>(deliveryOrderCodeSet));
        if(MapUtils.isEmpty(deliveryTagMap)){
            return Collections.emptyMap();
        }
        return deliveryTagMap;
    }

    @Override
    public PagingObject<DeliveryPageQueryResponse> queryRollDeliveryInfo(DeliveryAdminQueryPojo pojo) {
        DeliveryInfoQueryDo queryDo = new DeliveryInfoQueryDo();
        BeanUtils.copyProperties(pojo, queryDo);
        queryDo.setLimit(pojo.getPageSize());
        queryDo.setType(StringUtils.isNotBlank(pojo.getType()) ? Arrays.asList(pojo.getType().split(",")) : null);
        queryDo.setStatus(StringUtils.isNotBlank(pojo.getStatus()) ? Arrays.stream(pojo.getStatus().split(",")).map(Integer::parseInt).collect(Collectors.toList()) : null);
        queryDo.setBizType(StringUtils.isNotBlank(pojo.getBizType()) ? Arrays.stream(pojo.getBizType().split(",")).map(Integer::parseInt).collect(Collectors.toList()) : null);
        queryDo.setOrderTags(DeliveryTagV2Enum.getBitValByCodes(pojo.getTags()));
        List<DeliveryInfoResultDo> list = deliveryHeaderRepository.rollDeliveryInfo(queryDo);
        if (CollectionUtils.isEmpty(list)) {
            return new PagingObject<>();
        }
        if (queryDo.getFirstId() != null && queryDo.getFirstId() != 0L) {
            Collections.reverse(list);
        }

        PagingObject<DeliveryPageQueryResponse> responsePagingObject = new PagingObject<>();
        //转换成H5需要的PagingObject
        List<DeliveryPageQueryResponse> responseList = BeanUtil.copyListProperties(list, DeliveryPageQueryResponse.class);
        responsePagingObject.setContents(responseList);

        boolean containUniqueCode = StringUtils.isNotEmpty(pojo.getUniqueCode());
        //属性值Copy不进去的字段单独复制
        List<DeliveryPageQueryResponse> contentList = copyProperties(list, responsePagingObject.getContents(), containUniqueCode);

        Set<String> deliveryOrderCodeSet = contentList.stream().
                map(DeliveryPageQueryResponse::getDeliveryOrderCode).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(deliveryOrderCodeSet)) {
            return new PagingObject<>();
        }
        //关联单号设置
        Map<String, DeliveryRelatedOrdersEntity> relatedOrdersEntityMap =
                queryDeliveryRelatedMapByOrderCode(deliveryOrderCodeSet);
        Map<String, Long> deliveryTagMap = deliveryHeaderRepository.queryDeliveryTagMap(deliveryOrderCodeSet);

        //其他属性值添加
        buildContentList(contentList, relatedOrdersEntityMap, deliveryTagMap);
        /*处理清退的单据*/
        Set<String> cleanUpOrderSet = contentList.stream().filter(order -> WmsOutBoundTypeEnum.QLCK.getType().equals(order.getType())).map(DeliveryPageQueryResponse::getDeliveryOrderCode).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(cleanUpOrderSet)){
            processForCleanUpOrder(contentList,cleanUpOrderSet);
        }
        responsePagingObject.setContents(contentList);
        return responsePagingObject;
    }

    /**
     *
     * @param queryDo
     * @return
     */
    @Override
    public List<DeliveryInfoResultDo> rollDeliveryInfo2(DeliveryInfoQueryDo queryDo) {
        return deliveryHeaderRepository.rollDeliveryInfo2(queryDo);
    }


    private void processForCleanUpOrder(List<DeliveryPageQueryResponse> contentList, Set<String> cleanUpOrderSet) {
        List<DeliveryPermissionDo> deliveryPermissionDoList = deliveryPermissionRepository.batchSelectByDeliveryCodes(cleanUpOrderSet);
        Map<String, Boolean> canDeliveryMap = deliveryPermissionDoList.stream().collect(Collectors.toMap(DeliveryPermissionDo::getDeliveryOrderCode, DeliveryPermissionDo::getCanDelivery));
        /*只有清退单有值,其他单据为null*/

        contentList.forEach(content-> {
            String canDelivery = com.poizon.scm.wms.util.common.StringUtils.booleanToChineseCharacter(canDeliveryMap.get(content.getDeliveryOrderCode()));
            content.setCanDelivery(canDelivery);
        });
    }







    private void buildContentList(List<DeliveryPageQueryResponse> contentList,
                                  Map<String, DeliveryRelatedOrdersEntity> relatedOrdersEntityMap,
                                  Map<String, Long> deliveryTagMap) {
        contentList.stream().filter(Objects::nonNull).forEach(item -> {

            if (item.getStatus() != null) {
                item.setStatusCode(item.getStatus());
                item.setStatus(WmsOutBoundStatusEnum.getAllocMessage(Integer.parseInt(item.getStatus())));
            }
            if (item.getType() != null) {
                item.setTypeName(WmsOutBoundTypeEnum.getDescByType(item.getType()));
            }
            item.setBizTypeDesc(WmsBizTypeEnum.getBizTypeEnum(item.getBizType()) != null ? WmsBizTypeEnum.getBizTypeEnum(item.getBizType()).getBizName() : null);
            BaseTaskPojo queryPojo = new BaseTaskPojo();
            queryPojo.setType(TaskTypeEnum.SHIP.getTaskType());
            queryPojo.setReferenceType(item.getType());
            queryPojo.setReferenceNo(item.getDeliveryOrderCode());
            queryPojo.setWarehouseCode(item.getWarehouseCode());
            TaskHeaderPojo task = taskQueryService.getTaskHeader(queryPojo);
            String ownerName = scpMerchantService.getNameByCode(item.getOwnerCode());
            if (ownerName != null) {
                item.setOwnerName(ownerName);
            }
            if (task != null) {
                item.setTaskStatusCode(task.getStatus());
            }
            if (relatedOrdersEntityMap != null && relatedOrdersEntityMap.get(item.getDeliveryOrderCode()) != null) {
                item.setTargetEntryOrderCode(relatedOrdersEntityMap.get(item.getDeliveryOrderCode()).getRelatedOrderCode());
            }

            if(MapUtils.isNotEmpty(deliveryTagMap)){
                Long orderTags = deliveryTagMap.get(item.getDeliveryOrderCode());
                String tags = DeliveryTagV2Enum.getDescByBitVal(orderTags == null?0L:orderTags);
                item.setTags(tags);
            }
            item.setIsCombine(BooleanUtils.toInteger(StringUtils.isNotEmpty(item.getCombineType())));
            item.setCombineType(CombineTypeEnum.getDescByType(item.getCombineType()));
        });
    }

    private List<DeliveryPageQueryResponse> copyProperties(List<DeliveryInfoResultDo> pojoPageRecordList,
                                                           List<DeliveryPageQueryResponse> contentList,
                                                           boolean containUniqueCode) {
        if (containUniqueCode) {
            Map<String, DeliveryInfoResultDo> detailPojoMap =
                    pojoPageRecordList.stream().filter(item -> item.getUniqueCode() != null)
                            .collect(Collectors.toMap(item -> item.getDeliveryOrderCode() + item.getUniqueCode(), newItem -> newItem));

            contentList.stream().filter(item -> item.getUniqueCode() != null)
                    .forEach(item -> {
                        String key = item.getDeliveryOrderCode() + item.getUniqueCode();
                        item.setStatus(String.valueOf(detailPojoMap.get(key).getStatus()));
                        item.setOrderCreateTime(DateUtils.formatTimeForAdmin(detailPojoMap.get(key).getOrderCreateTime()));
                        item.setOutTime(DateUtils.formatTimeForAdmin(detailPojoMap.get(key).getOutTime()));
                        item.setInterceptTypeName(InterceptTypeEnum.getNameByCode(item.getInterceptType()));
                        item.setHandOverTime(DateUtils.formatTimeForAdmin(detailPojoMap.get(key).getHandOverTime()));
                        item.setConsigneeName(detailPojoMap.get(key).getReceiverChannel());
                        item.setLatestPickupTime(DateUtils.formatTimeForAdmin(detailPojoMap.get(key).getLatestPickupTime()));
                    });
        } else {
            Map<String, DeliveryInfoResultDo> deliveryOrderPojoMap =
                    pojoPageRecordList.stream().filter(item -> item.getDeliveryOrderCode() != null)
                            .collect(Collectors.toMap(DeliveryInfoResultDo::getDeliveryOrderCode, item -> item));
            contentList.stream().filter(item -> item.getDeliveryOrderCode() != null)
                    .forEach(item -> {
                        item.setStatus(String.valueOf(deliveryOrderPojoMap.get(item.getDeliveryOrderCode()).getStatus()));
                        item.setOrderCreateTime(DateUtils.formatTimeForAdmin(deliveryOrderPojoMap.get(item.getDeliveryOrderCode()).getOrderCreateTime()));
                        item.setOutTime(DateUtils.formatTimeForAdmin(deliveryOrderPojoMap.get(item.getDeliveryOrderCode()).getOutTime()));
                        item.setInterceptTypeName(InterceptTypeEnum.getNameByCode(item.getInterceptType()));
                        item.setHandOverTime(DateUtils.formatTimeForAdmin(deliveryOrderPojoMap.get(item.getDeliveryOrderCode()).getHandOverTime()));
                        item.setConsigneeName(deliveryOrderPojoMap.get(item.getDeliveryOrderCode()).getReceiverChannel());
                        item.setLatestPickupTime(DateUtils.formatTimeForAdmin(deliveryOrderPojoMap.get(item.getDeliveryOrderCode()).getLatestPickupTime()));
                    });
        }
        return contentList;
    }

    @Override
    public List<Object[]> queryDeliveryInfoObject(DeliveryAdminQueryPojo pojo) {

        Page<DeliveryAdminDetailPojo> pojoPage = new Page<>();

        ArrayList<DeliveryAdminDetailPojo> detailPojoArrayList = new ArrayList<>();

        int pageStartNum = 1;
        String tenantCode = OperationUserContextHolder.getTenantCode();
        while (true) {
            pojo.setTenantCode(tenantCode);
            pojo.setPageSize(1000);
            pojo.setPageNum(pageStartNum);
            Page page = MybatisPlusPageUtils.convert2MpPage(pojo);
            page.setSearchCount(false);
            Page<DeliveryAdminDetailPojo> pojoPageItem =
                    deliveryHeaderMapper.queryDeliveryInfo(page, pojo);

            if (pojoPageItem == null || pojoPageItem.getRecords() == null) {
                break;
            }

            detailPojoArrayList.addAll(pojoPageItem.getRecords());

            if (pojoPageItem.getRecords().size() < 1000) {
                break;
            }

            //分页查询来限制最多50万条
            if (pageStartNum == 500000 / 1000) {
                break;
            }

            pageStartNum++;
        }

        Set<String> deliveryOrderCodeSet = detailPojoArrayList.stream().
                map(DeliveryAdminDetailPojo::getDeliveryOrderCode).collect(Collectors.toSet());

        //关联单号设置
        Map<String, DeliveryRelatedOrdersEntity> relatedOrdersEntityMap = null;
        if (CollectionUtils.isNotEmpty(deliveryOrderCodeSet)) {
            //关联单号设置
            relatedOrdersEntityMap = queryDeliveryRelatedMapByOrderCode(deliveryOrderCodeSet);
        }

        pojoPage.setRecords(detailPojoArrayList);

        if (CollectionUtils.isEmpty(pojoPage.getRecords())) {
            return new ArrayList<>();
        } else {
            List<Object[]> responseList = new ArrayList<>();
            for (DeliveryAdminDetailPojo adminDetailPojo : pojoPage.getRecords()) {
                responseList.add(transform(adminDetailPojo, relatedOrdersEntityMap));
            }
            return responseList;
        }
    }

    /**
     * 批量根据出库单no查询header
     */
    @Override
    public List<DeliveryHeaderEntity> queryBatchDeliveryInfo(List<String> deliveryOrderCodeList) {
        QueryWrapper<DeliveryHeaderEntity> wrapper = new QueryWrapper<>();
        wrapper.in(DeliveryHeaderEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCodeList);
        wrapper.eq(DeliveryHeaderEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryHeaderMapper.selectList(wrapper);
    }

    /**
     * 批量根据出库单no查询detail
     * {
     * "delivery_order_code1"=>[{
     * DeliveryDetailEntity1
     * },{
     * DeliveryDetailEntity2
     * }],
     * "delivery_order_code2"=>[{
     * DeliveryDetailEntity1
     * },{
     * DeliveryDetailEntity2
     * }],
     * }
     *
     * @return
     */
    @Override
    public Map<String, List<DeliveryDetailEntity>> queryBatchDeliveryDetailInfo(List<String> deliveryOrderCodeList) {
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.in(DeliveryDetailEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCodeList);
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        wrapper.in(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        List<DeliveryDetailEntity> entityList = deliveryDetailBaseService.list(wrapper);
        Map<String, List<DeliveryDetailEntity>> hashMap = new HashMap<>();
        for (DeliveryDetailEntity entity : entityList) {
            if (hashMap.containsKey(entity.getDeliveryOrderCode())) {
                hashMap.get(entity.getDeliveryOrderCode()).add(entity);
            } else {
                List<DeliveryDetailEntity> list = new ArrayList<>();
                list.add(entity);
                hashMap.put(entity.getDeliveryOrderCode(), list);
            }
        }
        return hashMap;
    }


    /**
     * 分页查询已经拣货完成的出库单列表
     */
    @Override
    public Page<DeliveryHeaderEntity> queryDeliveryOrderList(DeliveryShipListPojo pojo) {
        QueryWrapper<DeliveryHeaderEntity> wrapper = new QueryWrapper<>();
        Page<DeliveryHeaderEntity> page = new Page<>(pojo.getPageNum(), pojo.getPageSize());
        wrapper.eq(DeliveryHeaderEntity.COL_WAREHOUSE_CODE, pojo.getWarehouseCode());
        wrapper.in(DeliveryHeaderEntity.COL_STATUS, pojo.getStatus());
        wrapper.eq(DeliveryHeaderEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.orderByDesc(DeliveryHeaderEntity.COL_ORDER_CREATE_TIME);
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryHeaderMapper.selectPage(page, wrapper);
    }

    /**
     * 分页查询出库单，目前使用方是波次
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public Page<DeliveryHeaderEntity> queryPageDeliveryHeaderList(QueryWrapper queryWrapper, Page<DeliveryHeaderEntity> page) {
        queryWrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryHeaderMapper.selectPage(page, queryWrapper);
    }

    /**
     * 查询出库单数量
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public Integer queryDeliveryHeaderCount(QueryWrapper queryWrapper) {
        queryWrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        Integer totalNum = deliveryHeaderMapper.selectCount(queryWrapper);
        return (null == totalNum ? 0 : totalNum);
    }

    /**
     * 查询出库单列表
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public List<DeliveryHeaderEntity> queryDeliveryHeaderList(QueryWrapper queryWrapper) {
        List<DeliveryHeaderEntity> list = deliveryHeaderMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    @Override
    public DeliveryHeaderEntity queryDeliveryInfo(String deliveryOrderCode) {
        QueryWrapper<DeliveryHeaderEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryHeaderEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCode);
        wrapper.eq(DeliveryHeaderEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryHeaderMapper.selectOne(wrapper);
    }

    @Override
    public List<DeliveryDetailEntity> queryDeliveryDetailByDeliveryCode(String deliveryOrderCode) {
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryDetailEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCode);
        wrapper.eq(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryDetailMapper.selectList(wrapper);
    }

    @Override
    public DeliveryDetailEntity queryDeliveryDetailByDetailNo(String deliveryDetailNo) {
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryDetailEntity.COL_DETAIL_NO, deliveryDetailNo);
        wrapper.eq(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryDetailMapper.selectOne(wrapper);
    }

    @Override
    public List<DeliveryDetailEntity> queryBatchDeliveryDetailByDetailNos(Set<String> detailNos) {
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.in(DeliveryDetailEntity.COL_DETAIL_NO, detailNos);
        wrapper.eq(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        return deliveryDetailMapper.selectList(wrapper);
    }

    @Override
    public Page<DeliveryDetailEntity> queryDeliveryDetailPage(String deliveryOrderCode, Integer pageNum, Integer pageSize) {
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryDetailEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCode);
        wrapper.eq(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        Page<DeliveryDetailEntity> page = new Page<>(pageNum, pageSize);
        return deliveryDetailMapper.selectPage(page, wrapper);
    }

    @Override
    public List<DeliveryRelatedOrdersEntity> queryDeliveryRelatedOrderByOrderCode(String deliveryOrderCode) {
        QueryWrapper<DeliveryRelatedOrdersEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryRelatedOrdersEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCode);
        wrapper.eq(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        return deliveryRelatedOrdersMapper.selectList(wrapper);
    }

    @Override
    public Map<String, DeliveryRelatedOrdersEntity> queryDeliveryRelatedMapByOrderCode(Set<String> deliveryOrderCodeList) {
        QueryWrapper<DeliveryRelatedOrdersEntity> wrapper = new QueryWrapper<>();
        wrapper.in(DeliveryRelatedOrdersEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCodeList);
        wrapper.eq(DeliveryRelatedOrdersEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.eq(DeliveryRelatedOrdersEntity.COL_TENANT_CODE,OperationUserContextHolder.getTenantCode());
        List<DeliveryRelatedOrdersEntity> relatedOrdersEntityList = deliveryRelatedOrdersMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(relatedOrdersEntityList)) {
            return Collections.emptyMap();
        }
        return relatedOrdersEntityList.stream().
                collect(Collectors.toMap(DeliveryRelatedOrdersEntity::getDeliveryOrderCode, item -> item, (key1, key2) -> key2));
    }

    @Override
    public DeliveryHeaderEntity queryUnPickedFinishDeliveryInfo(String deliveryOrderCode) {
        QueryWrapper<DeliveryHeaderEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        wrapper.eq(DeliveryHeaderEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCode);
        wrapper.eq(DeliveryHeaderEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.notIn(DeliveryHeaderEntity.COL_STATUS,
                Arrays.asList(WmsOutBoundStatusEnum.PICKED.getStatus(), WmsOutBoundStatusEnum.FORCE_PICKED.getStatus()));
        return deliveryHeaderMapper.selectOne(wrapper);
    }

    @Override
    public List<Object[]> reportDetailStatisticalByDay(DeliveryQueryRequest request) {
        List<DeliveryResponse> responses = exportStatisticalDetail(request);
        List<Object[]> list = new ArrayList<>();
        responses.forEach(e -> {
            Object[] obj = new Object[13];
            obj[0] = e.getDeliveryOrderCode();
            obj[1] = e.getRelatedOrderCode();
            obj[2] = e.getOrderTypeDesc();
            obj[3] = e.getTotalPlanQty();
            obj[4] = e.getLogisticsCode();
            obj[5] = e.getLogisticsName();
            list.add(obj);
        });
        return list;
    }

    @Override
    public PagingObject<DeliveryResponse> queryStatisticalDetail(DeliveryQueryRequest request) {
        return queryStatisticalDetail(request,true);
    }
    public PagingObject<DeliveryResponse> queryStatisticalDetail(DeliveryQueryRequest request,Boolean isCount){
        PagingObject<DeliveryResponse> pagingObject = new PagingObject<>();
        pagingObject.setPageNum(request.getPageNum());
        pagingObject.setPageSize(request.getPageSize());

        StatisticalDetailQueryParam queryParam = buildStatisticalDetailQueryParam(request);
        if(isCount) {
            PageHelper.startPage(request.getPageNum(), request.getPageSize(), isCount);
        }else{
            queryParam.setLimit(request.getPageSize());
        }
        List<DeliveryStatisticalDetailRspDo> detailRspDos = deliveryHeaderRepository.selectStatisticalDetail(queryParam);
        PageInfo<DeliveryStatisticalDetailRspDo> pageInfo = new PageInfo<>(detailRspDos);

        List<DeliveryStatisticalDetailRspDo> pojos = pageInfo.getList();
        if (CollectionUtils.isEmpty(pojos)) {
            return pagingObject;
        }
        Set<String> deliveryOrderCodes = pojos.stream().map(DeliveryStatisticalDetailRspDo::getDeliveryOrderCode).collect(Collectors.toSet());
        Map<String, DeliveryRelatedOrdersDo> relatedMap = queryRelatedOrderCode(deliveryOrderCodes);
        List<DeliveryResponse> receiveResponses = new ArrayList<>();
        pojos.forEach(e -> {
            DeliveryResponse deliveryResponse = new DeliveryResponse();
            BeanUtils.copyProperties(e, deliveryResponse);
            deliveryResponse.setOrderTypeDesc(WmsOutBoundTypeEnum.getDescByType(deliveryResponse.getOrderType()));
            DeliveryRelatedOrdersDo relatedOrder = relatedMap.get(deliveryResponse.getDeliveryOrderCode());
            if (relatedOrder != null) {
                deliveryResponse.setRelatedOrderCode(relatedOrder.getRelatedOrderCode());
            }
            deliveryResponse.setLogisticsName(LogisticsEnum.getNameByCode(deliveryResponse.getLogisticsCode()));
            receiveResponses.add(deliveryResponse);
        });
        pagingObject.setPages(pageInfo.getPages());
        pagingObject.setTotal(pageInfo.getTotal());
        pagingObject.setContents(receiveResponses);
        return pagingObject;
    }
    @Override
    public List<DeliveryResponse> queryListStatisticalDetail(DeliveryQueryRequest request) {
        PagingObject<DeliveryResponse> page = queryStatisticalDetail(request, false);
        return page.getContents();
    }

    private StatisticalDetailQueryParam buildStatisticalDetailQueryParam(DeliveryQueryRequest request) {
        StatisticalDetailQueryParam queryParam = new StatisticalDetailQueryParam();
        if(StringUtils.isNotBlank(request.getTenantId())){
            queryParam.setTenantCode(request.getTenantId());
        }else{
            queryParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        }
        queryParam.setWarehouseCode(request.getWarehouseCode());
        queryParam.setStartTime(request.getStartTime());
        queryParam.setEndTime(request.getEndTime());
        queryParam.setStatusList(getStatusListByCondition(request.getStatus()));
        queryParam.setWholeAllocateFlag(WmsOutBoundStatusEnum.WHOLE_ALLOCATE.name().equals(request.getStatus()));
        queryParam.setHandoverFlag(DeliveryQueryRequest.HANDOVER.equals(request.getStatus()));
        if(queryParam.isHandoverFlag()){
            queryParam.setStatusList(null);
        }
        queryParam.setLogisticsCode(request.getLogisticsCode());
        queryParam.setDeliveryTypeList(getDeliveryTypeList(request.getDeliveryType()));
        queryParam.setOrderTags(getDeliveryTag(request.getOrderTags()));
        queryParam.setExcludeOrderTags(getExcludeDeliveryTag());
        queryParam.setCreateLaunchFlag(DeliveryQueryRequest.LAUNCH.equals(request.getStatus()));
        queryParam.setOutedFlag(WmsOutBoundStatusEnum.OUTED.name().equals(request.getStatus()));
        queryParam.setLastPickedTimeList(buildLastPickedTime(request));
        queryParam.setId(request.getMinId());
        queryParam.setTenantCode(request.getTenantId());
        return queryParam;
    }
    private List<Date> buildLastPickedTime(DeliveryQueryRequest statisticalRequest) {
        if(StringUtils.isNotBlank(statisticalRequest.getLastPickedTime())){
            List<Date> lastPickedTimeList = new ArrayList<>(2);
            //如果传的默认值
            if(statisticalRequest.getLastPickedTime().equals(LatestPickupTimeEnum.DEFAULT.getTime())){
                Date lastPickedTimeOne = DateUtils.parseDateTime(statisticalRequest.getLastPickedTime(),DateUtils.FORMAT_TIME);
                lastPickedTimeList.add(lastPickedTimeOne);
            }else{
                //否则生成当天的最晚拣货时间和次日的最晚拣货时间，
                //注意，如果传的00:30:00,表示生成出来的lastPickedTimeOne实际不起作用
                if(statisticalRequest.getLastPickedTime().equals(LatestPickupTimeEnum.THREE.getTime())){
                    Date lastPickedTimeOne = DateUtils.parseDateTime(DateUtils.dateToString(statisticalRequest.getStartTime(),DateUtils.FROMAT_DATE)+" "+ statisticalRequest.getLastPickedTime(),DateUtils.FORMAT_TIME);
                    lastPickedTimeList.add(DateUtils.addDay(lastPickedTimeOne, 1));
                }else{
                    Date lastPickedTimeOne = DateUtils.parseDateTime(DateUtils.dateToString(statisticalRequest.getStartTime(),DateUtils.FROMAT_DATE)+" "+ statisticalRequest.getLastPickedTime(),DateUtils.FORMAT_TIME);
                    Date lastPickedTimeTwo = DateUtils.addDay(lastPickedTimeOne, 1);
                    lastPickedTimeList.add(lastPickedTimeOne);
                    lastPickedTimeList.add(lastPickedTimeTwo);
                }
            }
            return lastPickedTimeList;
        }
        return null;
    }
    private Long getExcludeDeliveryTag() {
        return DeliveryTagV2Enum.getBitValByCodes(Collections.singletonList(DeliveryTagV2Enum.OPERATION_PERSONAL_DEPOSIT_RETURN.getCode()));
    }

    private List<Integer> getStatusListByCondition(String status) {
        if (DeliveryQueryRequest.LAUNCH.equals(status)) {
            return Arrays.asList(WmsOutBoundStatusEnum.WHOLE_ALLOCATE.getStatus(), WmsOutBoundStatusEnum.PART_ALLOCATE.getStatus());
        }
        if (DeliveryQueryRequest.HANDOVER.equals(status) || WmsOutBoundStatusEnum.OUTED.name().equals(status)) {
            return Arrays.asList(WmsOutBoundStatusEnum.OUTED.getStatus(), WmsOutBoundStatusEnum.FORCE_OUTED.getStatus());
        }
        if (WmsOutBoundStatusEnum.PICKED.name().equals(status)) {
            return Arrays.asList(WmsOutBoundStatusEnum.PICKED.getStatus(), WmsOutBoundStatusEnum.FORCE_PICKED.getStatus());
        }
        if (!DeliveryQueryRequest.STATUS_ALL.equals(status)) {
            return Collections.singletonList(WmsOutBoundStatusEnum.valueOf(status).getStatus());
        }
        return new ArrayList<>();
    }

    private Long getDeliveryTag(String orderTags) {
        if (StringUtils.isNotBlank(orderTags)) {
            return DeliveryTagV2Enum.getBitValByCodes(Arrays.asList(orderTags.split(",")));
        }
        return null;
    }

    private List<String> getDeliveryTypeList(String deliveryType) {
        if (StringUtils.isNotBlank(deliveryType)) {
            return Arrays.asList(deliveryType.split(","));
        }
        return new ArrayList<>();
    }


    private List<DeliveryResponse> exportStatisticalDetail(DeliveryQueryRequest request) {
        List<DeliveryStatisticalDetailRspDo> list = exportQueryDeliveryStatisticalDetailRspDos(request);
        List<DeliveryResponse> receiveResponses = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            Set<String> deliveryOrderCodes = list.stream().map(DeliveryStatisticalDetailRspDo::getDeliveryOrderCode).collect(Collectors.toSet());
            Map<String, DeliveryRelatedOrdersDo> relatedMap = queryRelatedOrderCode(deliveryOrderCodes);
            list.forEach(e -> {
                DeliveryResponse deliveryResponse = new DeliveryResponse();
                BeanUtils.copyProperties(e, deliveryResponse);
                deliveryResponse.setOrderTypeDesc(WmsOutBoundTypeEnum.getDescByType(deliveryResponse.getOrderType()));
                DeliveryRelatedOrdersDo relatedOrder = relatedMap.get(deliveryResponse.getDeliveryOrderCode());
                if (relatedOrder != null) {
                    deliveryResponse.setRelatedOrderCode(relatedOrder.getRelatedOrderCode());
                }
                deliveryResponse.setLogisticsName(LogisticsEnum.getNameByCode(deliveryResponse.getLogisticsCode()));
                receiveResponses.add(deliveryResponse);
            });
        }
        return receiveResponses;
    }

    private List<DeliveryStatisticalDetailRspDo> exportQueryDeliveryStatisticalDetailRspDos(DeliveryQueryRequest request) {
        StatisticalDetailQueryParam params = buildStatisticalDetailQueryParam(request);
        params.setLimit(1000);
        List<DeliveryStatisticalDetailRspDo> list = new ArrayList<>();
        params.setId(0L);
        for (; ; ) {
            List<DeliveryStatisticalDetailRspDo> detailRspDos = deliveryHeaderRepository.selectStatisticalDetail(params);
            list.addAll(detailRspDos);
            if (CollectionUtils.isEmpty(detailRspDos) || detailRspDos.size() != NumberUtils.EXPORT_MAX_LIMIT) {
                break;
            }
            params.setId(detailRspDos.get(detailRspDos.size() - 1).getId());

        }
        return list;
    }


    /**
     * 构建请求参数
     * @param request
     */
    private void buildDeliveryAbnormalQueryRequest(DeliveryAbnormalQueryRequest request){

        if (StringUtils.isNotBlank(request.getBarcode())) {
            ItemCode itemCode = skuRepository.parse(request.getBarcode());
            if (!itemCode.isUniqueCode()) {
                request.setSkuIds(itemCode.getSkuIds());
            }
        }
        request.setTenantCode(OperationUserContextHolder.getTenantCode());
        //将>=32作为参数传下去
        List<Integer> deliveryHeadStatusList = WmsAbnormalEnum.LIST_ALL;
        request.setDeliveryHeadStatusList(deliveryHeadStatusList);
        //处理出库单 && 关联单号查出来的出库单 && 唯一码查出来的出库单 取交集
        List<String> deliveryOrderCodeList = new ArrayList<>();
        if(StringUtils.isNotBlank(request.getDeliveryOrderCode())){
            deliveryOrderCodeList.add(request.getDeliveryOrderCode());
        }
        //查询关联单号对应的出库单号
        List<String> relatedList = ListUtils.EMPTY_LIST;
        if(StringUtils.isNotBlank(request.getRelatedOrderCode())){
            relatedList = deliveryRelatedOrdersDoExtMapper.selectDeliveryOrderCodesByRelatedOrderCode(request.getRelatedOrderCode(),request.getTenantCode());
            if(CollectionUtils.isEmpty(relatedList)){
                throw new WmsOperationException(WmsExceptionCode.RELATED_ORDER_CODE_IS_EMPTY);
            }
            if(CollectionUtils.isNotEmpty(deliveryOrderCodeList)){
                deliveryOrderCodeList.retainAll(relatedList);
                if(CollectionUtils.isEmpty(deliveryOrderCodeList)){
                    throw new WmsOperationException(WmsExceptionCode.RELATED_ORDER_CODE_ERROR);
                }
            }else{
                deliveryOrderCodeList = relatedList;
            }
        }
        //查询库存分配的唯一码对应的出库单号
        List<String> inventoryDeliveryOrderList = ListUtils.EMPTY_LIST;
        if(StringUtils.isNotBlank(request.getUniqueCode())){
            List<InvInventoryAllocatedDo> list = invInventoryAllocatedRepository.queryAllocatedByUniqueCode(request.getUniqueCode(), request.getTenantCode());
            if(CollectionUtils.isEmpty(list)){
                throw new WmsOperationException(WmsExceptionCode.UNIQUE_CODE_IS_EMPTY);
            }
            inventoryDeliveryOrderList = list.stream().map(InvInventoryAllocatedDo::getShipmentNo).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(deliveryOrderCodeList)){
                deliveryOrderCodeList.retainAll(inventoryDeliveryOrderList);
                if(CollectionUtils.isEmpty(deliveryOrderCodeList)){
                    throw new WmsOperationException(WmsExceptionCode.UNIQUE_CODE_QUERY_ERROR);
                }
            }else{
                deliveryOrderCodeList = inventoryDeliveryOrderList;
            }
        }
        request.setDeliveryOrderCodeList(deliveryOrderCodeList);

    }

    @Override
    public PagingObject<DeliveryAbnormalQueryResponse> queryAbnormalList(DeliveryAbnormalQueryRequest request, Boolean searchCount) {

        com.github.pagehelper.Page<DeliveryAbnormalQueryPojo> page = null;
        try {
            DeliveryAbnormalQueryParams params = new DeliveryAbnormalQueryParams();
            BeanUtils.copyProperties(request, params);
            page = PageHelper.startPage(request.getPageNum(), request.getPageSize(), searchCount);
            List<DeliveryAbnormalQueryResp> list = deliveryHeaderRepository.selectAbnormalList(params);
            List<DeliveryAbnormalQueryResponse> responseList = buildDeliveryAbnormalQueryResponses(list);
            PagingObject objectPagingObject = new PagingObject();
            objectPagingObject.setPageSize(request.getPageSize());
            objectPagingObject.setPageNum(request.getPageNum());
            objectPagingObject.setTotal(page.getTotal());
            objectPagingObject.setContents(responseList);
            return objectPagingObject;
        } catch (Exception e) {
            throw e;
        } finally {
            if (page != null) {
                page.clear();
            }
        }
    }
    @Override
    public PagingObject<DeliveryAbnormalQueryResponse> selectAbnormalList(DeliveryAbnormalQueryRequest request, Boolean searchCount) {
        buildDeliveryAbnormalQueryRequest(request);
        return queryAbnormalList(request,searchCount);
    }
    /**
     * 构建返回结果
     * @param list
     * @return
     */
    private List<DeliveryAbnormalQueryResponse> buildDeliveryAbnormalQueryResponses(List<DeliveryAbnormalQueryResp> list) {
        List<DeliveryAbnormalQueryResponse> responseList = null;
        if (CollectionUtils.isNotEmpty(list)) {
            Set<String> deliveryOrderCodes = list.stream().map(DeliveryAbnormalQueryResp::getDeliveryOrderCode).collect(Collectors.toSet());
            Map<String, DeliveryRelatedOrdersDo> relatedMap = queryRelatedOrderCode(deliveryOrderCodes);
            responseList = list.stream().map(item -> {
                DeliveryAbnormalQueryResponse response = new DeliveryAbnormalQueryResponse();
                BeanUtils.copyProperties(item, response);
                response.setType(WmsOutBoundTypeEnum.getDescByType(item.getType()));
                response.setCommandStatusDesc(WmsOutCommandStatusEnum.getCommandDescByStatus(item.getCommandStatus()));
                response.setDeliveryStatusDesc(WmsOutBoundStatusEnum.getAllocMessage(item.getStatus()));
                response.setReturnShelfStatusDesc(DeliveryReturnShelfStatusEnum.getDescByType(item.getReturnShelfStatus()));
                response.setCreatedTime(DateUtils.formatDate(item.getCreatedTime(), DateUtils.FORMAT_TIME));
                response.setCancelTime(DateUtils.formatDate(item.getCancelTime(), DateUtils.FORMAT_TIME));
                DeliveryRelatedOrdersDo relatedDo = relatedMap.get(item.getDeliveryOrderCode());
                if(relatedDo != null){
                    response.setRelatedOrderCode(relatedDo.getRelatedOrderCode());
                }
                return response;
            }).collect(Collectors.toList());
        }
        return responseList;
    }

    /**
     * 查询关联单号，分页查询
     * @param deliveryOrderCodes
     * @return
     */
    private Map<String,DeliveryRelatedOrdersDo> queryRelatedOrderCode(Set<String> deliveryOrderCodes){
        Map<String,DeliveryRelatedOrdersDo> relatedOrderMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(deliveryOrderCodes)){
            for (int i =0;i<deliveryOrderCodes.size();i+=200){
                List<String> subList = CollUtil.sub(deliveryOrderCodes,i,i+200);
                if(CollectionUtils.isEmpty(subList)){
                    break;
                }
                List<DeliveryRelatedOrdersDo> relatedOrderList = deliveryRelatedOrdersDoExtMapper.selectRelatedOrderCodeByDeliveryOrderCodes(deliveryOrderCodes, OperationUserContextHolder.getTenantCode());

                if(CollectionUtils.isNotEmpty(relatedOrderList)){
                    Map<String, DeliveryRelatedOrdersDo> deliveryMap = relatedOrderList.stream().collect(Collectors.toMap(DeliveryRelatedOrdersDo::getDeliveryOrderCode, Function.identity(), (o1, o2) -> o1));
                   relatedOrderMap.putAll(deliveryMap);
                }
            }
        }
        return relatedOrderMap;
    }



    @Override
    public PagingObject<DeliveryAbnormalDetailResponse> queryAbnormalDetail(DeliveryAbnormalDetailQueryRequest request) {
        Page<DeliveryDetailDo> page = new Page<>(request.getPageNum(), request.getPageSize());
        detailRepository.pageQuery(page, request.getDeliveryOrderCode());
        PagingObject<DeliveryAbnormalDetailResponse> pagingObject = MybatisPlusPageUtils.wrapperIgnoreContent(page);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pagingObject;
        }
        List<DeliveryAbnormalDetailResponse> responseList = null;
        if (page != null && CollectionUtils.isNotEmpty(page.getRecords())) {
            List<DeliveryDetailEntity> deliveryDetailEntityList = this.queryDeliveryDetailByDeliveryCode(request.getDeliveryOrderCode());
            Set<String> detailEntitySet = deliveryDetailEntityList.stream().map(DeliveryDetailEntity::getSkuId).collect(Collectors.toSet());
            Map<String, SkuCommonRspDomain> commodityDetailPojoMap = commodityQueryV2Service.querySkuCommonMapBySkuIds(request.getTenantCode(), detailEntitySet);
            List<ReturnShelfTotalQueryPojo> queryPojos = page.getRecords().stream().map(item -> {
                ReturnShelfTotalQueryPojo shelfTotalQueryPojo = new ReturnShelfTotalQueryPojo();
                shelfTotalQueryPojo.setDeliveryOrderCode(item.getDeliveryOrderCode());
                shelfTotalQueryPojo.setDetailNo(item.getDetailNo());
                return shelfTotalQueryPojo;
            }).collect(Collectors.toList());
            Map<String, ReturnShelfTotalPojo> shelfTotalPojoMap = returnShelfService.queryReturnShelfTotal(queryPojos);
            responseList = page.getRecords().stream().map(item -> {
                DeliveryAbnormalDetailResponse abnormalDetailResponse = new DeliveryAbnormalDetailResponse();
                ReturnShelfTotalPojo shelfTotalPojo = shelfTotalPojoMap.get(item.getDeliveryOrderCode() + item.getDetailNo());
                SkuCommonRspDomain sku = commodityDetailPojoMap.get(item.getSkuId());
                abnormalDetailResponse.setUniqueCode(shelfTotalPojo.getUniqueCode());
                abnormalDetailResponse.setBarCode(GetPropertyHelper.getString(() -> sku.getBarcode()));
                abnormalDetailResponse.setCategory(GetPropertyHelper.getString(() -> sku.getCategoryNameLevel3()));
                abnormalDetailResponse.setGoodsArticleNumber(GetPropertyHelper.getString(() -> sku.getArtNoMain()));
                abnormalDetailResponse.setGoodsTitle(GetPropertyHelper.getString(() -> sku.getSpuName()));
                abnormalDetailResponse.setSpecs(GetPropertyHelper.getString(() -> sku.getPropertyText()));
                abnormalDetailResponse.setWaitForReturnShelfNum(shelfTotalPojo.getWaitTotal());
                abnormalDetailResponse.setCompletedReturnShelfNum(shelfTotalPojo.getOperationTotal());
                abnormalDetailResponse.setQualityLevel(sysDictService.getDictName(request.getWarehouseCode(),
                        DictTypeEnum.QUALITY_LEVEL,
                        item.getQualityLevel()));
                abnormalDetailResponse.setExtraCode(GetPropertyHelper.getString(() -> sku.getExtraCode()));
                abnormalDetailResponse.setStandardCode(GetPropertyHelper.getString(() -> sku.getStandCode()));
                return abnormalDetailResponse;
            }).collect(Collectors.toList());
        }
        pagingObject.setContents(responseList);
        return pagingObject;
    }

    @Override
    public List<Object[]> reportAbnormal(DeliveryAbnormalQueryRequest request) {
        int pageStartNum = 1;
        int pageSize = 1000;
        List<DeliveryAbnormalQueryResponse> responseList = new ArrayList<>();
        buildDeliveryAbnormalQueryRequest(request);
        while (true) {
            request.setPageSize(pageSize);
            request.setPageNum(pageStartNum);
            PagingObject<DeliveryAbnormalQueryResponse> responsePagingObject = this.queryAbnormalList(request, false);
            if (responsePagingObject == null || responsePagingObject.getContents() == null) {
                break;
            }
            responseList.addAll(responsePagingObject.getContents());
            if (responsePagingObject.getContents().size() < 1000) {
                break;
            }
            //分页查询来限制最多50万条
            if (pageStartNum == 500000 / 1000) {
                break;
            }
            pageStartNum++;
        }

        if (CollectionUtils.isEmpty(responseList)) {
            return new ArrayList<>();
        } else {
            return responseList.stream().map(item -> {
                List<Object> objects = new ArrayList<>();
                objects.add(item.getDeliveryOrderCode());
                objects.add(item.getRelatedOrderCode());
                objects.add(item.getType());
                objects.add(item.getDeliveryStatusDesc());
                objects.add(item.getCommandStatusDesc());
                objects.add(item.getReturnShelfStatusDesc());
                objects.add(item.getCreatedTime());
                objects.add(item.getCancelTime());
                return objects.toArray();
            }).collect(Collectors.toList());

        }
    }

    private Object[] transform(DeliveryAdminDetailPojo pojo, Map<String, DeliveryRelatedOrdersEntity> relatedOrdersEntityMap) {
        List<Object> tempList = new ArrayList<>();
        tempList.add(pojo.getWarehouseName());
        tempList.add(pojo.getDeliveryOrderCode());
        if (MapUtils.isNotEmpty(relatedOrdersEntityMap) && relatedOrdersEntityMap.get(pojo.getDeliveryOrderCode()) != null) {
            tempList.add(relatedOrdersEntityMap.get(pojo.getDeliveryOrderCode()).getRelatedOrderCode());
        } else {
            tempList.add(null);
        }
        tempList.add(transOrderType(pojo.getType()));
        tempList.add(transOrderStatus(pojo.getStatus()));
        tempList.add(pojo.getOwnerCode());
        tempList.add(scpMerchantService.getNameByCode(pojo.getOwnerCode()));
        tempList.add(BizTypeEnum.getBizTypeEnum(pojo.getBizType()) == null ?
                "" : BizTypeEnum.getBizTypeEnum(pojo.getBizType()).getBizName());
        tempList.add(pojo.getTotalPlanQty());
        tempList.add(pojo.getPartAllocateInv());
        tempList.add(DateUtils.formatDate(pojo.getOrderCreateTime(), DateUtils.FORMAT_TIME));
        tempList.add(DateUtils.formatDate(pojo.getOutTime(), DateUtils.FORMAT_TIME));

        return tempList.toArray();
    }

    /**
     * 转换发货单类型
     *
     * @param orderType
     * @return
     */
    private String transOrderType(String orderType) {
        String transType = "";
        if (orderType == null) {
            return transType;
        }
        if (Objects.nonNull(WmsOutBoundTypeEnum.getByType(orderType))) {
            transType = WmsOutBoundTypeEnum.getByType(orderType).getDesc();
        }
        return transType;
    }

    /**
     * 转换发货单状态
     *
     * @param orderStatus
     * @return
     */
    private String transOrderStatus(Integer orderStatus) {
        String transStatus = "";
        if (orderStatus == null) {
            return transStatus;
        }
        if (Objects.nonNull(WmsOutBoundStatusEnum.getAllocMessage(orderStatus))) {
            transStatus = WmsOutBoundStatusEnum.getAllocMessage(orderStatus);
        }
        return transStatus;
    }


    @Override
    public PagingObject<DeliveryAllocatedInventoryListResponse> queryDeliveryAllocatedInventoryPage(DeliveryAllocatedInventoryListRequest request, Boolean isSearchCount) {
        DeliveryAllocatedListQueryParam param = com.poizon.scm.wms.util.util.BeanUtil.deepCopy(request, DeliveryAllocatedListQueryParam.class);
        if(Objects.equals(NumberUtils.INTEGER_ONE,param.getXhAdditionalOrderTag())){
            //考虑现货加价购集货仓退货 type = THCK bizType=1的一个出库单
            Long orderTags = DeliveryTagV2Enum.getBitValByCodes(Lists.newArrayList(DeliveryTagV2Enum.XH_ADDITIONAL_ORDER.getStatus()));
            param.setOrderTags(orderTags);
        }
        //处理创建时间，如果填入了出库单号则抹其他查询条件。
        if (StringUtils.isNotBlank(param.getDeliveryOrderCode())){
            param.setOrderCreateStartTime(null);
            param.setOrderCreateEndTime(null);
            param.setStatusList(null);
            param.setType(null);
        }
        if(org.apache.commons.collections.CollectionUtils.isEmpty(param.getStatusList())){
            param.setStatusList(WmsOutBoundStatusEnum.CAN_CANCEL_ALLOCATE_LIST);
        }
        com.github.pagehelper.Page<DeliveryHeaderDo> page =
                PageMethod.startPage(request.getPageNum(), request.getPageSize())
                        .doSelectPage(() -> deliveryHeaderQueryRepository.queryDeliveryAllocatedList(param));
        List<DeliveryHeaderDo> deliveryHeaderDoList = page.getResult();
        PagingObject<DeliveryAllocatedInventoryListResponse> responsePagingObject = new PagingObject<>();
        if(org.springframework.util.CollectionUtils.isEmpty(deliveryHeaderDoList)){
            return responsePagingObject;
        }
        if (CollectionUtils.isNotEmpty(deliveryHeaderDoList)) {
            Set<String> deliveryOrderCodes = deliveryHeaderDoList.stream().map(DeliveryHeaderDo::getDeliveryOrderCode).collect(Collectors.toSet());
            Map<String, DeliveryRelatedOrdersEntity> relatedOrdersEntityMap = this.queryDeliveryRelatedMapByOrderCode(deliveryOrderCodes);
            Map<String, List<DeliveryDetailEntity>> detailMap = this.queryBatchDeliveryDetailInfo(new ArrayList<>(deliveryOrderCodes));
            List<DeliveryAllocatedInventoryListResponse> responseList = deliveryHeaderDoList.stream().map(item -> {
                DeliveryAllocatedInventoryListResponse response = new DeliveryAllocatedInventoryListResponse();
                if (WmsOutBoundStatusEnum.partAllocateInvList.contains(item.getStatus())) {
                    response.setReplaceButton(true);
                } else {
                    response.setReplaceButton(false);
                }
                if (WmsOutBoundStatusEnum.launchList.contains(item.getStatus())) {
                    response.setCancelAllocatedButton(true);
                } else {
                    response.setCancelAllocatedButton(false);
                }
                response.setDeliveryOrderCode(item.getDeliveryOrderCode());
                /*显示的时间要和查询时间保持一致,都是单据创建时间*/
                response.setOrderCreateTime(DateUtils.formatTimeForAdmin(item.getCreatedTime()));
                response.setLastAllocatedTime(DateUtils.formatTimeForAdmin(item.getLastAllocatedTime()));
                response.setType(WmsOutBoundTypeEnum.getDescByType(item.getType()));
                response.setPlanQty(item.getTotalPlanQty());
                response.setStatusDesc(WmsOutBoundStatusEnum.getAllocMessage(item.getStatus()));
                response.setStatus(item.getStatus());
                if (relatedOrdersEntityMap.get(item.getDeliveryOrderCode()) != null) {
                    response.setRelatedOrderCode(relatedOrdersEntityMap.get(item.getDeliveryOrderCode()).getRelatedOrderCode());
                } else {
                    response.setRelatedOrderCode("");
                }
                response.setAllocatedQty(detailMap.get(item.getDeliveryOrderCode()).stream().mapToInt(DeliveryDetailEntity::getAllocatedQty).sum());
                response.setDetailResponseList(com.poizon.scm.wms.util.util.BeanUtil.copyByList(detailMap.get(item.getDeliveryOrderCode()),DeliveryDetailExportResponse.class));
                return response;
            }).collect(Collectors.toList());
            responsePagingObject.setContents(responseList);
        } else {
            responsePagingObject = new PagingObject<>();
            responsePagingObject.setContents(Lists.newArrayList());
        }
        return responsePagingObject;
    }

    @Override
    public List<Object[]> queryDeliveryAllocatedInventory(DeliveryAllocatedInventoryListRequest request) {
        List<Object[]> responseList = new ArrayList<>();
        List<DeliveryAllocatedInventoryListResponse> allocatedInventoryResponses = new ArrayList<>();
        int pageStartNum = 1;
        while (true) {
            request.setPageSize(1000);
            request.setPageNum(pageStartNum);
            PagingObject<DeliveryAllocatedInventoryListResponse> responsePagingObject =
                    this.queryDeliveryAllocatedInventoryPage(request, false);
            if (responsePagingObject == null || responsePagingObject.getContents() == null) {
                break;
            }
            allocatedInventoryResponses.addAll(responsePagingObject.getContents());
            if (responsePagingObject.getContents().size() < 1000) {
                break;
            }

            //分页查询来限制最多50万条
            if (pageStartNum == 500000 / 1000) {
                break;
            }
            pageStartNum++;
        }
        if (CollectionUtils.isEmpty(allocatedInventoryResponses)) {
            return new ArrayList<>();
        }

        List<Object> objectList;
        if (!request.isIncludeDetail()) {
            for (DeliveryAllocatedInventoryListResponse response : allocatedInventoryResponses) {
                objectList = new ArrayList<>();
                objectList.add(response.getDeliveryOrderCode());
                objectList.add(response.getRelatedOrderCode());
                objectList.add(response.getType());
                objectList.add(response.getStatusDesc());
                objectList.add(response.getPlanQty());
                objectList.add(response.getAllocatedQty());
                objectList.add(response.getLastAllocatedTime());
                objectList.add(response.getOrderCreateTime());
                responseList.add(objectList.toArray());
            }
        } else {
            List<List<DeliveryAllocatedInventoryListResponse>> partitionList = Lists.partition(allocatedInventoryResponses,100);
            for (List<DeliveryAllocatedInventoryListResponse> headerResponseList : partitionList) {
                Set<String> skuIdSet = new HashSet<>();
                Set<String> ownerCodeSet = new HashSet<>();
                Set<String> deliveryDetailNoSet = new HashSet<>();
                headerResponseList.forEach(item -> {
                    skuIdSet.addAll(item.getDetailResponseList().stream().map(DeliveryDetailExportResponse::getSkuId).collect(Collectors.toSet()));
                    ownerCodeSet.addAll(item.getDetailResponseList().stream().map(DeliveryDetailExportResponse::getOwnerCode).collect(Collectors.toSet()));
                    deliveryDetailNoSet.addAll(item.getDetailResponseList().stream().map(DeliveryDetailExportResponse::getDetailNo).collect(Collectors.toSet()));
                });
                Map<String, SkuCommonRspDomain> commodityMap = commodityQueryV2Service.querySkuCommonMapBySkuIds((OperationUserContextHolder.getTenantCode()), skuIdSet);
                Map<String, ScpMerchantDo> scpMerchantDoMap = scpMerchantService.queryByOwnerCode(ownerCodeSet);
                List<InvInventoryAllocatedDo> allocatedList = invInventoryAllocatedRepository.queryAllocatedRecordsByShipmentDetailNos(new ArrayList<>(deliveryDetailNoSet));
                Map<String, List<InvInventoryAllocatedDo>> detailAllocatedMap = allocatedList.stream().collect(Collectors.groupingBy(InvInventoryAllocatedDo::getShipmentDetailNo));

                for (DeliveryAllocatedInventoryListResponse headerResponse : headerResponseList) {
                    List<Object> objectList2;

                    for (DeliveryDetailExportResponse detailResponse : headerResponse.getDetailResponseList()) {
                        objectList2 = new ArrayList<>();
                        //头信息
                        objectList2.add(headerResponse.getDeliveryOrderCode());
                        objectList2.add(headerResponse.getRelatedOrderCode());
                        objectList2.add(headerResponse.getType());
                        objectList2.add(headerResponse.getOrderCreateTime());
                        objectList2.add(headerResponse.getRemark());
                        objectList2.add(detailResponse.getPlanQty());
                        objectList2.add(detailResponse.getAllocatedQty());
                        //明细
                        objectList2.add(getAllocateStatusDesc(detailResponse));
                        WmsBizTypeEnum bizType = WmsBizTypeEnum.getBizType(detailResponse.getBizType());
                        objectList2.add(bizType != null? bizType.getBizName() : "");
                        List<InvInventoryAllocatedDo> detailAllocatedList = detailAllocatedMap.get(detailResponse.getDetailNo());
                        if (CollectionUtils.isNotEmpty(detailAllocatedList)) {
                            List<String> detailUniqueList = detailAllocatedList.stream().filter(item -> StringUtils.isNotBlank(item.getUniqueCode())).
                                    map(InvInventoryAllocatedDo::getUniqueCode).collect(Collectors.toList());
                            objectList2.add(String.join(",", Optional.ofNullable(detailUniqueList).orElse(Lists.newArrayList())));
                        } else {
                            objectList2.add(detailResponse.getUniqueCode());
                        }
                        SkuCommonRspDomain sku = commodityMap.get(detailResponse.getSkuId());
                        objectList2.add(sku != null ? sku.getStandCode() : "");
                        objectList2.add(sku != null ? sku.getExtraCode() : "");
                        objectList2.add(detailResponse.getOwnerCode());
                        ScpMerchantDo scpMerchantDo = scpMerchantDoMap.get(detailResponse.getOwnerCode());
                        objectList2.add(scpMerchantDo != null ? scpMerchantDo.getMerchantId() : "");
                        objectList2.add(sku != null ? sku.getArtNoMain() : "");
                        objectList2.add(sku != null ? sku.getSpuName() : "");
                        objectList2.add(sku != null ? sku.getCategoryNameLevel3() : "");
                        objectList2.add(sysDictService.getDictName(detailResponse.getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL,detailResponse.getQualityLevel()));
                        objectList2.add(sku != null ? sku.getPropertyText() : "");
                        objectList2.add(sku != null ? sku.getBrandName() : "");

                        responseList.add(objectList2.toArray());
                    }
                }

            }
        }


        return responseList;
    }

    /**
     * 推断明细状态
     *
     * @param deliveryDetailEntity
     * @return
     */
    private String getAllocateStatusDesc(DeliveryDetailExportResponse deliveryDetailEntity) {
        if (WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(deliveryDetailEntity.getCancelFlag())) {
            return WmsOutBoundStatusEnum.CANCEL.getDesc();
        }
        if (deliveryDetailEntity.getAllocatedQty() == 0) {
            return WmsOutBoundStatusEnum.INIT.getDesc();
        } else if (!deliveryDetailEntity.getAllocatedQty().equals(deliveryDetailEntity.getPlanQty())) {
            return WmsOutBoundStatusEnum.PART_ALLOCATE.getDesc();
        } else {
            return WmsOutBoundStatusEnum.WHOLE_ALLOCATE.getDesc();
        }
    }

    @Override
    public DeliveryRelatedOrdersEntity queryDeliverOrderByRelatedOrder(String deliverRelateOrderCode) {
        QueryWrapper<DeliveryRelatedOrdersEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryRelatedOrdersEntity.COL_RELATED_ORDER_CODE, deliverRelateOrderCode);
        return deliveryRelatedOrdersMapper.selectOne(wrapper);
    }

    @Override
    public List<DeliveryRelatedOrdersEntity> queryDeliverRelatedList(List<String> orderNoList) {
        QueryWrapper<DeliveryRelatedOrdersEntity> wrapper = new QueryWrapper<>();
        wrapper.in(DeliveryRelatedOrdersEntity.COL_DELIVERY_ORDER_CODE, orderNoList);
        return deliveryRelatedOrdersMapper.selectList(wrapper);
    }


    @Override
    public List<DeliveryDetailDo> queryDetailByDeliveryCodeAndOwnerCode(List<String> deliveryOrderCodeList, String ownerCode) {
        return detailRepository.queryDetailByDeliveryCodeAndOwnerCode(deliveryOrderCodeList,ownerCode);
    }
}
