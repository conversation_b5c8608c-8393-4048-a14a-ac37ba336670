package com.poizon.scm.wms.service.log;

import com.alibaba.fastjson.JSON;
import com.dewu.executor.annotation.EventualConsistency;
import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.common.model.BasLocationDo;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.domain.notify.offShelf.message.NotifyLmsMessage;
import com.poizon.scm.wms.infra.outbound.config.OutboundArkConfig;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.service.base.BasLocationService;
import com.poizon.scm.wms.service.log.param.PieceCountParam;
import com.poizon.scm.wms.service.log.param.UserPieceCountItemReportBatchParams;
import com.poizon.scm.wms.service.log.param.UserPieceCountItemReportParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class PieceCountService {
    @Resource
    private OutboundArkConfig outboundArkConfig;
    @Autowired
    private SendMessageHandler sendMessageHandler;

    @Autowired
    private BasLocationService basLocationService;

    public void eventNotifyLms(PieceCountParam preceCountParam) {
        SendMessage<NotifyLmsMessage> sendMessage = new SendMessage<>();
        sendMessage.setTopic(RocketMqConstants.ScmLmsTopic.TOPIC_NAME);
        sendMessage.setTag(RocketMqConstants.ScmLmsTopic.TAG);
        try {
            sendMessage.setMessageKey(preceCountParam.getUniqueKey());
            sendMessage.setMessageContent(buildSendLmsMsg(preceCountParam));
            String resultMessageId = sendMessageHandler.process(sendMessage);
            log.info("通知LMS LOG,消息体:{},messageId:{}", JSON.toJSONString(sendMessage), resultMessageId);
            if (StringUtils.isBlank(resultMessageId)) {
                throw new WmsException("通知LMS LOG失败");
            }
        } catch (Exception e) {
            log.warn("通知LMS LOG失败", e);
        }
    }

    NotifyLmsMessage buildSendLmsMsg(PieceCountParam preceCountParam) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        NotifyLmsMessage notifyLmsMessage = new NotifyLmsMessage();
        notifyLmsMessage.setUniqueKey(preceCountParam.getUniqueKey());
        notifyLmsMessage.setCount(preceCountParam.getCount());
        notifyLmsMessage.setOperationTime(preceCountParam.getOperationTime());
        notifyLmsMessage.setSignId(userContext.getSignId());
        notifyLmsMessage.setUserId(userContext.getUserId());
        notifyLmsMessage.setTenantId(userContext.getTenantCode());
        notifyLmsMessage.setOperateWarehouseCode(userContext.getWarehouseCode());
        notifyLmsMessage.setSkuId(preceCountParam.getSkuId());
        notifyLmsMessage.setReferenceNo(preceCountParam.getReferenceNo());
        notifyLmsMessage.setReferenceDetailNo(preceCountParam.getReferenceDetailNo());
        notifyLmsMessage.setReferenceType(preceCountParam.getReferenceType());
        notifyLmsMessage.setAppId(preceCountParam.getAppId());

        String locationCode = preceCountParam.getLocationCode();
        if (StringUtils.isNotBlank(locationCode)) {
            BasLocationDo basLocationEntity = basLocationService.detailByCode(
                    userContext.getWarehouseCode(),
                    locationCode
            );
            if (basLocationEntity != null) {
                notifyLmsMessage.setStatisticsPoint(preceCountParam.getStatisticsPoint() + basLocationEntity.getLevelNum());
            }
        } else {
            notifyLmsMessage.setStatisticsPoint(preceCountParam.getStatisticsPoint());
        }

        List<NotifyLmsMessage.LmsPieceCountItem> subCounts = preceCountParam.getSubCounts();
        notifyLmsMessage.setSubCounts(CollectionUtils.isNotEmpty(subCounts) ? subCounts : buildLmsPieceCountItems(preceCountParam));

        List<NotifyLmsMessage.LmsPieceCountDetail> detailContents = preceCountParam.getDetailContents();
        notifyLmsMessage.setDetailContents(CollectionUtils.isNotEmpty(detailContents)
                ? detailContents : buildLmsPieceCountDetailList(preceCountParam));
        return notifyLmsMessage;
    }

    private List<NotifyLmsMessage.LmsPieceCountDetail> buildLmsPieceCountDetailList(PieceCountParam preceCountParam) {
        List<NotifyLmsMessage.LmsPieceCountDetail> lmsPieceCountDetails = Lists.newArrayList();
        NotifyLmsMessage.LmsPieceCountDetail lmsPieceCountDetail = new NotifyLmsMessage.LmsPieceCountDetail();
        lmsPieceCountDetail.setUniqueCode(preceCountParam.getUniqueCode());
        lmsPieceCountDetail.setSkuId(preceCountParam.getSkuId());
        lmsPieceCountDetail.setWmsTaskNo(preceCountParam.getWmsTaskNo());
        lmsPieceCountDetails.add(lmsPieceCountDetail);
        return lmsPieceCountDetails;
    }

    @EventualConsistency(referenceNo = "#userPieceCountItemReportParams.uniqueKey")
    public void pieceCountMsg(UserPieceCountItemReportParams userPieceCountItemReportParams) {
        SendMessage<UserPieceCountItemReportParams> sendMessage = new SendMessage<>();
        sendMessage.setTopic(RocketMqConstants.ScmLmsTopic.TOPIC_NAME);
        sendMessage.setTag(RocketMqConstants.ScmLmsTopic.TAG);
        sendMessage.setMessageKey(userPieceCountItemReportParams.getUniqueKey());
        sendMessage.setMessageContent(userPieceCountItemReportParams);
        String resultMessageId = sendMessageHandler.process(sendMessage);
        log.info("计时计件发送消息:{},messageId:{}", JSON.toJSONString(sendMessage), resultMessageId);
    }

    @EventualConsistency(referenceNo = "#userPieceCountItemReportBatchParams.batchNo")
    public void batchPieceCountMsg(UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParams) {
        SendMessage<UserPieceCountItemReportParams> sendMessage = new SendMessage<>();
        sendMessage.setTopic(RocketMqConstants.ScmLmsTopic.TOPIC_NAME);
        sendMessage.setTag(RocketMqConstants.ScmLmsTopic.TAG);
        for (UserPieceCountItemReportParams userPieceCountItemReportParams : userPieceCountItemReportBatchParams.getUserPieceCountItemReportParams()) {
            if (outboundArkConfig.specialOrder007RepositoryCodeList.contains(userPieceCountItemReportParams.getOperateWarehouseCode())) {
                //007仓不发计件消息
                continue;
            }
            userPieceCountItemReportParams.setComSignId(NumberUtils.LONG_ZERO);
            sendMessage.setMessageKey(userPieceCountItemReportParams.getUniqueKey());
            sendMessage.setMessageContent(userPieceCountItemReportParams);
            String resultMessageId = sendMessageHandler.process(sendMessage);
            log.info("批量计时计件发送消息:messageId:{}", resultMessageId);
        }
    }

    private List<NotifyLmsMessage.LmsPieceCountItem> buildLmsPieceCountItems(PieceCountParam preceCountParam) {
        List<NotifyLmsMessage.LmsPieceCountItem> lmsPieceCountItems = new ArrayList<>();
        lmsPieceCountItems.add(buildLmsPieceCountItem(preceCountParam));
        return lmsPieceCountItems;
    }

    private NotifyLmsMessage.LmsPieceCountItem buildLmsPieceCountItem(PieceCountParam preceCountParam) {
        String statisticsPoint = preceCountParam.getStatisticsPoint();
        NotifyLmsMessage.LmsPieceCountItem lmsPieceCountItem = new NotifyLmsMessage.LmsPieceCountItem();
        lmsPieceCountItem.setCount(preceCountParam.getCount());
        lmsPieceCountItem.setOperationTime(
                DateUtils.dateToString(new Date(), DateUtils.FORMAT_TIME)
        );
        lmsPieceCountItem.setStatisticsPoint(statisticsPoint);
        return lmsPieceCountItem;
    }
}
