package com.poizon.scm.wms.domain.outbound.ship.processor.impl;

import com.alibaba.fastjson.JSONArray;
import com.dewu.doctor.annotation.ZsScan;
import com.dewu.scm.lms.api.interfaces.dto.AssistUserDetailV2Dto;
import com.poizon.scm.ofc.sdk.model.enums.InboundTipsEnum;
import com.poizon.scm.pink.sdk.enums.GoodsLabel;
import com.poizon.scm.pink.sdk.enums.OperationLinkEnum;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryLogisticDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryLogisticRepository;
import com.poizon.scm.wms.adapter.outbound.mes.param.MesDeliveryBeforeParam;
import com.poizon.scm.wms.adapter.outbound.mes.param.MesUserCommonParam;
import com.poizon.scm.wms.adapter.outbound.mes.result.MesDeliveryBeforeResult;
import com.poizon.scm.wms.adapter.outbound.mes.result.MesDeliveryOrderInfoResult;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.third.lms.LmsUserService;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.domain.outbound.print.validate.impl.RegularSingleOrderMultiPackageShipmentPrintValidator;
import com.poizon.scm.wms.domain.outbound.ship.param.WmsDeliveryOrderShipmentParam;
import com.poizon.scm.wms.domain.outbound.ship.processor.AbsDeliveryOrderShipmentProcessor;
import com.poizon.scm.wms.domain.outbound.ship.processor.DeliveryOrderShipmentProcessor;
import com.poizon.scm.wms.domain.outbound.ship.result.WmsDeliveryOrderShipmentResult;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.log.PieceCountService;
import com.poizon.scm.wms.service.log.param.UserPieceCountItemReportBatchParams;
import com.poizon.scm.wms.service.log.param.UserPieceCountItemReportParams;
import com.poizon.scm.wms.util.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 普通正向交易一单多包裹发货逻辑
 *
 * <AUTHOR>
 * @date 2023/11/29 21:16
 * @description 普通正向交易一单多包裹发货逻辑
 * @Version 1.0
 */
@Slf4j
@Component("SHIPMENT_REGULAR_POSITIVE_SINGLEORDER_SINGLEITEM_MULTIPACKAGE_PROCESSOR")
public class RegularSingleOrderMultiPackageShipmentProcessor extends AbsDeliveryOrderShipmentProcessor implements DeliveryOrderShipmentProcessor {
    @Resource
    private ICommodityQueryV2Service iCommodityQueryV2Service;
    @Resource
    private PieceCountService pieceCountService;
    @Resource
    private LmsUserService lmsUserService;
    @Autowired
    private DeliveryLogisticRepository deliveryLogisticRepository;
    @Resource
    private RegularSingleOrderMultiPackageShipmentPrintValidator deliveryOrderShipmentPrintValidator;
    /**
     * 出口在本地发货的仓库
     */
    @Value("#{'${way.out.local.delivery.bs.warehouse:FT01}'.split(',')}")
    private List<String> wayOutLocalDeliveryBsWarehouseList;

    /**
     * 发货逻辑
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @ZsScan(zsCases = {"zs_wms_jyck_ship_one"})
    public WmsDeliveryOrderShipmentResult shipment(WmsDeliveryOrderShipmentParam wmsDeliveryOrderShipmentParam) {
        DeliveryHeaderDo deliveryHeader = wmsDeliveryOrderShipmentParam.getDeliveryHeader();
        List<DeliveryDetailDo> deliveryDetails = wmsDeliveryOrderShipmentParam.getDeliveryDetailList();
        List<WmsPackDo> wmsPackList = wmsDeliveryOrderShipmentParam.getWmsPacks();
        List<WmsLogisticsBillDo> wmsLogisticsBills = wmsDeliveryOrderShipmentParam.getWmsLogisticsBills();
        //发货前查询mes相关透传信息
        MesDeliveryBeforeParam mesDeliveryBeforeParam = buildMesDeliveryBeforeParam(wmsDeliveryOrderShipmentParam);
        MesDeliveryBeforeResult mesDeliveryBeforeResult = deliveryBeforeNotify(mesDeliveryBeforeParam);
        //计时计件
        pieceCount(deliveryHeader, deliveryDetails, wmsPackList, wmsDeliveryOrderShipmentParam);
        //执行通用发货逻辑
        commonShipment(mesDeliveryBeforeResult, wmsDeliveryOrderShipmentParam, Boolean.TRUE);
        //操作日志
        DeliveryDetailDo deliveryDetail = deliveryDetails.get(0);
        sendMesLogMessageAsync(deliveryDetail.getTradeOrderNo(),wmsDeliveryOrderShipmentParam.getMesUserCommonParam());
        //视频埋点
        notifyMesVideoOperateLog(deliveryDetails,wmsDeliveryOrderShipmentParam.getMesUserCommonParam(),OperationLinkEnum.FANGWEI_FUJIAN);
        //构建返回参数
        WmsDeliveryOrderShipmentResult wmsDeliveryOrderShipmentResult = new WmsDeliveryOrderShipmentResult();
        wmsDeliveryOrderShipmentResult.setSupportValue(isSupportValue(wmsLogisticsBills));
        wmsDeliveryOrderShipmentResult.setGray(Boolean.TRUE);
        //wmsDeliveryOrderShipmentResult.setShipTips(buildShipTips(wmsDeliveryOrderShipmentParam));
        fillShipTips(wmsDeliveryOrderShipmentResult, wmsDeliveryOrderShipmentParam);
        return wmsDeliveryOrderShipmentResult;
    }

    private void fillShipTips(WmsDeliveryOrderShipmentResult wmsDeliveryOrderShipmentResult, WmsDeliveryOrderShipmentParam wmsDeliveryOrderShipmentParam) {
        boolean hitGray = deliveryOrderShipmentPrintValidator.hitGray(wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getOperationRepositoryCode(), wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getUserId());
        if (!hitGray) {
            wmsDeliveryOrderShipmentResult.setShipTips(StringUtils.EMPTY);
            return;
        }
        DeliveryLogisticDo deliveryLogisticDo = deliveryLogisticRepository.queryFirstOneByDeliveryOrderCode(wmsDeliveryOrderShipmentParam.getDeliveryHeader().getDeliveryOrderCode());
        if (Objects.equals(deliveryLogisticDo.getCrossFlag(), InboundTipsEnum.OVERSEAS_B.getType())
                && !deliveryOrderShipmentPrintValidator.isTemporaryExportShipmentSku(wmsDeliveryOrderShipmentParam.getDeliveryHeader(), wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getOperationRepositoryCode())
                && !wayOutLocalDeliveryBsWarehouseList.contains(wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getOperationRepositoryCode())
        ) {
            wmsDeliveryOrderShipmentResult.setShipTips("请移交至G1仓");
            return;
        }
        DeliveryHeaderDo deliveryHeader = wmsDeliveryOrderShipmentParam.getDeliveryHeader();
        List<String> orderTagList = DeliveryTagV2Enum.getCodesByBitVal(deliveryHeader.getOrderTags());
        if (orderTagList.contains(DeliveryTagV2Enum.CUSTOMS_INSPECTION.getCode())) {
            wmsDeliveryOrderShipmentResult.setShipTips("海关查验,请流转至查验区！");
            wmsDeliveryOrderShipmentResult.setActionWindowFlag(Boolean.TRUE);
            return;
        }
        wmsDeliveryOrderShipmentResult.setShipTips(StringUtils.EMPTY);
    }


    /**
     * 计时计件
     *
     * @param deliveryHeader
     * @param deliveryDetails
     * @param wmsDeliveryOrderShipmentParam
     */
    @ZsScan(zsCases = {"zs_wms_jyck_ship_pieceCount_one"})
    public void pieceCount(DeliveryHeaderDo deliveryHeader,
                            List<DeliveryDetailDo> deliveryDetails,
                            List<WmsPackDo> wmsPackList,
                            WmsDeliveryOrderShipmentParam wmsDeliveryOrderShipmentParam) {
        Map<String, MesDeliveryOrderInfoResult.MesDeliveryOrderInfo> mesDeliveryOrderInfoMap = wmsDeliveryOrderShipmentParam.getMesDeliveryOrderInfoMap();
        DeliveryDetailDo deliveryDetail = deliveryDetails.get(0);
        String tradeOrderNo = deliveryDetail.getTradeOrderNo();
        if(WmsOutBoundSubTypeEnum.SET_SALES.getCode().equals(deliveryHeader.getSubType())){
            tradeOrderNo = deliveryDetail.getDeliveryOrderCode();
        }
        MesDeliveryOrderInfoResult.MesDeliveryOrderInfo mesDeliveryOrderInfo = mesDeliveryOrderInfoMap.get(tradeOrderNo);
        String goodsLabel = mesDeliveryOrderInfo.getGoodsLabel();
        String moduleCode = wmsDeliveryOrderShipmentParam.getModuleCode();
        String statisticsPoint = "";
        //合并发货计时计件
        if (Objects.equals(GoodsLabel.GUOCHAO.getCode(), goodsLabel)) {
            UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParams = buildUserPieceCountItemReportBatchParams(deliveryHeader, deliveryDetails,
                    "DELIVER_GC", wmsDeliveryOrderShipmentParam, wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getUserId(), wmsDeliveryOrderShipmentParam.getMesUserCommonParam(), null);
            pieceCountService.batchPieceCountMsg(userPieceCountItemReportBatchParams);
        }
        if (Objects.equals(moduleCode, MesModuleEnums.PIN_PAI_FANG_WEI.getModuleCode())) {
            statisticsPoint = MesModuleEnums.PIN_PAI_FANG_WEI.name();
        } else if (Objects.equals(moduleCode, MesModuleEnums.HD_PIN_PAI_FANG_WEI.getModuleCode())) {
            statisticsPoint = MesModuleEnums.HD_PIN_PAI_FANG_WEI.name();
        }
        if (StringUtils.isNotBlank(statisticsPoint)) {
            UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParams = buildUserPieceCountItemReportBatchParams(deliveryHeader, deliveryDetails,
                    statisticsPoint, wmsDeliveryOrderShipmentParam, wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getUserId(), wmsDeliveryOrderShipmentParam.getMesUserCommonParam(), null);
            pieceCountService.batchPieceCountMsg(userPieceCountItemReportBatchParams);
        }

        //525 需求 如果开了直接打包 记录产能为正向包装
        if (wmsDeliveryOrderShipmentParam.getOpenPack()) {
            Long assistMasterUserId = null;
            String uniqueKey = null;
            WmsPackDo wmsPack = wmsPackList.get(0);
            //查找主作业人员绑定的打包员userId
            List<AssistUserDetailV2Dto> assistUserList = lmsUserService.getAssistUserInfo(wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getUserId(), "ANTIFAKE_COUNT");
            log.info("绑定打包员信息:{}", JSONArray.toJSONString(assistUserList));
            if (CollectionUtils.isNotEmpty(assistUserList)) {
                for (AssistUserDetailV2Dto assistUserDetailV2Dto : assistUserList) {
                    //查找 打包辅助人员
                    if ("OPEN_BOX_PACKAGE_USER_COUNT".equals(assistUserDetailV2Dto.getWorkNodeCode()) && assistUserDetailV2Dto.getAssistUserId() != null) {
                        assistMasterUserId = assistUserDetailV2Dto.getAssistUserId();
                    }
                }
            }else{
                assistMasterUserId = wmsDeliveryOrderShipmentParam.getMesUserCommonParam().getUserId();
            }
            uniqueKey = wmsPack.getExpressCode();
            UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParam = buildUserPieceCountItemReportBatchParams(deliveryHeader, deliveryDetails,
                    "FORWARD_PACKAGE", wmsDeliveryOrderShipmentParam, assistMasterUserId, wmsDeliveryOrderShipmentParam.getMesUserCommonParam(), uniqueKey);
            pieceCountService.batchPieceCountMsg(userPieceCountItemReportBatchParam);
        }
    }

    /**
     * 组装计件消息
     *
     * @param deliveryHeader
     * @param deliveryDetails
     * @param statisticsPoint
     * @param wmsDeliveryOrderShipmentParam
     * @return {@link UserPieceCountItemReportBatchParams}
     */
    protected UserPieceCountItemReportBatchParams buildUserPieceCountItemReportBatchParams(DeliveryHeaderDo deliveryHeader,
                                                                                           List<DeliveryDetailDo> deliveryDetails,
                                                                                           String statisticsPoint,
                                                                                           WmsDeliveryOrderShipmentParam wmsDeliveryOrderShipmentParam,
                                                                                           Long assistMasterUserId,
                                                                                           MesUserCommonParam mesUserCommonParam,
                                                                                           String uniqueKey) {

        Map<String, MesDeliveryOrderInfoResult.MesDeliveryOrderInfo> mesDeliveryOrderInfoMap = wmsDeliveryOrderShipmentParam.getMesDeliveryOrderInfoMap();
        Set<String> skuIdSet = deliveryDetails.stream().map(DeliveryDetailDo::getSkuId).collect(Collectors.toSet());
        Map<String, SkuCommonRspDomain> skuInfoMap = iCommodityQueryV2Service.queryFullInfoSkuCommonRspDomainMap(deliveryHeader.getTenantCode(), new HashSet<>(skuIdSet));
        List<UserPieceCountItemReportParams> userPieceCountItemReportParamsList = new ArrayList<>();
        UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParams = new UserPieceCountItemReportBatchParams();
        userPieceCountItemReportBatchParams.setBatchNo(deliveryHeader.getDeliveryOrderCode());
        userPieceCountItemReportBatchParams.setUserPieceCountItemReportParams(userPieceCountItemReportParamsList);
        List<UserPieceCountItemReportParams.LmsPieceCountDetail> pieceCountDetails = null;
        UserPieceCountItemReportParams.LmsPieceCountDetail lmsPieceCountDetail = null;
        UserPieceCountItemReportParams userPieceCountItemReportParams = null;
        for (DeliveryDetailDo deliveryDetail : deliveryDetails) {
            String tradeOrderNo = deliveryDetail.getTradeOrderNo();
            if(WmsOutBoundSubTypeEnum.SET_SALES.getCode().equals(deliveryHeader.getSubType())){
                tradeOrderNo = deliveryDetail.getTradeOrderNo();
            }
            MesDeliveryOrderInfoResult.MesDeliveryOrderInfo mesDeliveryOrderInfo = mesDeliveryOrderInfoMap.get(tradeOrderNo);
            SkuCommonRspDomain skuCommonRspDomain = skuInfoMap.get(deliveryDetail.getSkuId());
            userPieceCountItemReportParams = new UserPieceCountItemReportParams();
            userPieceCountItemReportParams.setTenantId(deliveryHeader.getTenantCode());
            userPieceCountItemReportParams.setSignId(mesUserCommonParam.getSignId());
            userPieceCountItemReportParams.setUserId(mesUserCommonParam.getUserId());
            userPieceCountItemReportParams.setAssistMasterUserId(assistMasterUserId);
            if (StringUtils.isNotBlank(uniqueKey)) {
                userPieceCountItemReportParams.setUniqueKey(uniqueKey);
            } else {
                userPieceCountItemReportParams.setUniqueKey(mesDeliveryOrderInfo.getUniqueCode());
            }
            userPieceCountItemReportParams.setStatisticsPoint(statisticsPoint);
            userPieceCountItemReportParams.setOperationTime(new Date());
            if (Objects.nonNull(skuCommonRspDomain)) {
                userPieceCountItemReportParams.setCategoryId(skuCommonRspDomain.getCategoryIdLevel1());
                userPieceCountItemReportParams.setCategoryName(skuCommonRspDomain.getCategoryNameLevel1());
                userPieceCountItemReportParams.setLevel3CategoryId(skuCommonRspDomain.getCategoryIdLevel3());
                userPieceCountItemReportParams.setLevel3CategoryName(skuCommonRspDomain.getCategoryNameLevel3());
            }
            if (WmsBizTypeEnum.JIAN_BIE.getBizType().equals(deliveryHeader.getBizType())) {
                userPieceCountItemReportParams.setCategoryId(String.valueOf(deliveryDetail.getLevel1CategoryId()));
                userPieceCountItemReportParams.setCategoryName(deliveryDetail.getLevel1CategoryName());
            }
            userPieceCountItemReportParams.setCount(NumberUtils.INTEGER_ONE);
            userPieceCountItemReportParams.setOperateWarehouseCode(mesUserCommonParam.getOperationRepositoryCode());
            userPieceCountItemReportParams.setReferenceType(PieceCountReferenceTypeEnum.OUTBOUND_WORK_ORDER.getCode());
            userPieceCountItemReportParams.setReferenceNo(deliveryHeader.getExternalOrderCode());
            userPieceCountItemReportParams.setReferenceDetailNo(deliveryDetail.getExternalDetailCode());
            userPieceCountItemReportParams.setSkuId(deliveryDetail.getSkuId());
            userPieceCountItemReportParams.setAppId("PINK");
            pieceCountDetails = new ArrayList<>();
            lmsPieceCountDetail = new UserPieceCountItemReportParams.LmsPieceCountDetail();
            lmsPieceCountDetail.setUniqueCode(mesDeliveryOrderInfo.getUniqueCode());
            lmsPieceCountDetail.setBizType(deliveryDetail.getBizType());
            pieceCountDetails.add(lmsPieceCountDetail);
            userPieceCountItemReportParams.setDetailContents(pieceCountDetails);
            userPieceCountItemReportParamsList.add(userPieceCountItemReportParams);
        }
        return userPieceCountItemReportBatchParams;
    }

}
