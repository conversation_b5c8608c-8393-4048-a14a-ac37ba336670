package com.poizon.scm.wms.service.inventory.operate;

import com.alibaba.fastjson.JSON;
import com.dewu.executor.annotation.Delay;
import com.dewu.executor.annotation.EventualConsistency;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.poizon.scm.cis.api.inventory.CisDimensionDetailRequest;
import com.poizon.scm.cis.api.inventory.CisSubtractRequest;
import com.poizon.scm.cis.api.inventory.WmsAllocatedDetailDto;
import com.poizon.scm.wms.adapter.inbound.entry.model.EntryDetailExtensionDo;
import com.poizon.scm.wms.adapter.inbound.entry.repository.db.query.EntryDetailExtensionRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryBatchPropertiesDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.model.QueryUnifiedInventoryReverseFinishTimeResult;
import com.poizon.scm.wms.adapter.inventory.param.QueryUnifiedInventoryReverseFinishTimeParam;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryBatchPropertiesRepository;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.inventory.repository.rpc.UnifiedInventoryReverseRepository;
import com.poizon.scm.wms.adapter.mq.SendMessage;
import com.poizon.scm.wms.adapter.outbound.delivery.model.*;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryExtraInfoRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryRelatedOrdersRepository;
import com.poizon.scm.wms.api.constants.WmsConstant;
import com.poizon.scm.wms.api.constants.WmsMessageQueueConstant;
import com.poizon.scm.wms.api.dto.request.inventory.DeliveryHeaderRedistributeRequest;
import com.poizon.scm.wms.api.dto.request.inventory.InventoryRedistributeRequest;
import com.poizon.scm.wms.api.dto.request.inventory.InventoryUnfreezeRequest;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.api.enums.TransLogTaskTypeEnum;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.LocationConstant;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsNoRollbackException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.common.utils.InventoryUtil;
import com.poizon.scm.wms.dao.curd.InventoryCurd;
import com.poizon.scm.wms.dao.entitys.InvInventoryAllocatedEntity;
import com.poizon.scm.wms.dao.mappers.InvInventoryMapper;
import com.poizon.scm.wms.domain.inbound.receive.task.EntryBatchService;
import com.poizon.scm.wms.domain.inbound.receive.task.service.param.CalculateInventoryBatchParam;
import com.poizon.scm.wms.domain.inventory.common.InventoryCommonOperation;
import com.poizon.scm.wms.domain.inventory.dto.AllocatedNotNeedReturnUpperMessage;
import com.poizon.scm.wms.domain.inventory.operate.*;
import com.poizon.scm.wms.domain.inventory.query.InventoryQuery;
import com.poizon.scm.wms.domain.launch.executor.factory.LaunchExecutionFactory;
import com.poizon.scm.wms.domain.outbound.fp.processor.FPInventoryAllocationCompletedProcessor;
import com.poizon.scm.wms.domain.outbound.outbound.entity.RepeatDeliveryInterceptorInfo;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.outbound.producer.entity.OutboundDto;
import com.poizon.scm.wms.domain.upper.enums.UpperTypeEnum;
import com.poizon.scm.wms.handler.launch.LaunchCreateHandler;
import com.poizon.scm.wms.infra.mq.SendMessageProcessor;
import com.poizon.scm.wms.pojo.commodity.mdm.rsp.SkuCommonPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryDetailPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryModifyAllocatePojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryModifyStatusPojo;
import com.poizon.scm.wms.pojo.inventory.*;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryService;
import com.poizon.scm.wms.service.delivery.common.DeliveryOrderCommonService;
import com.poizon.scm.wms.service.delivery.config.DeliveryAllocateWarehouseConfig;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.delivery.operate.handler.DeliveryEventHandler;
import com.poizon.scm.wms.service.delivery.operate.model.DeliveryEventModel;
import com.poizon.scm.wms.service.interceptor.IDeliveryInterceptService;
import com.poizon.scm.wms.service.inventory.service.InventoryUtilService;
import com.poizon.scm.wms.util.common.ReturnSalesCenterProperties;
import com.poizon.scm.wms.util.common.WarehouseSmallProperties;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.athena.api.enums.InvTagEnum;
import com.shizhuang.duapp.erp.api.enums.WmsNoticeTypeEnums;
import com.shizhuang.duapp.erp.api.model.request.WmsNoticeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.common.enums.LockEnum.INVENTORY_BATCH;
import static com.poizon.scm.wms.common.exceptions.WmsExceptionCode.*;

/**
 * @category <AUTHOR>
 * @since 2020/4/15 20:55
 */
@Service("inventoryOperateService")
@Slf4j
public class InventoryOperateServiceImpl implements InventoryOperateService {
    @Autowired
    InventoryAdd inventoryAdd;
    @Autowired
    InventoryModifyAttr inventoryModifyAttr;
    @Autowired
    InventorySubtract inventorySubtract;
    @Autowired
    InventoryAllocated inventoryAllocated;
    @Autowired
    InventoryMove inventoryMove;
    @Autowired
    InvInventoryMapper invInventoryMapper;
    @Autowired
    Executor asyncServiceExecutor;

    @Autowired
    Executor adminThreadPoolExecutor;
    @Autowired
    InventoryCurd inventoryCurd;
    @Autowired
    DeliveryOrderModifyService deliveryOrderModifyService;
    @Autowired
    DistributeLockUtil distributeLockUtil;
    @Autowired
    InventoryQuery inventoryQuery;
    @Autowired
    private InventoryOccupy inventoryOccupy;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private ICommodityQueryService commodityQueryService;

    @Autowired
    private IDeliveryInterceptService deliveryInterceptService;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Resource
    private InventoryRepository inventoryRepository;

    @Autowired
    WarehouseSmallProperties warehouseSmallProperties;

    @Autowired
    private ReturnSalesCenterProperties returnSalesCenterProperties;

    @Autowired
    private InventoryUtilService inventoryUtilService;

    @Autowired
    private DeliveryOrderCommonService deliveryOrderCommonService;

    @Resource(name = "scpRedisTemplate")
    private RedisTemplate<String, String> scpRedisTemplate;
    @Autowired
    private InventoryBatchPropertiesRepository inventoryBatchPropertiesRepository;

    @Autowired
    private EntryDetailExtensionRepository entryDetailExtensionRepository;

    @Autowired
    private DeliveryExtraInfoRepository deliveryExtraInfoRepository;

    @Autowired
    private SendMessageProcessor sendMessageProcessor;

    @Autowired
    private DeliveryRelatedOrdersRepository deliveryRelatedOrdersRepository;
    @Autowired
    private EntryBatchService entryBatchService;
    @Resource
    private OutboundMessageProducer outboundMessageProducer;

    @Resource
    private CisInventoryOperation cisInventoryOperation;

    @Autowired
    private DeliveryAllocateWarehouseConfig warehouseConfig;

    @Autowired
    private DeliveryEventHandler deliveryEventHandler;

    @Autowired
    private LaunchCreateHandler launchCreateHandler;

    @Autowired
    private LaunchExecutionFactory launchExecutionFactory;

    @Autowired
    private FPInventoryAllocationCompletedProcessor fpInventoryAllocationCompletedProcessor;

    @Resource
    private SendMessageHandler sendMessageHandler;

    @Resource
    private UnifiedInventoryReverseRepository unifiedInventoryReverseRepository;

    /**
     * 锁超时时间
     */
    public int lockTimeout = 5;

    @Override
    public String add(InventoryAddOperatePojo inventoryAddOperatePojo) {
        if (TenantEnum.DW.getCode().equals(inventoryAddOperatePojo.getTenantCode()) && StringUtils.isEmpty(inventoryAddOperatePojo.getBatchNo())) {
            throw new WmsException(WmsExceptionCode.BATCH_NO_NOT_EXIST);
        }
        return inventoryAdd.process(inventoryAddOperatePojo, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> receive(List<InventoryAddOperatePojo> inventoryAddOperatePojos) {
        inventoryAddOperatePojos.sort(Comparator.comparing(InventoryAddOperatePojo::toString));
        Map<String, String> resultMap = new HashMap<>(inventoryAddOperatePojos.size());
        for (InventoryAddOperatePojo inventoryAddOperatePojo : inventoryAddOperatePojos) {
            if (TenantEnum.DW.getCode().equals(inventoryAddOperatePojo.getTenantCode()) && StringUtils.isEmpty(inventoryAddOperatePojo.getBatchNo())) {
                throw new WmsException(WmsExceptionCode.BATCH_NO_NOT_EXIST);
            }
            //增加活动标示
            EntryDetailExtensionDo entryDetailExtensionDo = entryDetailExtensionRepository.selectExtensionByEntryDetailNo(
                    inventoryAddOperatePojo.getReferenceDetailNo(), inventoryAddOperatePojo.getReferenceNo());
            if (entryDetailExtensionDo != null) {
                inventoryAddOperatePojo.setActivityType(entryDetailExtensionDo.getActivityType());
            }
            String inventoryNo = receive(inventoryAddOperatePojo);
            resultMap.put(inventoryAddOperatePojo.getTaskDetailNo(), inventoryNo);
        }
        if (resultMap.size() != inventoryAddOperatePojos.size()) {
            throw new WmsException(INVENTORY_OPERATING_SYSTEM_EXCEPTION);
        }
        return resultMap;
    }

    @Override
    public String receive(InventoryAddOperatePojo inventoryAddOperatePojo) {
        inventoryAddOperatePojo.setTaskType(TaskTypeEnum.RECEIVED.getTaskType());

        /* 兼容去除调用handoverOutbound,以后WMS不再加在途库存，但可能会有部分历史存量数据，在加库存前,需要删除在途库存 */
//        if (WmsInboundTypeEnum.WMS_DEPOSIT_HANDOVER_List.contains(inventoryAddOperatePojo.getReferenceType())) {
//            InventoryDo inventoryDoCurrent = inventoryRepository.queryInventoryDoByUniCode(inventoryAddOperatePojo.getTenantCode(), inventoryAddOperatePojo.getUniqueCode());
//            if (inventoryDoCurrent != null) {
//                if (LocationConstant.INTRANSIT_DOCK.equals(inventoryDoCurrent.getLocationCode())) {
//                    log.info("兼容有在途库存的添加库存场景:{}", inventoryDoCurrent.getInventoryNo());
//                    inventoryRepository.deleteById(inventoryDoCurrent.getId());
//                }
//            }
//        }

        /*
         ** 唯一码不为空且是小仓库 设置二级质量等级
         */
        if (inventoryAddOperatePojo.getInventoryDimensionPojo() != null
                && StringUtils.isNotBlank(inventoryAddOperatePojo.getInventoryDimensionPojo().getUniqueCode())
                && (warehouseSmallProperties.isSmallWarehouse(inventoryAddOperatePojo.getInventoryDimensionPojo().getWarehouseCode()) || returnSalesCenterProperties.ifReturnSalesCenterSmallWarehouseBizType(inventoryAddOperatePojo.getInventoryDimensionPojo().getWarehouseCode(), inventoryAddOperatePojo.getInventoryDimensionPojo().getBizType()))) {

            inventoryModifyAttr.updateQualityLevelByPrimaryKey(inventoryAddOperatePojo.getInventoryDimensionPojo(), inventoryAddOperatePojo.getSecondQualityLevel(), inventoryAddOperatePojo.getOverseasFlag());
        }
        //收货时添加库存批次号（得物租户）
        if (StringUtils.isBlank(inventoryAddOperatePojo.getBatchNo()) && null != inventoryAddOperatePojo.getInventoryDimensionPojo() &&
                TenantEnum.DW.getCode().equals(inventoryAddOperatePojo.getInventoryDimensionPojo().getTenantCode())) {

            CalculateInventoryBatchParam calculateInventoryBatchParam = new CalculateInventoryBatchParam();
            BeanUtils.copyProperties(inventoryAddOperatePojo.getInventoryDimensionPojo(), calculateInventoryBatchParam);
            calculateInventoryBatchParam.setActivityType(inventoryAddOperatePojo.getActivityType());
            String batchNo = entryBatchService.calculateEntryBatchNo(calculateInventoryBatchParam);
            //添加到addPojo后续用于保存到库存表与日志
            inventoryAddOperatePojo.setBatchNo(batchNo);
        }
        if (TenantEnum.DW.getCode().equals(inventoryAddOperatePojo.getTenantCode()) && StringUtils.isEmpty(inventoryAddOperatePojo.getBatchNo())) {
            throw new WmsException(WmsExceptionCode.BATCH_NO_NOT_EXIST);
        }
        return inventoryAdd.process(inventoryAddOperatePojo, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getInventoryBatchNo(InventoryDimensionPojo inventoryDimensionPojo, String activityType) {
        return getInventoryBatchNo(inventoryDimensionPojo,activityType, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getInventoryBatchNo(InventoryDimensionPojo inventoryDimensionPojo, String activityType, Integer expiryDateRecordMode) {
        if (inventoryDimensionPojo != null &&
                (TenantEnum.DW.getCode().equals(inventoryDimensionPojo.getTenantCode()) ||
                        TenantEnum.CLOUD_WMS.getCode().equals(inventoryDimensionPojo.getTenantCode())) ){
            //计算批次签名
            String batchSign = inventoryUtilService.calculateBatchSignByDimension(inventoryDimensionPojo);

            //根据批次签名获取批次号
            return getInventoryBatchNo(inventoryDimensionPojo, batchSign, activityType, expiryDateRecordMode);
        }
        return null;
    }

    @Override
    public String modifyBatchAttrInfo(String batchNo, String activityType, Integer expiryDateRecordMode) {
        boolean lockFlag = false;
        if (StringUtils.isEmpty(activityType) || null == expiryDateRecordMode) {
            throw new WmsException(PARAMS_IS_EMPTY);
        }
        try {
            lockFlag = distributeLockUtil.tryLockForBiz(INVENTORY_BATCH, batchNo);
            if (!lockFlag) {
                throw new WmsException(GET_LOCK_FAILED);
            }
            InventoryBatchPropertiesDo updateBatchProperties = new InventoryBatchPropertiesDo();
            updateBatchProperties.setBatchNo(batchNo);
            updateBatchProperties.setActivityType(activityType);
            updateBatchProperties.setExpiryDateRecordMode(expiryDateRecordMode);
            inventoryBatchPropertiesRepository.updateAttrInfoByBatchNo(updateBatchProperties);
        } catch (InterruptedException e) {
            log.error("interrupt, 库存批次号生成失败，异常为:", e);
            Thread.currentThread().interrupt();
            throw new WmsException("库存批次号生成失败", e);
        } catch (Exception e) {
            log.error("库存批次号生成失败，异常为:", e);
            throw new WmsException("库存批次号生成失败", e);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(INVENTORY_BATCH, batchNo);
            }
        }
        return null;
    }

    public String getInventoryBatchNo(InventoryDimensionPojo inventoryDimensionPojo, String batchSign, String activityType, Integer expiryDateRecordMode) {

        String batchNo;

        //如果该批次签名在redis中已存在，直接取其值，否则重新计算
        if (scpRedisTemplate.hasKey(batchSign)) {
            batchNo = scpRedisTemplate.opsForValue().get(batchSign);
            if (StringUtils.isEmpty(batchNo)) {
                log.error("redis中存储的批次号空，对应的批次签名为:{}", JSON.toJSONString(batchSign));
                throw new WmsException("redis中存储的批次号为空");
            }
        } else {
            boolean lockFlag = false;
            try {
                lockFlag = distributeLockUtil.tryLockForBiz(INVENTORY_BATCH, batchSign);

                if (!lockFlag) {
                    throw new WmsException("生成库存批次号加锁失败，batchSign为:{}" + batchSign);
                }

                //如果redis中不存在，可能是调拨收货等场景，批次纬度早已存入数据库，去mysql中再捞一次数据
                InventoryBatchPropertiesDo propertiesDo = new InventoryBatchPropertiesDo();
                propertiesDo.setBatchSign(batchSign);
                InventoryBatchPropertiesDo batchPropertiesDo = inventoryBatchPropertiesRepository.find(propertiesDo);
                if (batchPropertiesDo != null && batchPropertiesDo.getBatchNo() != null) {
                    long expireDays = 1L;
                    if (!StringUtils.isEmpty(inventoryDimensionPojo.getInvTag()) && InvTagEnum.DEPOSIT_SKU.getCode().equals(inventoryDimensionPojo.getInvTag())) {
                        expireDays = 7L;
                    }

                    if (TransactionSynchronizationManager.isSynchronizationActive()) {
                        long finalExpireDays = expireDays;
                        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                            @Override
                            public void afterCommit() {
                                //存入redis，后续查询不走DB，过期时间为1天（因为有首次入库时间字段，如果batchNo相同，1天足矣）
                                scpRedisTemplate.opsForValue().set(batchSign, batchPropertiesDo.getBatchNo(), finalExpireDays, TimeUnit.DAYS);
                            }
                        });
                    }
                    return batchPropertiesDo.getBatchNo();
                }

                //以上两种场景都没走得到，表示是新的批次纬度，需要重新生成
                //计算批次号
                batchNo = inventoryUtilService.calculateBatchNo();

                //保存批次号（批次属性表/redis）
                inventoryUtilService.saveInventoryBatchContent(inventoryDimensionPojo, batchNo, activityType, expiryDateRecordMode);
            } catch (InterruptedException e) {
                log.error("interrupt, 库存批次号生成失败，异常为:", e);
                Thread.currentThread().interrupt();
                throw new WmsException("库存批次号生成失败", e);
            } catch (Exception e) {
                log.error("库存批次号生成失败，异常为:", e);
                throw new WmsException("库存批次号生成失败", e);
            } finally {
                if (lockFlag) {
                    distributeLockUtil.releaseLockForBiz(INVENTORY_BATCH, batchSign);
                }
            }
        }

        return batchNo;
    }

    @Override
    @SuppressWarnings("unchecked")
    public String onShelves(InventoryShelvesOperatePojo inventoryMoveOperatePojo) {
        buildShelvesTaskType(inventoryMoveOperatePojo);
        log.info("onShelves inventoryMoveOperatePojo:{}", JsonUtils.serialize(inventoryMoveOperatePojo));
        return inventoryMove.onShelves(inventoryMoveOperatePojo);
    }

    private void buildShelvesTaskType(InventoryShelvesOperatePojo inventoryMoveOperatePojo){
        //之前写死upper 上架任务是统一入口  区分移位任务得从upper_task_detail取taskType传进来 暂时写这里 不应该写库存 但写外面改动太大
        if(StringUtils.isBlank(inventoryMoveOperatePojo.getTaskType())){
            inventoryMoveOperatePojo.setTaskType(TaskTypeEnum.UPPER.getTaskType());
            return;
        }
        //赋值是该枚举
        if(UpperTypeEnum.MOVE_UPPER.getType().equals(inventoryMoveOperatePojo.getTaskType())){
            inventoryMoveOperatePojo.setTaskType(TransLogTaskTypeEnum.MOVE_UPPER.getTaskType());
            return;
        }
        inventoryMoveOperatePojo.setTaskType(TaskTypeEnum.UPPER.getTaskType());
        return;
    }

    @Override
    public String pick(InventoryMoveOperatePojo inventoryMoveOperatePojo) {
        if(StringUtils.isBlank(inventoryMoveOperatePojo.getTaskType())){
            inventoryMoveOperatePojo.setTaskType(TaskTypeEnum.PICK.getTaskType());
        }
        return inventoryMove.process(inventoryMoveOperatePojo);
    }



    /**
     * 给出库单做库存分配
     * 1.先并行去做一个出库单的多条出库单明细做库存分配
     * 2.更新出库单和出库单明细
     *
     * @param inventoryAllocatedOrderPojo
     * @param allocateAsync               是否是由同步线程调用过来的
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @SuppressWarnings("unchecked")
    public void allocatedInventory(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, Boolean allocateAsync) {
        log.info("进行库存分配:{}", JSON.toJSONString(inventoryAllocatedOrderPojo));
        String shipmentNo = inventoryAllocatedOrderPojo.getShipmentNo();
        if (StringUtils.isEmpty(shipmentNo)) {
            throw new WmsException("出库单号为空");
        }
        if (CollectionUtils.isEmpty(inventoryAllocatedOrderPojo.getInventoryAllocatedItemPojo())) {
            throw new WmsException("出库单明细为空");
        }
        String lockName = MessageFormat.format("inventory_allocated_{0}_{1}", inventoryAllocatedOrderPojo.getTenantCode(), shipmentNo);
        try {
            //防止重复占用库存,在出库单加锁
            if (!distributeLockUtil.tryLock(lockName)) {
                log.warn("出库单重复占用库存, 参数是[{}]", JSON.toJSONString(lockName));
                throw new WmsException("重复占用库存");
            }
            log.info("获取锁: {}", lockName);
            /*如果已经组了波次的不能在继续分配*/
            DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(inventoryAllocatedOrderPojo.getShipmentNo());
            if (deliveryHeaderDo != null && (StringUtils.isNotBlank(deliveryHeaderDo.getLaunchNo())
                    || WmsOutBoundStatusEnum.CANCEL.getStatus().equals(deliveryHeaderDo.getCommandStatus()))
                    || WmsOutBoundStatusEnum.FORCE_OUTED.getStatus().equals(deliveryHeaderDo.getStatus())) {
                /*已经分配波次的不能在分配,已经取消的不能分配*/
                log.warn("单据{}已经取消或者已经组波了,不能再分配库存", inventoryAllocatedOrderPojo.getShipmentNo());
                return;
            }
            // 现货并且非海外和跨境仓的不能分配库存
            if (WmsOutboundBusinessUtils.isXh(deliveryHeaderDo.getBizType(), deliveryHeaderDo.getOrderTags())
                    && !warehouseConfig.isOverseaOrCrosseWarehouse(deliveryHeaderDo.getWarehouseCode())) {
                log.warn("现货不需要分配库存。{}", deliveryHeaderDo.getDeliveryOrderCode());
                return;
            }
            // 某些单据类型，某些业务类型不能库存分配
            if (!checkInvAllocated(deliveryHeaderDo.getType(), deliveryHeaderDo.getBizType(), deliveryHeaderDo.getDeliveryOrderCode())) {
                log.warn("单据类型：{}，业务类型:{}不需要分配库存。", deliveryHeaderDo.getType(), deliveryHeaderDo.getBizType());
                return;
            }

            List<InventoryAllocatedItemPojo> inventoryAllocatedItemPojoList = inventoryAllocatedOrderPojo.getInventoryAllocatedItemPojo();
            /*因为使用了重试框架,需要修正请求的维度和数量*/
            inventoryAllocatedItemPojoList = revised(inventoryAllocatedOrderPojo, inventoryAllocatedItemPojoList, allocateAsync);
            if (CollectionUtils.isNotEmpty(inventoryAllocatedItemPojoList)) {
                Set<String> skuIds = inventoryAllocatedItemPojoList.stream()
                        .filter(entity -> StringUtils.isNotBlank(entity.getSkuId()))
                        .map(InventoryAllocatedItemPojo::getSkuId).collect(Collectors.toSet());
                Map<String, SkuCommonPojo> skuInfoMap = commodityQueryService.querySkuCommonMapBySkuIds(inventoryAllocatedOrderPojo.getTenantCode(), skuIds);
                if (!WmsOutBoundTypeEnum.DBCK.getType().equals(inventoryAllocatedOrderPojo.getOrderType())) {
                    /*非调拨库存分配*/
                    normalAllocatedInv(inventoryAllocatedOrderPojo, inventoryAllocatedItemPojoList, skuInfoMap);
                } else {
                    /*调拨库存分配*/
                    dbAllocatedInv(inventoryAllocatedOrderPojo, inventoryAllocatedItemPojoList, skuInfoMap);
                }
            }
            /*执行完毕,反查分配库存记录表,查看分配数量是否全部分配*/
            afterAllocated(inventoryAllocatedOrderPojo, allocateAsync, deliveryHeaderDo);
            /*重复发货同一买家拦截，拦截依赖库存分配，所以要放在分配库存操作之后*/
            // 把这个拦截的代码放到这个里面来，是因为它需要库存分配记录，所以不能放到外面，得放到库存分配这个方法里面来
            if (Objects.equal(com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum.JYCK.getCode(), inventoryAllocatedOrderPojo.getOrderType())) {
                RepeatDeliveryInterceptorInfo info = RepeatDeliveryInterceptorInfo.builder()
                        .deliveryOrderCode(shipmentNo)
                        .tenantCode(OperationUserContextHolder.getTenantCode())
                        .warehouseCode(inventoryAllocatedOrderPojo.getInventoryAllocatedItemPojo().get(0).getWarehouseCode()).build();
                deliveryInterceptService.repeatDeliveryInterceptor(info);
            }

            OperationUserContext operationUserContext = OperationUserContextHolder.get();
            if (null != operationUserContext) {
                DeliveryEventModel deliveryEventModel = new DeliveryEventModel();
                deliveryEventModel.setDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
                deliveryEventModel.setOperateTime(new Date());
                deliveryEventModel.setUserId(operationUserContext.getUserId());
                deliveryEventModel.setUserName(operationUserContext.getUserName());
                deliveryEventModel.setTenantCode(operationUserContext.getTenantCode());
                deliveryEventHandler.sendOutboundAllocateMsg(deliveryEventModel);
            }
        } catch (WmsException e) {
            log.error("库存操作业务异常:{}", e);
            //业务异常不处理直接抛出去
            throw new WmsException(e.getCode(), e.getMessage());
        } catch (WmsNoRollbackException e) {
            log.warn("库存重试分配失败", e);
            throw e;
        } catch (Exception e) {
            log.error("库存操作系统异常:{}", e);
            throw new WmsException(INVENTORY_OPERATING_SYSTEM_EXCEPTION);
        } finally {
            //释放锁
            distributeLockUtil.release(lockName);
            log.info("释放锁: {}", lockName);
        }
    }

    /**
     * 校验单据能否做库存分配
     *
     * @param deliveryType
     * @param bizType
     * @param deliveryOrderCode
     * @return false：不能，true 能
     */
    public boolean checkInvAllocated(String deliveryType, Integer bizType, String deliveryOrderCode) {
        if (WmsOutboundBusinessUtils.simpleShip(bizType)) {
            log.warn("不需要分配库存。{}", bizType);
            return false;
        }
        List<String> typeList = Lists.newArrayList(WmsOutBoundTypeEnum.ZMFSCK.getType(), WmsOutBoundTypeEnum.Z95CK.getType(),
                WmsOutBoundTypeEnum.NDBCK.getType(), WmsOutBoundTypeEnum.KTHCK.getType());
        if (typeList.contains(deliveryType)) {
            return false;
        }
        DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if (java.util.Objects.nonNull(deliveryExtraInfoDo) && judgmentCrossDockOrder(deliveryExtraInfoDo)) {
            return false;
        }
        //p

        return true;
    }

    private boolean judgmentCrossDockOrder(DeliveryExtraInfoDo deliveryExtraInfoDo) {
        if (StringUtils.isBlank(deliveryExtraInfoDo.getFeature())) {
            return false;
        }
        DeliveryExtraInfoFeatureModel featureModel = JSON.parseObject(deliveryExtraInfoDo.getFeature(), DeliveryExtraInfoFeatureModel.class);

        if (BooleanUtils.isTrue(featureModel.getNeedCrossDockingFlag())) {
            return true;
        }
        return false;
    }

    /**
     * 修正重试框架里面库存分配的请求
     *
     * @param inventoryAllocatedOrderPojo
     * @param inventoryAllocatedItemPojoList
     * @param allocateAsync                  是否是异步分配
     * @return
     */
    private List<InventoryAllocatedItemPojo> revised(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, List<InventoryAllocatedItemPojo> inventoryAllocatedItemPojoList, Boolean allocateAsync) {
        /*
         * 手动分配的请求被重置过分配数量
         * 自动重试的请求没有被重置过分配数量
         * */
        /*明细编号*/
        Set<String> shipmentDetailNoSet = new HashSet<>();
        /*本次要操作的数量*/
        Map<String, Integer> detailMappingOperatingQty = new HashMap<>();
        inventoryAllocatedItemPojoList.stream().forEach(item -> {
            shipmentDetailNoSet.add(item.getShipmentDetailNo());
            if (detailMappingOperatingQty.get(item.getShipmentDetailNo()) == null) {
                detailMappingOperatingQty.put(item.getShipmentDetailNo(), item.getOperateQty());
            } else {
                int qty = detailMappingOperatingQty.get(item.getShipmentDetailNo()) + item.getOperateQty();
                detailMappingOperatingQty.put(item.getShipmentDetailNo(), qty);
            }
        });
        if (CollectionUtils.isEmpty(shipmentDetailNoSet)) {
            throw new WmsException("出库单明细编号为空");
        }
        /*查询原始请求明细*/
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.listByDetailNoList(new ArrayList<>(shipmentDetailNoSet));
        Map<String, Byte> deliveryDetailMappingCancelFlag = deliveryDetailDos.stream().collect(Collectors.toMap(DeliveryDetailDo::getDetailNo, DeliveryDetailDo::getCancelFlag));
        /*查询分配记录*/
        List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList = inventoryCurd.listInventoryAllocatedList(shipmentDetailNoSet);
        if (CollectionUtils.isEmpty(inventoryAllocatedEntityList)) {
            log.info("出库单{}没有查询到分配记录", inventoryAllocatedOrderPojo.getShipmentNo());
            /*过滤掉取消*/
            inventoryAllocatedItemPojoList = inventoryAllocatedItemPojoList.stream().filter(item -> {
                Byte flag = deliveryDetailMappingCancelFlag.get(item.getShipmentDetailNo());
                return !WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(flag);
            }).collect(Collectors.toList());
            return inventoryAllocatedItemPojoList;
        }
        /*明细和已经分配的数量*/
        Map<String, Integer> detailMappingAllocatedQty = new HashMap<>();
        inventoryAllocatedEntityList.stream().forEach(inventoryAllocated -> {
            if (detailMappingAllocatedQty.get(inventoryAllocated.getShipmentDetailNo()) == null) {
                detailMappingAllocatedQty.put(inventoryAllocated.getShipmentDetailNo(), inventoryAllocated.getQty());
            } else {
                int qty = detailMappingAllocatedQty.get(inventoryAllocated.getShipmentDetailNo()) + inventoryAllocated.getQty();
                detailMappingAllocatedQty.put(inventoryAllocated.getShipmentDetailNo(), qty);
            }
        });

        /*设置原始单据和计划数量的映射关系*/
        Map<String, Integer> detailMappingPlanQty = deliveryDetailDos.stream().collect(Collectors.toMap(DeliveryDetailDo::getDetailNo, DeliveryDetailDo::getPlanQty));
        List<InventoryAllocatedItemPojo> revised = new ArrayList<>();
        if (allocateAsync) {
            /*如果是异步重试框架,请求就是原始的请求,只需要重置请求*/
            inventoryAllocatedItemPojoList.forEach(item -> {
                /*已经取消的明细不参与库存分配*/
                Byte flag = deliveryDetailMappingCancelFlag.get(item.getShipmentDetailNo());
                if (!WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(flag)) {
                    /*当前明细已分配数量*/
                    int allocatedQty = detailMappingAllocatedQty.getOrDefault(item.getShipmentDetailNo(), 0);
                    /*当前明细计划分配数量*/
                    int planQty = detailMappingPlanQty.get(item.getShipmentDetailNo());
                    /*剩余待分配数量*/
                    int remainQty = planQty - allocatedQty;
                    item.setOperateQty(remainQty);
                    revised.add(item);
                }
            });
        } else {
            inventoryAllocatedItemPojoList.forEach(item -> {
                /*已经取消的明细不参与库存分配*/
                Byte flag = deliveryDetailMappingCancelFlag.get(item.getShipmentDetailNo());
                if (!WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(flag)) {
                    /*当前明细已分配数量*/
                    int allocatedQty = detailMappingAllocatedQty.getOrDefault(item.getShipmentDetailNo(), 0);
                    /*当前明细计划分配数量*/
                    int planQty = detailMappingPlanQty.get(item.getShipmentDetailNo());
                    /*当前明细本次操作的数量*/
                    int operatingQty = detailMappingOperatingQty.getOrDefault(item.getShipmentDetailNo(), 0);
                    /*剩余待分配数量*/
                    int remainQty = planQty - allocatedQty;
                    if (operatingQty > remainQty) {
                        throw new WmsException("明细" + item.getShipmentDetailNo() + "的分配数量超过了计划数量");
                    }
                    item.setOperateQty(Math.min(item.getOperateQty(), remainQty));
                    revised.add(item);
                }
            });
        }
        return revised;
    }

    /**
     * 这个方法为多单库存分配，比如抽盒机异步去调用库存分配
     *
     * @param inventoryAllocatedOrderPojo
     */
    @Override
    @EventualConsistency(label = "asyncAllocatedInventory", delay = @Delay(delay = 1800), maxRetryTimes = 100,
            beanName = "inventoryOperateService", referenceNo = "#inventoryAllocatedOrderPojo.shipmentNo",
            listeners = {"inventoryAllocatedRetryListener"},
            noRollbackFor = WmsNoRollbackException.class)
    public void asyncAllocatedInventory(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, Boolean allocateAsync) {
        boolean locked = false;
        try {
            locked = lockBeforeAllocate(inventoryAllocatedOrderPojo);
            if (locked) {
                /*拿到锁在去分配库存*/
                allocatedInventory(inventoryAllocatedOrderPojo, allocateAsync);
            } else {
                throw new WmsException("库存分配获取锁失败,可能和单据取消并发");
            }
        } catch (Exception e) {
            log.warn("单据{}库存分配出现错误", inventoryAllocatedOrderPojo.getShipmentNo(), e);
            throw e;
        } finally {
            if (locked) {
                releaseAfterAllocate(inventoryAllocatedOrderPojo);
            }
        }
    }

    @Transactional(noRollbackFor = WmsNoRollbackException.class)
    @Override
    public void allocatedInventoryForJob(String deliveryOrderCode) {
        InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo = buildAllocatedOrderPojo(deliveryOrderCode);
        allocatedInventory(inventoryAllocatedOrderPojo, true);
    }

    private InventoryAllocatedOrderPojo buildAllocatedOrderPojo(String deliveryOrderCode) {
        InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo = deliveryOrderCommonService.buildAllocateInventory(deliveryOrderCode);
        return inventoryAllocatedOrderPojo;
    }

    /**
     * 释放取消锁和创建波次的锁
     *
     * @param inventoryAllocatedOrderPojo
     */
    private void releaseAfterAllocate(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo) {
        distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, inventoryAllocatedOrderPojo.getShipmentNo());
        distributeLockUtil.releaseLockForBiz(LockEnum.LAUNCH_DELIVERY, inventoryAllocatedOrderPojo.getShipmentNo());
    }

    /**
     * 异步库存分配前先获取取消和创建波次的锁
     *
     * @param inventoryAllocatedOrderPojo
     */
    private boolean lockBeforeAllocate(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo) {
        boolean lockForCancel = false;
        boolean lockForLaunch = false;
        try {
            /* 获取取消的锁*/
            lockForCancel = distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_CANCEL, inventoryAllocatedOrderPojo.getShipmentNo());
            if (!lockForCancel) {
                return false;
            }
            /* 获取波次的锁*/
            lockForLaunch = distributeLockUtil.tryLockForBiz(LockEnum.LAUNCH_DELIVERY, inventoryAllocatedOrderPojo.getShipmentNo());
            return lockForCancel && lockForLaunch;
        } catch (InterruptedException e) {
            log.warn("interrupt, 库存占用获取单据取消锁或波次创建锁失败");
            Thread.currentThread().interrupt();
            return lockForCancel && lockForLaunch;
        } catch (Exception e) {
            log.warn("库存占用获取单据取消锁或波次创建锁失败");
            return lockForCancel && lockForLaunch;
        } finally {
            if (lockForCancel && !lockForLaunch) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, inventoryAllocatedOrderPojo.getShipmentNo());
            }
        }
    }


    /**
     * 非调拨库存分配
     *
     * @param inventoryAllocatedOrderPojo
     * @param inventoryAllocatedItemPojoList
     * @param skuInfoMap
     */
    private void normalAllocatedInv(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, List<InventoryAllocatedItemPojo> inventoryAllocatedItemPojoList, Map<String, SkuCommonPojo> skuInfoMap) {
        //非调拨出库，库存占用
        List<InventoryAllocatedPojo> allocatedPojos = inventoryAllocatedItemPojoList.stream().map(item -> {
            if (StringUtils.isBlank(item.getQualityLevel())) {
                log.error("单据缺少质量等级,出库单明细编号[{}]", item.getShipmentDetailNo());
                throw new WmsException("单据缺少质量等级");
            }
            InventoryAllocatedPojo inventoryAllocatedPojo = new InventoryAllocatedPojo();
            inventoryAllocatedPojo.setInventoryDimensionPojo(item);
            inventoryAllocatedPojo.setSkuCommonPojo(skuInfoMap.get(item.getSkuId()));
            inventoryAllocatedPojo.setShipmentDetailNo(item.getShipmentDetailNo());
            inventoryAllocatedPojo.setRelativeInventoryNo(item.getRelativeInventoryNo());
            inventoryAllocatedPojo.setShipmentNo(inventoryAllocatedOrderPojo.getShipmentNo());
            inventoryAllocatedPojo.setOperateQty(item.getOperateQty());
            inventoryAllocatedPojo.setTaskType(inventoryAllocatedOrderPojo.getTaskType());
            inventoryAllocatedPojo.setTenantCode(inventoryAllocatedOrderPojo.getTenantCode());
            inventoryAllocatedPojo.setReferenceNo(inventoryAllocatedOrderPojo.getShipmentNo());
            inventoryAllocatedPojo.setReferenceType(inventoryAllocatedOrderPojo.getOrderType());
            inventoryAllocatedPojo.setGlobalOrderFlag(inventoryAllocatedOrderPojo.getGlobalOrderFlag());
            // 5.44 效期清退添加过期时间
            if(null != item.getOutOffStockDay() && item.getOutOffStockDay() > 0){
                /*将下架天数转换为过期时间*/
                inventoryAllocatedPojo.setExpireDate(DateUtils.plusDays(LocalDate.now(), item.getOutOffStockDay()));
            }
            return inventoryAllocatedPojo;
        }).collect(Collectors.toList());
        /*开启异步线程池进行库存分配*/
        try {
            allocatedPojos.stream().map(
                    item -> CompletableFuture.supplyAsync
                            (() -> {
                                try {
                                    // 多线程异步执行
                                    return inventoryAllocated.process(item);
                                } catch (Exception e) {
                                    log.error("库存分配异常", e);
                                    return false;
                                }
                            }, asyncServiceExecutor)
            ).collect(Collectors.toList()).
                    stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("库存分配异常", e);
        }
    }

    /**
     * 调拨库存分配
     *
     * @param inventoryAllocatedOrderPojo
     * @param inventoryAllocatedItemPojoList
     * @param skuInfoMap
     */
    private void dbAllocatedInv(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo,
                                List<InventoryAllocatedItemPojo> inventoryAllocatedItemPojoList,
                                Map<String, SkuCommonPojo> skuInfoMap) {
        /*按照多维度进行分组*/
        Map<String, List<InventoryAllocatedItemPojo>> skuOwnerAllocatedMap = inventoryAllocatedItemPojoList
                .stream().collect(Collectors.groupingBy(entity -> {
                    // 按照仓库、货主、业务类型、商品、质量等级、唯一码、库位进行合并
                    return new StringBuilder()
                            .append(entity.getWarehouseCode())
                            .append(WmsConstant.EMPTY)
                            .append(entity.getOwnerCode())
                            .append(WmsConstant.EMPTY)
                            .append((entity.getBizType()))
                            .append(WmsConstant.EMPTY)
                            .append(entity.getSkuId())
                            .append(WmsConstant.EMPTY)
                            .append(entity.getQualityLevel())
                            .append(WmsConstant.EMPTY)
                            .append(entity.getUniqueCode())
                            .append(WmsConstant.EMPTY)
                            .append(entity.getLocationCode())
                            .toString();
                }));

        List<List<InventoryAllocatedPojo>> batchAllocateList = new ArrayList<>(skuOwnerAllocatedMap.keySet().size());
        for (Map.Entry<String, List<InventoryAllocatedItemPojo>> entity : skuOwnerAllocatedMap.entrySet()) {
            List<InventoryAllocatedItemPojo> itemPojoList = entity.getValue();
            List<InventoryAllocatedPojo> allocatedPojos = itemPojoList.stream().map(item -> {
                if (StringUtils.isBlank(item.getQualityLevel())) {
                    log.error("单据缺少质量等级,出库单明细编号[{}]", item.getShipmentDetailNo());
                    throw new WmsException("单据缺少质量等级");
                }
                InventoryAllocatedPojo inventoryAllocatedPojo = new InventoryAllocatedPojo();
                inventoryAllocatedPojo.setInventoryDimensionPojo(item);
                inventoryAllocatedPojo.setSkuCommonPojo(skuInfoMap.get(item.getSkuId()));
                inventoryAllocatedPojo.setShipmentDetailNo(item.getShipmentDetailNo());
                inventoryAllocatedPojo.setRelativeInventoryNo(item.getRelativeInventoryNo());
                inventoryAllocatedPojo.setShipmentNo(inventoryAllocatedOrderPojo.getShipmentNo());
                inventoryAllocatedPojo.setOperateQty(item.getOperateQty());
                inventoryAllocatedPojo.setTaskType(inventoryAllocatedOrderPojo.getTaskType());
                inventoryAllocatedPojo.setTenantCode(inventoryAllocatedOrderPojo.getTenantCode());
                inventoryAllocatedPojo.setReferenceNo(inventoryAllocatedOrderPojo.getShipmentNo());
                inventoryAllocatedPojo.setReferenceType(inventoryAllocatedOrderPojo.getOrderType());
                // 添加商品信息
                inventoryAllocatedPojo.setSkuCommonPojo(null);
                return inventoryAllocatedPojo;
            }).collect(Collectors.toList());

            batchAllocateList.add(allocatedPojos);
        }
        try {
            // TODO 异步处理进行库存分配
            batchAllocateList.stream().map(
                    item -> CompletableFuture.supplyAsync
                            (() -> {
                                try {
                                    if (item.size() == NumberUtils.INTEGER_ONE) {
                                        // 多维度合并唯一,直接调用原始方法进行库存占用
                                        return inventoryAllocated.process(item.get(0));
                                    } else {
                                        // 分批库存占用
                                        return inventoryAllocated.batchAllocate(item);
                                    }
                                } catch (Exception e) {
                                    log.error("调拨库存分配异常", e);
                                    return false;
                                }
                            }, asyncServiceExecutor)
            ).collect(Collectors.toList()).
                    stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            log.error("调拨库存分配异常", ex);
        }
    }

    /**
     * 库存分配完成，修改出库单的状态，以及出库单明细的qty
     *
     * @param inventoryAllocatedOrderPojo
     * @param allocateAsync               是否是由异步线程调用
     * @param deliveryHeaderDo
     */
    private void afterAllocated(InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo, Boolean allocateAsync, DeliveryHeaderDo deliveryHeaderDo) {
        //执行完毕,反查分配库存记录表,查看分配数量是否全部分配
        int actualQty = 0;
        List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList = inventoryCurd.queryAllocateRecordList(
                inventoryAllocatedOrderPojo.getTenantCode(),
                inventoryAllocatedOrderPojo.getShipmentNo()
        );

        List<DeliveryDetailDo> deliveryDetailDoList = deliveryDetailRepository.queryDeliveryDetailExcludeCancelByDeliveryCode(inventoryAllocatedOrderPojo.getShipmentNo());
        if (CollectionUtils.isEmpty(deliveryDetailDoList)) {
            log.warn("单据{}明细全部取消", inventoryAllocatedOrderPojo.getShipmentNo());
            return;
        }

        //查询是否是回切后, 历史的一盘货库存
        List<String> matchUnifiedInventoryDetailNos = getMatchUnifiedInventoryDetails(deliveryDetailDoList, inventoryAllocatedEntityList);

        int totalPlanQty = deliveryDetailDoList.stream().mapToInt(DeliveryDetailDo::getPlanQty).sum();
        if (CollectionUtils.isNotEmpty(inventoryAllocatedEntityList)) {
            actualQty = inventoryAllocatedEntityList.stream().mapToInt(item -> item.getQty()).sum();
        }
        log.info("shipmentNo:{}, allocateAsync:{}, planQty:{}, actualQty:{}", inventoryAllocatedOrderPojo.getShipmentNo(), allocateAsync, inventoryAllocatedOrderPojo.getTotalPlanQty(), actualQty);
        //修改单据状态
        DeliveryModifyAllocatePojo deliveryModifyAllocatePojo = new DeliveryModifyAllocatePojo();
        DeliveryModifyStatusPojo deliveryModifyStatusPojo = new DeliveryModifyStatusPojo();
        deliveryModifyStatusPojo.setDeliveryOrderCode(inventoryAllocatedOrderPojo.getShipmentNo());

        if (actualQty == 0) {
            //分配失败
            deliveryModifyStatusPojo.setDeliveryStatus(WmsOutBoundStatusEnum.FAIL_ALLOCATE);
        } else if (actualQty < totalPlanQty) {
            //部分分配
            deliveryModifyStatusPojo.setDeliveryStatus(WmsOutBoundStatusEnum.PART_ALLOCATE);
        } else if (actualQty == totalPlanQty) {
            //完全分配
            deliveryModifyStatusPojo.setDeliveryStatus(WmsOutBoundStatusEnum.WHOLE_ALLOCATE);

        } else {
            throw new WmsException("库存重复分配");
        }
        deliveryModifyStatusPojo.setLastAllocatedTime(new Date());
        deliveryModifyAllocatePojo.setDelivery(deliveryModifyStatusPojo);
        List<DeliveryDetailPojo> deliveryDetailPojoList = new ArrayList<>();
        // copy出原分配记录
        List<InvInventoryAllocatedEntity> inventoryAllocatedList = BeanUtil.deepCopyByList(inventoryAllocatedEntityList, InvInventoryAllocatedEntity.class);
        //根据出库单明细分组聚合
        Map<String, InvInventoryAllocatedEntity> groupMap = groupInventoryAllocatedEntity(inventoryAllocatedEntityList);
        if (groupMap.size() > 0) {
            groupMap.forEach((key, invInventoryAllocatedEntity) -> {
                DeliveryDetailPojo deliveryDetailPojo = new DeliveryDetailPojo();
                deliveryDetailPojo.setDetailNo(invInventoryAllocatedEntity.getShipmentDetailNo());
                deliveryDetailPojo.setAllocatedQty(invInventoryAllocatedEntity.getQty());
                deliveryDetailPojo.setInvTag(invInventoryAllocatedEntity.getInvTag());
                deliveryDetailPojo.setInvManagementMode(invInventoryAllocatedEntity.getInvManagementMode());
                deliveryDetailPojoList.add(deliveryDetailPojo);
            });
        }
        deliveryModifyAllocatePojo.setDeliveryAllocate(deliveryDetailPojoList);
        //分配结束通知单据
        log.info("参数deliveryModifyAllocatePojo -> [{}]", JSON.toJSONString(deliveryModifyAllocatePojo));

        deliveryOrderModifyService.modifyDeliveryAllocate(deliveryModifyAllocatePojo, matchUnifiedInventoryDetailNos, inventoryAllocatedList);

        // 通知erp
        if(WmsBizTypeEnum.SB_BIZ_TYPE.contains(deliveryHeaderDo.getBizType())
            && !TenantEnum.MEN_DAO.getCode().equals(deliveryHeaderDo.getTenantCode())){
            DeliveryDetailDo deliveryDetailDo = deliveryDetailDoList.get(0);
            sendAllocatedFailedMessage(deliveryHeaderDo,deliveryDetailDo,actualQty);

//            if (judgementAutoPicking(inventoryAllocatedEntityList, deliveryHeaderDo)) {
//                // 送洗送修需要补充虚拟下架流程
//                autoLaunchAndExecutePickTask(inventoryAllocatedEntityList, LaunchExecuteSourceEnum.VIRTUAL_LOCATION_AUTO_PICK);
//            }

            return;
        }

        if (deliveryModifyStatusPojo.getDeliveryStatus().equals(WmsOutBoundStatusEnum.WHOLE_ALLOCATE)) {
            if (LocationConstant.CANCEL_DOCK.equals(inventoryAllocatedEntityList.get(0).getLocationCode())) {
                // 通知库内库存分配完成（命中了取消件无需返架）
                sendAllocatedNotNeedReturnUpperMessage(deliveryHeaderDo, inventoryAllocatedEntityList);

                //分配的cancel_dock库位的库存, 触发组波拣货流程
                autoLaunchAndExecutePickTask(inventoryAllocatedEntityList,LaunchExecuteSourceEnum.CANCEL_HIT_NEW_ORDER);
            }
            if (deliveryHeaderDo.getType().equals(WmsOutBoundTypeEnum.PFCK.getType()) && BooleanUtils.toBoolean(deliveryHeaderDo.getAutoShipFlag())) {
                // 赔付出库自动发货
                launchExecutionFactory.autoLaunchAndExecutePickAndShip(inventoryAllocatedEntityList.get(0), LaunchExecuteSourceEnum.STOCK_LOSS);
            }

            //fp需要感知是否在fp区域
            fpInventoryAllocationCompletedProcessor.execute(inventoryAllocatedOrderPojo);
        }
        /*如果使用了重试框架,库存分配不完全需要抛出异常重新分配*/
        if (allocateAsync && actualQty < inventoryAllocatedOrderPojo.getTotalPlanQty()) {
            throw new WmsNoRollbackException("库存分配失败" + inventoryAllocatedOrderPojo.getShipmentNo());
        }
    }

    /**
     * 判断是否在虚拟库位上
     *
     * @param inventoryAllocatedEntityList
     * @param deliveryHeaderDo
     * @return
     */
    private boolean judgementAutoPicking(List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList, DeliveryHeaderDo deliveryHeaderDo) {

        if ((WmsOutBoundTypeEnum.SEND_WASH.getType().equals(deliveryHeaderDo.getDeliveryOrderCode()) ||
                WmsOutBoundTypeEnum.ZXCK.getType().equals(deliveryHeaderDo.getDeliveryOrderCode()))
        ) {

            return true;
        }

        return false;
    }

    private List<String> getMatchUnifiedInventoryDetails(List<DeliveryDetailDo> deliveryDetailDoList, List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList) {
        //过滤出非一盘货的明细单号
        List<DeliveryDetailDo> filteredDetailList = deliveryDetailDoList.stream().filter(item -> !BooleanUtils.toBoolean(item.getUnifiedInventory())
                && WmsBizTypeEnum.UNIFIED_INVENTORY_BIZ_TYPE.contains(item.getBizType())
                && WmsOutBoundTypeEnum.UNIFIED_INVENTORY_RETURN_TYPE_LIST.contains(item.getDeliveryOrderType())
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredDetailList)) {
            return new ArrayList<>();
        }

        List<String> filteredDetailNoList = filteredDetailList.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList());

        List<String> skuIds = filteredDetailList.stream().map(DeliveryDetailDo::getSkuId).distinct().collect(Collectors.toList());

        List<QueryUnifiedInventoryReverseFinishTimeResult> reverseFinishTimeResults = unifiedInventoryReverseRepository.
                queryUnifiedInventoryReverseFinishTime(new QueryUnifiedInventoryReverseFinishTimeParam(skuIds));

        //库存分配记录根据明细单号分组
        Map<String, List<InvInventoryAllocatedEntity>> deliveryDetailMap = inventoryAllocatedEntityList.stream().filter(item -> filteredDetailNoList.contains(item.getShipmentDetailNo())).collect(Collectors.groupingBy(InvInventoryAllocatedEntity::getShipmentDetailNo));
        //属于一盘货期间的明细集合
        List<String> matchDetailNoList = new ArrayList<>();
        for (Map.Entry<String, List<InvInventoryAllocatedEntity>> entry : deliveryDetailMap.entrySet()) {
            List<InvInventoryAllocatedEntity> allocatedEntities = entry.getValue();
            allocatedEntities.sort(Comparator.comparing(InvInventoryAllocatedEntity::getFirstReceivedTime));
            InvInventoryAllocatedEntity invInventoryAllocatedEntity = allocatedEntities.get(0);

            QueryUnifiedInventoryReverseFinishTimeResult reverseFinishTimeResult = reverseFinishTimeResults.stream()
                    .filter(item -> item.getSkuId().equals(invInventoryAllocatedEntity.getSkuId())).findAny().orElse(null);

            if (reverseFinishTimeResult != null && reverseFinishTimeResult.getReverseFinishTime() != null) {
                Date date = reverseFinishTimeResult.getReverseFinishTime();
                if (invInventoryAllocatedEntity.getFirstReceivedTime().before(date)) {
                    matchDetailNoList.add(entry.getKey());
                }
            }

        }
        return matchDetailNoList;
    }

    private void autoLaunchAndExecutePickTask(List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList,LaunchExecuteSourceEnum launchExecuteSourceEnum) {
        if (inventoryAllocatedEntityList.size() > 1) {
            log.error("只有交易出库才能分配cancel_dock库存, 此处不应该存在多条分配记录");
            return;
        }
        launchExecutionFactory.autoLaunchAndExecutePickTask(inventoryAllocatedEntityList.get(0), launchExecuteSourceEnum);

    }


    private void sendAllocatedFailedMessage(DeliveryHeaderDo deliveryHeaderDo,DeliveryDetailDo deliveryDetailDo,int qty){
        SendMessage<WmsNoticeDto> message = new SendMessage<>();
        message.setMessageKey(deliveryDetailDo.getUniqueCode());

        WmsNoticeDto noticeDto = new WmsNoticeDto();
        noticeDto.setNoticeId(UUID.randomUUID().toString());
        noticeDto.setNoticeType(WmsNoticeTypeEnums.ALLOCATED_RESULT.getCode());
        noticeDto.setWarehouse(deliveryHeaderDo.getWarehouseCode());

        WmsNoticeDto.Content content = new WmsNoticeDto.Content();
        content.setCreator(-1L);
        content.setTime(new Date());

        List<WmsNoticeDto.Document> documentList = new ArrayList<>();
        WmsNoticeDto.Document document = new WmsNoticeDto.Document();
        document.setDocumentNo(deliveryHeaderDo.getDeliveryOrderCode());
        document.setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
        document.setOwnerCode(Long.valueOf(deliveryDetailDo.getOwnerCode()));

        List<WmsNoticeDto.DocumentDetail> details = new ArrayList<>();
        WmsNoticeDto.DocumentDetail detail = new WmsNoticeDto.DocumentDetail();
        detail.setUniqueCode(deliveryDetailDo.getUniqueCode());
        detail.setSkuId(Long.valueOf(deliveryDetailDo.getSkuId()));
        detail.setPlanQty(qty);
        if(com.poizon.scm.wms.util.util.NumberUtils.isNumber(deliveryDetailDo.getQualityLevel())){
            detail.setQualityLevel(Integer.valueOf(deliveryDetailDo.getQualityLevel()));
        }

        Set<String> deliveryOrderCodes = Sets.newHashSet(deliveryHeaderDo.getDeliveryOrderCode());
        List<DeliveryRelatedOrdersDo> relatedOrders = deliveryRelatedOrdersRepository.selectByDeliveryOrderCodes(deliveryOrderCodes);
        if(CollectionUtils.isNotEmpty(relatedOrders)){
            detail.setRelationNo(relatedOrders.get(0).getRelatedOrderCode());
        }
        details.add(detail);
        document.setDetails(details);
        documentList.add(document);
        content.setDocuments(documentList);
        noticeDto.setNoticeContent(content);
        message.setMessageContent(noticeDto);
        message.setTopic(WmsMessageQueueConstant.WMS_TO_ERP_CALL_BACK_TOPIC);
        message.setTag(WmsMessageQueueConstant.WMS_TO_ERP_CALL_BACK_TAG);
        sendMessageProcessor.process(message);
    }

    @Override
    public Boolean cancelAllocatedInventory(InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo) {
        return inventoryAllocated.cancelAllocatedInventory(inventoryAllocatedCancelPojo);
    }

    @Override
    @SuppressWarnings("unchecked")
    public String modifyInventoryAttr(InventoryModifyAttrOperatePojo inventoryOperatePojo) {
        inventoryOperatePojo.setTaskType(TaskTypeEnum.MODIFY_ATTR.getTaskType());
        return inventoryModifyAttr.process(inventoryOperatePojo);
    }

    @Override
    @SuppressWarnings("unchecked")
    public String moveInventory(InventoryMoveOperatePojo inventoryOperatePojo) {
        inventoryOperatePojo.setTaskType(TaskTypeEnum.MOVE.getTaskType());
        return inventoryMove.process(inventoryOperatePojo);
    }

    @Override
    @SuppressWarnings("unchecked")
    public String ship(InventoryBasePojo inventoryBasePojo) {
        inventoryBasePojo.setTaskType(TaskTypeEnum.SHIP.getTaskType());
        return inventorySubtract.process(inventoryBasePojo, null, null);
    }

    /**
     * 以出库单明细去groupBy库存分配记录，因为一条出库单明细可以有多个库存分配记录
     *
     * @param inventoryAllocatedEntityList
     * @return
     */
    public Map<String, InvInventoryAllocatedEntity> groupInventoryAllocatedEntity(List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList) {
        Map<String, InvInventoryAllocatedEntity> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inventoryAllocatedEntityList)) {
            for (InvInventoryAllocatedEntity invInventoryAllocatedEntity : inventoryAllocatedEntityList) {
                if (map.containsKey(invInventoryAllocatedEntity.getShipmentDetailNo())) {
                    InvInventoryAllocatedEntity mapValue = map.get(invInventoryAllocatedEntity.getShipmentDetailNo());
                    invInventoryAllocatedEntity.setQty(mapValue.getQty() + invInventoryAllocatedEntity.getQty());
                }
                map.put(invInventoryAllocatedEntity.getShipmentDetailNo(), invInventoryAllocatedEntity);
            }
        }
        return map;
    }

    @Override
    public String returnShelfInventory(InventoryMoveOperatePojo inventoryOperatePojo) {
        inventoryOperatePojo.setTaskType(TaskTypeEnum.RETURN_SHELF.getTaskType());
        return inventoryMove.process(inventoryOperatePojo);
    }

    @Override
    public String returnShelfInventoryV2(InventoryMoveOperatePojo inventoryOperatePojo) {
        inventoryOperatePojo.setTaskType(TaskTypeEnum.EXCEPTION_SHELF.getTaskType());
        return inventoryMove.process(inventoryOperatePojo);
    }

    /**
     * 补货下架
     *
     * @param inventoryMoveOperatePojo
     * @return
     */
    @Override
    public String replenishOff(InventoryMoveOperatePojo inventoryMoveOperatePojo, Integer returnQty) {
        if (returnQty > 0) {
            // 需要减占用库存
            inventoryOccupy.occupyForReplenish(inventoryMoveOperatePojo.getTenantCode(),
                    inventoryMoveOperatePojo.getRelativeInventoryNo(), returnQty,
                    inventoryMoveOperatePojo.getTaskNo(), inventoryMoveOperatePojo.getTaskDetailNo());
        }
        // 做了0件不操作库存
        if (inventoryMoveOperatePojo.getOperateQty() == 0) {
            return null;
        }
        inventoryMoveOperatePojo.setTaskType(TaskTypeEnum.REPLENISH_OFF.getTaskType());
        inventoryMoveOperatePojo.setTargetLocationCode(LocationConstant.REPLENISH_DOCK);
        return inventoryMove.process(inventoryMoveOperatePojo);
    }

    @Override
    public String replenishOnShelf(InventoryMoveOperatePojo inventoryMoveOperatePojo) {
        inventoryMoveOperatePojo.setTaskType(TaskTypeEnum.REPLENISH_UPPER.getTaskType());
        return inventoryMove.process(inventoryMoveOperatePojo);
    }

    @Override
    public void redistributeInventory(InventoryRedistributeRequest inventoryRedistributeRequest) {
        if (CollectionUtils.isEmpty(inventoryRedistributeRequest.getDeliveryHeaderIds())) {
            return;
        }
        String tenantId = OperationUserContextHolder.getTenantCode();
        List<Integer> statusList = Lists.newArrayList(WmsOutBoundStatusEnum.FAIL_ALLOCATE.getStatus(),WmsOutBoundStatusEnum.INIT.getStatus());
        List<DeliveryOrderDo> deliveryOrderListDos = deliveryHeaderRepository.selectByWarehouseCode(inventoryRedistributeRequest.getDeliveryHeaderIds(), inventoryRedistributeRequest.getWarehouseCode(), statusList, tenantId);
        if (deliveryOrderListDos == null || deliveryOrderListDos.size() == 0) {
            log.info("该仓库不存在库存分配失败的发货单");
            return;
        }
        // 需要先过滤下那些不需要跑波次的订单，这块后面需要重构，现在目前没有一个统一的过滤出库单不能跑波次的地方
        deliveryOrderListDos = deliveryOrderListDos.stream().filter(item -> {
            if (item != null && (StringUtils.isNotBlank(item.getLaunchNo())
                    || WmsOutBoundStatusEnum.CANCEL.getStatus().equals(item.getCommandStatus()))
                    || WmsOutBoundStatusEnum.FORCE_OUTED.getStatus().equals(item.getStatus())) {
                /*已经分配波次的不能在分配,已经取消的不能分配*/
                log.warn("单据{}已经取消或者已经组波,不需要再分配", item.getDeliveryOrderCode());
                return false;
            }
            // 现货并且非海外和跨境仓的不能分配库存
            if (WmsOutboundBusinessUtils.isXh(item.getBizType(), item.getOrderTags())
                    && !warehouseConfig.isOverseaOrCrosseWarehouse(item.getWarehouseCode())) {
                log.warn("现货不需要分配库存。{}", item.getDeliveryOrderCode());
                return false;
            }
            // 新品来样不能分配库存
            if (java.util.Objects.equals(WmsBizTypeEnum.NEW_GOODS_SAMPLE.getBizType(),item.getBizType())) {
                log.warn("新品来样不需要分配库存。{}", item.getDeliveryOrderCode());
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        List<DeliveryOrderDo> finalDeliveryOrderListDos = deliveryOrderListDos;
        adminThreadPoolExecutor.execute(() -> {
            for (DeliveryOrderDo deliveryOrderDo : finalDeliveryOrderListDos) {
                try {
                    if(java.util.Objects.equals(WmsOutBoundStatusEnum.INIT.getStatus(),deliveryOrderDo.getStatus())
                            &&!com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum.ALLOWED_INITSTATUS_SELFALLOCATE_ORDERTYPE.contains(deliveryOrderDo.getOrderType())){
                        //初始化状态非手动分配库存单据类型单据不允许手动分配
                        throw new WmsOperationException(deliveryOrderDo.getDeliveryOrderCode()+"非手动分配库存单据类型单据不允许手动分配"+ com.poizon.fusion.utils.JsonUtils.serialize(com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum.ALLOWED_INITSTATUS_SELFALLOCATE_ORDERTYPE));
                    }
                    deliveryOrderModifyService.partAllocateInv(deliveryOrderDo.getDeliveryOrderCode());
                } catch (WmsException e) {
                    log.error("库存分配出错，错误信息:{}", e);
                    log.error("当前出库单号为:{}", deliveryOrderDo.getDeliveryOrderCode());
                }
            }
        });
    }

    @Override
    public void redistributeInventory(DeliveryHeaderRedistributeRequest request) {
        if (CollectionUtils.isEmpty(request.getDeliveryHeaderList())) {
            return;
        }
        for (String deliveryOrderCode : request.getDeliveryHeaderList()) {
            try {
                log.info("----------处理出库单{}----------", deliveryOrderCode);
                deliveryOrderModifyService.partAllocateInv(deliveryOrderCode);
                log.info("----------处理出库单结束{}----------", deliveryOrderCode);
            } catch (Exception e) {
                log.error("库存分配出错，错误信息:{}", e);
                log.error("当前出库单号为:{}", deliveryOrderCode);
            }
        }
    }

    /**
     * 解锁库存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int unfreezeQty(InventoryUnfreezeRequest inventoryUnfreezeRequest) {
        boolean lock = false;
        String lockKey = null;
        int status;
        try {
            // 1.查询原库存记录
            InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(inventoryUnfreezeRequest.getTenantCode(), inventoryUnfreezeRequest.getUniqueCode());
            if (inventoryDo == null) {
                throw new WmsException(WmsExceptionCode.INVENTORY_ADJUST_FIND_NULL.getMessage());
            }

            // 2.锁定库存,公用库存操作的锁防止并发
            String inventoryNo = inventoryDo.getInventoryNo();
            lockKey = InventoryCommonOperation.getInventoryLockName(inventoryDo.getTenantCode(), inventoryNo);
            lock = distributeLockUtil.tryLock(lockKey, lockTimeout);
            if (!lock) {
                throw new WmsException(INVENTORY_OPERATION_FAILED);
            }
            inventoryDo = inventoryRepository.queryInventoryDoByUniCode(inventoryUnfreezeRequest.getTenantCode(), inventoryUnfreezeRequest.getUniqueCode());
            if (inventoryDo.getFreezeQty() == null || inventoryDo.getFreezeQty() <= 0) {
                throw new WmsException(NOT_ENOUGH_STOCK);
            }

            //构建更新对象
            InventoryDo redoInventoryDo = new InventoryDo();
            redoInventoryDo.setFreezeQty(inventoryDo.getFreezeQty() - inventoryUnfreezeRequest.getOperateQty());
            redoInventoryDo.setId(inventoryDo.getId());
            redoInventoryDo.setUpdatedTime(new Date());
            if (inventoryRepository.updateByPrimaryKeySelective(redoInventoryDo) != 1) {
                throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL.getMessage());
            }
            status = 1;
        } catch (InterruptedException e) {
            log.error("interrupt, 库存操作系统异常:{}", e);
            Thread.currentThread().interrupt();
            throw new WmsException(INVENTORY_OPERATING_SYSTEM_EXCEPTION, e);
        } catch (Exception e) {
            log.error("库存操作系统异常:{}", e);
            throw new WmsException(INVENTORY_OPERATING_SYSTEM_EXCEPTION, e);
        } finally {
            //释放锁
            if (lock) {
                distributeLockUtil.release(lockKey);
            }
        }
        return status;
    }

    @Override
    public void modifyContainerCode(String inventoryNo, String tenantCode, String containerCode) {
        boolean lock = false;
        String lockKey = null;
        try {
            lockKey = InventoryCommonOperation.getInventoryLockName(tenantCode, inventoryNo);
            lock = distributeLockUtil.tryLock(lockKey, lockTimeout);
            if (!lock) {
                throw new WmsException(INVENTORY_OPERATION_FAILED);
            }

            // 1.查询原库存记录
            InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByInventoryNo(tenantCode, inventoryNo);
            if (inventoryDo == null) {
                throw new WmsException(WmsExceptionCode.INVENTORY_ADJUST_FIND_NULL.getMessage());
            }

            //构建更新对象
            InventoryDo inventoryDo4update = new InventoryDo();
            inventoryDo4update.setContainerCode(containerCode);
            inventoryDo4update.setId(inventoryDo.getId());
            //因为需要新增的库存行不存在，所以用库存纬度算md5去查数据库是否有这条记录
            InventoryDimensionPojo inventoryDimensionPojo = new InventoryDimensionPojo();
            BeanUtils.copyProperties(inventoryDo, inventoryDimensionPojo);
            inventoryDimensionPojo.setContainerCode(containerCode);
            String md5 = InventoryUtil.calcMd5(inventoryDimensionPojo);
            inventoryDo4update.setMd5(md5);
            if (inventoryRepository.updateByPrimaryKeySelective(inventoryDo4update) != 1) {
                throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL.getMessage());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("interrupted, 库存操作系统异常:{}", e);
            throw new WmsException(INVENTORY_OPERATING_SYSTEM_EXCEPTION, e);
        } catch (Exception e) {
            log.error("库存操作系统异常:{}", e);
            throw new WmsException(INVENTORY_OPERATING_SYSTEM_EXCEPTION, e);
        } finally {
            //释放锁
            if (lock) {
                distributeLockUtil.release(lockKey);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void detectInventory(List<String> uniqueCodes, String tenantCode, String warehouseCode, String orderCode, String orderType) {
        if (CollectionUtils.isEmpty(uniqueCodes)) {
            throw new WmsException("参数不能为空");
        }
        List<InventoryDo> inventoryDos = inventoryRepository.findByUniqueCodes(new HashSet<>(uniqueCodes), tenantCode);
        if (CollectionUtils.isEmpty(inventoryDos)) {
            log.warn("唯一码[{}]对应的库存行不存在", uniqueCodes);
            return;
        }

        for (InventoryDo inventoryDo : inventoryDos) {
            log.info("pink寄存交接删库存{}", JSON.toJSONString(inventoryDo));
            /*if (!InventoryTypeEnum.OPERATION_WAREHOUSE_TYPE.contains(inventoryDo.getInventoryType())) {
                log.info("寄存交接扣减库存, [inventoryNo={}, uniqueCode={}]非等真询问类型, 不处理",
                        inventoryDo.getInventoryNo(), inventoryDo.getUniqueCode());
                continue;
            }*/
            InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
            inventoryBasePojo.setTenantCode(OperationUserContextHolder.getTenantCode());
            inventoryBasePojo.setTaskType(inventoryDo.getEntryOrderType());
            inventoryBasePojo.setOperateQty(1);
            inventoryBasePojo.setTaskNo(inventoryDo.getEntryOrderCode());
            inventoryBasePojo.setTaskDetailNo(inventoryDo.getEntryOrderCode());
            inventoryBasePojo.setTaskResultNo(inventoryDo.getEntryOrderCode());
            inventoryBasePojo.setReferenceType(inventoryDo.getEntryOrderType());
            inventoryBasePojo.setReferenceNo(inventoryDo.getEntryOrderCode());
            inventoryBasePojo.setRelativeInventoryNo(inventoryDo.getInventoryNo());
            inventoryBasePojo.setReferenceDetailNo(inventoryDo.getEntryOrderCode());
            log.info("寄存交接扣减库存, 库存维度[{}]", inventoryBasePojo);
            this.ship(inventoryBasePojo);
        }

        List<WmsAllocatedDetailDto> allocatedDetailDtos = new ArrayList<>();
        for (InventoryDo inventoryDo : inventoryDos) {
            if (WmsBizTypeEnum.XIAN_HUO.getBizType().equals(inventoryDo.getBizType())) {
                continue;
            }
            WmsAllocatedDetailDto wmsAllocatedDetailDto = new WmsAllocatedDetailDto();
            wmsAllocatedDetailDto.setCisDimensionDetailRequestList(Collections.singletonList(buildCisDimensionDetail(inventoryDo)));
            wmsAllocatedDetailDto.setOrderDetailNo(orderCode);
            wmsAllocatedDetailDto.setTradeOrderNo(orderCode);
            wmsAllocatedDetailDto.setShouldQty(1);
            allocatedDetailDtos.add(wmsAllocatedDetailDto);
        }

        CisSubtractRequest cisSubtractRequest = new CisSubtractRequest();
        cisSubtractRequest.setOrderCode(orderCode);
        cisSubtractRequest.setOrderType("PINK_HANDOVER");
        cisSubtractRequest.setTenantCode(tenantCode);
        cisSubtractRequest.setRequestId(orderCode);
        cisSubtractRequest.setWmsAllocatedDetailDtoList(allocatedDetailDtos);
        cisSubtractRequest.setFromChannel("WMS");

        // 异步扣sci库存
        cisInventoryOperation.sendSubtractSciInventoryMsg(cisSubtractRequest);

        log.info("寄存交接出库消息uniqueCodes[{}]", uniqueCodes);
        outboundMessageProducer.outboundMessage(new OutboundDto(uniqueCodes, orderCode, false,
                OperationUserContextHolder.getTenantCode()));
    }

    @Override
    public Boolean cancelAllocatedByInventoryNo(InventoryAllocatedCancelByInventoryNoPojo inventoryAllocatedCancelByInventoryNoPojo) {
        InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
        inventoryBasePojo.setTenantCode(inventoryAllocatedCancelByInventoryNoPojo.getTenantId());
        inventoryBasePojo.setRelativeInventoryNo(inventoryAllocatedCancelByInventoryNoPojo.getInventoryNo());
        inventoryBasePojo.setOperateQty(inventoryAllocatedCancelByInventoryNoPojo.getQty());
        inventoryBasePojo.setReferenceNo(inventoryAllocatedCancelByInventoryNoPojo.getBillNo());
        inventoryBasePojo.setReferenceType(inventoryAllocatedCancelByInventoryNoPojo.getBillType());
        inventoryBasePojo.setReferenceDetailNo(inventoryAllocatedCancelByInventoryNoPojo.getBillDetailNo());
        inventoryBasePojo.setTaskType(TaskTypeEnum.RETURN_INVENTORY.getTaskType());
        inventoryAllocated.cancelAllocatedByInventoryNo(inventoryBasePojo);
        return Boolean.TRUE;
    }

    private CisDimensionDetailRequest buildCisDimensionDetail(InventoryDo inventoryDo) {
        CisDimensionDetailRequest dimensionDetailRequest = new CisDimensionDetailRequest();
        dimensionDetailRequest.setActualQty(1);
        dimensionDetailRequest.setUniqueCode(inventoryDo.getUniqueCode());
        dimensionDetailRequest.setOwnerCode(inventoryDo.getOwnerCode());
        dimensionDetailRequest.setSkuId(inventoryDo.getSkuId());
        dimensionDetailRequest.setWarehouseCode(inventoryDo.getWarehouseCode());
        dimensionDetailRequest.setTenantCode(inventoryDo.getTenantCode());
        dimensionDetailRequest.setBizType(inventoryDo.getBizType());
        return dimensionDetailRequest;
    }

    /**
     * 发送分配完成命中取消件无需返架的MQ
     * @param deliveryHeaderDo
     * @param inventoryAllocatedEntityList
     */
    private void sendAllocatedNotNeedReturnUpperMessage(DeliveryHeaderDo deliveryHeaderDo, List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList) {
        AllocatedNotNeedReturnUpperMessage allocatedNotNeedReturnUpperMessage = new AllocatedNotNeedReturnUpperMessage();
        allocatedNotNeedReturnUpperMessage.setDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
        allocatedNotNeedReturnUpperMessage.setOrderType(deliveryHeaderDo.getType());
        allocatedNotNeedReturnUpperMessage.setTenantCode(deliveryHeaderDo.getTenantCode());
        allocatedNotNeedReturnUpperMessage.setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
        allocatedNotNeedReturnUpperMessage.setInventoryAllocatedEntityList(inventoryAllocatedEntityList);
        com.poizon.scm.wms.producer.message.SendMessage<AllocatedNotNeedReturnUpperMessage> sendMessage = new com.poizon.scm.wms.producer.message.SendMessage<>();
        sendMessage.setMessageKey(deliveryHeaderDo.getDeliveryOrderCode());
        sendMessage.setTopic(WmsMessageQueueConstant.WMS_TOPIC);
        sendMessage.setTag(WmsMessageQueueConstant.ALLOCATED_NOT_NEED_RETURN_UPPER_TAG);
        sendMessage.setMessageContent(allocatedNotNeedReturnUpperMessage);
        sendMessageHandler.sendToNewCluster(sendMessage);
    }

}
