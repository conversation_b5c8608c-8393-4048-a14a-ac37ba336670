package com.poizon.scm.wms.service.frame;

import com.dw.scp.ship.enums.OutboundTypeEnum;
import com.poizon.scm.cis.api.inventory.CisDimensionDetailRequest;
import com.poizon.scm.cis.api.inventory.CisOrderDimensionDto;
import com.poizon.scm.cis.api.inventory.CisSubtractRequest;
import com.poizon.scm.cis.api.inventory.WmsAllocatedDetailDto;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryExtraInfoDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryExtraInfoRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.resp.DeliveryExtraInfoJsonContentResp;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailResultCommandRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.api.enums.WmsOutCommandStatusEnum;
import com.poizon.scm.wms.api.enums.WmsQualityLevelEnum;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.BaseOperationUserContext;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.dao.entitys.task.TaskEntity;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.ship.event.message.ShipEventParam;
import com.poizon.scm.wms.domain.ship.event.producer.DeliveryShipEventProducer;
import com.poizon.scm.wms.domain.task.biz.ship.ShipCommonHandler;
import com.poizon.scm.wms.pojo.third.notify.request.base.WmsOperateLogRequest;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.notifyPink.NotifyPinkService;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.shizhuang.shield.common.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:按出库单发货执行
 * @author:chaoyuan
 * @createTime:2024/1/11 16:03
 */
@Component
@Slf4j
public class DeliveryShipDataExecutionServer implements DataHandler<String> {

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Resource
    private DeliveryDetailRepository deliveryDetailRepository;

    @Resource
    private ICommodityQueryV2Service commodityQueryV2Service;


    @Autowired
    private InventoryRepository inventoryRepository;


    @Resource
    private OutboundMessageProducer outboundMessageProducer;

    @Resource
    private ShipCommonHandler shipCommonHandler;

    @Resource
    private DeliveryShipEventProducer deliveryShipEventProducer;

    @Resource
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;

    @Resource
    private ShipTaskDetailResultCommandRepository shipTaskDetailResultCommandRepository;

    @Resource
    private ShipTaskCommandRepository shipTaskCommandRepository;

    @Resource
    private CisInventoryOperation cisInventoryOperation;

    @Autowired
    private NotifyPinkService notifyPinkService;

    @Resource
    private DeliveryExtraInfoRepository deliveryExtraInfoRepository;

    private static final String NON_OCCUPY_XNCK = "NON_OCCUPY_XNCK";

    private static final String OPERATE_REAL_NAME = "系统自动";
    private static final String OPERATE_STATION_CODE = "0000";

    @Transactional
    @Override
    public void process(String deliveryOrderCode) {

        //出库单明细信息
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (Objects.isNull(deliveryHeaderDo)) throw new WmsException("出库单未查到");
        //出库单信息
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(), deliveryHeaderDo.getTenantCode());
        if (CollectionUtils.isEmpty(deliveryDetailDos)) throw new WmsException("出库单明细未查到");

        DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);

        List<InventoryDo> inventoryDos = inventoryRepository
                .queryInventoryDoByUniqueCode(deliveryDetailDos.stream().filter(x -> StringUtils.isNotBlank(x.getUniqueCode()))
                        .map(DeliveryDetailDo::getUniqueCode).collect(Collectors.toList()), deliveryHeaderDo.getTenantCode(), deliveryHeaderDo.getWarehouseCode());
        //校验出库单是否能发货
        verify(deliveryHeaderDo, deliveryDetailDos, inventoryDos);

        //幂等校验 条件= 出库状态=43则代表已经发货成功
        if (deliveryHeaderDo.getStatus().equals(WmsOutBoundStatusEnum.OUTED.getStatus())){
            log.info("命中幂等出库单已经发货完成");
            return;
        }

        Map<String, InventoryDo> inventoryDoMap = inventoryDos.stream().collect(Collectors.toMap(InventoryDo::getUniqueCode, v -> v, (i, j) -> i));

        //构建发货任务
        ShipTaskDo shipTaskDo = buildShipTaskDo(deliveryHeaderDo, deliveryDetailDos);
        List<ShipTaskDetailDo> shipTaskDetailDos = buildShipTaskDetailDos(deliveryHeaderDo, deliveryDetailDos, shipTaskDo, inventoryDoMap);
        List<ShipTaskDetailResultDo> shipTaskDetailResultDos = buildShipTaskDetailResultDos(shipTaskDetailDos);
        //发货任务落DB
        saveShipTask(shipTaskDo, shipTaskDetailDos, shipTaskDetailResultDos);

        TaskEntity taskEntity = BeanUtil.deepCopy(shipTaskDo, TaskEntity.class);

        //通知出库单发货
        shipCommonHandler.notifyDeliveryOrder(taskEntity, TaskStatusEnum.COMPLETE);
        //扣wms库存
        shipCommonHandler.executeShipInventory(taskEntity);
        //扣sci库存
        cisInventoryOperation.sendSubtractSciInventoryMsg(buildCisSubtractRequest(deliveryHeaderDo, deliveryDetailDos, deliveryExtraInfoDo, inventoryDoMap));

        //发布标准事件
        ShipEventParam shipEventParam = ShipEventParam.builder().deliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode())
                .uniqueCodes(shipTaskDetailDos.stream().map(ShipTaskDetailDo::getUniqueCode).collect(Collectors.toList()))
                .warehouseCode(deliveryHeaderDo.getWarehouseCode())
                .notExistShipTaskFlag(Boolean.FALSE)
                .build();
        deliveryShipEventProducer.publishShipEvent(shipEventParam);


        //通知外部系统单据发货完成
        log.info("虚拟发货出库单：{},", JsonUtils.serialize(deliveryHeaderDo));
        if(WmsOutBoundSubTypeEnum.THIRTY_DAY_BUSINESS_SELLER_RETURN_FIRST_SALE.getCode().equals(deliveryHeaderDo.getSubType())){
            return;
        }
        if(WmsOutBoundSubTypeEnum.INCORRECT_GOODS.getCode().equals(deliveryHeaderDo.getSubType())){
            // 通知mes记录虚拟发货操作日志
            WmsOperateLogRequest operateLogRequest = buildPinkOprateLogRequest(deliveryHeaderDo.getDeliveryOrderCode(), deliveryDetailDos.get(0).getUniqueCode());
            notifyPinkService.notifyPinkOperationLog(operateLogRequest);
        }else{

            outboundMessageProducer.xnckDeliveryShip2Ofc(deliveryOrderCode);
        }

    }

    private WmsOperateLogRequest buildPinkOprateLogRequest(String deliveryOrderCode, String uniqueCode) {
        WmsOperateLogRequest operateLogRequest = new WmsOperateLogRequest();
        operateLogRequest.setDeliveryOrderCode(deliveryOrderCode);
        operateLogRequest.setUniqueCode(uniqueCode);
        operateLogRequest.setOperatorId(0L);
        operateLogRequest.setOperateTime(new Date());
        operateLogRequest.setOperateRealName(OPERATE_REAL_NAME);
        operateLogRequest.setOperateStationCode(OPERATE_STATION_CODE);
        operateLogRequest.setOperateRemark("虚拟发货-退货虚拟发货成功");
        operateLogRequest.setOperateType(Integer.valueOf(OperateTypeEnum.INCORRECT_GOODS_SOURCE_ORDER_VIRTUAL_OUT.getType()));
        return operateLogRequest;

    }

    /**
     * 构建发货明细结果对象
     * @param shipTaskDetailDos
     * @return
     */
    private List<ShipTaskDetailResultDo> buildShipTaskDetailResultDos(List<ShipTaskDetailDo> shipTaskDetailDos) {
        List<ShipTaskDetailResultDo> resultDos = new ArrayList<>(shipTaskDetailDos.size());
        for (ShipTaskDetailDo detailDo : shipTaskDetailDos) {
            ShipTaskDetailResultDo detailResultDo = new ShipTaskDetailResultDo();
            detailResultDo.setDeleted(NumberUtils.INTEGER_ZERO);
            detailResultDo.setTaskNo(detailDo.getTaskNo());
            detailResultDo.setTaskType(detailDo.getTaskType());
            detailResultDo.setTaskDetailNo(detailDo.getDetailNo());

            // 商品基础信息
            detailResultDo.setBarcode(detailDo.getBarcode());
            detailResultDo.setUniqueCode(detailDo.getUniqueCode());
            detailResultDo.setSkuId(detailDo.getSkuId());
            detailResultDo.setQualityLevel(detailDo.getQualityLevel());
            detailResultDo.setOwnerCode(detailDo.getOwnerCode());
            detailResultDo.setVendorCode(detailDo.getVendorCode());
            detailResultDo.setTenantCode(detailDo.getTenantCode());
            detailResultDo.setWarehouseCode(detailDo.getWarehouseCode());

            detailResultDo.setInventoryNo(detailDo.getInventoryNo());

            //原始单号&效期
            detailResultDo.setMfgTime(detailDo.getMfgTime());
            detailResultDo.setExpTime(detailDo.getExpTime());
            detailResultDo.setOriginalOrderCode(detailDo.getOriginalOrderCode());

            /*第一次执行*/
            String taskDetailResultNo = IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL_RESULT.getSequenceType());
            detailResultDo.setResultNo(taskDetailResultNo);
            detailResultDo.setOperationQty(detailDo.getQty());

            // 操作人信息
            Date currentDate = new Date();
            detailResultDo.setVersion(0);
            detailResultDo.setCreatedTime(currentDate);
            detailResultDo.setUpdatedTime(currentDate);
            detailResultDo.setCreatedUserId(detailDo.getCreatedUserId());
            detailResultDo.setCreatedUserName(detailDo.getCreatedUserName());
            detailResultDo.setCreatedRealName(detailDo.getCreatedRealName());
            detailResultDo.setUpdatedUserId(detailDo.getCreatedUserId());
            detailResultDo.setUpdatedUserName(detailDo.getCreatedUserName());
            detailResultDo.setUpdatedRealName(detailDo.getUpdatedRealName());
            detailResultDo.setOperationUserId(detailDo.getCreatedUserId());
            detailResultDo.setOperationUserName(detailDo.getCreatedUserName());
            detailResultDo.setOperationRealName(detailDo.getCreatedRealName());
            resultDos.add(detailResultDo);
        }
        return resultDos;
    }


    private CisSubtractRequest buildCisSubtractRequest(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDos,
                                                       DeliveryExtraInfoDo deliveryExtraInfoDo, Map<String, InventoryDo> inventoryDoMap) {

        CisSubtractRequest cisSubtractRequest = new CisSubtractRequest();
        cisSubtractRequest.setOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
        cisSubtractRequest.setOrderType(getOrderType(deliveryHeaderDo, deliveryExtraInfoDo));
        cisSubtractRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        List<String> orderDetailNoList = deliveryDetailDos.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList());
        /*请求SCI的幂等键要加上明细编号,因为发货是通过明细发的*/
        String detailNos = org.springframework.util.StringUtils.collectionToDelimitedString(orderDetailNoList, "_");
        String requestId = String.join("_", deliveryHeaderDo.getDeliveryOrderCode(), detailNos);
        cisSubtractRequest.setRequestId(MD5Util.getMD5Text(requestId));

        //因为是未在架的虚拟出库。
        List<WmsAllocatedDetailDto> allocatedDetails = new ArrayList<>();
        cisSubtractRequest.setWmsAllocatedDetailDtoList(allocatedDetails);
        for (DeliveryDetailDo orderLine : deliveryDetailDos) {
            WmsAllocatedDetailDto item = buildWmsAllocatedDetailDto(orderLine, inventoryDoMap.get(orderLine.getUniqueCode()), deliveryHeaderDo);
            allocatedDetails.add(item);
        }
        cisSubtractRequest.setFromChannel("WMS");
        return cisSubtractRequest;

    }

    private String getOrderType(DeliveryHeaderDo deliveryHeaderDo, DeliveryExtraInfoDo deliveryExtraInfoDo) {
        if(StringUtils.isNotBlank(deliveryHeaderDo.getShipDirection()) && ShipDirectionEnum.BS_XPF.getCode().equals(deliveryHeaderDo.getShipDirection())){
            return NON_OCCUPY_XNCK;
        }

        if (Objects.isNull(deliveryExtraInfoDo) || StringUtils.isEmpty(deliveryExtraInfoDo.getJsonContent())) {
            return deliveryHeaderDo.getType();
        }
        try {
            DeliveryExtraInfoJsonContentResp resp = JsonUtils.deserialize(deliveryExtraInfoDo.getJsonContent(), DeliveryExtraInfoJsonContentResp.class);
            if (resp.getCross2Local() != null && resp.getCross2Local()) {
                return NON_OCCUPY_XNCK;
            }
            return deliveryHeaderDo.getType();
        } catch (Exception e) {
            log.error("未在架虚出, 解析出库单扩展表json失败 {}", deliveryExtraInfoDo);
            return deliveryHeaderDo.getType();
        }
    }

    private WmsAllocatedDetailDto buildWmsAllocatedDetailDto(DeliveryDetailDo deliveryDetailDo, InventoryDo inventoryDo, DeliveryHeaderDo deliveryHeaderDo) {
        WmsAllocatedDetailDto result = new WmsAllocatedDetailDto();
        result.setOrderDetailNo(deliveryDetailDo.getDetailNo());
        CisOrderDimensionDto dimensionDto = createCisOrderDimension(deliveryDetailDo, inventoryDo, deliveryHeaderDo);
        result.setOrderInventoryDimensionRequest(dimensionDto);
        CisDimensionDetailRequest detailRequest = buildDetailRequest(deliveryDetailDo, inventoryDo, deliveryHeaderDo);
        result.setCisDimensionDetailRequestList(Collections.singletonList(detailRequest));
        result.setShouldQty(detailRequest.getActualQty());
        result.setTradeOrderNo(deliveryDetailDo.getTradeOrderNo());
        return result;
    }

    private CisDimensionDetailRequest buildDetailRequest(DeliveryDetailDo deliveryDetailDo, InventoryDo inventoryDo, DeliveryHeaderDo deliveryHeaderDo) {
        CisDimensionDetailRequest detailRequest = new CisDimensionDetailRequest();
        detailRequest.setActualQty(deliveryDetailDo.getPlanQty());
        detailRequest.setWarehouseCode(deliveryDetailDo.getWarehouseCode());
        detailRequest.setBizType(deliveryDetailDo.getBizType());
        detailRequest.setExpTime(inventoryDo.getExpTime());
        detailRequest.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
        detailRequest.setLotsNo(StringUtils.EMPTY);
        detailRequest.setQualityLevel(inventoryDo.getQualityLevel());
        detailRequest.setOriginalOrderCode(inventoryDo.getOriginalOrderCode());
        detailRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        // 原始业务类型为企业寄存且现有业务类型为保税企业的时候, 不传唯一码, 其他都传唯一码
        if (!(WmsBizTypeEnum.BAO_SHUI_QI_YE.getBizType().equals(deliveryDetailDo.getBizType())
                && (WmsBizTypeEnum.QI_YE_JI_CUN.getBizType().equals(deliveryDetailDo.getOriginalBizType()) || ShipDirectionEnum.BS_XPF.getCode().equals(deliveryHeaderDo.getShipDirection())))) {
            detailRequest.setUniqueCode(deliveryDetailDo.getUniqueCode());
        }
        detailRequest.setOwnerCode(inventoryDo.getOwnerCode());
        detailRequest.setSkuId(inventoryDo.getSkuId());
        detailRequest.setVendorCode(inventoryDo.getVendorCode());
        detailRequest.setOriginalCountry(inventoryDo.getOriginalCountry());
        return detailRequest;
    }

    private CisOrderDimensionDto createCisOrderDimension(DeliveryDetailDo deliveryDetailDo, InventoryDo inventoryDo, DeliveryHeaderDo deliveryHeaderDo) {
        CisOrderDimensionDto dimensionDto = new CisOrderDimensionDto();
        dimensionDto.setBizType(deliveryDetailDo.getBizType());
        dimensionDto.setWarehouseCode(deliveryDetailDo.getWarehouseCode());
        dimensionDto.setTenantCode(OperationUserContextHolder.getTenantCode());
        dimensionDto.setQualityLevel(deliveryDetailDo.getQualityLevel());
        // 原始业务类型为企业寄存且现有业务类型为保税企业的时候, 不传唯一码, 其他都传唯一码
        if (!(WmsBizTypeEnum.BAO_SHUI_QI_YE.getBizType().equals(deliveryDetailDo.getBizType())
                && (WmsBizTypeEnum.QI_YE_JI_CUN.getBizType().equals(deliveryDetailDo.getOriginalBizType()) || ShipDirectionEnum.BS_XPF.getCode().equals(deliveryHeaderDo.getShipDirection())))) {
            dimensionDto.setUniqueCode(deliveryDetailDo.getUniqueCode());
        }
        dimensionDto.setOriginalOrderCode(inventoryDo.getOriginalOrderCode());
        dimensionDto.setOwnerCode(deliveryDetailDo.getOwnerCode());
        dimensionDto.setSkuId(deliveryDetailDo.getSkuId());
        dimensionDto.setVendorCode(inventoryDo.getVendorCode());
        dimensionDto.setOriginalCountry(inventoryDo.getOriginalCountry());
        return dimensionDto;
    }


    /**
     * 校验出库单是否能发货
     *
     * @param deliveryHeaderDo
     * @param deliveryDetailDos
     * @param inventoryDos
     */
    private void verify(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDos, List<InventoryDo> inventoryDos) {

        //取消校验
        if (WmsOutCommandStatusEnum.CANCEL.getStatus().equals(deliveryHeaderDo.getCommandStatus())
                || deliveryDetailDos.stream().anyMatch(x -> WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(x.getCancelFlag()))) {
            //shippingCancelService.executeCancel(pojo.getTaskNo(), delivery.getDeliveryOrderCode(), delivery.getType());
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_EMPTY_ERROR);
        }

        //库存校验
        if (CollectionUtils.isEmpty(inventoryDos)) {
            throw new WmsOperationException(WmsExceptionCode.UPPER_BATCH_NO_NOT_EXIST);
        }

    }


    private void saveShipTask(ShipTaskDo shipTaskDo, List<ShipTaskDetailDo> shipTaskDetailDos, List<ShipTaskDetailResultDo> shipTaskDetailResultDos) {
        int saveHeaderResult = shipTaskCommandRepository.save(shipTaskDo);
        if (saveHeaderResult != 1) {
            throw new WmsException("插入发货任务头报错");
        }

        /*按照出库单明细分组获取当前明细分配的数量*/
        int count = shipTaskDetailCommandRepository.batchSave(shipTaskDetailDos);
        if (count != shipTaskDetailDos.size()) {
            throw new WmsException("插入发货任务明细报错");
        }

        shipTaskDetailResultCommandRepository.batchSave(shipTaskDetailResultDos);

    }

    /**
     * 构建发货任务明细
     *
     * @param deliveryHeaderDo
     * @param deliveryDetailDos
     * @param shipTaskDo
     * @param inventoryDoMap
     * @return
     */
    private List<ShipTaskDetailDo> buildShipTaskDetailDos(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDos, ShipTaskDo shipTaskDo, Map<String, InventoryDo> inventoryDoMap) {
        List<ShipTaskDetailDo> result = new ArrayList<>();

        Set<String> skuIdSet = deliveryDetailDos.stream().map(DeliveryDetailDo::getSkuId).collect(Collectors.toSet());
        Map<String, SkuCommonRspDomain> skuMap = commodityQueryV2Service.querySkuCommonMapBySkuIds(OperationUserContextHolder.getTenantCode(), skuIdSet);


        for (DeliveryDetailDo detail : deliveryDetailDos) {
            ShipTaskDetailDo shipTaskDetail = new ShipTaskDetailDo();

            if (!inventoryDoMap.containsKey(detail.getUniqueCode())) {
                //目前只支持唯一码模式～
                throw new WmsOperationException("目前只支持唯一码模式出库");
            }
            InventoryDo inventoryResponse = inventoryDoMap.get(detail.getUniqueCode());
            shipTaskDetail.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            shipTaskDetail.setTaskNo(shipTaskDo.getTaskNo());
            shipTaskDetail.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            shipTaskDetail.setBizType(detail.getBizType().byteValue());
            shipTaskDetail.setReferenceNo(detail.getDeliveryOrderCode());
            shipTaskDetail.setReferenceType(deliveryHeaderDo.getType());
            shipTaskDetail.setReferenceDetailNo(detail.getDetailNo());
            /*库存编号从任务结果中获取*/
            shipTaskDetail.setInventoryNo(inventoryResponse.getInventoryNo());
            shipTaskDetail.setContainerCode(inventoryResponse.getContainerCode());
            shipTaskDetail.setQty(detail.getPlanQty());
            shipTaskDetail.setLocationCode(detail.getLocationCode());
            shipTaskDetail.setStatus(TaskStatusEnum.COMPLETE.getStatus().byteValue());
            shipTaskDetail.setOperationQty(detail.getPlanQty());
            shipTaskDetail.setPoNo(inventoryResponse.getOriginalOrderCode());
            shipTaskDetail.setOriginalDetailNo(inventoryResponse.getOriginalOrderCode());
            shipTaskDetail.setWarehouseCode(detail.getWarehouseCode());
            shipTaskDetail.setTenantCode(detail.getTenantCode());
            shipTaskDetail.setOwnerCode(detail.getOwnerCode());
            shipTaskDetail.setVendorCode(detail.getVendorCode());
            shipTaskDetail.setBarcode(StringUtils.EMPTY);
            shipTaskDetail.setUniqueCode(detail.getUniqueCode());
            shipTaskDetail.setSkuId(detail.getSkuId());
            shipTaskDetail.setQualityLevel(detail.getQualityLevel());
            shipTaskDetail.setUom(StringUtils.EMPTY);
            shipTaskDetail.setAllocatedNo(StringUtils.EMPTY);
            shipTaskDetail.setFlowCode(StringUtils.EMPTY);
            shipTaskDetail.setCreatedUserId(BaseOperationUserContext.userId);
            shipTaskDetail.setCreatedUserName(BaseOperationUserContext.userName);
            shipTaskDetail.setCreatedRealName(BaseOperationUserContext.realName);
            shipTaskDetail.setMfgTime(inventoryResponse.getMfgTime());
            shipTaskDetail.setExpTime(inventoryResponse.getExpTime());
            shipTaskDetail.setOriginalOrderCode(inventoryResponse.getOriginalOrderCode());
            shipTaskDetail.setBatchNo(inventoryResponse.getBatchNo());
            shipTaskDetail.setFirstReceivedTime(inventoryResponse.getFirstReceivedTime());
            shipTaskDetail.setEntryOrderCode(inventoryResponse.getEntryOrderCode());
            fillSkuInfo(skuMap, shipTaskDetail);
            result.add(shipTaskDetail);
        }
        return result;
    }

    private void fillSkuInfo(Map<String, SkuCommonRspDomain> skuMap, ShipTaskDetailDo shipTaskDetail) {
        SkuCommonRspDomain skuCommonRspDomain = skuMap.get(shipTaskDetail.getSkuId());
        if (skuCommonRspDomain == null) {
            log.error("sku不存在:{}", shipTaskDetail.getSkuId());
            throw new WmsException("sku不存在");
        }
        shipTaskDetail.setSpecs(skuCommonRspDomain.getPropertyText());
        shipTaskDetail.setGoodsTitle(skuCommonRspDomain.getSpuName());
        shipTaskDetail.setGoodsPic(skuCommonRspDomain.getSkuLogoUrl());
        shipTaskDetail.setGoodsArticleNumber(skuCommonRspDomain.getArtNoMain());
        shipTaskDetail.setBarcode(skuCommonRspDomain.getBarcode());
    }

    private ShipTaskDo buildShipTaskDo(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDos) {
        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        String warehouseCode = deliveryHeaderDo.getWarehouseCode();
        taskDo.setReferenceType(deliveryHeaderDo.getType());
        taskDo.setReferenceNo(deliveryHeaderDo.getDeliveryOrderCode());
        taskDo.setStatus(TaskStatusEnum.COMPLETE.getStatus());
        taskDo.setWarehouseCode(warehouseCode);
        taskDo.setTenantCode(deliveryHeaderDo.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setTotalQty(deliveryDetailDos.size());
        taskDo.setInitialTotalQty(deliveryDetailDos.size());
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        Date now = new Date();
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setCreatedUserId(deliveryHeaderDo.getCreatedUserId());
        taskDo.setCreatedUserName(deliveryHeaderDo.getCreatedUserName());
        taskDo.setCreatedRealName(deliveryHeaderDo.getCreatedRealName());
        taskDo.setOperationUserId(deliveryHeaderDo.getCreatedUserId());
        taskDo.setOperationRealName(deliveryHeaderDo.getCreatedRealName());
        taskDo.setOperationUserName(deliveryHeaderDo.getCreatedUserName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        return taskDo;
    }


}
