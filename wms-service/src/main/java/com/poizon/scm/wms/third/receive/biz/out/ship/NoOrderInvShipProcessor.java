package com.poizon.scm.wms.third.receive.biz.out.ship;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.domain.flow.rule.base.WmsFlowCodeEnum;
import com.poizon.scm.wms.domain.outbound.ship.ShipTaskNothingnessCommandService;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskCreateParam;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskDetailParam;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskHeaderParam;
import com.poizon.scm.wms.domain.ship.event.message.ShipEventParam;
import com.poizon.scm.wms.domain.ship.event.producer.DeliveryShipEventProducer;
import com.poizon.scm.wms.pojo.third.notify.base.task.BaseTaskDetailRequest;
import com.poizon.scm.wms.pojo.third.receive.request.TaskReceiveRequest;
import com.poizon.scm.wms.service.shipping.ShippingService;
import com.poizon.scm.wms.third.receive.biz.out.ship.base.AbstractShipProcessor;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @Date 2023/12/14 15:44
 * @Description 客制维修C端鉴别
 */
@Component
@Slf4j
public class NoOrderInvShipProcessor extends AbstractShipProcessor {
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private ShipTaskNothingnessCommandService commandService;
    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    private ShippingService shippingService;
    @Resource
    private DeliveryShipEventProducer deliveryShipEventProducer;
    @Override
    public List<String> getTenantIdList() {
        return Lists.newArrayList(TenantEnum.DW.getCode());
    }
    @Override
    public Boolean matchProcessor(DeliveryHeaderDo deliveryHeaderDo) {
        String type = deliveryHeaderDo.getType();
        Integer bizType = deliveryHeaderDo.getBizType();
        // 客制、维修、C端鉴别
        List<String> deliveryTypes = Lists.newArrayList(WmsOutBoundTypeEnum.KZCMJ.getType(),
                WmsOutBoundTypeEnum.WXCFWS.getType(), WmsOutBoundTypeEnum.WXCMJ.getType(), WmsOutBoundTypeEnum.SWJB.getType());
        // 新品来样
        Boolean newProductFlag = WmsOutBoundTypeEnum.THCK.getType().equals(type)
                && WmsBizTypeEnum.NEW_GOODS_SAMPLE.getBizType().compareTo(bizType) == 0;
        if (deliveryTypes.contains(type) || newProductFlag) {
            return true;
        }
        return false;
    }
    @Override
    public void handle(TaskReceiveRequest receiveRequest, DeliveryHeaderDo deliveryHeaderDo) {
        String deliveryOrderCode  = deliveryHeaderDo.getDeliveryOrderCode();
        String tenantCode = deliveryHeaderDo.getTenantCode();
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.
                queryDeliveryDetailByDeliveryCode(deliveryOrderCode, tenantCode);
        //单据发货状态更新
        shippingService.simpleShipment(deliveryHeaderDo);
        //保存出库单承运商信息
        shippingService.saveDeliveryLogistics(receiveRequest,deliveryHeaderDo.getType());
        Set<String> uniqueCodeList = deliveryDetailDos.stream().map(DeliveryDetailDo::getUniqueCode).collect(Collectors.toSet());
        List<InventoryDo> inventoryDoList = inventoryRepository.findByUniqueCodes(uniqueCodeList, tenantCode);
        reSetWarehouseCodeAndSignId(deliveryHeaderDo.getWarehouseCode(), receiveRequest.getSignId());
        commandService.createAndShipTaskForInvNothingness(buildShipTaskCreateParam(receiveRequest, deliveryHeaderDo, deliveryDetailDos, inventoryDoList));
        //发布出库完成事件消息
        List<String> tradeOrderNoList = receiveRequest.getTaskDetails().stream().
                map(BaseTaskDetailRequest::getTradeOrderNo).collect(Collectors.toList());
        ShipEventParam shipEventParam = ShipEventParam.builder().deliveryOrderCode(receiveRequest.getOrderCode())
                .tradeOrderNos(tradeOrderNoList).build();
        deliveryShipEventProducer.publishShipEvent(shipEventParam);
    }
    private ShipTaskCreateParam buildShipTaskCreateParam(TaskReceiveRequest receiveRequest,
                                                         DeliveryHeaderDo deliveryHeaderDo,
                                                         List<DeliveryDetailDo> deliveryDetailDos,
                                                         List<InventoryDo> inventoryDoList) {
        ShipTaskCreateParam shipTaskCreateParam = new ShipTaskCreateParam();
        Map<String, InventoryDo> invMap = inventoryDoList.stream().collect(Collectors.toMap(InventoryDo::getUniqueCode, Function.identity(), (oldItem, newItem) -> newItem));
        ShipTaskHeaderParam header = new ShipTaskHeaderParam();
        header.setReferenceType(deliveryHeaderDo.getType());
        ShipTaskDetailParam shipTaskDetailParam;
        List<ShipTaskDetailParam> detailList = new ArrayList<>();
        for (DeliveryDetailDo deliveryDetailDo : deliveryDetailDos) {
            shipTaskDetailParam = new ShipTaskDetailParam();
            shipTaskDetailParam.setOwnerCode(deliveryDetailDo.getOwnerCode());
            shipTaskDetailParam.setFlowCode(WmsFlowCodeEnum.IMMEDIATE_SHIP_OUT.getFlowCode());
            shipTaskDetailParam.setBizType(deliveryDetailDo.getBizType());
            shipTaskDetailParam.setBarcode(deliveryDetailDo.getBarcode());
            String uniqueCode = deliveryDetailDo.getUniqueCode();
            shipTaskDetailParam.setUniqueCode(uniqueCode);
            shipTaskDetailParam.setSkuId(deliveryDetailDo.getSkuId());
            shipTaskDetailParam.setQty(deliveryDetailDo.getPlanQty());
            InventoryDo inventoryDo = invMap.getOrDefault(uniqueCode,new InventoryDo());
            shipTaskDetailParam.setInventoryNo(Optional.ofNullable(inventoryDo.getInventoryNo()).orElse(""));
            shipTaskDetailParam.setLocationCode(Optional.ofNullable(inventoryDo.getLocationCode()).orElse(""));
            shipTaskDetailParam.setReferenceDetailNo(deliveryDetailDo.getDetailNo());
            shipTaskDetailParam.setReferenceNo(deliveryDetailDo.getDeliveryOrderCode());
            shipTaskDetailParam.setExpTime(inventoryDo.getExpTime());
            shipTaskDetailParam.setMfgTime(inventoryDo.getMfgTime());
            shipTaskDetailParam.setOriginalOrderCode(Optional.ofNullable(inventoryDo.getOriginalOrderCode()).orElse(""));
            shipTaskDetailParam.setWarehouseCode(deliveryDetailDo.getWarehouseCode());
            shipTaskDetailParam.setOriginalDetailNo("");
            shipTaskDetailParam.setGoodsTitle(deliveryDetailDo.getGoodsTitle());
            shipTaskDetailParam.setGoodsPic(deliveryDetailDo.getGoodsPic());
            shipTaskDetailParam.setGoodsArticleNumber(deliveryDetailDo.getGoodsArticleNumber());
            shipTaskDetailParam.setQualityLevel(deliveryDetailDo.getQualityLevel());
            shipTaskDetailParam.setSpecs(deliveryDetailDo.getSpecs());
            shipTaskDetailParam.setUom("");
            shipTaskDetailParam.setAllocatedNo("");
            detailList.add(shipTaskDetailParam);
        }
        header.setReferenceNo(deliveryHeaderDo.getDeliveryOrderCode());
        header.setFlowCode(WmsFlowCodeEnum.IMMEDIATE_SHIP_OUT.getFlowCode());
        header.setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
        header.setExpressNo(receiveRequest.getTaskDetails().get(0).getExpressCode());
        shipTaskCreateParam.setHeader(header);
        shipTaskCreateParam.setDetail(detailList);
        return shipTaskCreateParam;
    }
}