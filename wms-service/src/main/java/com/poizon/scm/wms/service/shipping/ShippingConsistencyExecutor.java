package com.poizon.scm.wms.service.shipping;

import com.alibaba.fastjson.JSON;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.shizhuang.duapp.erp.api.model.request.WmsNoticeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * date 2020-12-14
 */
@Slf4j
@Component
public class ShippingConsistencyExecutor {

    @Autowired
    private SendMessageHandler sendMessageHandler;

    @EventualConsistency
    public void execute(SendMessage<WmsNoticeDto> sendMessage) {
        String messageId = sendMessageHandler.process(sendMessage);
        if (StringUtils.isBlank(messageId)) {
            log.error("execute fail send message={}", JSON.toJSONString(sendMessage));
            throw new WmsOperationException("execute fail send message!");
        }
    }
}
