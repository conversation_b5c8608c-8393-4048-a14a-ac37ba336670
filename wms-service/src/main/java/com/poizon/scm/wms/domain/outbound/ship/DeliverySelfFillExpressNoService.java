package com.poizon.scm.wms.domain.outbound.ship;

import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryLogisticDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryLogisticRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackRepository;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.outbound.returngoods.service.WmsPackService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.handler.ExpressPrintHandler;
import com.poizon.scm.wms.domain.outbound.ship.param.DeliverySelfFillExpressParam;
import com.poizon.scm.wms.domain.ship.event.message.ShipEventParam;
import com.poizon.scm.wms.domain.ship.event.producer.DeliveryShipEventProducer;
import com.poizon.scm.wms.util.enums.ExpressChannelEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.enums.WmsPackStatusEnum;
import com.poizon.scm.wms.util.framework.RequestException;
import com.poizon.scm.wms.util.framework.RequestExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum.statusAfterPacking;

/**
 * 自填运单处理
 * <AUTHOR>
 * @date 2022/05/05
 */
@Slf4j
@Component
public class DeliverySelfFillExpressNoService {

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Resource
    private DeliveryLogisticRepository deliveryLogisticRepository;
    @Resource
    private WmsPackRepository wmsPackRepository;
    @Resource
    private ExpressPrintHandler expressPrintHandler;
    @Resource
    private WmsPackService wmsPackService;
    @Resource
    private DeliveryShipEventProducer deliveryShipEventProducer;

    /**
     * 校验出库单信息：
     * 单据是否存在
     * 单据状态是否正确
     */
    public DeliveryHeaderDo validateDeliveryOrder(String deliveryOrderCode) {
        //出库单状态为装箱完成, 才能打印面单
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsOperationException("出库单不存在");
        }
        return deliveryHeaderDo;
    }
    /**
     * 校验出库单包裹信息：
     * 包裹信息是否为空
     * 包裹数量=子运单数
     */
    public List<WmsPackDo> validateWmsPack(String deliveryOrderCode,int subExpressCodesSize) {
        //查询包裹信息
        List<WmsPackDo> wmsPackDoList = wmsPackRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if(CollectionUtils.isEmpty(wmsPackDoList)){
            throw new WmsException("包裹为空");
        }
        if(CollectionUtils.isNotEmpty(wmsPackDoList)&&
                !Objects.equals(wmsPackDoList.size(),subExpressCodesSize)){
                throw new RequestException(RequestExceptionCode.UPDATE_EXPRESS_ERROR);
        }
        return wmsPackDoList;
    }
    /**
     * 校验出库单承运商信息：
     * 承运商不能为空
     * 自填面单承运商才能回填面单
     */
    public DeliveryLogisticDo validateDeliveryLogistic(String deliveryOrderCode) {
        //出库单承运商信息
        DeliveryLogisticDo deliveryLogisticDo = deliveryLogisticRepository.queryFirstOneByDeliveryOrderCode(deliveryOrderCode);
        //校验是否存在
       if(Objects.isNull(deliveryLogisticDo)){
           throw new RequestException(WmsExceptionCode.PARAMS_ERROR.getCode(), "出库单没有承运商信息");
       }
       if(!Objects.equals(ExpressChannelEnum.ZTMD.getCode(),deliveryLogisticDo.getCarrierCode())){
           throw new RequestException(WmsExceptionCode.PARAMS_ERROR.getCode(), "承运商为自填面单才能回填面单");
       }
        return deliveryLogisticDo;
    }

    /**
     * 发货单自填面单
     */
    @Transactional(rollbackFor = Exception.class)
    public void deliveryFillExpressNo(DeliverySelfFillExpressParam deliverySelfFillExpressParam,DeliveryHeaderDo deliveryHeaderDo,List<WmsPackDo> wmsPackDoList) {
        if (!statusAfterPacking.contains(deliveryHeaderDo.getStatus())) {
            //如果不是整单完成状态则走整单完成逻辑
            wmsPackService.completeWholeOrder(deliveryHeaderDo.getDeliveryOrderCode());
        }
        Integer packSize = wmsPackDoList.size();
        String deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
        List<String> subExpressCodes = deliverySelfFillExpressParam.getSubExpressCodes();
        String expressCode = deliverySelfFillExpressParam.getExpressCode();
        String carrierCode = deliverySelfFillExpressParam.getCarrierCode();
        String carrierName = deliverySelfFillExpressParam.getCarrierName();
        Date bindingTime = new Date();
        List<WmsPackDo> updateList = new ArrayList<>();
        List<WmsLogisticsBillDo> logisticsBills = new ArrayList<>();
        WmsPackDo updatePackDo = null;
        WmsLogisticsBillDo wmsLogisticsBillDo = null;
        for (int i=0;i<packSize;i++) {
            WmsPackDo wmsPack = wmsPackDoList.get(i);
            updatePackDo = new WmsPackDo();
            updatePackDo.setId(wmsPack.getId());
            updatePackDo.setLogisticsCode(carrierCode);
            updatePackDo.setLogisticsName(carrierName);
            updatePackDo.setExpressCode(expressCode);
            updatePackDo.setSubExpressCode(subExpressCodes.get(i));
            updatePackDo.setBindingTime(bindingTime);
            updatePackDo.setPackStatus(WmsPackStatusEnum.PRINTED.getStatus());
            if (WmsOutBoundStatusEnum.outComplete.contains(deliveryHeaderDo.getStatus())){
                updatePackDo.setPackStatus(WmsPackStatusEnum.SHIPPED.getStatus());
            }
            wmsLogisticsBillDo = new WmsLogisticsBillDo();
            wmsLogisticsBillDo.setDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
            wmsLogisticsBillDo.setDeliveryOrderType(deliveryHeaderDo.getType());
            wmsLogisticsBillDo.setLogisticsCode(carrierCode);
            wmsLogisticsBillDo.setLogisticsName(carrierName);
            wmsLogisticsBillDo.setExpressCode(expressCode);
            wmsLogisticsBillDo.setSubExpressCode(subExpressCodes.get(i));
            wmsLogisticsBillDo.setTenantCode(OperationUserContextHolder.getTenantCode());
            logisticsBills.add(wmsLogisticsBillDo);
            updateList.add(updatePackDo);
        }
        //插入面单数据并更新包裹数据
        expressPrintHandler.updatePackInfo(deliveryOrderCode,updateList,logisticsBills);
        if (WmsOutBoundStatusEnum.outComplete.contains(deliveryHeaderDo.getStatus())){
            //如果出库已完成再回填面单补发出库完成时间，兼容门道先发货后打单逻辑
            ShipEventParam shipEventParam = ShipEventParam.builder().deliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode()).build();
            deliveryShipEventProducer.publishShipEvent(shipEventParam);
        }
    }
}
