package com.poizon.scm.wms.service.delivery.operate.handler;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.poizon.scm.wms.adapter.container.model.ContainerDetailDo;
import com.poizon.scm.wms.adapter.container.model.param.rpc.ContainerUnPackParam;
import com.poizon.scm.wms.adapter.container.model.rpc.ContainerDetailPageDto;
import com.poizon.scm.wms.adapter.container.repository.ContainerDetailRepository;
import com.poizon.scm.wms.adapter.container.repository.rpc.ContainerLpnRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryAheadCancelDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryAheadCancelRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.command.PickTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.api.command.outbound.request.WmsDbDeliveryCancelRequest;
import com.poizon.scm.wms.api.enums.FpOrderOutEvenEnum;
import com.poizon.scm.wms.api.enums.InterceptTypeEnum;
import com.poizon.scm.wms.api.enums.WmsQualityLevelEnum;
import com.poizon.scm.wms.api.enums.pick.PickOperationEventEnum;
import com.poizon.scm.wms.api.enums.pick.PickOperationStepEventEnum;
import com.poizon.scm.wms.api.mq.outbound.pick.PickTaskDetailStatusChange;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.inbound.quality.quality.process.SortingQualityAfterUpperProcessor;
import com.poizon.scm.wms.domain.inner.container.instance.ContainerOperateService;
import com.poizon.scm.wms.domain.inner.container.instance.entity.param.TakeContainerItemParam;
import com.poizon.scm.wms.domain.outbound.config.DeliverySwitch;
import com.poizon.scm.wms.domain.outbound.container.lpn.DeliveryContainerQueryService;
import com.poizon.scm.wms.domain.outbound.container.lpn.resp.ContainerDetailResult;
import com.poizon.scm.wms.domain.outbound.container.lpn.resp.ScpContainerInstanceResult;
import com.poizon.scm.wms.domain.outbound.fp.processor.FPOrderOutProcessor;
import com.poizon.scm.wms.domain.outbound.pick.event.PickTaskDetailStatusMessageProducer;
import com.poizon.scm.wms.domain.outbound.pick.service.param.cancel.PickTaskCancelParam;
import com.poizon.scm.wms.domain.outbound.pick.service.processor.opearte.PickTaskCancel;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.outbound.report.DeliveryOrderDetailOperateEvent;
import com.poizon.scm.wms.domain.outbound.report.OutboundOrderEvent;
import com.poizon.scm.wms.domain.outbound.ship.ShipTaskCommandService;
import com.poizon.scm.wms.domain.strategy.StrategyConfigCodeEnum;
import com.poizon.scm.wms.domain.strategy.WmsStrategyFactory;
import com.poizon.scm.wms.domain.strategy.cancel.DeliveryPickedCancelUpdateStrategyContext;
import com.poizon.scm.wms.domain.task.TaskFactory;
import com.poizon.scm.wms.domain.upper.enums.UpperTypeEnum;
import com.poizon.scm.wms.lpn.api.container.enums.LpnContainerStepEnum;
import com.poizon.scm.wms.pojo.task.operation.modify.CancelTaskPojo;
import com.poizon.scm.wms.service.config.DeliveryCancelContainerConfig;
import com.poizon.scm.wms.service.delivery.config.DeliveryAllocateWarehouseConfig;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.returnshelf.DeliveryExceptionDetailGenerator;
import com.poizon.scm.wms.service.shipping.CombineDeliveryShipOrderService;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import com.shizhuang.athena.api.inventory.WmsInventoryApi;
import com.shizhuang.athena.api.request.inventory.handover.ModifyAttr4WorkDetail;
import com.shizhuang.athena.api.request.inventory.handover.ModifyAttr4WorkRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description 出库单取消处理器
 * @Date 2021/5/10 3:43 下午
 */
@Component
@Slf4j
public class DeliveryOrderCancelHandler {

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private TaskFactory taskFactory;

    @Autowired
    private DeliveryOrderModifyService cancelAllocatedInventoryOperate;

    @Autowired
    private WmsStrategyFactory wmsStrategyFactory;

    @Autowired
    private DeliveryExceptionDetailGenerator deliveryExceptionDetailGenerator;

    @Resource
    private PickTaskCancel pickTaskCancelProcessor;

    @Autowired
    private CombineDeliveryShipOrderService combineDeliveryShipOrderService;

    @Autowired
    private DeliveryCancelContainerConfig deliveryCancelContainerConfig;
    @Resource
    private DeliveryAheadCancelRepository deliveryAheadCancelRepository;
    @Resource
    private ContainerDetailRepository containerDetailRepository;
    @Resource
    private ContainerOperateService containerOperateService;
    @Resource
    private OutboundMessageProducer outboundMessageProducer;
    @Resource
    private ContainerLpnRepository containerLpnRepository;

    @Resource
    private DeliveryAllocateWarehouseConfig warehouseConfig;

    @Autowired
    private SortingQualityAfterUpperProcessor sortingQualityAfterUpperProcessor;

    @Resource(name = "newWmsInventoryApi")
    private WmsInventoryApi wmsInventoryApi;

    @Resource
    private InventoryRepository inventoryRepository;

    @Resource
    private PickTaskDetailQueryRepository pickTaskDetailQueryRepository;

    @Resource
    private PickTaskDetailCommandRepository pickTaskDetailCommandRepository;

    @Resource
    private PickTaskDetailStatusMessageProducer pickTaskDetailStatusMessageProducer;

    @Resource
    private FPOrderOutProcessor fpOrderOutProcessor;

    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Resource
    private ShipTaskCommandService shipTaskCommandService;

    @Resource
    private DeliverySwitch deliverySwitch;

    @Resource
    private DeliveryContainerQueryService deliveryContainerQueryService;

    private List<Integer> RETURN_SUPPORT_CANCEL_BIZ_TYPE = Lists.newArrayList(WmsBizTypeEnum.XIAN_HUO.getBizType(), WmsBizTypeEnum.GE_REN_JI_CUN.getBizType());

    /**
     * 做出库单取消后的一系列事情
     * 1.库存分配
     * 2.拣货任务
     * 3.发货任务
     *
     * @param deliveryOrderCode
     * @param detailNo
     */
    public void doDeliveryCancel(String deliveryOrderCode, String detailNo) {
        DeliveryHeaderDo deliveryHeaderDo = queryDeliveryHeader(deliveryOrderCode);
        if (null == deliveryHeaderDo) {
            log.error("都走到出库单取消最后了，出库单不存在!,{},{}", deliveryOrderCode, detailNo);
            return;
        }
        if (WmsBizTypeEnum.XH_BIZ_TYPE.contains(deliveryHeaderDo.getBizType()) || WmsOutboundBusinessUtils.simpleShip(deliveryHeaderDo.getBizType())) {
            /*因为入库没有接跨境个人现货的取消，所以这一块只针对海外仓和跨境仓的跨境个人现货的取消调用入库生成上架任务*/
            if (warehouseConfig.isOverseaOrCrosseWarehouse(deliveryHeaderDo.getWarehouseCode())
                    && WmsBizTypeEnum.KUA_JING_XIAN_HUO.getBizType().equals(deliveryHeaderDo.getBizType())) {
                /*海外和跨境的再给入库上个架*/
                log.info("海外和跨境的现货订单在取消时调用入库生成上架任务");
                List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryOrderCode, deliveryHeaderDo.getTenantCode());
                /*目前个人寄存跨境的只有一个明细*/
                deliveryDetailDos.forEach(detail -> {
                    InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(detail.getUniqueCode());
                    /*修改库存为次品*/
                    wmsInventoryApi.modifyAttr4work(buildModifyInventoryParam(deliveryHeaderDo, detail, inventoryDo));
                    log.info("唯一码{}生成上架任务", detail.getUniqueCode());
                    sortingQualityAfterUpperProcessor.createdEntryUpperInfo(detail.getUniqueCode(), UpperTypeEnum.ENTRY_UPPER);
                    log.info("唯一码{}生成上架任务结束", detail.getUniqueCode());
                });
            }
            log.info("现货订单走到这可以停了,不需要做后续处理{},{}", deliveryOrderCode, detailNo);
            return;
        }

        // 拣货之前，有可能是没有组波次有可能是组了波次
        if (isBeforePick(deliveryHeaderDo)) {
            doCancelBeforePick(deliveryHeaderDo, detailNo);
        } else if (commonOrderIsPicking(deliveryHeaderDo)) {
            doCommonCancelPicking(deliveryHeaderDo, detailNo);
        }
        //拣货中取消处理。 目前只有保税组套会走进来 。 处于灰度状态 。
        else if (isPicking(deliveryHeaderDo)) {
            doCancelPicking(deliveryHeaderDo, detailNo);
        } else {
            doCancelAfterPick(deliveryHeaderDo, detailNo);
            Boolean flag = isUpdatePickedForCancel(deliveryHeaderDo);
            if (flag) {
                //发货任务与取消任务均会影响最终的单据状态，所以需要根据两边的情况一起评定出库单据头状态
                combineDeliveryShipOrderService.cancelUpdateDeliveryHeaderIfNecessary(deliveryOrderCode);
            }
        }

        sendOutboundCanceledMessage(deliveryHeaderDo, detailNo);
        if (isExceptionCancel(deliveryOrderCode, detailNo)) {
            //是异常提报触发关单则不生成异常明细，且从工作台出箱。
            doExceptionCancel(deliveryHeaderDo, detailNo);
            return;
        }

        //订单取消通知库内
        //容器返架, 取消件灰度仓直接写新异常明细记录
        if (deliveryCancelContainerConfig.isCancelOrderWhiteWarehouses(deliveryHeaderDo.getWarehouseCode())) {
            deliveryExceptionDetailGenerator.generateDeliveryCancelExceptionDetailV2(deliveryOrderCode, detailNo);
        } else if (isUpdatePickedForCancel(deliveryHeaderDo)) {
            //生成返架明细,如果单据在容器内则不生成异常明细 ==》495容器解耦需求改为容器内也继续生成。
            deliveryExceptionDetailGenerator.generateDeliveryExceptionDetail(deliveryOrderCode, detailNo);
        }
    }

    /**
     * 非组套场景的拣货中取消逻辑
     *
     * @param deliveryHeaderDo
     * @param detailNo
     */
    private void doCommonCancelPicking(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        log.info("通用开始取消拣货中的出库单 {} {}", JsonUtils.serialize(deliveryHeaderDo), deliveryHeaderDo);

        // 是否全部取消
        boolean isAllCancel = WmsOutBoundStatusEnum.CANCEL.getStatus().equals(deliveryHeaderDo.getCommandStatus());
        //圈定要取消的出库单明细
        List<DeliveryDetailDo> details;
        if (isAllCancel) {
            log.info("拣货中整单取消 {}", deliveryHeaderDo.getDeliveryOrderCode());
            details = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(), deliveryHeaderDo.getTenantCode());
        } else {
            DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDetail(detailNo);
            if (Objects.isNull(deliveryDetailDo)) {
                throw new WmsException("出库单明细为空 无法取消");
            }
            details = Arrays.asList(deliveryDetailDo);
        }

        //将初始化的任务需要更新到强制完成
        List<Integer> pickStatus = Arrays.asList(PickTaskStatusEnum.INIT.getCode());
        for (DeliveryDetailDo detail : details) {
            //根据出库单查询拣货任务明细。 拣货任务置为完成
            List<PickTaskDetailDo> pickTaskDetailDos = pickTaskDetailQueryRepository
                    .queryByReferenceDetailNoAndStatus(detail.getDetailNo(), pickStatus);

            if (CollectionUtils.isNotEmpty(pickTaskDetailDos)) {
                //完成拣货任务
                pickTaskDetailDos.forEach(x -> completePickTaskDetailDo(buildTaskDetailDo(x)));
            }
        }

        // 根据库存行取消库存分配
        Set<String> deliveryDetailNoSet = details.stream().map(k -> k.getDetailNo()).collect(Collectors.toSet());

        List<Integer> needCancelAllocationStatus = Arrays.asList(PickTaskStatusEnum.INIT.getCode(), PickTaskStatusEnum.COMPLETE.getCode());
        List<PickTaskDetailDo> pickTaskDetailDos =
                pickTaskDetailQueryRepository.batchQueryByReferenceDetailNoAndStatus(deliveryDetailNoSet, needCancelAllocationStatus);

        pickTaskCancelProcessor.cancelAllocateInventory(pickTaskDetailDos);
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setId(deliveryHeaderDo.getId());
        updateDeliveryHeaderDo.setReturnShelfStatus(DeliveryReturnShelfStatusEnum.WAIT.getStatus());
        if (isAllCancel) {
            updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.CANCEL.getStatus());
        }
        deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
    }

    /**
     * 取消拣选
     *
     * @param deliveryHeaderDo
     * @param detailNo
     */
    private void doCancelPicking(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        log.info("保税组套, 取消拣货中的出库单 {} {}", JsonUtils.serialize(deliveryHeaderDo), detailNo);

        //这边扫描到非整单取消的时候 暂时不支持 默认为true
        boolean isAllCancel = true;
        //圈定要取消的出库单明细
        List<DeliveryDetailDo> details;
        if (isAllCancel) {
            log.info("拣货中整单取消 {}", deliveryHeaderDo.getDeliveryOrderCode());
            details = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(), deliveryHeaderDo.getTenantCode());

        } else {
            DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDetail(detailNo);
            if (Objects.isNull(deliveryDetailDo)) {
                throw new WmsException("入库单明细为空 无法取消");
            }
            details = Arrays.asList(deliveryDetailDo);
        }

        //查询拣选任务明细
        List<Integer> pickStatus = Arrays.asList(PickTaskStatusEnum.EXECUTING.getCode(), PickTaskStatusEnum.INIT.getCode());

        for (DeliveryDetailDo detail : details) {

            //根据出库单查询拣货任务明细。 拣货任务置为完成
            List<PickTaskDetailDo> pickTaskDetailDos = pickTaskDetailQueryRepository
                    .queryByReferenceDetailNoAndStatus(detail.getDetailNo(), pickStatus);

            if (CollectionUtils.isNotEmpty(pickTaskDetailDos)) {
                //完成拣货任务
                pickTaskDetailDos.forEach(x -> completePickTaskDetailDo(buildTaskDetailDo(x)));
            }

        }


        //指令取消为整单取消 前面已经计算过了
        if (isAllCancel) {
            //取消明细的库存分配。 这边会过滤掉已拣货的 ，如果已拣货的 不会参与取消分配
            cancelAllocatedInventoryOperate.cancelInventoryAllocation(deliveryHeaderDo.getDeliveryOrderCode(), null, deliveryHeaderDo.getTenantCode(), Boolean.FALSE);

            DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
            updateDeliveryHeaderDo.setId(deliveryHeaderDo.getId());
            updateDeliveryHeaderDo.setReturnShelfStatus(DeliveryReturnShelfStatusEnum.WAIT.getStatus());
            updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.CANCEL.getStatus());
            deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);

        }
    }

    private void completePickTaskDetailDo(PickTaskDetailDo buildTaskDetailDo) {
        /*执行更新数据流程*/
        int updateCount = this.pickTaskDetailCommandRepository.updateSelectiveUseLock(buildTaskDetailDo, buildTaskDetailDo.getVersion());
        if (updateCount != org.apache.commons.lang3.math.NumberUtils.INTEGER_ONE) {
            log.error("任务明细更新失败,updateDetailEntity -> [{}] ， count->[{}]", JSON.toJSONString(buildTaskDetailDo), updateCount);
            throw new WmsException(WmsExceptionCode.TASK_DETAIL_UPDATE_FAIL);
        }
        //触发拣货任务消息
        PickTaskDetailStatusChange buildStatusChange = PickTaskDetailStatusChange.builder()
                .eventType(PickOperationStepEventEnum.PICK.getCode())
                .referenceType(buildTaskDetailDo.getReferenceType())
                .taskDetailNo(buildTaskDetailDo.getDetailNo())
                .tenantCode(OperationUserContextHolder.getTenantCode())
                .wareHouseCode(buildTaskDetailDo.getWarehouseCode())
                .userId(OperationUserContextHolder.get().getUserId())
                .signId(OperationUserContextHolder.get().getSignId())
                .userName(OperationUserContextHolder.get().getUserName())
                .userRealName(OperationUserContextHolder.get().getRealName())
                .eventTime(new Date())
                //这边是拣货强制完成
                .operateType(PickOperationEventEnum.FORCE_FINISH.getCode())
                .build();
        //取消明细为异常的明细设置标志，方便异常系统处理
        pickTaskDetailStatusMessageProducer.sendMessage(buildStatusChange);

    }

    private PickTaskDetailDo buildTaskDetailDo(PickTaskDetailDo pickTaskDetailDo) {
        if (pickTaskDetailDo == null) {
            log.error("任务明细更新实体为空");
            throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY);
        }
        PickTaskDetailDo resultDo = BeanUtil.copy(pickTaskDetailDo, PickTaskDetailDo.class);
        /**
         * 设置理货单明细状态
         */
        resultDo.setStatus(PickTaskStatusEnum.COMPLETE.getCode());
        resultDo.setExceptionFlag(org.apache.commons.lang3.math.NumberUtils.INTEGER_ZERO);
        /*更新业务数据*/
        resultDo.setUpdatedTime(new Date());
        resultDo.setVersion(pickTaskDetailDo.getVersion());
        resultDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        resultDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        resultDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        return resultDo;
    }

    /**
     * 判定拣货任务是否在拣货中
     *
     * @param deliveryHeaderDo
     * @return
     */
    private boolean isPicking(DeliveryHeaderDo deliveryHeaderDo) {
        return WmsOutBoundStatusEnum.PICKING.getStatus().equals(deliveryHeaderDo.getStatus()) && WmsOutBoundSubTypeEnum.SET_SALES.getCode().equals(deliveryHeaderDo.getSubType());
    }

    private boolean commonOrderIsPicking(DeliveryHeaderDo deliveryHeaderDo) {
        return WmsOutBoundStatusEnum.PICKING.getStatus().equals(deliveryHeaderDo.getStatus()) && !WmsOutBoundSubTypeEnum.SET_SALES.getCode().equals(deliveryHeaderDo.getSubType()) && deliverySwitch.isOpenCommonPickCancel(deliveryHeaderDo.getWarehouseCode());
    }

    /**
     * 需要修改库存为次品
     *
     * @param detail
     * @return
     */
    private ModifyAttr4WorkRequest buildModifyInventoryParam(DeliveryHeaderDo deliveryHeaderDo, DeliveryDetailDo detail, InventoryDo inventoryDo) {
        ModifyAttr4WorkRequest request = new ModifyAttr4WorkRequest();
        request.setTenantId(detail.getTenantCode());
        request.setBillNo(detail.getDeliveryOrderCode());
        request.setBillType(deliveryHeaderDo.getType());
        request.setRequestId(detail.getDetailNo() + "_" + detail.getUniqueCode());
        request.setUserName("system");
        request.setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
        request.setModifyAttrList(buildModifyDetailList(detail, inventoryDo));
        return request;
    }

    /**
     * 构建更新明细
     *
     * @param detail
     * @return
     */
    private List<ModifyAttr4WorkDetail> buildModifyDetailList(DeliveryDetailDo detail, InventoryDo inventoryDo) {
        ModifyAttr4WorkDetail workDetail = new ModifyAttr4WorkDetail();
        workDetail.setUniqueCode(detail.getUniqueCode());
        workDetail.setQualityLevel(WmsQualityLevelEnum.CP.getCode());
        workDetail.setInventoryNo(inventoryDo.getInventoryNo());
        return Arrays.asList(workDetail);
    }

    public void sendOutboundCanceledMessage(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        //合并发货单特殊逻辑
        if (Objects.equals(deliveryHeaderDo.getType(), WmsOutBoundTypeEnum.HBFHCK.getType())) {
            //增加合并发货报表特殊逻辑处理
            List<String> deliveryDetails = Lists.newArrayList(detailNo);
            OutboundOrderEvent outboundOrderEvent = new OutboundOrderEvent();
            outboundOrderEvent.setTenantId(deliveryHeaderDo.getTenantCode());
            outboundOrderEvent.setShipmentOrderNo(deliveryHeaderDo.getDeliveryOrderCode());
            outboundOrderEvent.setShipmentDetailNos(deliveryDetails);
            outboundOrderEvent.setEventType(OutboundEventEnum.CANCELED_EVENT.name());
            outboundMessageProducer.sendShipmentOrderEvent(outboundOrderEvent);
        }
        //取消埋点
        DeliveryOrderDetailOperateEvent deliveryOrderDetailOperateEvent = new DeliveryOrderDetailOperateEvent();
        deliveryOrderDetailOperateEvent.setShipmentOrderNo(deliveryHeaderDo.getDeliveryOrderCode());
        deliveryOrderDetailOperateEvent.setProcessCode(OutboundEventEnum.CANCELED_EVENT.getProcessCode());
        deliveryOrderDetailOperateEvent.setShipmentDetailNo(detailNo);
        deliveryOrderDetailOperateEvent.setOperateTime(new Date());
        deliveryOrderDetailOperateEvent.setTenantId(deliveryHeaderDo.getTenantCode());
        outboundMessageProducer.sendShipmentOrderOperateDetailEvent(deliveryOrderDetailOperateEvent, OutboundEventEnum.CANCELED_EVENT.name());
    }

    private void doExceptionCancel(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        //处理老容器
        handleContainer(deliveryHeaderDo, detailNo);
        //处理lpn容器
        handleLpn(deliveryHeaderDo, detailNo);
    }

    private void handleContainer(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        try {
            //根据单明细号和单据号查询箱明细，如果存在则出箱。
            ContainerDetailDo detailDo = new ContainerDetailDo();
            detailDo.setReferenceNo(deliveryHeaderDo.getDeliveryOrderCode());
            detailDo.setReferenceDetailNo(detailNo);
            List<ContainerDetailDo> containerDetailDos = containerDetailRepository.queryCanOutContainerDetails(detailDo);
            if (CollectionUtils.isEmpty(containerDetailDos)) {
                return;
            }
            //说明容器处理，处理源容器出箱
            Map<String, List<ContainerDetailDo>> containerDetailMap = containerDetailDos.stream()
                    .collect(Collectors.groupingBy(ContainerDetailDo::getContainerCode));
            containerDetailMap.forEach((k, v) -> {
                int needOutQty = v.stream()
                        .mapToInt(containerDetailDo -> containerDetailDo.getInQty() - containerDetailDo.getOutQty()).sum();
                TakeContainerItemParam param = TakeContainerItemParam.builder()
                        .containerCode(k)
                        .qty(needOutQty)
                        .referenceNo(deliveryHeaderDo.getDeliveryOrderCode())
                        .referenceDetailNo(StringUtils.isNotBlank(detailNo) ? detailNo : v.get(0).getReferenceDetailNo())
                        .build();
                containerOperateService.takeContainerItem(param);
            });
        } catch (Exception e) {
            log.error("异常触发老容器出箱出错咯", e);
        }
    }

    private void handleLpn(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        try {
            //调用lpn查询箱明细，如果存在则出箱。
            List<ContainerDetailPageDto> containerDetailPageDtos = containerLpnRepository.queryCanOutContainerDetailByReferenceNos(
                    deliveryHeaderDo.getWarehouseCode(), deliveryHeaderDo.getTenantCode(), Arrays.asList(deliveryHeaderDo.getDeliveryOrderCode()));
            if (CollectionUtils.isEmpty(containerDetailPageDtos)) {
                return;
            }
            if (StringUtils.isNotBlank(detailNo)) {
                containerDetailPageDtos = containerDetailPageDtos.stream().filter(x -> x.getReferenceDetailNo().equals(detailNo)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(containerDetailPageDtos)) {
                return;
            }
            for (ContainerDetailPageDto containerDetailDo : containerDetailPageDtos) {
                //出箱
                ContainerUnPackParam unPackParam = new ContainerUnPackParam();
                unPackParam.setRequestId(containerDetailDo.getContainerDetailNo() + UUID.randomUUID());
                unPackParam.setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
                unPackParam.setTenantId(deliveryHeaderDo.getTenantCode());
                unPackParam.setReferenceDetailNo(containerDetailDo.getReferenceDetailNo());
                unPackParam.setReferenceNo(containerDetailDo.getReferenceNo());
                unPackParam.setContainerCode(containerDetailDo.getContainerCode());
                unPackParam.setQty(containerDetailDo.getAvailableQty());
                unPackParam.setCurrentBusinessStep(LpnContainerStepEnum.EXCEPTION_CANCEL.getStep());
                unPackParam.setOperationTime(new Date());
                unPackParam.setOperationUserId(OperationUserContextHolder.get().getUserId());
                unPackParam.setOperationUserName(OperationUserContextHolder.get().getRealName());
                containerLpnRepository.unpackByReference(unPackParam);
            }
        } catch (Exception e) {
            log.error("异常触发lpn出箱出错咯", e);
        }
    }

    /**
     * 是否是异常提报触发关单
     *
     * @param deliveryOrderCode
     * @param detailNo
     */
    private boolean isExceptionCancel(String deliveryOrderCode, String detailNo) {
        DeliveryAheadCancelDo queryDo = new DeliveryAheadCancelDo();
        queryDo.setDetailNo(detailNo);
        queryDo.setDeliveryOrderCode(deliveryOrderCode);
        List<DeliveryAheadCancelDo> deliveryAheadCancelDos = deliveryAheadCancelRepository.queryByCondition(queryDo);
        if (CollectionUtils.isEmpty(deliveryAheadCancelDos)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_AHEAD_CANCEL_NULL);
        }
        if (NumberUtils.INTEGER_ONE < deliveryAheadCancelDos.size()) {
            return false;
        }
        return org.apache.commons.lang3.math.NumberUtils.INTEGER_ONE.equals(deliveryAheadCancelDos.get(0).getExceptionFlag());
    }

    /**
     * 拣货之后取消出库单以及明细，发货目前不取消明细
     *
     * @param deliveryHeaderDo
     */
    private void doCancelAfterPick(DeliveryHeaderDo deliveryHeaderDo, String deliveryDetailNo) {
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();

        if (deliveryDetailNo != null) {
            log.info("拣货后出库单明细取消, 开始取消对应发货任务, deliveryDetailNo:{}", deliveryDetailNo);
            taskFactory.process(buildCancelShipTask(deliveryHeaderDo, null, deliveryDetailNo));
        } else if (deliveryHeaderDo.getCommandStatus().equals(WmsOutBoundStatusEnum.CANCEL.getStatus())) {
            //发货单据整单取消
            Boolean flag = isUpdatePickedForCancel(deliveryHeaderDo);
            //2.DQ业务时将出库单业务状态设置为取消并且取消发货任务
            if (flag && WmsOutBoundStatusEnum.pickComplete.contains(deliveryHeaderDo.getStatus())) {
                log.info("出库单拣货完成整单取消, 更新出库单状态90, deliveryOrderCode:{}", deliveryHeaderDo.getDeliveryOrderCode());
                updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.CANCEL.getStatus());
                taskFactory.process(buildCancelShipTask(deliveryHeaderDo, null, null));
            } else if (smallWarehouseDtcCancel(deliveryHeaderDo)) {
                updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.CANCEL.getStatus());
                taskFactory.process(buildCancelShipTask(deliveryHeaderDo, null, null));
            }
        }
        if (isExceptionCancel(deliveryHeaderDo.getDeliveryOrderCode(), deliveryDetailNo)) {
            //异常调用的关单不走返架逻辑，不修改返架状态。
            return;
        }
        //拣货后取消把状态更新成待返架状态
        updateDeliveryHeaderDo.setId(deliveryHeaderDo.getId());
        updateDeliveryHeaderDo.setReturnShelfStatus(DeliveryReturnShelfStatusEnum.WAIT.getStatus());
        deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
    }

    /**
     * 用的地方有三处
     * 1.判断是否需要在取消的代码里直接更新出库单的status
     * 2.是否需要生成老的返架任务
     * 3.取消发货任务
     * add By devin 2022-02-23 mfs按照道理是商家仓，RCDJ这种用wms发货，能在操作发货的时候告诉用户，取消了
     * 而 QHCK DFCK 是用wms退供，所以这块需要生成返架，不要再wms退供处生成，因为没有生成发货任务
     *
     * @param deliveryHeaderDo
     * @return
     */
    private boolean isUpdatePickedForCancel(DeliveryHeaderDo deliveryHeaderDo) {
        DeliveryPickedCancelUpdateStrategyContext context = new DeliveryPickedCancelUpdateStrategyContext();
        context.setDeliveryHeaderDo(deliveryHeaderDo);
        /**
         * 查询是否有配置, 有配置则该方法返回true
         * select * from sys_config_detail WHERE warehouse_code = 'SH25'AND config_code = 'DELIVERY_PICKED_CANCEL_UPDATE_STRATEGY';
         */
        Object flag = wmsStrategyFactory.getWmsStrategy(deliveryHeaderDo.getWarehouseCode(), StrategyConfigCodeEnum.DELIVERY_PICKED_CANCEL_UPDATE_STRATEGY,
                deliveryHeaderDo.getWarehouseCode()).apply(context);
        log.info("出库单拣货后被取消更新策略:{}", flag);
        if (flag == null) {
            // 如果没有配置取消发货任务，修改出库单的sys_config_detail配置，查看下是否是mfs的逻辑这里写的很硬
            return mfsCancel(deliveryHeaderDo) || smallWarehouseDtcCancel(deliveryHeaderDo);
        }
        return true;
    }

    /**
     * 小仓库dtc订单
     *
     * @param deliveryHeaderDo
     * @return
     */
    private boolean smallWarehouseDtcCancel(DeliveryHeaderDo deliveryHeaderDo) {
        return WmsOutboundBusinessUtils.isDtc(deliveryHeaderDo.getType(), deliveryHeaderDo.getBizType());
    }

    private boolean mfsCancel(DeliveryHeaderDo deliveryHeaderDo) {
        if (WmsOutboundBusinessUtils.isMFS(deliveryHeaderDo.getBizType())) {
            if (Lists.newArrayList(WmsOutBoundTypeEnum.DFCK.getType(),
                    WmsOutBoundTypeEnum.QHCK.getType(), WmsOutBoundTypeEnum.MFSQLCK.getType()).contains(deliveryHeaderDo.getType())) {
                return true;
            }
        }
        return false;
    }

    /**
     * build 取消发货任务
     */
    private CancelTaskPojo buildCancelShipTask(DeliveryHeaderDo entity, String taskNo, String deliveryDetailNo) {
        CancelTaskPojo pojo = new CancelTaskPojo();
        pojo.setTaskTypeEnum(TaskTypeEnum.SHIP);
        pojo.setTaskStatusEnum(TaskStatusEnum.CANCEL);
        pojo.setReferenceNo(entity.getDeliveryOrderCode());
        pojo.setReferenceType(entity.getType());
        pojo.setTaskNo(taskNo);
        pojo.setReferenceDetailNo(deliveryDetailNo);
        return pojo;
    }

    /**
     * 拣货之前取消
     *
     * @param deliveryHeaderDo
     * @param detailNo
     */
    private void doCancelBeforePick(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        log.info("出库单拣货前取消, 取消拣货任务&库存分配&更新出库单状态为90, 出库单号:{}, 明细号:{}", deliveryHeaderDo.getDeliveryOrderCode(), detailNo);
        cancelPickTaskAndInventoryAllocation(deliveryHeaderDo, detailNo);
        // 出库单commandStatus更新为90 才把单据更新为90
        if (deliveryHeaderDo.getCommandStatus().equals(WmsOutBoundStatusEnum.CANCEL.getStatus())) {
            //1.取消波次更新单据无锁 2.波次号做乐观锁是因为取消波次时更新单据波次号为空
            int row = deliveryHeaderRepository.updateCancelStatusByIdAndLaunchNo(deliveryHeaderDo.getId(), deliveryHeaderDo.getLaunchNo());
            if (row < NumberUtils.INTEGER_ONE) {
                throw new WmsException(WmsExceptionCode.BOUND_OPERATING_SYSTEM_EXCEPTION);
            }
        }
        //fp出列 订单取消如果是fp标记的需要fp出列
        if (InterceptTypeEnum.FP.getCode().equals(deliveryHeaderDo.getInterceptType())) {
            //
            fpOrderOutProcessor.execute(deliveryHeaderDo, detailNo, FpOrderOutEvenEnum.ORDER_CANCEL.getCode());
        }

        beforePickingCancelShipTaskIfNecessary(deliveryHeaderDo);

    }

    /**
     * 退货场景拣货前可能存在发货任务，需要取消掉发货任务
     *
     * @param deliveryHeaderDo
     */
    private void beforePickingCancelShipTaskIfNecessary(DeliveryHeaderDo deliveryHeaderDo) {
        if (null == deliveryHeaderDo) {
            return;
        }
        if (!WmsOutBoundTypeEnum.THCK.getType().equals(deliveryHeaderDo.getType())) {
            return;
        }

        if (!RETURN_SUPPORT_CANCEL_BIZ_TYPE.contains(deliveryHeaderDo.getBizType())) {
            return;
        }

        // 退货单据目前只支持整单取消
        if (deliveryHeaderDo.getCommandStatus().equals(WmsOutBoundStatusEnum.CANCEL.getStatus())) {
            log.info("退货出库单据取消, 出库单号:{}", deliveryHeaderDo.getDeliveryOrderCode());
            String deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
            String tenantCode = deliveryHeaderDo.getTenantCode();
            String warehouseCode = deliveryHeaderDo.getWarehouseCode();
            List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.queryByReferenceNo(deliveryOrderCode, warehouseCode, tenantCode);
            if (CollectionUtils.isEmpty(shipTaskDetailDos)) {
                return;
            }
            shipTaskCommandService.cancelShipTask(deliveryOrderCode, warehouseCode, tenantCode);
        }
    }

    /**
     * 取消单据头或者明细
     *
     * @param deliveryHeaderDo
     * @param detailNo
     */
    private void cancelPickTaskAndInventoryAllocation(DeliveryHeaderDo deliveryHeaderDo, String detailNo) {
        // 整单取消
        if (deliveryHeaderDo.getCommandStatus().equals(WmsOutBoundStatusEnum.CANCEL.getStatus())) {
            //波次号不为空表示还未组波，未产生拣货任务，只需取消库存分配
            if (StringUtils.isNotBlank(deliveryHeaderDo.getLaunchNo())) {
                // 取消所有拣货任务以及明细 （取消整单）
                pickTaskCancelProcessor.cancelUsePreTransactional(PickTaskCancelParam.builder()
                        .referenceNo(deliveryHeaderDo.getDeliveryOrderCode())
                        .referenceType(deliveryHeaderDo.getType())
                        .build());
            }
            // 取消出库单库存分配
            cancelAllocatedInventoryOperate.cancelInventoryAllocation(deliveryHeaderDo.getDeliveryOrderCode(),
                    "",
                    deliveryHeaderDo.getTenantCode(), Boolean.TRUE);
        } else {
            // 取消部分拣货明细 （取消部分明细）
            //波次号不为空，表示已产生拣货任务，需要取消拣货明细
            if (StringUtils.isNotBlank(deliveryHeaderDo.getLaunchNo())) {
                // 取消拣货任务明细 (注：如果如果其他明细都被取消，且发现最后一个明细，会取消整个拣货任务)
                pickTaskCancelProcessor.cancelUsePreTransactional(PickTaskCancelParam.builder()
                        .referenceNo(deliveryHeaderDo.getDeliveryOrderCode())
                        .referenceDetailNoList(Lists.newArrayList(detailNo))
                        .referenceType(deliveryHeaderDo.getType())
                        .build());
            }
            // 取消出库单库存分配
            cancelAllocatedInventoryOperate.cancelInventoryAllocation(deliveryHeaderDo.getDeliveryOrderCode(),
                    detailNo,
                    deliveryHeaderDo.getTenantCode(), Boolean.TRUE);
        }
    }

    /**
     * 1.如果出库单明细为空，则判断整个出库单的拣货情况
     * 2.如果出库单明细不为空，则先判断明细是否拣货，如果未拣货直接返回true
     *
     * @param deliveryHeaderDo
     * @return true 代表在拣货前 false 代表已拣货
     */
    private boolean isBeforePick(DeliveryHeaderDo deliveryHeaderDo) {
        return WmsOutBoundStatusEnum.billOperationCancelList.contains(deliveryHeaderDo.getStatus());
    }


    /**
     * 校验出库单是否存在
     *
     * @param deliveryOrderCode
     * @return
     */
    public DeliveryHeaderDo queryDeliveryHeader(String deliveryOrderCode) {
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (Objects.isNull(deliveryHeader) ||
                (Objects.equals(WmsBizTypeEnum.common.getBizType(), deliveryHeader.getBizType()))
                        && WmsOutBoundStatusEnum.outIngComplete.contains(deliveryHeader.getStatus())) {
            //根据交易单号发货如果未查询到单据则，则根据交易单号查询明细反差出出库单信息
            deliveryHeader = deliveryHeaderRepository.queryXHDeliveryInfoWithDeliveryDetail(deliveryOrderCode);
        }
        return deliveryHeader;
    }

    /**
     * 取消单据头会获取所有的出库单明细
     * 取消明细只会获取当前取消的明细
     *
     * @param deliveryOrderCode
     * @param detailNo
     * @return
     */
    public List<DeliveryDetailDo> getNeedCancelDeliveryDetailList(String deliveryOrderCode, String detailNo) {
        List<DeliveryDetailDo> deliveryDetailDoList = Lists.newArrayList();
        DeliveryDetailDo detailDo;
        // 出库单明细不存在
        if (StringUtils.isNotBlank(detailNo)) {
            detailDo = checkDeliveryDetail(deliveryOrderCode, detailNo);
            deliveryDetailDoList.add(detailDo);
        } else {
            // 取消出库单头
            deliveryDetailDoList = checkDeliveryDetail(deliveryOrderCode);
        }
        return deliveryDetailDoList;
    }

    /**
     * 查询出库单明细
     *
     * @param deliveryOrderCode
     * @param detailNo
     * @return
     */
    private DeliveryDetailDo checkDeliveryDetail(String deliveryOrderCode, String detailNo) {
        DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDeliveryDetailByOrderCodeAndDetailNo(deliveryOrderCode, detailNo);
        if (null == deliveryDetailDo) {
            throw new WmsOperationException("出库单明细取消，出库单明细不存在！");
        }
        return deliveryDetailDo;
    }

    /**
     * check出库单明细是否不存在
     *
     * @param deliveryOrderCode
     * @return
     */
    public List<DeliveryDetailDo> checkDeliveryDetail(String deliveryOrderCode) {
        return deliveryDetailRepository.queryDeliveryDetailExcludeCancelByDeliveryCode(deliveryOrderCode);
    }

    public List<DeliveryDetailDo> checkDeliveryDetailWithCancel(String deliveryOrderCode) {
        List<DeliveryDetailDo> deliveryDetailDoList = deliveryDetailRepository.queryDeliveryDetailByDeliveryCodeWithCancel(deliveryOrderCode);
        if (CollectionUtils.isEmpty(deliveryDetailDoList)) {
            throw new WmsOperationException("出库单取消，出库单明细为空");
        }
        return deliveryDetailDoList;
    }

    /**
     * 取消单件调拨单出库单以及明细
     *
     * @param deliveryHeaderDo
     */
    public void cancelDbDelivery(WmsDbDeliveryCancelRequest request, DeliveryHeaderDo deliveryHeaderDo) {
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }
        String deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
        // 单件调拨禁止取消状态
        List<Integer> forbidCancelStatusList = com.google.common.collect.Lists.newArrayList(WmsOutBoundStatusEnum.PACKING.getStatus(),
                WmsOutBoundStatusEnum.PACK_FINISH.getStatus(), WmsOutBoundStatusEnum.OUTING.getStatus(),
                WmsOutBoundStatusEnum.OUTED.getStatus(), WmsOutBoundStatusEnum.FORCE_OUTED.getStatus());
        if (forbidCancelStatusList.contains(deliveryHeaderDo.getStatus())) {
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_CANNOT_CANCEL);
        }
        // 按流向调拨出,商品分流后装箱前不允许取消
        this.checkAllocateShipByFlowCancel(request, deliveryHeaderDo);

        // 2.更新出库单为取消，并发为不能为装箱中
        int updateDetailResult = deliveryDetailRepository.updateDeliveryDetailForCancelByDeliveryOrderCode(deliveryOrderCode);
        if (updateDetailResult <= 0) {
            log.error("单件调拨单出库单取消失败，出库单是如下:{}", deliveryOrderCode);
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_DETAIL_CANCEL_ERROR);
        }
        // 取消出库单头 设置status & commandStatus = 90
        int rows = deliveryHeaderRepository.updateDeliveryCancelByOrderCode(deliveryHeaderDo.getDeliveryOrderCode(),
                OperationUserContextHolder.getTenantCode(), forbidCancelStatusList);
        if (rows <= 0) {
            log.error("单件调拨单明细取消失败，出库单是如下:{}", deliveryOrderCode);
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_CANCEL_ERROR);
        }
    }

    /**
     * 按流向调拨出，商品分流后，调拨出扫容器装箱前，不允许取消
     * @param deliveryHeaderDo
     * @return
     */
    private void checkAllocateShipByFlowCancel(WmsDbDeliveryCancelRequest request, DeliveryHeaderDo deliveryHeaderDo) {
        try {
            // 调拨出模块按类目流向扫容器，如果是全次品，会主动通知OFC取消调拨单，这里防止不能取消，加个标
            boolean flowDeliveryCancel = request.isFlowDeliveryCancel();
            if (flowDeliveryCancel) {
                return;
            }

            // 校验是否按类目流向调拨出的
            String flowCode = deliveryHeaderDo.getFlowCode();
            String targetWarehouseCode = deliveryHeaderDo.getTargetWarehouseCode();
            if (Strings.isBlank(flowCode) || Strings.isNotBlank(targetWarehouseCode)) {
                return;
            }
            String deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
            String tenantCode = deliveryHeaderDo.getTenantCode();
            List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryOrderCode, tenantCode);
            if (CollectionUtils.isEmpty(deliveryDetailDos)) {
                return;
            }
            // 校验是否绑容器的
            String uniqueCode = deliveryDetailDos.get(0).getUniqueCode();
            ContainerDetailResult containerDetailResult = deliveryContainerQueryService.queryContainerDetailByUniqueCode(tenantCode, uniqueCode);
            // 未绑定容器，不做处理
            if (Objects.isNull(containerDetailResult)) {
                return;
            }
            String containerCode = containerDetailResult.getContainerCode();
            ScpContainerInstanceResult containerInstanceResult = deliveryContainerQueryService.queryContainerInstanceByContainerCode(tenantCode, containerCode);
            if (Objects.isNull(containerInstanceResult)) {
                return;
            }
            // 校验是否商品分流后绑的容器，比如分拣绑的容器就不需要拦截 TODO 区分调拨出库的场景（工作台）
            String step = containerInstanceResult.getCurrentBusinessStep();
            if (!Objects.equals(step, LpnContainerStepEnum.SPLIT_FLOW.getStep())) {
                return;
            }
            // 校验容器明细里是否已经有被取消的调拨单了，有的话也不拦截，兼容存量的单据
            List<ContainerDetailResult> containerDetailResults = deliveryContainerQueryService.queryContainerDetailByContainerCode(tenantCode, containerCode);
            if (CollectionUtils.isEmpty(containerDetailResults)) {
                return;
            }
            List<String> containerUniqueCodeList = containerDetailResults.stream().map(ContainerDetailResult::getUniqueCode).collect(Collectors.toList());
            List<DeliveryDetailDo> deliveryDetailDoList = deliveryDetailRepository.queryDetailListByUniqueCodes(containerUniqueCodeList, tenantCode);
            if (CollectionUtils.isEmpty(deliveryDetailDoList)) {
                return;
            }
            // 存在已经取消的，那就继续取消这单，保持一致
            boolean existCanceled = deliveryDetailDoList.stream()
                    .anyMatch(deliveryDetail -> WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(deliveryDetail.getCancelFlag()));
            if (existCanceled) {
                log.info("checkAllocateShipByFlowCancel existCanceled, tenantCode:{},deliveryOrderCode:{}", deliveryHeaderDo.getTenantCode(), deliveryHeaderDo.getDeliveryOrderCode());
                return;
            }
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_CANNOT_CANCEL);
        } catch (Exception e) {
            log.warn("checkAllocateShipByFlowCancel error, tenantCode:{},deliveryOrderCode:{}, e:{}",
                    deliveryHeaderDo.getTenantCode(), deliveryHeaderDo.getDeliveryOrderCode(), ExceptionUtils.getStackTrace(e));
            throw e;
        }
    }
}
