package com.poizon.scm.wms.service.shipping;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryLogisticDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryLogisticRepository;
import com.poizon.scm.wms.adapter.outbound.grey.repository.IGreyRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDetailDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsLogisticsBillRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackDetailRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackRepository;
import com.poizon.scm.wms.api.dto.response.logistics.LogisticsInfoResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.outbound.returngoods.entity.LogisticsInfo;
import com.poizon.scm.wms.domain.outbound.returngoods.service.ExpressBillPrintService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.PrintExpressParam;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.pojo.third.notify.request.base.NewWmsOperateLogRequest;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.service.shipping.param.RCShipPrintExpressParam;
import com.poizon.scm.wms.util.common.StringUtils;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.enums.WmsPackStatusEnum;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退货中心打单
 */
@Component
@Slf4j
public class RCShipPrintExpressService {
    @Resource
    private ExpressBillPrintService expressBillPrintService;
    @Resource
    private WmsLogisticsBillRepository logisticsBillRepository;
    @Resource
    private WmsPackRepository packRepository;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Resource
    private WmsPackDetailRepository packDetailRepository;

    @Autowired
    private SendMessageHandler sendMessageHandler;

    @Resource
    private IGreyRepository greyRepository;

    @Resource
    private DeliveryLogisticRepository deliveryLogisticRepository;

    private static final String remark_1 ="退货下单";
    private static final String remark_2 ="打印面单";
    @Autowired
    private WmsPackDetailRepository wmsPackDetailRepository;
    @Transactional(rollbackFor = Exception.class)
    public LogisticsInfoResponse printExpress(RCShipPrintExpressParam param) {
        boolean lock = false;
        try {
            lock = distributeLockUtil.tryLockForBiz(LockEnum.WMS_PACK_LOCK, param.getPackNo());
            if (!lock) {
                log.error("打面单获取锁失败:{}", param.getPackNo());
                throw new WmsException("打面单获取锁失败");
            }
            //加锁后再查一下包裹
            checkPackAgain(param);
            //封箱更新包裹数量，状态
            seal(param);
            //更改出库状态为装箱完成
            updateDeliveryPacked(param);
            //打单（包含更新包裹）
            param.setNeedSealPack(true);
            LogisticsInfo logisticsInfo = print(param);
            String remark = remark_1;
            if (StringUtils.isNotBlank(param.getWmsPackDo().getExpressCode())) {
                    remark = remark_2;
            }
            sendOperateLog(buildOperateLog(param.getWmsPackDetailDos(),remark),param.getPackNo());
            return BeanUtil.deepCopy(logisticsInfo, LogisticsInfoResponse.class);
        } catch (InterruptedException e) {
            log.error("打面单获取锁失败,未获取到锁", param.getPackNo());
            Thread.currentThread().interrupt();
            throw new WmsException("打面单获取锁失败，未获取到锁，请稍后重试");
        } finally {
            if (lock) {
                distributeLockUtil.releaseLockForBiz(LockEnum.WMS_PACK_LOCK, param.getPackNo());
            }
        }

    }

    private void checkPackAgain(RCShipPrintExpressParam param){
        //加锁后再查一下包裹
        WmsPackDo wmsPackDo = packRepository.queryByPackNo(param.getPackNo());
        if(wmsPackDo ==null ){
            throw new WmsException("包裹不存在，不能打单");
        }
        List<WmsPackDetailDo> wmsPackDetailDos = packDetailRepository.queryByPackNo(param.getPackNo());
        if (CollectionUtils.isEmpty(wmsPackDetailDos)) {
            throw new WmsException("待打单明细为空");
        }
        //如果已打单，必须存在物流信息
        List<WmsLogisticsBillDo> logisticsBillDoList = logisticsBillRepository.queryByDeliveryOrderCode(wmsPackDetailDos.get(0).getDeliveryOrderCode());
        if (WmsPackStatusEnum.PRINTED.getStatus().equals(wmsPackDo.getPackStatus()) && CollectionUtils.isEmpty(logisticsBillDoList)) {
            //如果包裹是已打单, 且非tms下单则不能打印面单
            throw new WmsOperationException("线下生成的面单, 无法重新打印面单");
        }

        //重新set包裹参数
        param.setWmsPackDetailDos(wmsPackDetailDos);
        param.setWmsPackDo(wmsPackDo);
    }

    /**
     * 批量更新出库单为装箱完成
     * @param param
     */
    private void updateDeliveryPacked(RCShipPrintExpressParam param){
        List<WmsPackDetailDo> wmsPackDetailDos = param.getWmsPackDetailDos();
        Set<String> deliveryOrderCodes = wmsPackDetailDos.stream().map(WmsPackDetailDo::getDeliveryOrderCode).collect(Collectors.toSet());
        List<DeliveryHeaderDo> deliveryHeaderDos = deliveryHeaderRepository.queryBatchDeliveryInfo(deliveryOrderCodes);
        Set<Long> ids = deliveryHeaderDos.stream().map(DeliveryHeaderDo::getId).collect(Collectors.toSet());
        deliveryHeaderRepository.updateStatusByIds(Lists.newArrayList(ids), WmsOutBoundStatusEnum.PACK_FINISH.getStatus());
    }

    /**
     * 封箱更新包裹数量，状态
     * @param param
     */
    private void seal(RCShipPrintExpressParam param) {
        WmsPackDo packDo = param.getWmsPackDo();
        if(WmsPackStatusEnum.PRINTED.getStatus().equals(packDo.getPackStatus())
            || WmsPackStatusEnum.SHIPPED.getStatus().equals(packDo.getPackStatus())){
            //重新打印进来不改状态
            return;
        }

        try {
            // 新版打单存在事务问题，这里跳过封箱
            String currentPageCode = OperationUserContextHolder.get().getCurrentPageCode();
            String warehouseCode = OperationUserContextHolder.get().getWarehouseCode();
            Long userId = OperationUserContextHolder.get().getUserId();
            String deliveryOrderCode = packDo.getDeliveryOrderCode();
            DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
            if (Objects.nonNull(deliveryHeaderDo)) {
                DeliveryLogisticDo deliveryLogisticDo = deliveryLogisticRepository.queryFirstOneByDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
                Integer crossFlag = 0;
                if(Objects.nonNull(deliveryLogisticDo)){
                    crossFlag = deliveryLogisticDo.getCrossFlag();
                }
                boolean hitForwardOutbound = greyRepository.expressPrintHitForwardOutbound(currentPageCode, warehouseCode, deliveryHeaderDo.getType(), deliveryHeaderDo.getBizType(), crossFlag,userId);
                if (hitForwardOutbound) {
                    return;
                }
            }
        } catch (Exception e) {
            log.error("hitForwardOutbound error, packNo:{}", param.getPackNo());
        }

        packDo.setId(packDo.getId());
        packDo.setPackStatus(WmsPackStatusEnum.SEALED.getStatus());
        //PackSizeType,PackWeight,PackSizeDesc，长宽高 为空
        packDo.setSealTime(new Date());
        int sum = param.getWmsPackDetailDos().stream().mapToInt(WmsPackDetailDo::getQty).sum();
        packDo.setGoodsQty(sum);
        packRepository.updateSelective(packDo);
    }

    private LogisticsInfo print(RCShipPrintExpressParam param) {
        //出库单取包裹头的
        String deliveryOrderCode = param.getWmsPackDo().getDeliveryOrderCode();
        //下单，返回打印面单信息
        PrintExpressParam printExpressParam = new PrintExpressParam();
        printExpressParam.setDeliveryOrderCode(deliveryOrderCode);
        printExpressParam.setSubExpressCode("");
        printExpressParam.setAutoMakeOrder(true);
        printExpressParam.setLogisticsCode(param.getLogisticsCode());
        printExpressParam.setLogisticsProductCode(param.getLogisticsProductCode());
        printExpressParam.setPrintWaybillNumberType(param.getPrintWaybillNumberType());
        printExpressParam.setNeedSealPack(param.getNeedSealPack());
        return expressBillPrintService.printExpress(printExpressParam);
    }
    private NewWmsOperateLogRequest buildOperateLog(List<WmsPackDetailDo> packDetailList, String remark){
        NewWmsOperateLogRequest request = new NewWmsOperateLogRequest();
        List<NewWmsOperateLogRequest.OperateLog> list = new ArrayList<>();
        packDetailList.forEach(e->{
            NewWmsOperateLogRequest.OperateLog operateLog = new NewWmsOperateLogRequest.OperateLog();
            operateLog.setUniqueCode(e.getUniqueCode());
            operateLog.setOperateRemark(remark);
            operateLog.setOperateType(95);
            operateLog.setOperateTime(new Date());
            operateLog.setOperateRealName(OperationUserContextHolder.get().getRealName());
            operateLog.setOperatorId(OperationUserContextHolder.get().getUserId());
            operateLog.setOperateStationCode(OperationUserContextHolder.get().getStationNo());
            operateLog.setOperateRepositoryCode(OperationUserContextHolder.get().getWarehouseCode());
            list.add(operateLog);
        });
        request.setOperateLogVoList(list);
        return request;
    }
    private void sendOperateLog(NewWmsOperateLogRequest request, String messageKey){
        SendMessage<NewWmsOperateLogRequest> sendMessage = new SendMessage<>();
        sendMessage.setMessageContent(request);
        sendMessage.setTopic(RocketMqConstants.NotifyPinkLog.TOPIC_NAME);
        sendMessage.setTag(RocketMqConstants.NotifyPinkLog.TAG);
        sendMessage.setMessageKey(messageKey);
        sendMessageHandler.process(sendMessage);
    }



}
