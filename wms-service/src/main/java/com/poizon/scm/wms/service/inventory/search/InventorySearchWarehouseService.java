package com.poizon.scm.wms.service.inventory.search;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.query.InventoryAllocateRequestDetail;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryPermissionDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryPermissionRepository;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedOrderPojo;
import com.poizon.scm.wms.service.delivery.common.DeliveryOrderCommonService;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateService;
import com.poizon.scm.wms.service.inventory.search.extend.DefaultForceReturnWarehouseFinder;
import com.poizon.scm.wms.service.inventory.search.extend.InventorySorter;
import com.poizon.scm.wms.service.inventory.search.extend.LongStorageForceReturnWarehouseFinder;
import com.poizon.scm.wms.util.enums.DeliveryPermissionAllocatedStatusEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundSubTypeEnum;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 寻仓服务
 *
 * <AUTHOR>
 * @data 2021/3/12
 */
@Slf4j
@Service
public class InventorySearchWarehouseService {
    @Resource
    private DeliveryOrderCommonService deliveryOrderCommonService;
    @Resource
    private InventoryOperateService inventoryOperateService;

    @Autowired
    private InventorySearchService searchService;

    @Autowired
    private InventoryAllocateService allocateService;

    @Autowired
    private DefaultForceReturnWarehouseFinder defaultForceReturnWarehouseFinder;
    @Autowired
    private LongStorageForceReturnWarehouseFinder longStorageForceReturnWarehouseFinder;

    @Autowired
    private InventorySorter inventorySorter;

    @Autowired
    private SciInventoryAllocateExecutor executor;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private DeliveryPermissionRepository deliveryPermissionRepository;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private DistributeLockUtil distributeLockUtil;

    @Autowired
    private CleanUpOrderShipService cleanUpOrderShipService;

    @Autowired
    private ScpWarehouseRepository warehouseRepository;

    /**
     * 库存分配并更新许可证状态
     *
     * @param deliveryOrderCode
     * @return 返回库存分配记录
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void allocateInventory(String deliveryOrderCode) {
        InventoryAllocatedOrderPojo inventoryAllocatedOrderPojo = deliveryOrderCommonService.buildAllocateInventory(deliveryOrderCode);
        inventoryOperateService.asyncAllocatedInventory(inventoryAllocatedOrderPojo, Boolean.TRUE);
    }


    /**
     * 根据参数寻仓
     *
     * @param deliveryCode
     * @return 返回库存分配记录
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public List<InvInventoryAllocatedDo> searchWarehouseAndAllocateInventory(String deliveryCode) {
        boolean lockFlag = false;
        try {
            lockFlag = distributeLockUtil.tryLockForBiz(LockEnum.WMS_SEARCH_WAREHOUSE_LOCK, deliveryCode);
            if (!lockFlag) {
                throw new WmsOperationException("获取寻仓的锁失败");
            }
            return doSearchWarehouseAndAllocateInventory(deliveryCode);
        } catch (Exception e) {
            log.error("单据{}寻仓出错", deliveryCode, e);
            throw e;
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.WMS_SEARCH_WAREHOUSE_LOCK, deliveryCode);
            }
        }
    }

    /**
     * 实际寻仓
     *
     * @param deliveryCode
     * @return
     */
    private List<InvInventoryAllocatedDo> doSearchWarehouseAndAllocateInventory(String deliveryCode) {
        DeliveryPermissionDo deliveryPermissionDo = queryAndValidatePermission(deliveryCode);
        if (deliveryPermissionDo == null) {
            return null;
        }
        boolean intercepted = tryInterceptForWaitingCancel(deliveryPermissionDo);
        if (intercepted) {
            log.info("单据{}是等待取消状态,被提前拦截了", deliveryCode);
            return null;
        }
        log.info("清退出库开始寻仓, deliveryCode - > [{}]", deliveryCode);
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryCode);
        if (Objects.isNull(deliveryHeaderDo)) {
            log.error("出库单[{}]不存在", deliveryCode);
            throw new WmsException(WmsExceptionCode.DELIVERY_EMPTY_ERROR);
        }
        log.info("清退出库单, deliveryCode -> [{}], orderType类型 -> [{}]", deliveryCode, deliveryHeaderDo.getType());
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailExcludeCancelByDeliveryCode(deliveryCode);
        if (CollectionUtils.isEmpty(deliveryDetailDos)) {
            log.error("出库单{}明细为空", deliveryCode);
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY_ERROR);
        }
        List<InventoryAllocateRequestDetail> params = toAllocateRequestDetail(deliveryHeaderDo, deliveryDetailDos);
        return executeSearchWarehouseAndAllocateInventory(deliveryHeaderDo, params);
    }

    /**
     * 获取和验证出库准许
     *
     * @param deliveryCode
     * @return
     */
    private DeliveryPermissionDo queryAndValidatePermission(String deliveryCode) {
        DeliveryPermissionDo deliveryPermissionDo = deliveryPermissionRepository.selectOneByDeliveryCode(deliveryCode);
        if (Objects.isNull(deliveryPermissionDo)) {
            log.error("未找到单据:{}", deliveryCode);
            throw new WmsException(WmsExceptionCode.DELIVERY_EMPTY_ERROR);
        }
        if (!DeliveryPermissionAllocatedStatusEnum.cleanJobNeedRetryStatus.contains(deliveryPermissionDo.getAllocatedStatus())) {
            log.info("单据{}分配状态不正确: {}", deliveryCode, deliveryPermissionDo.getAllocatedStatus());
            return null;
        }
        return deliveryPermissionDo;
    }


    /**
     * 寻仓
     *
     * @param deliveryHeaderDo
     * @param inventoryAllocateRequestDetails
     * @return
     */
    private List<InvInventoryAllocatedDo> executeSearchWarehouseAndAllocateInventory(DeliveryHeaderDo deliveryHeaderDo, List<InventoryAllocateRequestDetail> inventoryAllocateRequestDetails) {
        /*
         * 1、寻找库存
         * 2、 寻仓
         * 3、反写仓库
         * 4、过滤库存
         * 5、 排序
         * 6、占用库存
         * 7、异步请求SCI占用库存
         * */
        /*1、寻找库存*/
        log.info("查询清退出库单据的可用库存,deliveryCode -> [{}]", deliveryHeaderDo.getDeliveryOrderCode());
        List<InventoryDo> originalInventoryList = searchService.search(inventoryAllocateRequestDetails);
        log.info("originalInventoryList size : {}", originalInventoryList.size());

        /*2、过滤可用库存的园区与出库单指定的园区相匹配*/
        originalInventoryList = filterOrderParkWithInvPark(deliveryHeaderDo, originalInventoryList);

        /*寻仓*/
        String warehouseCode = StringUtils.EMPTY;
        if(StringUtils.isEmpty(deliveryHeaderDo.getSubType())
                ||WmsOutBoundSubTypeEnum.EXPIRY_FORCE_RETURN.getCode().equals(deliveryHeaderDo.getSubType())) {
            // 默认清退出库的寻仓逻辑
            warehouseCode = defaultForceReturnWarehouseFinder.find(originalInventoryList);

        }else if(WmsOutBoundSubTypeEnum.LONG_STORAGE_FORCE_RETURN.getCode().equals(deliveryHeaderDo.getSubType())){
            // 长库龄清退出库(个人)
            warehouseCode = longStorageForceReturnWarehouseFinder.findLongStorageWarehouse(originalInventoryList);
        }else if(WmsOutBoundSubTypeEnum.UNSALABLE_FORCE_RETURN.getCode().equals(deliveryHeaderDo.getSubType())){
            // 企业滞销强退(企业)
            warehouseCode = longStorageForceReturnWarehouseFinder.findLongStorageWarehouse(originalInventoryList);
        }
        log.info("单据{}寻仓结果:{}", deliveryHeaderDo.getDeliveryOrderCode(), warehouseCode);

        if (StringUtils.isBlank(warehouseCode)) {
            /*尝试强制发货*/
            log.info("清退寻仓为空,启用强制发货流程, orderCode -> [{}]", deliveryHeaderDo.getDeliveryOrderCode());
            cleanUpOrderShipService.forceShip(deliveryHeaderDo.getDeliveryOrderCode());
            return null;
        }

        List<InventoryDo> targetInventoryList = beforeAllocateInventory(deliveryHeaderDo.getDeliveryOrderCode(), originalInventoryList, warehouseCode);
        List<InvInventoryAllocatedDo> allocateRecordList = allocateService.allocateInventory(inventoryAllocateRequestDetails, targetInventoryList);
        if (CollectionUtils.isEmpty(allocateRecordList)) {
            log.warn("单据{}的分配记录为空", deliveryHeaderDo.getDeliveryOrderCode());
            /*清除仓库*/
            deliveryHeaderRepository.saveWarehouseCode(deliveryHeaderDo.getDeliveryOrderCode(),StringUtils.EMPTY,StringUtils.EMPTY);
            deliveryDetailRepository.saveWarehouseCodeByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(),StringUtils.EMPTY);
            /*尝试强制发货*/
            cleanUpOrderShipService.forceShip(deliveryHeaderDo.getDeliveryOrderCode());
            return allocateRecordList;
        }
        log.info("单据{}的库存分配记录:{}", deliveryHeaderDo.getDeliveryOrderCode(), JsonUtils.serialize(allocateRecordList));
        /*4、调用SIC API进行库存占用*/
        deliveryPermissionRepository.updateAllocatedStatus(deliveryHeaderDo.getDeliveryOrderCode(), DeliveryPermissionAllocatedStatusEnum.INVOKE.getStatus());
        executor.executor(deliveryHeaderDo.getDeliveryOrderCode(), deliveryHeaderDo.getType());
        return allocateRecordList;
    }

    private List<InventoryDo> filterOrderParkWithInvPark(DeliveryHeaderDo deliveryHeaderDo, List<InventoryDo> originalInventoryList) {
        //原流程是不指定园区，如果下发过来仍然没有园区，直接返回原invList
        if (StringUtils.isEmpty(deliveryHeaderDo.getParkCode())){
            return originalInventoryList;
        }

        if (CollectionUtils.isEmpty(originalInventoryList)){
            log.info("查询清退出库单据的可用库存list为空,deliveryCode -> [{}]", deliveryHeaderDo.getDeliveryOrderCode());
            return originalInventoryList;
        }

        //可用库存仓库号Set
        Set<String> warehouseCodeSet = originalInventoryList.stream().map(InventoryDo::getWarehouseCode).collect(Collectors.toSet());

        List<ScpWarehouseDo> warehouseDoList = warehouseRepository.batchSelectByWarehouseCode(warehouseCodeSet, deliveryHeaderDo.getTenantCode());

        if (CollectionUtils.isEmpty(warehouseDoList)){
            log.info("查询清退出库单据的可用园区仓库list为空,warehouseDoList -> [{}]", JSON.toJSONString(warehouseCodeSet));
            return new ArrayList<>();
        }

        //key : warehouseCode   value : parkCode
        Map<String, String> warehouseParkMap = warehouseDoList.stream().collect(Collectors.toMap(ScpWarehouseDo::getWarehouseCode, ScpWarehouseDo::getParkCode));

        originalInventoryList = originalInventoryList.stream().filter(
                item -> deliveryHeaderDo.getParkCode().equals(warehouseParkMap.get(item.getWarehouseCode()))).collect(Collectors.toList());

        return originalInventoryList;
    }

    /**
     * 分配库存前处理
     *
     * @param deliveryCode
     * @param originalInventoryList
     * @param warehouseCode
     * @return
     */
    private List<InventoryDo> beforeAllocateInventory(String deliveryCode, List<InventoryDo> originalInventoryList, String warehouseCode) {
        ScpWarehouseDo scpWarehouseDo = warehouseRepository.queryByCodeAndTenant(warehouseCode);
        if (Objects.isNull(scpWarehouseDo)) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_NOT_EXIST);
        }

        deliveryHeaderRepository.saveWarehouseCode(deliveryCode, warehouseCode, scpWarehouseDo.getWarehouseName());
        deliveryDetailRepository.saveWarehouseCodeByDeliveryCode(deliveryCode, warehouseCode);
        /*2、过滤库存*/
        log.info("过滤单据{}库存", deliveryCode);
        List<InventoryDo> targetInventoryList = originalInventoryList.stream().filter(inventory -> warehouseCode.equals(inventory.getWarehouseCode())).collect(Collectors.toList());
        log.info("单据{}目标库存{}", deliveryCode, JsonUtils.serialize(targetInventoryList));
        if (CollectionUtils.isEmpty(targetInventoryList)) {
            log.info("单据{}目标库存按仓库{}过滤后为空", deliveryCode, warehouseCode);
            return targetInventoryList;
        }
        //排序
        inventorySorter.sort(targetInventoryList);
        /*3、占用库存*/
        log.info("单据{}开始分配库存", deliveryCode);
        return targetInventoryList;
    }


    /**
     * 尝试对等待取消对进行拦截
     *
     * @param deliveryPermissionDo
     * @return
     */
    private boolean tryInterceptForWaitingCancel(DeliveryPermissionDo deliveryPermissionDo) {
        if (!DeliveryPermissionAllocatedStatusEnum.WAITING_CANCEL.getStatus().equals(deliveryPermissionDo.getAllocatedStatus())) {
            return false;
        }
        /*尝试强制发货*/
        cleanUpOrderShipService.forceShipAfterCancel(deliveryPermissionDo.getDeliveryOrderCode());
        return true;
    }


    /**
     * 将单据明细转成库存分配请求
     *
     * @param deliveryHeaderDo
     * @param deliveryDetailDoList
     * @return
     */
    private List<InventoryAllocateRequestDetail> toAllocateRequestDetail(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDoList) {
        List<InventoryAllocateRequestDetail> allocateRequestDetails = new ArrayList<>();
        if (CollectionUtils.isEmpty(deliveryDetailDoList)) {
            return allocateRequestDetails;
        }
        deliveryDetailDoList.forEach(detail -> {
            InventoryAllocateRequestDetail allocateRequestDetail = new InventoryAllocateRequestDetail();
            allocateRequestDetail.setReferenceNo(detail.getDeliveryOrderCode());
            allocateRequestDetail.setReferenceType(deliveryHeaderDo.getType());
            allocateRequestDetail.setReferenceDetailNo(detail.getDetailNo());
            allocateRequestDetail.setSkuId(detail.getSkuId());
            allocateRequestDetail.setOwnerCode(detail.getOwnerCode());
            allocateRequestDetail.setUniqueCode(detail.getUniqueCode());
            allocateRequestDetail.setBizType(detail.getBizType());
            allocateRequestDetail.setQualityLevel(detail.getQualityLevel());
            allocateRequestDetail.setTenantId(detail.getTenantCode());
            if(StringUtils.isEmpty(deliveryHeaderDo.getSubType())
                    ||WmsOutBoundSubTypeEnum.EXPIRY_FORCE_RETURN.getCode().equals(deliveryHeaderDo.getSubType())) {
                /*将下架天数转换为过期时间*/
                allocateRequestDetail.setExpireDate(DateUtils.plusDays(LocalDate.now(), detail.getOutOfStockDay()));
            }else if(WmsOutBoundSubTypeEnum.LONG_STORAGE_FORCE_RETURN.getCode().equals(deliveryHeaderDo.getSubType())
                    || WmsOutBoundSubTypeEnum.UNSALABLE_FORCE_RETURN.getCode().equals(deliveryHeaderDo.getSubType())){
                // TODO 长库龄清退 或者 企业滞销强退
            }
            allocateRequestDetail.setQty(detail.getPlanQty());
            allocateRequestDetails.add(allocateRequestDetail);
        });
        return allocateRequestDetails;
    }

}
