package com.poizon.scm.wms.service.interceptor.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.util.Map;

/**
 * 1）替换测试环境task_detail_result、task_detail、task为真实表
 * 2）只在测试环境使用，baseline.dynamic.data.switch:false
 */
@Slf4j
@Component
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class MybatisInterceptor implements Interceptor {

    @Value("${baseline.dynamic.data.switch:false}")
    private boolean baselineDynamicDataSwitch;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (!baselineDynamicDataSwitch) {
            return invocation.proceed();
        }

        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = MetaObject.forObject(statementHandler, SystemMetaObject.DEFAULT_OBJECT_FACTORY, SystemMetaObject.DEFAULT_OBJECT_WRAPPER_FACTORY, new DefaultReflectorFactory());
        BoundSql boundSql = statementHandler.getBoundSql();
        String originalSql = boundSql.getSql().trim();
        if (StringUtils.isBlank(originalSql)) {
            return invocation.proceed();
        }
        if (!originalSql.contains("task_detail_result") && !originalSql.contains("task_detail") && !originalSql.contains("task")) {
            return invocation.proceed();
        }

        try {
            Map<String, Object> paramMap = JSONObject.parseObject(JSON.toJSONString(boundSql.getParameterObject()));
            String type = getType(paramMap);
            if (StringUtils.isNotEmpty(type)) {
                log.info("===》 替换前的sql:{}", originalSql);
                String sql = TsqlTablePrefixVisitor.getSql(originalSql, type);
                log.info("===》 替换后的sql:{}", sql);
                metaObject.setValue("delegate.boundSql.sql", sql);
            }
        }catch (Exception exception){
            exception.printStackTrace();
            log.error("===》 替换task逻辑表异常，跳过替换:{}", originalSql);
        }
        return invocation.proceed();
    }

    private String getType(Map<String, Object> paramMap) {
        if (MapUtils.isNotEmpty(paramMap)) {
            String taskType = (String) ((null == paramMap.get("type")) ? paramMap.get("taskType") : paramMap.get("type"));
            if (StringUtils.isEmpty(taskType)) {
                for (Object value : paramMap.values()) {
                    try {
                        if(null == value || value instanceof String || value instanceof Integer){
                            continue;
                        }

                        Map<String, Object> entityMap = JSONObject.parseObject(JSON.toJSONString(value));
                        String entityType = (String) ((null == entityMap.get("type")) ? entityMap.get("taskType") : entityMap.get("type"));
                        if (StringUtils.isNotEmpty(entityType)) {
                            return entityType;
                        }
                    }catch (Exception exception){
                        exception.printStackTrace();
                        log.error("===》 获取type异常，跳过当前字段");
                    }
                }
            }
            return taskType;
        }
        return null;
    }
}
