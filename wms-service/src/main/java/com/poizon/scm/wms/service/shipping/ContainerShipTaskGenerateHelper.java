package com.poizon.scm.wms.service.shipping;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.graph.MutableValueGraph;
import com.poizon.scm.wms.adapter.container.model.ContainerInstanceDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.domain.flow.pojo.FlowEdgeValuePojo;
import com.poizon.scm.wms.domain.flow.pojo.FlowFactor;
import com.poizon.scm.wms.domain.flow.pojo.TaskFlowNodePojo;
import com.poizon.scm.wms.domain.flow.rule.FlowRuleFindFactory;
import com.poizon.scm.wms.domain.flow.rule.base.RuleTypeEnum;
import com.poizon.scm.wms.domain.inner.container.instance.entity.param.ContainerDetailParam;
import com.poizon.scm.wms.domain.inner.container.instance.enums.ContainerUseTypeEnum;
import com.poizon.scm.wms.domain.inner.container.swap.SwapContainerService;
import com.poizon.scm.wms.domain.inner.container.swap.entity.ContainerDetailResult;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2021/8/6
 */
@Slf4j
@Service
public class ContainerShipTaskGenerateHelper {

    @Autowired
    private ShipTaskCommandRepository shipTaskCommandRepository;

    @Autowired
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;

    @Autowired
    private SwapContainerService swapContainerService;

    @Autowired
    private ICommodityQueryV2Service iCommodityQueryV2Service;

    @Autowired
    private FlowRuleFindFactory flowRuleFindFactory;

    @Autowired
    private InventoryRepository inventoryRepository;

    /**
     * 根据箱明细生成发货任务
     *
     * @param referenceNo          单据头
     * @param deliveryDetailNoList 出库单未取消的明细编号
     * @param containerInstances   箱实例编号
     * @return
     */
    public List<ShipTaskDetailDo> createShipTaskFromContainerDetails(String referenceNo, @Nullable List<String> deliveryDetailNoList, List<ContainerInstanceDo> containerInstances) {
        Preconditions.checkBiz(StringUtils.isNotEmpty(referenceNo),"通过换箱明细创建发货任务，出库单号不能为空");
        //去查询容器明细(可能包含换箱明细结果信息和拣货明细结果信息)
        List<ContainerDetailParam> params = new ArrayList<>();
        containerInstances.forEach(y -> {
            ContainerDetailParam param = ContainerDetailParam.builder()
                    .instanceNo(y.getInstanceNo())
                    .referenceNo(referenceNo)
                    .useTypeEnum(ContainerUseTypeEnum.convert(y.getUseType()))
                    .build();
            params.add(param);
        });
        /*这里查到的是单据所有的箱明细*/
        List<ContainerDetailResult> containerDetails = swapContainerService.findContainerDetail(params);
        if (CollectionUtils.isEmpty(containerDetails)) {
            log.error("未找到容器明细，当前实例号列表:{},出库单号:{}", JSON.toJSONString(containerDetails), referenceNo);
            throw new WmsException("未找到容器明细信息");
        }
        /*指定了出库单明细*/
        if (CollectionUtils.isNotEmpty(deliveryDetailNoList)){
            containerDetails = containerDetails.stream().filter(containerDetailResult -> deliveryDetailNoList.contains(containerDetailResult.getReferenceDetailNo())).collect(Collectors.toList());
        }
        //创建发货任务头
        ShipTaskDo shipTask = this.buildTaskByContainer(containerDetails);
        log.info("新增发货任务头:{}",JSON.toJSONString(shipTask));
        int thResult = shipTaskCommandRepository.save(shipTask);
        Preconditions.checkBiz(thResult == 1, WmsExceptionCode.TASK_HEADER_SAVE_FAIL);
        //创建发货任务明细
        List<ShipTaskDetailDo> shipTDList = this.buildTaskDetailListBySwap(shipTask,containerDetails);
        log.info("新增发货任务明细:{}",JSON.toJSONString(shipTDList));
        int tdListResult = shipTaskDetailCommandRepository.batchSave(shipTDList);
        Preconditions.checkBiz(tdListResult > 0, WmsExceptionCode.TASK_DETAIL_SAVE_FAIL);
        return shipTDList;
    }


    private ShipTaskDo buildTaskByContainer(List<ContainerDetailResult> containerDetailResults) {
        //目前都是一单一明细
        int totalQty = containerDetailResults.stream().mapToInt(ContainerDetailResult::getQty).sum();
        ContainerDetailResult containerDetailResult = containerDetailResults.get(0);
        //由于换箱明细结果里没有flow所以现找flow
        FlowFactor flowFactor = new FlowFactor(containerDetailResult.getWarehouseCode(), containerDetailResult.getReferenceType(), RuleTypeEnum.OUT);
        Pair<String, MutableValueGraph<TaskFlowNodePojo, FlowEdgeValuePojo>> pair = flowRuleFindFactory.getFlow(flowFactor);
        String flowCode = pair == null ? null : pair.getLeft();
        return getTaskDo(flowCode, containerDetailResult.getReferenceType(), containerDetailResult.getReferenceNo(), totalQty);
    }


    private ShipTaskDo getTaskDo(String flowCode, String referenceType, String referenceNo, int totalQty) {
        OperationUserContext user = OperationUserContextHolder.get();
        Date now = new Date();

        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        taskDo.setFlowCode(flowCode);
        taskDo.setReferenceType(referenceType);
        taskDo.setReferenceNo(referenceNo);
        taskDo.setStatus(TaskStatusEnum.INIT.getStatus());
        taskDo.setWarehouseCode(user.getWarehouseCode());
        taskDo.setTenantCode(user.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setOperationUserId(user.getUserId());
        taskDo.setOperationRealName(user.getRealName());
        taskDo.setOperationUserName(user.getUserName());
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        taskDo.setCreatedUserId(user.getUserId());
        taskDo.setCreatedUserName(user.getUserName());
        taskDo.setCreatedRealName(user.getRealName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        taskDo.setVersion(NumberUtils.INTEGER_ZERO);
        taskDo.setDeleted(NumberUtils.INTEGER_ZERO);
        return taskDo;
    }


    private List<ShipTaskDetailDo> buildTaskDetailListBySwap(ShipTaskDo shipTask, List<ContainerDetailResult> containerDetailResults) {
        List<ShipTaskDetailDo> shipTaskDetailList = new ArrayList<>();
        Date now = new Date();

        // 按照referenceDetailNo和inventoryNo进行合并
        Map<String, List<ContainerDetailResult>> cleanRepeatMap = containerDetailResults.stream().collect(Collectors.groupingBy(x -> x.getReferenceDetailNo() + "_" + x.getInventoryNo()));
        final List<ContainerDetailResult> finalTdList = new ArrayList<>();
        cleanRepeatMap.forEach((k, v) -> {
            // 合并操作数
            v.get(0).setQty(v.stream().mapToInt(ContainerDetailResult::getQty).sum());
            finalTdList.add(v.get(0));
        });
        log.info("合并前数量：{}，合并后数量：{}.", containerDetailResults.size(), finalTdList.size());
        cleanRepeatMap.clear();
        containerDetailResults.clear();

        finalTdList.forEach(item ->{
            ShipTaskDetailDo shipTD = new ShipTaskDetailDo();
            shipTD.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            shipTD.setTaskNo(shipTask.getTaskNo());
            shipTD.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            shipTD.setStatus(NumberUtils.INTEGER_ZERO.byteValue());
            shipTD.setReferenceNo(item.getReferenceNo());
            shipTD.setReferenceType(item.getReferenceType());
            shipTD.setReferenceDetailNo(item.getReferenceDetailNo());
            shipTD.setInventoryNo(item.getInventoryNo());
            shipTD.setMfgTime(item.getMfgTime());
            shipTD.setExpTime(item.getExpTime());
            shipTD.setOriginalOrderCode(item.getOriginalOrderCode());
            //库存维度信息
            List<InventoryDo> inventoryDos = inventoryRepository.queryInventoryDoByInventoryNoList(item.getTenantId(), Lists.newArrayList(item.getInventoryNo()), null);
            if (CollectionUtils.isEmpty(inventoryDos)) {
                throw new WmsException("deliveryHeaderCode =" + item.getReferenceNo() + "，inventoryNo=" + item.getInventoryNo() + " 未找到库存");
            }
            InventoryDo inventoryDo = inventoryDos.get(0);
            shipTD.setBizType(inventoryDo.getBizType().byteValue());
            shipTD.setQualityLevel(inventoryDo.getQualityLevel());
            shipTD.setLocationCode(inventoryDo.getLocationCode());
            shipTD.setPoNo(inventoryDo.getOriginalOrderCode());

            shipTD.setContainerCode(item.getContainerCode());
            shipTD.setQty(item.getQty());
            shipTD.setOperationQty(NumberUtils.INTEGER_ZERO);
            shipTD.setWarehouseCode(item.getWarehouseCode());
            shipTD.setTenantCode(item.getTenantId());
            shipTD.setOwnerCode(item.getOwnerCode());
            shipTD.setVendorCode(Optional.ofNullable(item.getVendorCode()).orElse(StringUtils.EMPTY));
            shipTD.setBarcode(Optional.ofNullable(item.getBarcode()).orElse(StringUtils.EMPTY));
            shipTD.setUniqueCode(Optional.ofNullable(item.getUniqueCode()).orElse(StringUtils.EMPTY));
            shipTD.setSkuId(item.getSkuId());
            shipTD.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
            //商品维度信息
            Map<String, SkuCommonRspDomain> skuIdSkuMap =
                    iCommodityQueryV2Service.querySkuCommonMapBySkuIds(item.getTenantId(), Sets.newHashSet(item.getSkuId()));
            if (MapUtils.isNotEmpty(skuIdSkuMap) && Objects.nonNull(skuIdSkuMap.get(item.getSkuId()))){
                SkuCommonRspDomain skuCommonRspDomain = skuIdSkuMap.get(item.getSkuId());
                shipTD.setGoodsTitle(skuCommonRspDomain.getSpuName());
                shipTD.setGoodsPic(skuCommonRspDomain.getSpuLogoUrl());
                shipTD.setGoodsArticleNumber(skuCommonRspDomain.getArtNoMain());
                shipTD.setSpecs(skuCommonRspDomain.getSpecs());
            }else {
                shipTD.setGoodsTitle(StringUtils.EMPTY);
                shipTD.setGoodsPic(StringUtils.EMPTY);
                shipTD.setGoodsArticleNumber(StringUtils.EMPTY);
                shipTD.setSpecs(StringUtils.EMPTY);
            }
            //没有用处的值全部塞空
            shipTD.setUom(StringUtils.EMPTY);
            shipTD.setAllocatedNo(StringUtils.EMPTY);
            shipTD.setOriginalDetailNo(StringUtils.EMPTY);
            //基础信息
            shipTD.setFlowCode(shipTask.getFlowCode());
            shipTD.setCreatedUserId(shipTask.getCreatedUserId());
            shipTD.setCreatedUserName(shipTask.getCreatedUserName());
            shipTD.setCreatedRealName(shipTask.getCreatedRealName());
            shipTD.setCreatedTime(now);
            shipTD.setUpdatedUserId(shipTask.getUpdatedUserId());
            shipTD.setUpdatedUserName(shipTask.getUpdatedUserName());
            shipTD.setUpdatedRealName(shipTask.getUpdatedRealName());
            shipTD.setUpdatedTime(now);
            shipTD.setVersion(NumberUtils.INTEGER_ZERO);
            shipTD.setDeleted(NumberUtils.INTEGER_ZERO);
            shipTD.setBatchNo(inventoryDo.getBatchNo());
            shipTD.setProductionBatchNo(inventoryDo.getProductionBatchNo());
            shipTD.setEntryOrderCode(inventoryDo.getEntryOrderCode());
            shipTaskDetailList.add(shipTD);
        });

        return shipTaskDetailList;
    }
}
