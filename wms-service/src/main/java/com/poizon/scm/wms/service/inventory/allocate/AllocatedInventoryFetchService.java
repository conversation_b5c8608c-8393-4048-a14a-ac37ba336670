package com.poizon.scm.wms.service.inventory.allocate;

import com.poizon.scm.wms.adapter.common.model.BasLocationDo;
import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.repository.InvInventoryAllocatedRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.api.enums.YesOrNoEnum;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.dao.curd.InventoryCurd;
import com.poizon.scm.wms.dao.entitys.InvInventoryAllocatedEntity;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.service.base.BasLocationV2Service;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.delivery.operate.impl.DeliveryOrderModifyServiceImpl;
import com.poizon.scm.wms.service.frame.DataWrapper;
import com.poizon.scm.wms.service.frame.FetchService;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.athena.api.enums.InvManagementModeEnum;
import com.shizhuang.athena.api.inventory.WmsInventoryApi;
import com.shizhuang.athena.api.request.inventory.QueryAllocationCommandDetailRequest;
import com.shizhuang.athena.api.response.inventory.AllocationCommandDetailResponse;
import com.shizhuang.avatar.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AllocatedInventoryFetchService implements FetchService<InvInventoryAllocatedDo> {

    @Autowired
    private InvInventoryAllocatedRepository invInventoryAllocatedRepository;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private DistributeLockUtil distributeLockUtil;

    @Autowired
    private ForkJoinPool fillLackThreadPool;

    @Autowired
    private BasLocationV2Service basLocationV2Service;

    @Autowired
    private ICommodityQueryV2Service commodityQueryV2Service;

    @Autowired
    private WmsInventoryApi wmsInventoryApi;

    @Autowired
    private List<Interceptor> interceptors;

    @Autowired
    private DeliveryOrderModifyServiceImpl deliveryOrderModifyService;

    @Autowired
    private InventoryCurd inventoryCurd;

    @Value("${inventory.allocated.fill.lack.limit.size:100}")
    private int LIMIT_SIZE = 100;

    @Override
    public int calculateNextPageNumber(String referenceNo, int pageSize) {
        int count = invInventoryAllocatedRepository.count(referenceNo);
        return count / pageSize + 1;
    }

    @Override
    public DataWrapper<InvInventoryAllocatedDo> pageFetch(String referenceNo, int pageSize, int pageNumber) {
        QueryAllocationCommandDetailRequest request = new QueryAllocationCommandDetailRequest();
        request.setOrderCode(referenceNo);
        request.setPageNum(pageNumber);
        request.setPageSize(pageSize);
        log.info("请求获取库存分配记录的参数:{}", JsonUtils.serialize(request));
        Result<AllocationCommandDetailResponse> result = wmsInventoryApi.queryAllocationCommandDetail(request);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.error("请求库存分配记录的响应结果为" + JsonUtils.serialize(result));
            throw new WmsException("请求库存分配记录的响应报文为" + JsonUtils.serialize(result));
        }
        log.info("请求库存分配记录的响应报文:{}", JsonUtils.serialize(result));
        if (!Result.SUCCESS_CODE.equals(result.getCode())) {
            String errorMsg = JsonUtils.serialize(result.getErrors());
            log.error("请求库存分配记录的响应报错:{}", errorMsg);
            throw new WmsException("请求库存分配记录的报错" + errorMsg);
        }
        return convertFrom(result.getData());
    }

    @Override
    public void verifyDataOverflow(String referenceNo, int size, int total) {
        int count = invInventoryAllocatedRepository.count(referenceNo);
        /*已经存在的加上当前处理的超过了要处理的，说明数据溢出了*/
        if (count + size > total) {
            throw new WmsException("拉取的库存分配明细总量已经大于预计总量，单据号: " + referenceNo);
        }
    }

    /**
     * 将对象转成所需的结果
     *
     * @param response
     * @return
     */
    private DataWrapper<InvInventoryAllocatedDo> convertFrom(AllocationCommandDetailResponse response) {
        if (Objects.isNull(response.getTotalCommandResultLines())) {
            throw new WmsException("获取出库单库存分配记录的响应结果未给总行数，无法处理");
        }
        List<InvInventoryAllocatedDo> expectList = new ArrayList<>();
        List<AllocationCommandDetailResponse.AllocationCommandDetail> dataList = response.getAllocationCommandDetailList();
        if (Objects.isNull(dataList)) {
            /*为空场景：1、零出；2、正好拉取到最后一满页后，又获取了一页*/
            return new DataWrapper<InvInventoryAllocatedDo>().setData(expectList).setTotal(response.getTotalCommandResultLines());
        }
        for (AllocationCommandDetailResponse.AllocationCommandDetail detail : dataList) {
            InvInventoryAllocatedDo allocated = new InvInventoryAllocatedDo();
            allocated.setSourceInventoryNo(detail.getInventoryNo());
            allocated.setOriginalOrderCode(detail.getOriginalOrderCode());
            allocated.setShipmentNo(detail.getOrderCode());
            allocated.setShipmentDetailNo(detail.getDetailCode());
            allocated.setSkuId(detail.getSkuId());
            allocated.setUniqueCode(detail.getUniqueCode());
            allocated.setQualityLevel(detail.getQualityLevel());
            allocated.setQty(detail.getQty());
            allocated.setWarehouseCode(detail.getWarehouseCode());
            allocated.setTenantCode(detail.getTenantCode());
            allocated.setOwnerCode(detail.getOwnerCode());
            allocated.setBizType(detail.getBizType());
            /*这个字段不从库存那边获取了，直接自己生成*/
            allocated.setAllocatedNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.INVENTORY_ALLOCATED.getSequenceType()));
            allocated.setLocationCode(detail.getLocationCode());
            allocated.setVenderCode(detail.getVendorCode());
            allocated.setFirstReceivedTime(detail.getFirstReceivedTime());
            allocated.setReceivedTime(detail.getReceivedTime());
            allocated.setMfgTime(detail.getMfgTime());
            allocated.setExpTime(detail.getExpTime());
            allocated.setEntryOrderCode(detail.getEntryOrderCode());
            allocated.setContainerCode(detail.getContainerCode());
            allocated.setLotsNo(detail.getLotsNo());
            allocated.setBatchNo(detail.getBatchNo());
            allocated.setProductionBatchNo(detail.getProductionBatchNo());
            allocated.setInvTag(detail.getInvTag());
            allocated.setInvManagementMode(detail.getInvManagementMode());
            allocated.setInvProcessingType(detail.getInvProcessingType());
            expectList.add(allocated);
        }
        return new DataWrapper<InvInventoryAllocatedDo>().setData(expectList).setTotal(response.getTotalCommandResultLines());
    }

    @Transactional
    @Override
    public void process(DataWrapper<InvInventoryAllocatedDo> dataWrapper) {
        /*
         * 处理库存分配记录(获取排、列、层、库区、甬道、工作区信息)
         * 插入库存分配记录表
         * 修改出库单明细的分配数量
         * 修改出库单状态
         * */
        List<InvInventoryAllocatedDo> dataList = dataWrapper.data();
        String shipmentNo = dataList.get(0).getShipmentNo();
        boolean locked = false;
        try {
            fillLack(dataList);
            locked = distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_HEADER, shipmentNo);
            if (locked) {
                /*按照出库单明细分组获取当前明细分配的数量*/
                int count = invInventoryAllocatedRepository.batchSave(dataList);
                if (count != dataList.size()) {
                    throw new WmsException("插入库存分配记录报错");
                }
                Map<String, List<InvInventoryAllocatedDo>> invAllocateMap = dataList.stream().collect(Collectors.groupingBy(InvInventoryAllocatedDo::getShipmentDetailNo));
                List<DeliveryDetailDo> updateList = buildFrom(invAllocateMap);
                deliveryDetailRepository.batchAddActualAllocatedQty(updateList);
            }
        } catch (InterruptedException e) {
            log.warn("处理库存分配记录时获取锁失败");
            Thread.currentThread().interrupt();
        } finally {
            if (locked) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_HEADER, shipmentNo);
            }
        }
    }

    /**
     * 通过分配记录构造出库单明细的更新对象
     *
     * @param detailMappingAllocatedQty
     * @return
     */
    private List<DeliveryDetailDo> buildFrom(Map<String, List<InvInventoryAllocatedDo>> invAllocateMap) {
        List<DeliveryDetailDo> deliveryDetailDos = new ArrayList<>();
        invAllocateMap.forEach((detailNo, allocatedList) -> {
            DeliveryDetailDo deliveryDetailDo = new DeliveryDetailDo();
            deliveryDetailDo.setDetailNo(detailNo);
            deliveryDetailDo.setAllocatedQty(allocatedList.stream().mapToInt(InvInventoryAllocatedDo::getQty).sum());
            InvInventoryAllocatedDo invInventoryAllocatedDo = allocatedList.get(0);
            deliveryDetailDo.setInvTag(Optional.ofNullable(invInventoryAllocatedDo.getInvTag()).orElse(""));
            deliveryDetailDo.setInvManagementMode(Optional.ofNullable(invInventoryAllocatedDo.getInvManagementMode()).orElse(""));
            deliveryDetailDos.add(deliveryDetailDo);
        });
        return deliveryDetailDos;
    }

    /**
     * 填充库存分配的信息，包括：排、列、层、库区、甬道、工作区、货号、商品名称、分类、分类名称、颜色、规格
     *
     * @param dataList
     */
    private void fillLack(List<InvInventoryAllocatedDo> dataList) {
        if ((dataList.size() / LIMIT_SIZE) <= 2) {
            doFillLack(dataList);
        } else {
            log.info("一次性填充的数据太多，使用forkJoin框架执行");
            /*超过限制的2倍使用ForkJoin框架*/
            fillLackThreadPool.invoke(new FillLackTsk(dataList, fillLackThreadPool));
        }
    }

    /**
     * 实际填充
     *
     * @param dataList
     */
    private void doFillLack(List<InvInventoryAllocatedDo> dataList) {
        Set<String> skuIdSet = new HashSet<>();
        Set<String> locationCodeSet = new HashSet<>();
        for (InvInventoryAllocatedDo allocatedDo : dataList) {
            skuIdSet.add(allocatedDo.getSkuId());
            locationCodeSet.add(allocatedDo.getLocationCode());
        }
        InvInventoryAllocatedDo record = dataList.get(0);
        doFillLocationInfo(dataList, locationCodeSet, record.getWarehouseCode(), record.getTenantCode());
        doFillSkuInfo(dataList, skuIdSet, record.getTenantCode());
    }

    /**
     * 填充商品信息
     *
     * @param dataList
     * @param skuIdSet
     * @param tenantCode
     */
    private void doFillSkuInfo(List<InvInventoryAllocatedDo> dataList, Set<String> skuIdSet, String tenantCode) {
        Map<String, SkuCommonRspDomain> skuCommonMap = commodityQueryV2Service.querySkuCommonMapBySkuIds(tenantCode, skuIdSet);
        for (InvInventoryAllocatedDo data : dataList) {
            SkuCommonRspDomain skuCommon = skuCommonMap.get(data.getSkuId());
            if (Objects.isNull(skuCommon)) {
                log.error("商品{}信息未找到", data.getSkuId());
                throw new WmsException(WmsExceptionCode.SKU_NOT_EXIST);
            }
            data.setGoodsArticleNumber(skuCommon.getArtNoMain());
            data.setGoodsTitle(skuCommon.getSpuName());
            data.setCategoryId(skuCommon.getCategoryId());
            data.setCategoryName(skuCommon.getCategoryNameLevel3());
            data.setColor(skuCommon.getColor());
            data.setSpecs(skuCommon.getPropertyText());
        }
    }

    /**
     * 填充库位信息
     *
     * @param dataList
     * @param locationCodeSet
     * @param warehouseCode
     * @param tenantCode
     */
    private void doFillLocationInfo(List<InvInventoryAllocatedDo> dataList, Set<String> locationCodeSet, String warehouseCode, String tenantCode) {
        List<BasLocationDo> locationList = basLocationV2Service.areaDetailByCodes(warehouseCode, locationCodeSet, tenantCode);
        Map<String, BasLocationDo> LocationMap = locationList.stream().collect(Collectors.toMap(BasLocationDo::getLocationCode, Function.identity()));
        for (InvInventoryAllocatedDo data : dataList) {
            BasLocationDo basLocation = LocationMap.get(data.getLocationCode());
            if (Objects.isNull(basLocation)) {
                log.error("库位{}信息未找到", data.getLocationCode());
                throw new WmsException(WmsExceptionCode.LOCATION_NOT_EXIST);
            }
            data.setPassNum(basLocation.getPassNum());
            data.setLevelNum(basLocation.getLevelNum());
            data.setRowNum(basLocation.getRowNum());
            data.setColumnNum(basLocation.getColumnNum());
            data.setAreaCode(basLocation.getAreaCode());
            data.setWorkZoneCode(basLocation.getWorkZoneCode());
        }
    }

    @Override
    public boolean hadFetchAll(String referenceNo, int total) {
        int count = invInventoryAllocatedRepository.count(referenceNo);
        if (count > total) {
            throw new WmsException("库存分配明细总量大于预计总量，单据号: " + referenceNo);
        }
        return count == total;
    }

    @Override
    public List<InvInventoryAllocatedDo> filterNotProcessed(String referenceNo, List<InvInventoryAllocatedDo> originalList) {
        /*库存服务没有回传库存分配编号，所以没有办法过滤已经拉取的数据，这里只能抛出异常了*/
        throw new WmsException("库存分配暂不支持兜底拉取策略");
    }

    @Transactional
    @Override
    public void afterFetchAll(String referenceNo) {
        updateDeliveryHeaderStatus(referenceNo);
    }


    /**
     * 更新单据的分配状态
     *
     * @param shipmentNo
     */
    private void updateDeliveryHeaderStatus(String shipmentNo) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(shipmentNo);
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }
        if (!WmsOutBoundStatusEnum.INIT.getStatus().equals(deliveryHeaderDo.getStatus())) {
            throw new WmsException(WmsExceptionCode.UPDATE_ERROR_FOR_DELIVERY_STATUS);
        }
        int totalAllocatedQty = invInventoryAllocatedRepository.sumAllocateQty(shipmentNo);
        WmsOutBoundStatusEnum status;
        if (totalAllocatedQty <= 0) {
            status = WmsOutBoundStatusEnum.FAIL_ALLOCATE;
        } else {
            status = totalAllocatedQty == deliveryHeaderDo.getTotalPlanQty() ? WmsOutBoundStatusEnum.WHOLE_ALLOCATE : WmsOutBoundStatusEnum.PART_ALLOCATE;
        }
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(shipmentNo, deliveryHeaderDo.getTenantCode());

        beforeUpdateStatus(deliveryHeaderDo, status);
        DeliveryHeaderDo updateHeader = new DeliveryHeaderDo();
        updateHeader.setId(deliveryHeaderDo.getId());
        updateHeader.setStatus(status.getStatus());
        boolean containsSnFlag = deliveryDetailDos.stream().anyMatch(item -> InvManagementModeEnum.SN.getCode().equals(item.getInvManagementMode()));
        if (containsSnFlag) {
            updateHeader.setContainsSnFlag(YesOrNoEnum.YES.getCode());
        }
        deliveryHeaderRepository.updateById(updateHeader);
        afterUpdateStatus(deliveryHeaderDo, status);
        // 完全分配状态, 仓储托管回写明细
        if (WmsOutBoundStatusEnum.WHOLE_ALLOCATE.getStatus().equals(status.getStatus()) && containsSnFlag) {
            List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList = inventoryCurd.queryAllocateRecordList(deliveryHeaderDo.getTenantCode(), deliveryHeaderDo.getDeliveryOrderCode());
            deliveryOrderModifyService.writeBackDeliveryDetail(inventoryAllocatedEntityList, deliveryDetailDos);
        }
    }

    /**
     * 更新状态前的拦截处理
     *
     * @param deliveryHeaderDo
     * @param status
     */
    private void beforeUpdateStatus(DeliveryHeaderDo deliveryHeaderDo, WmsOutBoundStatusEnum status) {
        for (Interceptor interceptor : interceptors) {
            interceptor.before(deliveryHeaderDo, status);
        }
    }

    /**
     * 更新状态后的拦截处理
     *
     * @param deliveryHeaderDo
     * @param status
     */
    private void afterUpdateStatus(DeliveryHeaderDo deliveryHeaderDo, WmsOutBoundStatusEnum status) {
        for (Interceptor interceptor : interceptors) {
            interceptor.after(deliveryHeaderDo, status);
        }
    }


    /**
     * ForkJoinTask
     */
    class FillLackTsk extends RecursiveTask<List<InvInventoryAllocatedDo>> {

        private List<InvInventoryAllocatedDo> dataList;

        private ForkJoinPool forkJoinPool;


        public FillLackTsk(List<InvInventoryAllocatedDo> inventoryAllocatedList, ForkJoinPool forkJoinPool) {
            this.dataList = inventoryAllocatedList;
            this.forkJoinPool = forkJoinPool;
        }

        @Override
        protected List<InvInventoryAllocatedDo> compute() {
            if (dataList.size() <= LIMIT_SIZE) {
                doFillLack(dataList);
                return dataList;
            } else {
                List<InvInventoryAllocatedDo> left = dataList.subList(0, dataList.size() / 2);
                FillLackTsk leftTask = new FillLackTsk(left, forkJoinPool);
                leftTask.fork();
                List<InvInventoryAllocatedDo> right = dataList.subList(dataList.size() / 2, dataList.size());
                FillLackTsk rightTask = new FillLackTsk(right, forkJoinPool);
                rightTask.fork();
                List<InvInventoryAllocatedDo> merged = new ArrayList<>();
                merged.addAll(leftTask.join());
                merged.addAll(rightTask.join());
                return merged;
            }
        }
    }

    /**
     * 拦截器
     */
    public interface Interceptor {

        default void before(DeliveryHeaderDo deliveryHeader, WmsOutBoundStatusEnum target) {
        }

        default void after(DeliveryHeaderDo deliveryHeader, WmsOutBoundStatusEnum target) {
        }
    }
}
