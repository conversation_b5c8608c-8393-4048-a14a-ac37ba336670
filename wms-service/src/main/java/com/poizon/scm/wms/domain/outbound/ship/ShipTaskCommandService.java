package com.poizon.scm.wms.domain.outbound.ship;

import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskCreateParam;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskDetailParam;
import com.poizon.scm.wms.domain.outbound.ship.processor.ShipTaskCreateProcessor;
import com.poizon.scm.wms.service.inventory.query.InventoryQueryService;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 11/05/2021
 * @desc
 */
@Service
@Slf4j
public class ShipTaskCommandService {

    @Resource
    private ShipTaskCreateProcessor shipTaskCreateProcessor;
    @Resource
    private ShipTaskCommandRepository shipTaskCommandRepository;
    @Resource
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;
    @Resource
    private InventoryQueryService inventoryQueryService;
    @Resource
    private DistributeLockUtil distributeLockUtil;
    @Resource
    private ShipTaskQueryRepository shipTaskQueryRepository;
    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    public void create(ShipTaskCreateParam param) {
        shipTaskCreateProcessor.process(param);
    }

    public void createV2(ShipTaskCreateParam param) {
        boolean lockFlag = false;
        try {
            lockFlag = distributeLockUtil.tryLockForBiz(LockEnum.SHIP_TASK_CREATE_LOCK, param.getHeader().getReferenceNo());
            if (!lockFlag) {
                throw new WmsOperationException("创建发货任务加锁失败");
            }
            /*发货任务幂等校验*/
            if (shipIdempotentCheck(param.getHeader().getReferenceNo())) {
                return;
            }
            ShipTaskDo taskDo = saveShipTask(param);
            saveShipTaskDetail(param, taskDo);
        } catch (InterruptedException ex) {
            log.error("发货任务创建加锁错误", ex);
            Thread.currentThread().interrupt();
            throw new WmsException("创建发货任务失败");
        } catch (Exception ex) {
            log.error("发货任务创建加锁错误", ex);
            throw new WmsException("创建发货任务失败");
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SHIP_TASK_CREATE_LOCK, param.getHeader().getReferenceNo());
            }
        }
    }

    private boolean shipIdempotentCheck(String deliveryOrderCode) {
        List<ShipTaskDo> currentShipTaskDoList = shipTaskQueryRepository.findByReferenceNo(deliveryOrderCode)
                .stream()
                .filter(entity -> !TaskStatusEnum.CANCEL.getStatus().equals(entity.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentShipTaskDoList)) {
            log.info("发货任务已经生成, deliveryOrderCode  -> [{}]", deliveryOrderCode);
            return true;
        }
        return false;
    }

    private void saveShipTaskDetail(ShipTaskCreateParam param, ShipTaskDo taskDo) {
        List<String> inventoryNos = param.getDetail().stream().map(ShipTaskDetailParam::getInventoryNo).collect(Collectors.toList());
        Map<String, InventoryDo> inventoryMap = inventoryQueryService.queryInventoryDoByInventoryNos(inventoryNos);
        // 创建发货任务明细
        List<ShipTaskDetailDo> shipTaskDetailList = new ArrayList<>();
        param.getDetail().forEach(pickTD -> {
            ShipTaskDetailDo detailDo = new ShipTaskDetailDo();
            detailDo.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            detailDo.setTaskNo(taskDo.getTaskNo());
            detailDo.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            detailDo.setBizType(pickTD.getBizType().byteValue());
            detailDo.setStatus(TaskStatusEnum.INIT.getStatus().byteValue());
            detailDo.setReferenceNo(taskDo.getReferenceNo());
            detailDo.setReferenceType(taskDo.getReferenceType());
            detailDo.setReferenceDetailNo(pickTD.getReferenceDetailNo());
            detailDo.setInventoryNo(pickTD.getInventoryNo());
            detailDo.setQty(pickTD.getQty());
            detailDo.setOperationQty(NumberUtils.INTEGER_ZERO);

            InventoryDo inventoryDo = inventoryMap.get(pickTD.getInventoryNo());
            if (Objects.isNull(inventoryDo)){
                log.error("新增发货任务明细未找到库存，inventoryNo:{}",pickTD.getInventoryNo());
                throw new WmsException("新增发货任务未找到库存");
            }
            detailDo.setContainerCode(Optional.ofNullable(inventoryDo.getContainerCode()).orElse(StringUtils.EMPTY));
            detailDo.setPoNo(inventoryDo.getOriginalOrderCode());
            detailDo.setOriginalDetailNo(pickTD.getOriginalDetailNo());
            detailDo.setVendorCode(inventoryDo.getVendorCode());
            detailDo.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
            detailDo.setWarehouseCode(pickTD.getWarehouseCode());
            detailDo.setTenantCode(OperationUserContextHolder.getTenantCode());
            detailDo.setOwnerCode(pickTD.getOwnerCode());
            detailDo.setBarcode(pickTD.getBarcode());
            detailDo.setUniqueCode(pickTD.getUniqueCode());
            detailDo.setSkuId(pickTD.getSkuId());
            detailDo.setGoodsTitle(pickTD.getGoodsTitle());
            detailDo.setGoodsPic(pickTD.getGoodsPic());
            detailDo.setGoodsArticleNumber(pickTD.getGoodsArticleNumber());
            detailDo.setQualityLevel(pickTD.getQualityLevel());
            detailDo.setSpecs(pickTD.getSpecs());
            detailDo.setUom(pickTD.getUom());
            detailDo.setLocationCode(pickTD.getLocationCode());
            detailDo.setAllocatedNo(pickTD.getAllocatedNo());
            detailDo.setFlowCode(pickTD.getFlowCode());
            OperationUserContext userContext = OperationUserContextHolder.get();
            detailDo.setCreatedUserId(userContext.getUserId());
            detailDo.setCreatedUserName(userContext.getUserName());
            detailDo.setCreatedRealName(userContext.getRealName());
            detailDo.setMfgTime(pickTD.getMfgTime());
            detailDo.setExpTime(pickTD.getExpTime());
            detailDo.setOriginalOrderCode(pickTD.getOriginalOrderCode());
            detailDo.setBatchNo(inventoryDo.getBatchNo());
            detailDo.setProductionBatchNo(inventoryDo.getProductionBatchNo());
            detailDo.setEntryOrderCode(inventoryDo.getEntryOrderCode());
            shipTaskDetailList.add(detailDo);
        });
        int tdListResult = shipTaskDetailCommandRepository.batchSave(shipTaskDetailList);
        Preconditions.checkBiz(tdListResult > 0, WmsExceptionCode.TASK_DETAIL_SAVE_FAIL);
    }

    private ShipTaskDo saveShipTask(ShipTaskCreateParam param) {
        int totalQty = param.getDetail().stream().mapToInt(ShipTaskDetailParam::getQty).sum();
        // 创建发货任务头
        OperationUserContext user = OperationUserContextHolder.get();
        Date now = new Date();

        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        taskDo.setFlowCode(param.getHeader().getFlowCode());
        taskDo.setReferenceType(param.getHeader().getReferenceType());
        taskDo.setReferenceNo(param.getHeader().getReferenceNo());
        taskDo.setStatus(TaskStatusEnum.INIT.getStatus());
        taskDo.setWarehouseCode(user.getWarehouseCode());
        taskDo.setTenantCode(user.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setOperationUserId(user.getUserId());
        taskDo.setOperationRealName(user.getRealName());
        taskDo.setOperationUserName(user.getUserName());
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        taskDo.setCreatedUserId(user.getUserId());
        taskDo.setCreatedUserName(user.getUserName());
        taskDo.setCreatedRealName(user.getRealName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        taskDo.setVersion(NumberUtils.INTEGER_ZERO);
        taskDo.setDeleted(NumberUtils.INTEGER_ZERO);
        int thResult = shipTaskCommandRepository.save(taskDo);
        Preconditions.checkBiz(thResult == 1, WmsExceptionCode.TASK_HEADER_SAVE_FAIL);
        return taskDo;
    }

    /**
     * 根据关联单号取消发货明细
     *  1.先查询发货任务
     *  2.查询发货明细
     *  3.取消发货明细
     *  4.取消发货任务
     *
     * @param referenceNo
     * @param tenantCode
     * @param warehouseCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelShipTask(String referenceNo, String warehouseCode, String tenantCode) {
        ShipTaskDo taskDo = shipTaskQueryRepository.findByReferenceNo(referenceNo, warehouseCode, tenantCode);
        if (taskDo == null) {
            throw new WmsOperationException(WmsExceptionCode.SHIP_TASK_NOT_EXIST);
        }
        List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.queryByTaskNo(taskDo.getTaskNo(),
                taskDo.getWarehouseCode(), taskDo.getTenantCode());
        if (CollectionUtils.isEmpty(shipTaskDetailDos)) {
            throw new WmsOperationException(WmsExceptionCode.SHIP_TASK_DETAIL_NOT_EXIST);
        }
        ShipTaskDo updateTask = new ShipTaskDo();
        updateTask.setTaskNo(taskDo.getTaskNo());
        updateTask.setVersion(taskDo.getVersion());
        updateTask.setStatus(TaskStatusEnum.CANCEL.getStatus());
        updateTask.setTotalQty(taskDo.getTotalQty());
        int detailResult = shipTaskDetailCommandRepository.batchCancelShipTaskDetailList(taskDo.getTaskNo(),
                taskDo.getTenantCode(), taskDo.getWarehouseCode());
        if (detailResult != shipTaskDetailDos.size()) {
            throw new WmsOperationException(WmsExceptionCode.CANCEL_SHIP_TASK_DETAIL_ERROR);
        }
        int taskResult = shipTaskCommandRepository.updateSelectiveUseVersionLock(updateTask);
        if (taskResult < 1) {
            throw new WmsOperationException(WmsExceptionCode.CANCEL_SHIP_TASK_ERROR);
        }
        return true;
    }

}
