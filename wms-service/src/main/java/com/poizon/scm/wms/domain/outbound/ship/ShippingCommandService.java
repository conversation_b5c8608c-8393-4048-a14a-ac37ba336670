package com.poizon.scm.wms.domain.outbound.ship;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.AntiFakeCodeDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipAntiFakeDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipPhotosDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.AntiFakeCodeCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipAntiFakeCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipPhotosCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.AntiFakeCodeQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipAntiFakeQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.outbound.returngoods.WmsPackShipService;
import com.poizon.scm.wms.domain.outbound.returngoods.entity.LogisticsInfo;
import com.poizon.scm.wms.domain.outbound.returngoods.service.ExpressBillPrintService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.WmsPackService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.AutoPackGoodsParam;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.PrintExpressParam;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.SealBoxParam;
import com.poizon.scm.wms.domain.outbound.ship.param.ShipAntiFakeParam;
import com.poizon.scm.wms.domain.outbound.ship.param.ShipTakePhotosParam;
import com.poizon.scm.wms.domain.outbound.ship.param.ShippingTakePhotoAntiFakeParam;
import com.poizon.scm.wms.domain.outbound.worklog.WmsOutboundWorkLogService;
import com.poizon.scm.wms.domain.outbound.worklog.enums.WorkLogTypeEnum;
import com.poizon.scm.wms.domain.outbound.worklog.param.OperationLogParam;
import com.poizon.scm.wms.util.enums.AntiFakeCodeStatusEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.enums.WmsPackStatusEnum;
import com.poizon.scm.wms.util.util.NumberUtils;
import com.poizon.scp.wms.outbound.sdk.enums.ScpGrayConfigKeyEnum;
import com.poizon.scp.wms.outbound.sdk.gray.config.GrayConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/26 8:47 下午
 **/
@Slf4j
@Service
public class ShippingCommandService {

    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;
    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Resource
    private ShipPhotosCommandRepository shipPhotosCommandRepository;
    @Resource
    private ShipAntiFakeCommandRepository shipAntiFakeCommandRepository;
    @Resource
    private ShipAntiFakeQueryRepository shipAntiFakeQueryRepository;
    @Resource
    private AntiFakeCodeQueryRepository antiFakeCodeQueryRepository;
    @Resource
    private AntiFakeCodeCommandRepository antiFakeCodeCommandRepository;
    @Resource
    private GrayConfigRepository grayConfigRepository;
    @Resource
    private WmsPackService wmsPackService;
    @Resource
    private ExpressBillPrintService expressBillPrintService;
    @Resource
    private WmsPackRepository wmsPackRepository;
    @Autowired
    private WmsPackShipService wmsPackShipService;
    @Resource
    private WmsOutboundWorkLogService wmsOutboundWorkLogService;
    /**
     * 发货拍照
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void takePhotos(ShipTakePhotosParam param) {
        //查询发货明细
        ShipTaskDetailDo shipTaskDetailDo = queryAndCheckShipTask(param.getUniqueCode());
        //全量更新，先删除，再批量插入
        List<String> photoList = removePhotosPrefix(param.getPhotos());
        List<ShipPhotosDo> shipPhotosDoList = buildShipPhotoDoList(shipTaskDetailDo, photoList);
        shipPhotosCommandRepository.deleteByDeliveryDetailNo(shipTaskDetailDo.getReferenceNo(),shipTaskDetailDo.getReferenceDetailNo(), param.getUniqueCode());
        int rows = shipPhotosCommandRepository.batchInsert(shipPhotosDoList);
        if (rows == 0) {
            throw new WmsOperationException("照片保存失败");
        }
        //日志中心
        OperationLogParam operationLogParam = OperationLogParam.builder()
                .orderNo(Lists.newArrayList(shipTaskDetailDo.getReferenceDetailNo()))
                .workLogTypeEnum(WorkLogTypeEnum.OUTBOUND_DETAIL_NO)
                .qty(NumberUtils.INTEGER_ONE)
                .build();
        wmsOutboundWorkLogService.operateLog(operationLogParam);
    }

    private ShipTaskDetailDo queryAndCheckShipTask(String uniqueCode) {
        List<ShipTaskDetailDo> shipTaskDetailDoList =  shipTaskDetailQueryRepository.queryByUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(shipTaskDetailDoList)) {
            throw new WmsOperationException("发货任务未找到");
        }
        ShipTaskDetailDo shipTaskDetailDo = shipTaskDetailDoList.get(0);
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(shipTaskDetailDo.getReferenceNo());
        if (deliveryHeaderDo == null) {
            log.info("出库单不存在,deliveryOrderCode:{}",shipTaskDetailDo.getReferenceNo());
            throw new WmsOperationException("出库单不存在");
        }
        if (WmsOutBoundStatusEnum.outComplete.contains(deliveryHeaderDo.getStatus())) {
            throw new WmsOperationException("商品已完成发货！");
        }
        return shipTaskDetailDo;
    }

    /**
     * 构建照片集合
     * @param deliveryHeader
     * @param photos
     * @return
     */
    private List<ShipPhotosDo> buildShipPhotoDoList(DeliveryHeaderDo deliveryHeader,List<String> photos) {
        List<ShipPhotosDo> shipPhotosDoList = new ArrayList<>();
        for (String photoUrl : photos) {
            ShipPhotosDo item = new ShipPhotosDo();
            item.setPhotoUrl(photoUrl);
            item.setDeliveryOrderCode(deliveryHeader.getDeliveryOrderCode());
            item.setTenantCode(deliveryHeader.getTenantCode());
            item.setWarehouseCode(deliveryHeader.getWarehouseCode());
            item.setCreatedUserId(OperationUserContextHolder.get().getUserId());
            item.setCreatedUserName(OperationUserContextHolder.get().getUserName());
            item.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
            item.setUpdatedUserName(OperationUserContextHolder.get().getUserName());

            shipPhotosDoList.add(item);
        }
        return shipPhotosDoList;
    }

    /**
     * 构建照片集合
     * @param shipTaskDetailDo
     * @param photos
     * @return
     */
    private List<ShipPhotosDo> buildShipPhotoDoList(ShipTaskDetailDo shipTaskDetailDo,List<String> photos) {
        List<ShipPhotosDo> shipPhotosDoList = new ArrayList<>();
        for (String photoUrl : photos) {
            ShipPhotosDo item = new ShipPhotosDo();
            item.setPhotoUrl(photoUrl);
            item.setDeliveryOrderCode(shipTaskDetailDo.getReferenceNo());
            item.setDeliveryDetailNo(shipTaskDetailDo.getReferenceDetailNo());
            item.setUniqueCode(shipTaskDetailDo.getUniqueCode());
            item.setSkuId(shipTaskDetailDo.getSkuId());
            item.setOwnerCode(shipTaskDetailDo.getOwnerCode());
            item.setTenantCode(shipTaskDetailDo.getTenantCode());
            item.setWarehouseCode(shipTaskDetailDo.getWarehouseCode());
            item.setCreatedUserId(OperationUserContextHolder.get().getUserId());
            item.setCreatedUserName(OperationUserContextHolder.get().getUserName());
            item.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
            item.setUpdatedUserName(OperationUserContextHolder.get().getUserName());

            shipPhotosDoList.add(item);
        }
        return shipPhotosDoList;
    }

    /**
     * 移除照片前缀
     * @param photoList
     * @return
     */
    private List<String> removePhotosPrefix(List<String> photoList) {
        if (CollectionUtils.isEmpty(photoList)) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        String pictureDomainPrefix = grayConfigRepository.getConfigValue(ScpGrayConfigKeyEnum.PICTURE_DOMAIN_NAME_PREFIX);
        photoList.forEach(item -> {
            resultList.add(item.replace(pictureDomainPrefix,""));
        });
        return resultList;
    }

    /**
     * 发货绑扣
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void takePhotoAntiFakeWithDeliveryOrder(ShippingTakePhotoAntiFakeParam param) {
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(param.getDeliveryOrderCode());
        if(Objects.isNull(deliveryHeader)){
            log.info("出库单不存在,deliveryOrderCode:{}",param.getDeliveryOrderCode());
            throw new WmsOperationException("出库单不存在");
        }
        if (WmsOutBoundStatusEnum.outComplete.contains(deliveryHeader.getStatus())) {
            throw new WmsOperationException("单据已完成发货！");
        }
        //照片不为空才拍照留档
        if(CollectionUtils.isNotEmpty(param.getPhotos())){
            takePhotosWithDeliveryOrder(deliveryHeader,param.getPhotos());
        }
        //扣编码不为空才绑扣
        if(CollectionUtils.isNotEmpty(param.getCodes())){
            antiFakeWithDeliveryOrder(deliveryHeader,param.getCodes());
        }
    }

    /**
     * 按单据的拍照处理
     *
     * @param deliveryHeader 参数
     * @param photoUrls 拍的招聘url
     */
    public void takePhotosWithDeliveryOrder(DeliveryHeaderDo deliveryHeader,List<String> photoUrls) {
        //全量更新，先删除，再批量插入
        List<String> photos = removePhotosPrefix(photoUrls);
        List<ShipPhotosDo> shipPhotosList = buildShipPhotoDoList(deliveryHeader,photos);
        shipPhotosCommandRepository.deleteByDeliveryOrder(deliveryHeader.getDeliveryOrderCode(),deliveryHeader.getTenantCode());
        shipPhotosCommandRepository.batchSave(shipPhotosList);
    }

    /**
     * 按单据的拍照处理
     *
     * @param deliveryHeader 参数
     * @param antiFakeCodes 扣编码集合
     */
    public void antiFakeWithDeliveryOrder(DeliveryHeaderDo deliveryHeader,List<String> antiFakeCodes) {
        /**
         * 扣码校验
         * 1、code有效可用
         */
        Set<String> codeSet = new HashSet<>();
        codeSet.addAll(antiFakeCodes);
        if (codeSet.size() != antiFakeCodes.size()) {
            throw new WmsOperationException("防伪扣码重复");
        }
        List<AntiFakeCodeDo> antiFakeCodeDoList = antiFakeCodeQueryRepository.queryByCodes(codeSet);
        if (CollectionUtils.isEmpty(antiFakeCodeDoList) || antiFakeCodeDoList.size() != codeSet.size()) {
            throw new WmsOperationException("存在非法的防伪扣码");
        }
        //未使用
        List<String> unAntiFakeCodeList = antiFakeCodeDoList.stream().filter(item->Objects.equals(AntiFakeCodeStatusEnum.UN_USE.getStatus(),item.getStatus()))
                .map(AntiFakeCodeDo::getCode).collect(Collectors.toList());
        //已使用
        List<String> usedAntiFakeCodeList = antiFakeCodeDoList.stream().filter(item->Objects.equals(AntiFakeCodeStatusEnum.USED.getStatus(),item.getStatus()))
                .map(AntiFakeCodeDo::getCode).collect(Collectors.toList());
        List<ShipAntiFakeDo> existedShipAntiFakeList = shipAntiFakeQueryRepository.queryByDeliveryOrderCode(deliveryHeader.getDeliveryOrderCode());
        if((unAntiFakeCodeList.size()+usedAntiFakeCodeList.size())!=antiFakeCodeDoList.size()){
            throw new WmsOperationException("存在无效的防伪扣码");
        }
        if(CollectionUtils.isEmpty(existedShipAntiFakeList)&&CollectionUtils.isNotEmpty(usedAntiFakeCodeList)){
            //出库单未绑定任何证扣如果传入的防伪扣有已使用的则存在无效的防伪扣码
            throw new WmsOperationException("存在无效的防伪扣码");
        }
        if(CollectionUtils.isNotEmpty(existedShipAntiFakeList)
                &&CollectionUtils.isNotEmpty(usedAntiFakeCodeList)){
            //出库单已绑定扣如果数量不等于
            if(existedShipAntiFakeList.size()!=usedAntiFakeCodeList.size()){
                throw new WmsOperationException("存在无效的防伪扣码");
            }
            List<String> existedAntiFakeCodeList= existedShipAntiFakeList.stream().map(ShipAntiFakeDo::getAntiFakeCode).collect(Collectors.toList());
            boolean pageUsedAntiFakeCodeIsExistedUsedListFlag = usedAntiFakeCodeList.stream().anyMatch(code->!existedAntiFakeCodeList.contains(code));
            //如果页面传过来的已使用的扣不存在与当前单据已绑定的扣中说明把其他的已使用的扣拿过来了
            if(!pageUsedAntiFakeCodeIsExistedUsedListFlag){
                throw new WmsOperationException("存在无效的防伪扣码");
            }
        }
        shipAntiFakeCommandRepository.deleteByDeliveryOrderNo(deliveryHeader.getDeliveryOrderCode());
        //全量更新，先删除，再批量插入
        List<ShipAntiFakeDo> shipAntiFakeDoList = buildShipAntiFakeDoList(deliveryHeader,antiFakeCodes);
        shipAntiFakeCommandRepository.batchSave(shipAntiFakeDoList);
        int rows = antiFakeCodeCommandRepository.batchUpdateCodeStatus(codeSet,AntiFakeCodeStatusEnum.USED.getStatus());
        if (rows != codeSet.size()) {
            throw new WmsOperationException("防伪码操作失败");
        }
    }

    /**
     * 发货绑扣
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void antiFake(ShipAntiFakeParam param) {
        //查询发货明细
        ShipTaskDetailDo shipTaskDetailDo = queryAndCheckShipTask(param.getUniqueCode());
        /**
         * 扣码校验
         * 1、code有效可用
         * 2、最多只能绑定2个扣码
         */
        //
        Set<String> codeSet = new HashSet<>();
        codeSet.addAll(param.getCodes());
        if (codeSet.size() != param.getCodes().size()) {
            throw new WmsOperationException("防伪扣码重复");
        }
        List<AntiFakeCodeDo>  antiFakeCodeDoList = antiFakeCodeQueryRepository.queryByCodes(codeSet);
        if (CollectionUtils.isEmpty(antiFakeCodeDoList) || antiFakeCodeDoList.size() != codeSet.size()) {
            throw new WmsOperationException("存在非法的防伪扣码");
        }

        List<ShipAntiFakeDo> existedCodeList = shipAntiFakeQueryRepository.queryByUniqueCode(shipTaskDetailDo.getReferenceNo(),param.getUniqueCode());
        boolean allUsable;
        List<String> existedCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(existedCodeList)) {
            // 商品已绑扣，扣码未使用||当前商品使用
            List<String> tmpExistedCodes = existedCodeList.stream().map(ShipAntiFakeDo::getAntiFakeCode).collect(Collectors.toList());
            existedCodes.addAll(tmpExistedCodes);
            allUsable = antiFakeCodeDoList.stream().allMatch(item -> (AntiFakeCodeStatusEnum.UN_USE.getStatus().equals(item.getStatus()) || (
                    (AntiFakeCodeStatusEnum.USED.getStatus().equals(item.getStatus()) && existedCodes.contains(item.getCode())))));
        } else {
            //未绑扣，扣码都未使用
            allUsable = antiFakeCodeDoList.stream().allMatch(item -> AntiFakeCodeStatusEnum.UN_USE.getStatus().equals(item.getStatus()));
        }
        if (!allUsable) {
            throw new WmsOperationException("存在无效的防伪扣码");
        }
        //全量更新，先删除，再批量插入
        List<ShipAntiFakeDo> shipAntiFakeDoList = buildShipAntiFakeDoList(shipTaskDetailDo, param.getCodes());
        shipAntiFakeCommandRepository.deleteByDeliveryDetailNo(shipTaskDetailDo.getReferenceNo(),shipTaskDetailDo.getReferenceDetailNo(), param.getUniqueCode());
        int rows = shipAntiFakeCommandRepository.batchInsert(shipAntiFakeDoList);
        if (rows == 0 || rows != shipAntiFakeDoList.size()) {
            throw new WmsOperationException("防伪扣绑定失败");
        }
        //更新code码的状态,已经绑过的扣码状态无需更新
        codeSet.removeAll(existedCodes);
        if (CollectionUtils.isEmpty(codeSet)) {
            log.info("code码已经绑定该商品，codes:{}", JSONUtil.toJsonStr(codeSet));
            return;
        }
        rows = antiFakeCodeCommandRepository.batchUpdateCodeStatus(codeSet,AntiFakeCodeStatusEnum.USED.getStatus());
        if (rows != codeSet.size()) {
            throw new WmsOperationException("防伪码操作失败");
        }
        //日志中心
        OperationLogParam operationLogParam = OperationLogParam.builder()
                .orderNo(Lists.newArrayList(shipTaskDetailDo.getReferenceDetailNo()))
                .workLogTypeEnum(WorkLogTypeEnum.OUTBOUND_DETAIL_NO)
                .qty(NumberUtils.INTEGER_ONE)
                .build();
        wmsOutboundWorkLogService.operateLog(operationLogParam);


    }

    /**
     * 构建防伪码集合
     * @param deliveryHeader 出库单信息
     * @param photos 拍的照片url集合
     * @return
     */
    private List<ShipAntiFakeDo> buildShipAntiFakeDoList(DeliveryHeaderDo deliveryHeader, List<String> photos) {
        List<ShipAntiFakeDo> shipAntiFakeDoList = new ArrayList<>();
        for (String antiFakeCode : photos) {
            ShipAntiFakeDo item = new ShipAntiFakeDo();
            item.setAntiFakeCode(antiFakeCode);
            item.setLinkUrl(StringUtils.EMPTY);
            item.setDeliveryOrderCode(deliveryHeader.getDeliveryOrderCode());
            item.setTenantCode(deliveryHeader.getTenantCode());
            item.setWarehouseCode(deliveryHeader.getWarehouseCode());
            item.setCreatedUserId(OperationUserContextHolder.get().getUserId());
            item.setCreatedUserName(OperationUserContextHolder.get().getUserName());
            item.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
            item.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
            shipAntiFakeDoList.add(item);
        }
        return shipAntiFakeDoList;
    }

    /**
     * 构建防伪码集合
     * @param shipTaskDetailDo
     * @param photos
     * @return
     */
    private List<ShipAntiFakeDo> buildShipAntiFakeDoList(ShipTaskDetailDo shipTaskDetailDo, List<String> photos) {
        List<ShipAntiFakeDo> shipAntiFakeDoList = new ArrayList<>();
        for (String antiFakeCode : photos) {
            ShipAntiFakeDo item = new ShipAntiFakeDo();
            item.setAntiFakeCode(antiFakeCode);
            item.setLinkUrl(StringUtils.EMPTY);
            item.setDeliveryOrderCode(shipTaskDetailDo.getReferenceNo());
            item.setDeliveryDetailNo(shipTaskDetailDo.getReferenceDetailNo());
            item.setUniqueCode(shipTaskDetailDo.getUniqueCode());
            item.setSkuId(shipTaskDetailDo.getSkuId());
            item.setOwnerCode(shipTaskDetailDo.getOwnerCode());
            item.setTenantCode(shipTaskDetailDo.getTenantCode());
            item.setWarehouseCode(shipTaskDetailDo.getWarehouseCode());
            item.setCreatedUserId(OperationUserContextHolder.get().getUserId());
            item.setCreatedUserName(OperationUserContextHolder.get().getUserName());
            item.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
            item.setUpdatedUserName(OperationUserContextHolder.get().getUserName());

            shipAntiFakeDoList.add(item);
        }
        return shipAntiFakeDoList;
    }

    /**
     * 创建快递包裹
     * @param deliveryOrderCode
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void createExpressPack(String deliveryOrderCode) {

        /**
         * 1、创建包裹
         * 2、装箱、封箱
         * 3、整单完成
         */
        List<ShipTaskDetailDo> shipTaskDetailDoList = shipTaskDetailQueryRepository.queryByReferenceNo(deliveryOrderCode,
                OperationUserContextHolder.get().getWarehouseCode(),OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isEmpty(shipTaskDetailDoList)) {
            throw new WmsOperationException("未找到订单的发货任务");
        }
        //创建包裹，暂时只支持一个包裹
        WmsPackDo pack = wmsPackService.createPack(deliveryOrderCode);
        //装箱(多个商品装一个箱子)，查询发货商品的唯一码或barcode
        for (ShipTaskDetailDo shipTaskDetailDo : shipTaskDetailDoList) {
            AutoPackGoodsParam packGoodsParam = new AutoPackGoodsParam();
            packGoodsParam.setPackNo(pack.getPackNo());
            String code = StringUtils.isNotBlank(shipTaskDetailDo.getUniqueCode()) ?
                    shipTaskDetailDo.getUniqueCode() : shipTaskDetailDo.getSkuId();
            Integer codeType = StringUtils.isNotBlank(shipTaskDetailDo.getUniqueCode()) ? NumberUtils.INTEGER_ONE
                    : NumberUtils.INTEGER_TWO;
            packGoodsParam.setCodeType(codeType);
            packGoodsParam.setCode(code);
            packGoodsParam.setNum(shipTaskDetailDo.getQty());
            wmsPackService.packGoodsWithoutScan(packGoodsParam);
        }
        //封箱
        SealBoxParam sealBoxParam = new SealBoxParam();
        sealBoxParam.setPackNo(pack.getPackNo());
        wmsPackService.sealBox(sealBoxParam);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void createExpressPackIndependentTransaction(String deliveryOrderCode) {
        List<WmsPackDo> wmsPackDos = wmsPackRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if (CollectionUtils.isNotEmpty(wmsPackDos)) {
            boolean existSealed = wmsPackDos.stream().anyMatch(item -> item.getPackStatus().equals(WmsPackStatusEnum.SEALED.getStatus()));
            if (existSealed) {
                log.warn("createExpressPack existSealed, deliveryOrderCode:{}", deliveryOrderCode);
                return;
            }
        }

        /**
         * 1、创建包裹
         * 2、装箱、封箱
         * 3、整单完成
         */
        List<ShipTaskDetailDo> shipTaskDetailDoList = shipTaskDetailQueryRepository.queryByReferenceNo(deliveryOrderCode,
                OperationUserContextHolder.get().getWarehouseCode(),OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isEmpty(shipTaskDetailDoList)) {
            throw new WmsOperationException("未找到订单的发货任务");
        }
        //创建包裹，暂时只支持一个包裹
        WmsPackDo pack = wmsPackService.createPackNotUptDeliveryHeader(deliveryOrderCode);
        //装箱(多个商品装一个箱子)，查询发货商品的唯一码或barcode
        for (ShipTaskDetailDo shipTaskDetailDo : shipTaskDetailDoList) {
            AutoPackGoodsParam packGoodsParam = new AutoPackGoodsParam();
            packGoodsParam.setPackNo(pack.getPackNo());
            String code = StringUtils.isNotBlank(shipTaskDetailDo.getUniqueCode()) ?
                    shipTaskDetailDo.getUniqueCode() : shipTaskDetailDo.getSkuId();
            Integer codeType = StringUtils.isNotBlank(shipTaskDetailDo.getUniqueCode()) ? NumberUtils.INTEGER_ONE
                    : NumberUtils.INTEGER_TWO;
            packGoodsParam.setCodeType(codeType);
            packGoodsParam.setCode(code);
            packGoodsParam.setNum(shipTaskDetailDo.getQty());
            wmsPackService.packGoodsWithoutScan(packGoodsParam);
        }
        //封箱
        SealBoxParam sealBoxParam = new SealBoxParam();
        sealBoxParam.setPackNo(pack.getPackNo());
        wmsPackService.sealBox(sealBoxParam);
    }

    /**
     * 打印面单
     *
     * @param deliveryOrderCode
     * @return
     */
    public LogisticsInfo printExpress(String deliveryOrderCode) {
        PrintExpressParam printExpressParam = new PrintExpressParam();
        printExpressParam.setDeliveryOrderCode(deliveryOrderCode);
        printExpressParam.setSubExpressCode(StringUtils.EMPTY);
        printExpressParam.setAutoMakeOrder(true);
        LogisticsInfo logisticsInfo = expressBillPrintService.printExpress(printExpressParam);
        if (logisticsInfo == null) {
            throw new WmsOperationException("未找到快递面单信息，请先下单后再打印");
        }
        //面单打印完成后，更新包裹状态为已发货
        WmsPackDo wmsPackDo = wmsPackRepository.findByDeliveryOrderCode(deliveryOrderCode);
        if (wmsPackDo != null && WmsPackStatusEnum.PRINTED.getStatus().equals(wmsPackDo.getPackStatus())) {
            wmsPackShipService.confirmShippingPack(wmsPackDo);
        }
        //日志中心
        OperationLogParam operationLogParam = OperationLogParam.builder()
                .orderNo(Lists.newArrayList(deliveryOrderCode))
                .workLogTypeEnum(WorkLogTypeEnum.OUTBOUND_NO)
                .build();
        wmsOutboundWorkLogService.operateLog(operationLogParam);
        return logisticsInfo;
    }
}
