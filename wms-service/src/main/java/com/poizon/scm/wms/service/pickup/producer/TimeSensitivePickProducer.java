package com.poizon.scm.wms.service.pickup.producer;

import com.alibaba.fastjson.JSON;
import com.dewu.executor.annotation.Delay;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.service.delivery.query.IDeliveryTagRelationQueryService;
import com.poizon.scm.wms.service.pickup.PickupService;
import com.poizon.scm.wms.util.Preconditions;
import com.poizon.scm.wms.util.enums.DeliveryTagV2Enum;
import com.poizon.scm.wms.util.framework.RequestExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.poizon.scm.wms.api.constants.WmsMessageQueueConstant.*;

/**
 * 功能描述：时效标识出库单拣货完成发送效期给履约系统
 *
 * @Author: cuixiao
 * @Date: 2021/3/23 4:53 下午
 */
@Component
@Slf4j
public class TimeSensitivePickProducer {

    @Autowired
    IDeliveryTagRelationQueryService deliveryTagRelationQueryService;
    @Autowired
    private SendMessageHandler sendMessageHandler;

    @Autowired
    private PickupService pickupService;
    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @EventualConsistency(label = "sendMessageToOFC",referenceNo = "#deliveryOrderCode",
            delay = @Delay(delay = 30))
    public void sendMessageToOFC(String deliveryOrderCode){
        Preconditions.checkStringNotBlank(deliveryOrderCode, RequestExceptionCode.REFERENCE_NO_NULL);
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if ( (deliveryHeaderDo.getOrderTags() & DeliveryTagV2Enum.getBitValByCodes(Arrays.asList(DeliveryTagV2Enum.AGAIN.getStatus()))) == 0L) {
            return;
        }

        // 发送消息给OFC
        Date endPickupTime = pickupService.getEndPickupTime(deliveryOrderCode);
        if(Objects.isNull(endPickupTime)){
            throw new WmsException("获取拣货完成时间失败！");
        }
        SendMessage<PickedToLeadTimeMessageBody> sendMessage = buildMessage(deliveryOrderCode, endPickupTime);
        String messageId = sendMessageHandler.process(sendMessage);
        if (StringUtils.isBlank(messageId)) {
            log.error("execute fail send message={}", JSON.toJSONString(sendMessage));
            throw new WmsException("时效订单拣货完成发送消息给时效系统失败！");
        }
    }

    private SendMessage<PickedToLeadTimeMessageBody> buildMessage(String deliveryOrderCode, Date endPickupTime) {
        SendMessage<PickedToLeadTimeMessageBody> sendMessage = new SendMessage<>();
        sendMessage.setTopic(WMS_TOPIC);
        sendMessage.setTag(WMS_TO_LEADTIME_TAG);
        sendMessage.setMessageKey(deliveryOrderCode);
        PickedToLeadTimeMessageBody messageBody = new PickedToLeadTimeMessageBody(deliveryOrderCode, endPickupTime);
        sendMessage.setMessageContent(messageBody);
        return sendMessage;
    }

    @EventualConsistency(referenceNo = "#promiseTypeNotifyPojo.deliveryOrderCode",delay = @Delay(delay = 30))
    public void sendPromiseTypeNotifyMessage(PromiseTypeNotifyPojo promiseTypeNotifyPojo){
        if ((promiseTypeNotifyPojo.getOrderTags() & DeliveryTagV2Enum.getBitValByCodes(Arrays.asList(DeliveryTagV2Enum.AGAIN.getStatus(),DeliveryTagV2Enum.TODAY.getStatus()))) == 0) {
            return;
        }
        SendMessage<PromiseTypeNotifyPojo> sendMessage = new SendMessage<>();
        sendMessage.setTopic(TOPIC_CALLBACK_LEADTIME);
        sendMessage.setTag(WMS_TO_LEADTIME_TAG);
        sendMessage.setMessageKey(promiseTypeNotifyPojo.getDeliveryOrderCode());
        sendMessage.setMessageContent(promiseTypeNotifyPojo);

        String messageId = sendMessageHandler.process(sendMessage);
        if (StringUtils.isBlank(messageId)) {
            log.error("execute fail send message={}", JSON.toJSONString(sendMessage));
            throw new WmsException("时效订单发货完成发送消息给时效系统失败！");
        }
    }

    /**
     * 构建时效消息
     *
     * @param deliveryHeader
     * @param deliveryDetails
     * @return {@link PromiseTypeNotifyPojo}
     */
    public PromiseTypeNotifyPojo buildPromiseTypeNotifyPojo(DeliveryHeaderDo deliveryHeader, List<DeliveryDetailDo> deliveryDetails){
        PromiseTypeNotifyPojo promiseTypeNotifyPojo = new PromiseTypeNotifyPojo();
        promiseTypeNotifyPojo.setDeliveryOrderCode(deliveryHeader.getDeliveryOrderCode());
        promiseTypeNotifyPojo.setOrderTags(deliveryHeader.getOrderTags());
        List<PromiseTypeNotifyPojo.CallBackItem > callBackItemList = new ArrayList<>();
        promiseTypeNotifyPojo.setCallBackItemList(callBackItemList);
        PromiseTypeNotifyPojo.CallBackItem callBackItem = null;
        for (DeliveryDetailDo deliveryDetail:deliveryDetails){
            callBackItem = new PromiseTypeNotifyPojo.CallBackItem();
            callBackItem.setOrderNo(deliveryDetail.getTradeOrderNo());
            Date outTime = new Date();
            callBackItem.setDeliveryTime((int) (outTime.getTime() / 1000));
            callBackItemList.add(callBackItem);
        }
        promiseTypeNotifyPojo.setCallBackItemList(callBackItemList);
        return promiseTypeNotifyPojo;
    }
}
