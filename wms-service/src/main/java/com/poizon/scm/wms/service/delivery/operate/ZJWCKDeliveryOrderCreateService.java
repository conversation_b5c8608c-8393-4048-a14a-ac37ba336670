package com.poizon.scm.wms.service.delivery.operate;

import cn.hutool.core.util.BooleanUtil;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.LocationConstant;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBill;
import com.poizon.scm.wms.outbound.api.enums.ShipTypeEnum;
import com.poizon.scm.wms.service.delivery.executor.DeliveryOrderAutoShipExecutor;
import com.poizon.scm.wms.service.delivery.operate.impl.RefundCenterDeliveryService;
import com.poizon.scm.wms.service.shipping.param.AutoPackAndShipParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 转95出库单创建服务
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Slf4j
@Component("ZJWCKDeliveryOrderCreateService")
public class ZJWCKDeliveryOrderCreateService extends AbstractDeliveryOrderCreateService {

    @Resource
    private RefundCenterDeliveryService refundCenterDeliveryService;

    @Resource
    private DeliveryOrderAutoShipExecutor deliveryOrderAutoShipExecutor;

    @Resource
    private InventoryRepository inventoryRepository;

    @Override
    protected boolean interruptProcess(WmsDeliveryBill deliveryBill) {
        return false;
    }

    @Override
    protected boolean needAllocateInventory(WmsDeliveryBill deliveryBill) {
        return false;
    }

    @Override
    protected boolean isVirtualDelivery(WmsDeliveryBill deliveryBill) {
        return false;
    }

    @Override
    protected void executeInventoryAllocate(WmsDeliveryBill deliveryBill) {
        log.info("转95出库单无需分配库存{}",deliveryBill.getDeliveryOrderCode());
    }

    @Override
    protected void executeShip(WmsDeliveryBill deliveryBill, boolean isVirtualDelivery) {
        refundCenterDeliveryService.createShipTask(deliveryBill);
        //判断是否自动发货
        if (BooleanUtil.isTrue(deliveryBill.getAutoShipFlag())) {
            //判断库存是否还在货架上
            String uniqueCode = deliveryBill.getWmsDeliveryBillDetails().get(0).getUniqueCode();
            InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(uniqueCode);
            if (inventoryDo == null) {
                throw new WmsException("转95分出库单库存不存在, 出库单号:{}, 唯一码:{}", deliveryBill.getDeliveryOrderCode(), uniqueCode);
            }
            if (LocationConstant.isVirtualLocation(inventoryDo.getLocationCode())) {
                //虚出
                deliveryOrderAutoShipExecutor.sendZ95AutoShipMsg(buildAutoPackAndShipParam(deliveryBill));
            }
        }
    }

    @Override
    protected void specialHandle(WmsDeliveryBill deliveryBill) {
        log.info("转95出库单无特殊逻辑{}",deliveryBill.getDeliveryOrderCode());
    }

    private AutoPackAndShipParam buildAutoPackAndShipParam(WmsDeliveryBill deliveryBill) {
        AutoPackAndShipParam autoPackAndShipParam = new AutoPackAndShipParam();
        autoPackAndShipParam.setUniqueCode(deliveryBill.getWmsDeliveryBillDetails().get(0).getUniqueCode());
        autoPackAndShipParam.setShipType(ShipTypeEnum.DFS295.getCode());
        autoPackAndShipParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        autoPackAndShipParam.setWarehouseCode(deliveryBill.getWarehouseCode());
        autoPackAndShipParam.setUserId(OperationUserContext.DEFAULT_USER_ID);
        autoPackAndShipParam.setUserName(OperationUserContext.DEFAULT_USER_NAME);
        autoPackAndShipParam.setRealName(OperationUserContext.DEFAULT_USER_NAME);
        return autoPackAndShipParam;
    }
}