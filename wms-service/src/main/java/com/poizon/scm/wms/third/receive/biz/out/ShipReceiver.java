package com.poizon.scm.wms.third.receive.biz.out;

import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.pojo.third.receive.request.TaskReceiveRequest;
import com.poizon.scm.wms.pojo.third.receive.response.TaskReceiveResponse;
import com.poizon.scm.wms.service.shipping.ShippingService;
import com.poizon.scm.wms.third.receive.ITaskReceiver;
import com.poizon.scm.wms.third.receive.biz.out.ship.ShipProcessorFactory;
import com.poizon.scm.wms.third.receive.biz.out.ship.base.AbstractShipProcessor;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 收到发货任务完成的消息 FBI WARING 95分也在用！！！！！
 * @Date 2020/8/3 1:54 下午
 */
@Component
@Slf4j
public class ShipReceiver implements ITaskReceiver {

    @Autowired
    private ShippingService shippingService;
    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Autowired
    private ShipProcessorFactory shipProcessorFactory;

    @Override
    public TaskTypeEnum getTaskTypeEnum() {
        return TaskTypeEnum.SHIP;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public TaskReceiveResponse process(TaskReceiveRequest receiveRequest) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(receiveRequest.getOrderCode());
        //小仓库的bizType = 0
        deliveryHeaderDo = shippingService.replaceSmallWarehouseXHDeliveryHeader(deliveryHeaderDo,receiveRequest.getOrderCode());
        if (Objects.isNull(deliveryHeaderDo)) {
            if (WmsOutboundBusinessUtils.personalDepositReturn(receiveRequest.getOrderType(), receiveRequest.getBizType())) {
                log.warn("个人寄存退出库单不存在需要重试{}", receiveRequest.getOrderCode());
                throw new WmsOperationException("个人寄存退发货如果出库单不存在需要重试");
            }
            log.warn("出库单{}在wms不存在，不消费", receiveRequest.getOrderCode());
            return new TaskReceiveResponse();
        }
        //校验是否已出库
        if (deliveryHeaderDo.getStatus().equals(WmsOutBoundStatusEnum.OUTED.getStatus())) {
            log.warn("出库单[{}]已出库", deliveryHeaderDo.getDeliveryOrderCode());
            return new TaskReceiveResponse();
        }
        AbstractShipProcessor abstractShipProcessor = shipProcessorFactory.createProcessor(deliveryHeaderDo);

        if (Objects.isNull(abstractShipProcessor)) {
            log.error("出库单{}shipReceiver找不到对应的执行", deliveryHeaderDo.getDeliveryOrderCode());
            throw new WmsException("shipReceiver找不到对应的执行");
        }
        abstractShipProcessor.handle(receiveRequest, deliveryHeaderDo);
        return new TaskReceiveResponse();
    }

    @Override
    public boolean whetherSetUserContext() {
        return true;
    }
}
