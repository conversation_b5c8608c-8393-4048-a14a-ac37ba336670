package com.poizon.scm.wms.service.delivery.operate.async;

import com.poizon.scm.wms.adapter.container.model.ContainerDetailDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBill;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class JcAllotDeliveryParam {
    private WmsDeliveryBill deliveryBill;
    private List<ContainerDetailDo> containerDetailDoList;
    private List<InventoryDo> inventoryDos;
    private String targetWarehouseCode;
    /**
     * 交接时间
     */
    private Date handoverTime;
    /**
     * 交接人
     */
    private String handoverUserId;
    private String handoverUserName;

    @Tolerate
    public JcAllotDeliveryParam() {
    }
}
