package com.poizon.scm.wms.service.delivery.operate.impl;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryAheadCancelDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliverySNBindRecordDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryAheadCancelRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliverySNBindRecordRepository;
import com.poizon.scm.wms.adapter.outbound.remark.model.MergeShipmentReportDo;
import com.poizon.scm.wms.adapter.outbound.report.repository.MergeShipmentReportRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.api.command.outbound.request.WmsDbDeliveryCancelRequest;
import com.poizon.scm.wms.api.enums.WmsOutCommandStatusEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.outbound.ship.ShipTaskCommandService;
import com.poizon.scm.wms.infra.common.ServiceRegionMark;
import com.poizon.scm.wms.service.config.SNConfig;
import com.poizon.scm.wms.service.delivery.event.message.DeliveryCancelEventParam;
import com.poizon.scm.wms.service.delivery.event.producer.DeliveryCancelEventProducer;
import com.poizon.scm.wms.service.delivery.executor.DeliveryCancelConsistencyExecutor;
import com.poizon.scm.wms.service.delivery.executor.DeliveryNotExistCancelExecutor;
import com.poizon.scm.wms.service.delivery.executor.OverseaDeliveryNotExistCancelExecutor;
import com.poizon.scm.wms.service.delivery.operate.IDeliveryOrderCancelService;
import com.poizon.scm.wms.service.delivery.operate.handler.DeliveryOrderCancelHandler;
import com.poizon.scm.wms.util.common.ReturnSalesCenterProperties;
import com.poizon.scm.wms.util.common.WarehouseSmallProperties;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import com.poizon.scm.wms.util.enums.WmsDeliveryDetailCancelEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 出库单取消逻辑新
 * @Date 2021/5/10 11:16 上午
 */
@Service
@Slf4j
public class DeliveryOrderCancelServiceImpl implements IDeliveryOrderCancelService {
    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    private DeliveryNotExistCancelExecutor deliveryNotExistCancelExecutor;

    @Autowired
    private OverseaDeliveryNotExistCancelExecutor overseaDeliveryNotExistCancelExecutor;
    @Autowired
    private DeliveryOrderCancelHandler deliveryOrderCancelHandler;
    @Autowired
    private DeliveryAheadCancelRepository deliveryAheadCancelRepository;
    @Autowired
    private DeliveryCancelConsistencyExecutor consistencyExecutor;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Resource
    private DeliveryCancelEventProducer deliveryCancelEventProducer;

    @Resource
    private MergeShipmentReportRepository mergeShipmentReportRepository;

    @Resource
    private DeliverySNBindRecordRepository deliverySNBindRecordRepository;

    @Resource
    private ShipTaskCommandService shipTaskCommandService;

    @Resource
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;
    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;
    @Resource
    private ShipTaskCommandRepository shipTaskCommandRepository;
    @Resource
    private ServiceRegionMark serviceRegionMark;

    @Value("${switch.delivery.cancel.new.message:false}")
    private boolean deliveryCancelUseNewMessage;

    @Autowired
    private WarehouseSmallProperties warehouseSmallProperties;

    @Autowired
    private ReturnSalesCenterProperties returnSalesCenterProperties;

    private final static Integer BATCH_SIZE = 1000;

    /**
     * 新的出库单取消逻辑，包含明细
     *
     * @param deliveryOrderCode
     * @param deliveryDetailNo
     * @return
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deliveryOrderCancelV3(String deliveryOrderCode, String deliveryDetailNo, boolean exceptionFlag) {
        boolean lockFlag = false;
        String lockCode = deliveryOrderCode;
        try {
            // 这把锁创建出库单那边也有，防止发货与取消并发
            lockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.DELIVERY_CANCEL, lockCode);

            if (!lockFlag) {
                if (serviceRegionMark.isOverseaEnv() && BooleanUtils.isTrue(deliveryCancelUseNewMessage)) {
                    // 海外的走新的消息，因为没有group
                    overseaDeliveryNotExistCancelExecutor.overseaDeliveryNotExistCancel(deliveryOrderCode,deliveryDetailNo,exceptionFlag);
                    return true;
                }
                // 拿不到锁,用一致性框架进行重试
                deliveryNotExistCancelExecutor.deliveryNotExistCancel(deliveryOrderCode, deliveryDetailNo, exceptionFlag);
                return true;
            }

            DeliveryHeaderDo deliveryHeaderDo = deliveryOrderCancelHandler.queryDeliveryHeader(deliveryOrderCode);
            // 出库单不存在直接发消息，取消消息先来，异步处理
            if (dealDeliveryNotExist(deliveryHeaderDo, deliveryOrderCode, deliveryDetailNo, exceptionFlag)) {
                return true;
            }
            // 欧洲直发取消逻辑
            if(Objects.equals(WmsBizTypeEnum.SELECT_SHOP_DIRECT_DELIVERY.getBizType(),deliveryHeaderDo.getBizType())){
                return this.europeShipCancelDelivery(deliveryHeaderDo,exceptionFlag);
            }

            //云仓校验是否能取消
            if (!cancelCheckForDBOS(deliveryHeaderDo)) {
                return false;
            }

            deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
            //正常取消流程
            return doDeliveryOrderCancel(deliveryHeaderDo, deliveryDetailNo, deliveryOrderCode, exceptionFlag);

        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, lockCode);
            }
        }
    }

    private boolean cancelCheckForDBOS(DeliveryHeaderDo deliveryHeaderDo) {


        if (!WmsOutboundBusinessUtils.isCloudWarehouse(deliveryHeaderDo.getBizType())) {
            return true;
        }

        /*if (deliveryHeaderDo.getStatus() > WmsOutBoundStatusEnum.PICKED.getStatus()) {
            log.info("云仓拣货完成后不允许取消 {}", deliveryHeaderDo.getStatus());
            return false;
        }*/
        if (WmsOutBoundTypeEnum.THCK.getType().equals(deliveryHeaderDo.getType()) && StringUtils.isNotBlank(deliveryHeaderDo.getLaunchNo())) {
            log.info("云仓退货出库组波后不允许取消");
            return false;
        }
        return true;
    }


    /**
     * mfs订单取消
     *
     * @param deliveryOrderCode
     * @param deliveryDetailNo
     * @return
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deliveryOrderCancelForMfs(String deliveryOrderCode, String deliveryDetailNo) {
        boolean lockFlag = false;
        String lockCode = deliveryOrderCode;
        try {
            // 这把锁创建出库单那边也有，防止发货与取消并发
            lockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.DELIVERY_CANCEL, lockCode);

            if (!lockFlag) {
                // 拿不到锁,用一致性框架进行重试
                throw new WmsOperationException("业务正在处理该出库单，请稍后重试取消");
            }
            DeliveryHeaderDo deliveryHeaderDo = deliveryOrderCancelHandler.queryDeliveryHeader(deliveryOrderCode);
            // 出库单不存在直接报错
            if (null == deliveryHeaderDo) {
                throw new WmsOperationException("出库单在wms系统中不存在，请稍后重试取消");
            }
            deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();

            // 波次后不给取消
            cancelCheckForMfs(deliveryHeaderDo);

            //正常取消流程
            return doDeliveryOrderCancel(deliveryHeaderDo, deliveryDetailNo, deliveryOrderCode, false);

        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, lockCode);
            }
        }
    }

    /**
     * 为mfs的取消
     *
     * @param deliveryHeaderDo
     */
    private void cancelCheckForMfs(DeliveryHeaderDo deliveryHeaderDo) {

        if (WmsOutBoundStatusEnum.CANCEL.getStatus().compareTo(deliveryHeaderDo.getStatus()) == 0) {
            throw new WmsOperationException("出库单已取消");
        }

        if (StringUtils.isNotBlank(deliveryHeaderDo.getLaunchNo()) ||
                (deliveryHeaderDo.getStatus().compareTo(WmsOutBoundStatusEnum.PICKING.getStatus()) >= 0)) {
            throw new WmsOperationException("拣货后或组波后不支持取消");
        }

        if (WmsOutBoundStatusEnum.OUTING.getStatus().compareTo(deliveryHeaderDo.getStatus()) == 0) {
            throw new WmsOperationException("出库单正在发货中不允许取消");
        }
    }

    private Boolean doDeliveryOrderCancel(DeliveryHeaderDo deliveryHeaderDo,
                                          String deliveryDetailNo,
                                          String deliveryOrderCode,
                                          boolean exceptionFlag) {
        // 出库完成状态的不可取消，check一下
        if (!checkDeliveryCanCancel(deliveryHeaderDo)) {
            return false;
        }

        List<DeliveryDetailDo> needCancelDeliveryDetailList =
                deliveryOrderCancelHandler.getNeedCancelDeliveryDetailList(deliveryOrderCode, deliveryDetailNo);

        // 幂等一下取消
        if (idempotentDeliveryCancel(deliveryHeaderDo, deliveryDetailNo, needCancelDeliveryDetailList)) {
            return true;
        }

        // 插入出库取消表
        buildAndBatchSaveDeliveryAheadCancel(deliveryOrderCode, needCancelDeliveryDetailList,exceptionFlag);
        // 正式做取消，因为在明细上加了状态，所以，如果有必要头跟明细都会取消 (同步)
        cancelDelivery(deliveryHeaderDo, needCancelDeliveryDetailList);
        // 异步去取消单之后的事情，比如拣货任务，库存占用，发货任务，还要去执行返架
        consistencyExecutor.asyncDeliveryCancel(deliveryOrderCode, deliveryDetailNo);
        // 发布取消事件消息
        DeliveryCancelEventParam param = DeliveryCancelEventParam.builder()
                .deliveryOrderCode(deliveryOrderCode)
                .deliveryDetailNo(deliveryDetailNo)
                .build();
        deliveryCancelEventProducer.publishShipEvent(param);
        return true;
    }

    /**
     * 异步取消出库单，主要是防止跟出库单创建并发
     *
     * @param deliveryOrderCode
     * @param detailNo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deliveryOrderCancelForAsync(String deliveryOrderCode, String detailNo, boolean exceptionFlag) {
        boolean lockFlag = false;
        String lockCode = deliveryOrderCode;
        try {
            lockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.DELIVERY_CANCEL, lockCode);

            if (!lockFlag) {
                // 拿不到锁也需要重试
                throw new WmsOperationException("拿不到出库单取消的锁，需要重试");
            }

            DeliveryHeaderDo deliveryHeaderDo = deliveryOrderCancelHandler.queryDeliveryHeader(deliveryOrderCode);
            if (null == deliveryHeaderDo) {
                log.warn("出库单不存在{}!", deliveryOrderCode);
                throw new WmsOperationException(WmsExceptionCode.CANCEL_DELIVERY_NOT_EXIST_ERROR);
            }
            if (deliveryHeaderDo.getCommandStatus().equals(WmsOutCommandStatusEnum.CANCEL.getStatus())) {
                log.warn("出库单[{}]已经取消", deliveryOrderCode);
                return false;
            }
            // 欧洲直发取消逻辑
            if(Objects.equals(WmsBizTypeEnum.SELECT_SHOP_DIRECT_DELIVERY.getBizType(),deliveryHeaderDo.getBizType())){
                return this.europeShipCancelDelivery(deliveryHeaderDo,exceptionFlag);
            }
            deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
            OperationUserContextHolder.buildDefaultUserContextWithTenantCode(
                deliveryHeaderDo.getWarehouseCode(), deliveryHeaderDo.getTenantCode());

            return doDeliveryOrderCancel(deliveryHeaderDo, detailNo, deliveryOrderCode,exceptionFlag);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }catch (WmsOperationException e) {
            log.warn("消费出库单取消报错，需要重试！{},{}", deliveryOrderCode, detailNo);
            throw e;
        } catch (Exception e) {
            log.error("消费出库单取消报错，需要重试！{},{}", deliveryOrderCode, detailNo);
            throw e;
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, lockCode);
            }
            OperationUserContextHolder.clear();
        }
        return true;
    }

    /**
     * 欧洲直发取消出库单
     *
     * @param deliveryHeaderDo
     * @return {@link Boolean}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean europeShipCancelDelivery(DeliveryHeaderDo deliveryHeaderDo,boolean exceptionFlag){
        // 幂等一下取消
        if (idempotentDeliveryCancel(deliveryHeaderDo, null, null)) {
            return true;
        }
        List<DeliveryDetailDo> deliveryDetails = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode(),deliveryHeaderDo.getTenantCode());
        //取消单据由于复用代码 状态List<Integer> statusList 不能为空 所以传入了三个欧洲直发不会有的终态状态 COLLECTION_OUT，OUTING，FORCE_OUTED 欧洲直发的终态是 OUTED
        deliveryHeaderRepository.cancelDeliveryWithStatus(buildCancelParam(deliveryHeaderDo), Lists.newArrayList(WmsOutBoundStatusEnum.OUTING.getStatus(), WmsOutBoundStatusEnum.FORCE_OUTED.getStatus(), WmsOutBoundStatusEnum.COLLECTION_OUT.getStatus()));
        //取消明细
        deliveryDetailRepository.batchUpdateDeliveryDetailForCancel(deliveryDetails);
        // 插入出库取消表
        buildAndBatchSaveDeliveryAheadCancel(deliveryHeaderDo.getDeliveryOrderCode(), deliveryDetails,exceptionFlag);
        // 发布取消事件消息
        DeliveryCancelEventParam param = DeliveryCancelEventParam.builder()
                .deliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode())
                .build();
        for (DeliveryDetailDo deliveryDetail:deliveryDetails){
            param.setDeliveryDetailNo(deliveryDetail.getDetailNo());
            deliveryCancelEventProducer.publishShipEvent(param);
        }
        return true;
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelAllocateDelivery(String deliveryOrderCode) {
        boolean lockFlag = false;
        try {
            //使用波次创建锁
            lockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.LAUNCH_DELIVERY, deliveryOrderCode);
            if (!lockFlag) {
                throw new WmsException("调拨出库单取消获取锁失败");
            }
            DeliveryHeaderDo deliveryHeaderDo = deliveryOrderCancelHandler.queryDeliveryHeader(deliveryOrderCode);
            if (Objects.isNull(deliveryHeaderDo)){
                throw new WmsException(WmsExceptionCode.DELIVERY_NOT_EXIST);
            }
            // 幂等一下取消
            if (deliveryHeaderDo.getCommandStatus().equals(WmsOutCommandStatusEnum.CANCEL.getStatus())) {
                return true;
            }
            //取消调拨单前置校验：1、单据状态为可取消库存分配的状态。2、波次号为空。
            if (!checkAllocateDeliveryCanCancel(deliveryHeaderDo)){
                return false;
            }

            //调拨出库单取消流程
            doAllocateDeliveryCancel(deliveryHeaderDo);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.LAUNCH_DELIVERY, deliveryOrderCode);
            }
        }
        return true;
    }

    /**
     * 单件调拨取消
     *
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelDbDelivery(WmsDbDeliveryCancelRequest request) {

        String deliveryOrderCode = request.getDeliveryOrderCode();
        // 1.校验出库单是否存在以及装箱中
        DeliveryHeaderDo deliveryHeaderDo = deliveryOrderCancelHandler.queryDeliveryHeader(deliveryOrderCode);
        if (Objects.isNull(deliveryHeaderDo)) {
            throw new WmsOperationException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }
        try {
            // 幂等一下
            if (deliveryHeaderDo.getStatus().compareTo(WmsOutBoundStatusEnum.CANCEL.getStatus()) == 0) {
                log.info("出库单{}已取消", deliveryOrderCode);
                return true;
            }
            // 2.取消出库单以及明细
            deliveryOrderCancelHandler.cancelDbDelivery(request, deliveryHeaderDo);
            // 3.取消发货任务
            shipTaskCommandService.cancelShipTask(deliveryHeaderDo.getDeliveryOrderCode(), deliveryHeaderDo.getWarehouseCode(), deliveryHeaderDo.getTenantCode());
        } catch (WmsOperationException exception) {
            log.warn("调拨出库单取消失败，出库单:{}，error:{}", deliveryOrderCode, exception);
            return false;
        }
        return true;
    }

    private void doAllocateDeliveryCancel(DeliveryHeaderDo deliveryHeaderDo) {
        // 取消出库单头和明细
        cancelDeliveryHeaderAndDetails(deliveryHeaderDo);
        // 异步去取消单之后的事情，取消库存占用，更新单据status=90，写异常明细。
        consistencyExecutor.asyncDeliveryCancel(deliveryHeaderDo.getDeliveryOrderCode(), null);
    }

    /**
     * 取消明细，调拨出库单取消是整单取消，所以全部明细都需要取消。
     * @param deliveryHeaderDo
     */
    private void cancelDeliveryHeaderAndDetails(DeliveryHeaderDo deliveryHeaderDo) {
        String deliveryOrderCode = deliveryHeaderDo.getDeliveryOrderCode();
        // 根据出库单号查出库单明细
        List<DeliveryDetailDo> needCancelDeliveryDetailList =
                deliveryOrderCancelHandler.checkDeliveryDetailWithCancel(deliveryOrderCode);

        // 避免插入超大结果集
        List<List<DeliveryDetailDo>> partition = Lists.partition(needCancelDeliveryDetailList, BATCH_SIZE);
        for (List<DeliveryDetailDo> partitionRecords : partition) {
            // 插入出库取消表
            buildAndBatchSaveDeliveryAheadCancel(deliveryOrderCode, partitionRecords,false);
        }
        // 取消出库单明细 设置cancel_flag = 20
        int updateDetailResult = deliveryDetailRepository.updateDeliveryDetailForCancelByDeliveryOrderCode(deliveryOrderCode);
        if (updateDetailResult != needCancelDeliveryDetailList.size()) {
            log.error("出库单明细取消失败，出库单明细如下:{}", deliveryOrderCode);
            throw new WmsException("取消出库单明细失败");
        }
        // 取消出库单头 设置commandStatus = 90
        int rows = deliveryHeaderRepository.updateCancelStatus(deliveryHeaderDo);
        if (rows <= 0) {
            throw new WmsException("取消出库单失败");
        }
    }

    /**
     * 批量插入出库单取消
     *
     * @param deliveryOrderCode
     * @param detailDoList
     */
    private void buildAndBatchSaveDeliveryAheadCancel(String deliveryOrderCode,
                                                      List<DeliveryDetailDo> detailDoList,
                                                      boolean exceptionFlag) {
        List<DeliveryAheadCancelDo> deliveryAheadCancelDoList = Lists.newArrayList();
        DeliveryAheadCancelDo deliveryAheadCancelDo;
        for (DeliveryDetailDo deliveryDetailDo : detailDoList) {
            deliveryAheadCancelDo = new DeliveryAheadCancelDo();
            deliveryAheadCancelDo.setDeliveryOrderCode(deliveryOrderCode);
            deliveryAheadCancelDo.setDetailNo(deliveryDetailDo.getDetailNo());
            deliveryAheadCancelDo.setTenantId(OperationUserContextHolder.getTenantCode());
            deliveryAheadCancelDo.setExceptionFlag(exceptionFlag ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO);
            deliveryAheadCancelDoList.add(deliveryAheadCancelDo);
        }
        deliveryAheadCancelRepository.batchInsertDeliveryAheadCancel(deliveryAheadCancelDoList);
    }

    /**
     * 出库单取消
     *
     * @param deliveryHeaderDo
     * @param deliveryDetailDoList
     */
    private void cancelDelivery(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDoList) {
        // 排个序
        Collections.sort(deliveryDetailDoList, Comparator.comparing(DeliveryDetailDo::getDetailNo));
        //更新取消的明细状态
        int dRows = deliveryDetailRepository.batchUpdateDeliveryDetailForCancel(deliveryDetailDoList);
        if (dRows != deliveryDetailDoList.size()) {
            log.error("出库单明细取消失败，出库单明细如下:{}", deliveryDetailDoList);
            throw new WmsException("取消出库单明细失败");
        }

        // 删除SN信息
        //deleteDeliverySnIfNecessary(deliveryHeaderDo, deliveryDetailDoList);

        //查询整个出库单的明细状态
        List<DeliveryDetailDo> finalDeliveryDetailList = deliveryOrderCancelHandler.
                checkDeliveryDetailWithCancel(deliveryHeaderDo.getDeliveryOrderCode());
        // 改出库单头（如果明细均为取消，则将出库单头改为取消状态）
        Set<Byte> collectFlag = finalDeliveryDetailList.stream().map(DeliveryDetailDo::getCancelFlag).collect(Collectors.toSet());
        boolean isAllCancel = collectFlag.size() == 1 && collectFlag.contains(WmsDeliveryDetailCancelEnum.CANCEL.getStatus());
        if (isAllCancel) {
            int rows = deliveryHeaderRepository.cancelDeliveryWithStatus(buildCancelParam(deliveryHeaderDo), Lists.newArrayList(WmsOutBoundStatusEnum.OUTING.getStatus(),
                    WmsOutBoundStatusEnum.OUTED.getStatus(), WmsOutBoundStatusEnum.FORCE_OUTED.getStatus(), WmsOutBoundStatusEnum.COLLECTION_OUT.getStatus()));
            if (rows <= 0) {
                throw new WmsException("取消出库单失败");
            }
        }

        //非小仓库的取消发货任务(小仓库需要在发货时提示现场订单取消,不能在这边取消发货任务)
        if (!warehouseSmallProperties.isSmallWarehouse(deliveryHeaderDo.getWarehouseCode()) && !returnSalesCenterProperties.ifReturnSalesCenterSmallWarehouseBizType(deliveryHeaderDo.getWarehouseCode(), deliveryHeaderDo.getBizType())) {
            // 非小仓库的取消发货任务(小仓库需要在发货时提示现场订单取消,不能在这边取消发货任务)
            List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.queryByReferenceNo(deliveryHeaderDo.getDeliveryOrderCode(), deliveryHeaderDo.getWarehouseCode(), deliveryHeaderDo.getTenantCode());
            if (CollectionUtils.isNotEmpty(shipTaskDetailDos)) {
                shipTaskDetailCommandRepository.cancelByDeliveryDetailNos(deliveryDetailDoList.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList()));
                if (isAllCancel) {
                    shipTaskCommandRepository.cancelTaskByReferenceNo(deliveryHeaderDo.getDeliveryOrderCode());
                }
            }
        }

        if(Objects.equals(deliveryHeaderDo.getType(), WmsOutBoundTypeEnum.HBFHCK.getType())){
            for (DeliveryDetailDo deliveryDetail:finalDeliveryDetailList){
                MergeShipmentReportDo mergeShipmentReportDo = mergeShipmentReportRepository.selectMergeShipmentReportByShipmentNoAndDetailNo(deliveryDetail.getTenantCode(),deliveryDetail.getDeliveryOrderCode(),deliveryDetail.getDetailNo());
                if(Objects.isNull(mergeShipmentReportDo)){
                    continue;
                }
                mergeShipmentReportRepository.updateStatusByShipmentNoAndDetailNo(deliveryDetail.getTenantCode(),
                        deliveryDetail.getDeliveryOrderCode(),
                        deliveryDetail.getDetailNo(),
                        WmsOutBoundStatusEnum.CANCEL.getStatus());
            }
        }
    }

    private void deleteDeliverySnIfNecessary(DeliveryHeaderDo deliveryHeaderDo, List<DeliveryDetailDo> deliveryDetailDoList) {

        // 删除sn信息,之前outbound代码里面是物理删除，而且这个里面所有的查询都是没有deleted=1,我们这边也直接物理删除
        List<String> detailNoList = deliveryDetailDoList.stream().map(k -> k.getDetailNo()).collect(Collectors.toList());
        List<DeliverySNBindRecordDo> deliverySNBindRecordDoList
                = deliverySNBindRecordRepository.queryByDeliveryDetailNos(deliveryHeaderDo.getWarehouseCode(), detailNoList);
        if (CollectionUtils.isNotEmpty(deliverySNBindRecordDoList)) {
            List<Long> idList = deliverySNBindRecordDoList.stream().map(k -> k.getId()).collect(Collectors.toList());
            deliverySNBindRecordRepository.batchDelete(idList, deliveryHeaderDo.getTenantCode());
        }
    }

    private DeliveryHeaderDo buildCancelParam(DeliveryHeaderDo deliveryHeaderDo) {
        DeliveryHeaderDo update = new DeliveryHeaderDo();
        update.setDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
        update.setTenantCode(deliveryHeaderDo.getTenantCode());
        update.setCommandStatus(WmsOutBoundStatusEnum.CANCEL.getStatus());
        if (WmsBizTypeEnum.XH_BIZ_TYPE.contains(deliveryHeaderDo.getBizType())||
                Objects.equals(WmsBizTypeEnum.SELECT_SHOP_DIRECT_DELIVERY.getBizType(),deliveryHeaderDo.getBizType())
                || WmsBizTypeEnum.SPECIAL_ORDER_BIZ_TYPE.contains(deliveryHeaderDo.getBizType())) {
            update.setStatus(WmsOutBoundStatusEnum.CANCEL.getStatus());
        }
        update.setCancelTime(new Date());
        return update;
    }

    /**
     * 幂等一下取消
     *
     * @param deliveryHeaderDo
     * @param detailNo
     * @param deliveryDetailDoList
     * @return
     */
    private boolean idempotentDeliveryCancel(DeliveryHeaderDo deliveryHeaderDo,
                                             String detailNo,
                                             List<DeliveryDetailDo> deliveryDetailDoList) {
        if (deliveryHeaderDo.getCommandStatus().equals(WmsOutCommandStatusEnum.CANCEL.getStatus())) {
            return true;
        }
        if (StringUtils.isNotBlank(detailNo)) {
            Map<String, DeliveryDetailDo> detailDoMap = deliveryDetailDoList.stream().collect(Collectors.toMap(
                    DeliveryDetailDo::getDetailNo, item -> item, (newItem, oldItem) -> newItem));
            DeliveryDetailDo deliveryDetailDo = detailDoMap.get(detailNo);
            if (null == deliveryDetailDo) {
                throw new WmsOperationException("出库单取消明细，明细不存在，在幂等这！");
            }
            // 明细取消
            if (WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(deliveryDetailDo.getCancelFlag())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验出库单是否能取消
     *
     * @param deliveryHeaderDo
     * @return
     */
    private boolean checkDeliveryCanCancel(DeliveryHeaderDo deliveryHeaderDo) {
       if (WmsOutBoundStatusEnum.outComplete.contains(deliveryHeaderDo.getStatus())) {
            return false;
        }
        return true;
    }


    private boolean checkAllocateDeliveryCanCancel(DeliveryHeaderDo deliveryHeaderDo){
        if (WmsOutBoundStatusEnum.CAN_CANCEL_ALLOCATE_LIST.contains(deliveryHeaderDo.getStatus())
                && StringUtils.isEmpty(deliveryHeaderDo.getLaunchNo())){
            return true;
        }
        return false;
    }


    /**
     * 处理出库单还未下发到wms的情况
     *
     * @param deliveryHeaderDo
     * @param deliveryOrderCode
     * @param detailNo
     * @return
     */
    private Boolean dealDeliveryNotExist(DeliveryHeaderDo deliveryHeaderDo, String deliveryOrderCode, String detailNo, boolean exceptionFlag) {
        if (null == deliveryHeaderDo) {
            // 取消消息来的比出库单还要早,发个消息给admin处理去吧,肯定是能取消的掉的
            deliveryNotExistCancelExecutor.deliveryNotExistCancel(deliveryOrderCode, detailNo, exceptionFlag);
            return true;
        }
        return false;
    }

}
