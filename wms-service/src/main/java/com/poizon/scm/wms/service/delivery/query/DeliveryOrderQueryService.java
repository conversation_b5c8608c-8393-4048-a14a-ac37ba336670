package com.poizon.scm.wms.service.delivery.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryInfoQueryDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryInfoResultDo;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAbnormalDetailQueryRequest;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAbnormalQueryRequest;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAllocatedInventoryListRequest;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryQueryRequest;
import com.poizon.scm.wms.api.dto.response.delivery.*;
import com.poizon.scm.wms.dao.entitys.DeliveryDetailEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryHeaderEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryRelatedOrdersEntity;
import com.poizon.scm.wms.pojo.delivery.DeliveryAdminQueryPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipListPojo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 发货查询Service
 *
 * <AUTHOR>
 * @date 2020/5/14 6:25 下午
 * @description
 */
public interface DeliveryOrderQueryService {

    /**
     * 查询后台发货单等信息
     *
     * @param pojo
     * @return
     */
    PagingObject<DeliveryPageQueryResponse> queryDeliveryInfo(DeliveryAdminQueryPojo pojo);

    /**
     * 滚动查询后台发货单等信息
     *
     * @param pojo
     * @return
     */
    PagingObject<DeliveryPageQueryResponse> queryRollDeliveryInfo(DeliveryAdminQueryPojo pojo);

    /**
     * 滚动查询后台发货单等信息
     *
     * @param pojo
     * @return
     */
    List<DeliveryInfoResultDo> rollDeliveryInfo2(DeliveryInfoQueryDo pojo);



    /**
     * 查询后台发货单等信息（报表使用，转换为List<Object[]> ）
     *
     * @param pojo
     * @return
     */
    List<Object[]> queryDeliveryInfoObject(DeliveryAdminQueryPojo pojo);

    /**
     * 根据多个出库单号批量查询出库单
     * @param deliveryOrderCodeList
     * @return
     */
    List<DeliveryHeaderEntity> queryBatchDeliveryInfo(List<String> deliveryOrderCodeList);

    /**
     * 根据多个出库单号批量查询出库单明细
     * @param deliveryOrderCodeList
     * @return
     */
    Map<String,List<DeliveryDetailEntity>> queryBatchDeliveryDetailInfo(List<String> deliveryOrderCodeList);

    /**
     * 查询上架列表
     * @param pojo
     * @return
     */
    Page<DeliveryHeaderEntity> queryDeliveryOrderList(DeliveryShipListPojo pojo);

    Page<DeliveryHeaderEntity> queryPageDeliveryHeaderList(QueryWrapper queryWrapper,Page<DeliveryHeaderEntity> page);

    /**
     * 查询出库数量
     * @param queryWrapper
     * @return
     */
    Integer queryDeliveryHeaderCount(QueryWrapper queryWrapper);

    /**
     * 查询出库头列表
     * @param queryWrapper
     * @return
     */
    List<DeliveryHeaderEntity> queryDeliveryHeaderList(QueryWrapper queryWrapper);

    /**
     * 查询出库头详情
     * @param deliveryOrderCode
     * @return
     */
    DeliveryHeaderEntity queryDeliveryInfo(String deliveryOrderCode);

    /**
     * 根据发货单号查Entity
     *
     * @param deliveryOrderCode
     * @return
     */
    List<DeliveryDetailEntity> queryDeliveryDetailByDeliveryCode(String deliveryOrderCode);



    DeliveryDetailEntity queryDeliveryDetailByDetailNo(String deliveryDetailNo);

    List<DeliveryDetailEntity> queryBatchDeliveryDetailByDetailNos(Set<String> detailNos);

    /**
     * 根据发货单号查Entity
     *
     * @param deliveryOrderCode
     * @return
     */
    Page<DeliveryDetailEntity> queryDeliveryDetailPage(String deliveryOrderCode,Integer pageNum,Integer pageSize);

    List<DeliveryRelatedOrdersEntity> queryDeliveryRelatedOrderByOrderCode(String deliveryOrderCode);

    Map<String, DeliveryRelatedOrdersEntity> queryDeliveryRelatedMapByOrderCode(Set<String> deliveryOrderCodeList);

    /**
     * 查询非拣货完成，强制完成的出库单
     */
    DeliveryHeaderEntity queryUnPickedFinishDeliveryInfo(String deliveryOrderCode);

    /**
     * 查看出库单状态为初始状态,部分分配，全部分配，分配失败并且不是取消
     */
    PagingObject<DeliveryAllocatedInventoryListResponse> queryDeliveryAllocatedInventoryPage(DeliveryAllocatedInventoryListRequest request,Boolean isSearchCount);


    List<Object[]> queryDeliveryAllocatedInventory(DeliveryAllocatedInventoryListRequest request);
    /**
     * 收货日报明细导出
     * @param request
     * @return
     */
    List<Object[]> reportDetailStatisticalByDay(DeliveryQueryRequest request);

    /**
     * 收货日报明细
     * @param request
     * @return
     */
    PagingObject<DeliveryResponse> queryStatisticalDetail(DeliveryQueryRequest request);

    List<DeliveryResponse> queryListStatisticalDetail(DeliveryQueryRequest request);

    PagingObject<DeliveryAbnormalQueryResponse> queryAbnormalList(DeliveryAbnormalQueryRequest request,Boolean searchCount);
    PagingObject<DeliveryAbnormalQueryResponse> selectAbnormalList(DeliveryAbnormalQueryRequest request, Boolean searchCount);
    PagingObject<DeliveryAbnormalDetailResponse> queryAbnormalDetail(DeliveryAbnormalDetailQueryRequest request);

    List<Object[]> reportAbnormal(DeliveryAbnormalQueryRequest request);

    /**
     * 通过关联单号查询出库单号
     * @return
     */
    DeliveryRelatedOrdersEntity queryDeliverOrderByRelatedOrder(String deliverRelateOrderCode);

    List<DeliveryRelatedOrdersEntity> queryDeliverRelatedList(List<String> orderNoList);


    List<DeliveryDetailDo> queryDetailByDeliveryCodeAndOwnerCode(List<String> deliveryOrderCodeList, String ownerCode);
}
