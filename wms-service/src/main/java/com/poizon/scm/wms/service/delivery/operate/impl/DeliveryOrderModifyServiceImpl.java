package com.poizon.scm.wms.service.delivery.operate.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Maps;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.cis.api.inventory.CisSubtractRequest;
import com.poizon.scm.wms.adapter.handover.model.DeliveryHandoverHeaderDo;
import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.repository.InvInventoryAllocatedRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.*;
import com.poizon.scm.wms.adapter.outbound.delivery.model.ext.DeliveryHeaderDto;
import com.poizon.scm.wms.adapter.outbound.delivery.params.DeliveryExtraInfoUpdateParam;
import com.poizon.scm.wms.adapter.outbound.delivery.query.DeliveryPermissionUpdateParam;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.*;
import com.poizon.scm.wms.adapter.outbound.model.BackToOriginalDo;
import com.poizon.scm.wms.adapter.outbound.outbill.repository.db.command.WmsOutBillDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.outbill.repository.db.command.WmsOutBillHeaderCommandRepository;
import com.poizon.scm.wms.adapter.outbound.outhandover.WmsOutHandoverRepository;
import com.poizon.scm.wms.adapter.outbound.outhandover.model.WmsOutHandoverDetailResponseModel;
import com.poizon.scm.wms.adapter.outbound.report.repository.MergeShipmentReportRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackRepository;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.model.SelfOrderExpressDo;
import com.poizon.scm.wms.adapter.outbound.selforderexpress.repository.SelfOrderExpressRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipAntiFakeDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipAntiFakeQueryRepository;
import com.poizon.scm.wms.adapter.returned.model.ReturnApplyRecordDo;
import com.poizon.scm.wms.adapter.returned.repository.ReturnApplyRecordRepository;
import com.poizon.scm.wms.adapter.third.mfs.MfsShipRepository;
import com.poizon.scm.wms.api.constants.WmsMessageQueueConstant;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.api.enums.*;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.DeliveryHeaderInventoryAllocateModeEnum;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.dao.entitys.*;
import com.poizon.scm.wms.dao.mappers.DeliveryDetailMapper;
import com.poizon.scm.wms.dao.mappers.DeliveryHeaderMapper;
import com.poizon.scm.wms.domain.outbound.fp.processor.FPOrderOutProcessor;
import com.poizon.scm.wms.domain.outbound.outbound.entity.RepeatDeliveryInterceptorInfo;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.outbound.returngoods.entity.LogisticsInfo;
import com.poizon.scm.wms.domain.outbound.returngoods.service.ExpressBillPrintService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.logistic.CustomMakeOrderLogisticsService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.LogisticMakeParam;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.PrintExpressParam;
import com.poizon.scm.wms.domain.outbound.ship.ShippingCommandService;
import com.poizon.scm.wms.domain.ship.event.message.ShipEventParam;
import com.poizon.scm.wms.domain.ship.event.producer.DeliveryShipEventProducer;
import com.poizon.scm.wms.domain.ship.executor.KolShipExecutor;
import com.poizon.scm.wms.infra.outbound.returngoods.mapper.WmsLogisticsBillDoExtMapper;
import com.poizon.scm.wms.pojo.delivery.*;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedCancelPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedOrderPojo;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.service.apply.OubApplyService;
import com.poizon.scm.wms.service.backToOriginal.BackToOriginalService;
import com.poizon.scm.wms.service.delivery.common.DeliveryOrderCommonService;
import com.poizon.scm.wms.service.delivery.config.DeliveryAllocateWarehouseConfig;
import com.poizon.scm.wms.service.delivery.mapper.DeliveryBaseService;
import com.poizon.scm.wms.service.delivery.mapper.DeliveryDetailBaseService;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.delivery.operate.IDeliveryOrderCancelService;
import com.poizon.scm.wms.service.delivery.query.DeliveryOrderQueryService;
import com.poizon.scm.wms.service.delivery.user.IDeliveryUserService;
import com.poizon.scm.wms.service.haandover.DeliveryHandoverHeaderService;
import com.poizon.scm.wms.service.interceptor.IDeliveryInterceptService;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateService;
import com.poizon.scm.wms.service.inventory.search.CleanUpOrderShipService;
import com.poizon.scm.wms.service.pickup.PickupService;
import com.poizon.scm.wms.service.returnshelf.DeliveryExceptionDetailGeneratorConsistencyExecutor;
import com.poizon.scm.wms.service.shipping.ShippingCancelService;
import com.poizon.scm.wms.service.shipping.ShippingConsistencyExecutor;
import com.poizon.scm.wms.service.shipping.producer.DBAllocateMessageToOfcProducer;
import com.poizon.scm.wms.third.build.CisSubtractRequestBuilder;
import com.poizon.scm.wms.third.receive.biz.out.executor.SciInventoryCancelExecutor;
import com.poizon.scm.wms.util.DeliveryHeaderUtils;
import com.poizon.scm.wms.util.common.WarehouseSmallProperties;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.athena.api.enums.InvManagementModeEnum;
import com.shizhuang.athena.api.enums.InvTagEnum;
import com.shizhuang.duapp.erp.api.definition.OrderApi;
import com.shizhuang.duapp.erp.api.enums.WmsNoticeTypeEnums;
import com.shizhuang.duapp.erp.api.model.request.PlatformTradeOrderDeliverRequest;
import com.shizhuang.duapp.erp.api.model.request.WmsNoticeDto;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeliveryOrderModifyServiceImpl implements DeliveryOrderModifyService {

    @Autowired
    Executor asyncServiceExecutor;
    @Autowired
    PickupService pickupService;
    @Autowired
    private OubApplyService oubApplyService;
    @Autowired
    private ShippingConsistencyExecutor shippingConsistencyExecutor;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private DeliveryBaseService deliveryBaseService;
    @Autowired
    private DeliveryHeaderMapper deliveryHeaderMapper;
    @Autowired
    private DeliveryDetailMapper deliveryDetailMapper;
    @Autowired
    private BackToOriginalService backToOriginalService;
    @Resource
    private DeliveryOrderQueryService deliveryOrderQueryService;
    @Autowired
    private DeliveryOrderCommonService deliveryOrderCommonService;
    @Resource
    private InventoryOperateService inventoryOperateService;
    @Autowired
    private IDeliveryUserService deliveryUserService;
    @Autowired
    DBAllocateMessageToOfcProducer product;
    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    private InvInventoryAllocatedRepository inventoryAllocatedRepository;
    @Autowired
    private DeliveryStatusRepository deliveryStatusRepository;
    @Autowired
    private IDeliveryInterceptService deliveryInterceptService;

    @Autowired
    private DeliveryDetailBaseService deliveryDetailBaseService;

    @Autowired
    private DeliveryHandoverHeaderService deliveryHandoverHeaderService;

    @Resource
    private ReturnApplyRecordRepository returnApplyRecordRepository;

    @Autowired
    DeliveryExceptionDetailGeneratorConsistencyExecutor deliveryExceptionDetailGeneratorConsistencyExecutor;

    @Resource
    private OrderApi orderApi;

    @Autowired
    private CleanUpOrderShipService cleanUpOrderShipService;

    @Autowired
    private ShippingCancelService shippingCancelService;

    @Autowired
    private IDeliveryOrderCancelService iDeliveryOrderCancelService;

    @Resource
    private CisInventoryOperation cisInventoryOperation;
    @Resource
    private CisSubtractRequestBuilder cisSubtractRequestBuilder;
    @Autowired
    WarehouseSmallProperties warehouseSmallProperties;
    @Autowired
    SciInventoryCancelExecutor sciInventoryCancelExecutor;
    @Resource
    private DeliveryShipEventProducer deliveryShipEventProducer;
    @Resource
    private ShippingCommandService shippingCommandService;
    @Resource
    private DeliveryPermissionRepository deliveryPermissionRepository;
    @Resource
    private KolShipExecutor kolShipExecutor;
    @Resource
    private DeliveryRelatedOrdersRepository deliveryRelatedOrdersRepository;

    @Resource
    private WmsOutBillHeaderCommandRepository wmsOutBillHeaderCommandRepository;

    @Resource
    private ExpressBillPrintService expressBillPrintService;

    @Resource
    private WmsOutBillDetailCommandRepository wmsOutBillDetailCommandRepository;
    @Resource
    private OutboundMessageProducer outboundMessageProducer;
    @Resource
    private CustomMakeOrderLogisticsService customMakeOrderLogisticsService;
    @Resource
    private DeliveryExtraInfoRepository deliveryExtraInfoRepository;
    @Resource
    private DeliveryLogisticRepository deliveryLogisticRepository;
    @Resource
    private WmsPackRepository wmsPackRepository;
    @Resource
    private MergeShipmentReportRepository mergeShipmentReportRepository;

    @Resource
    protected DeliveryAllocateWarehouseConfig deliveryAllocateWarehouseConfig;
    @Resource
    private MfsShipRepository mfsShipRepository;
    @Resource
    private WmsLogisticsBillDoExtMapper wmsLogisticsBillDoExtMapper;

    @Resource
    private ShipAntiFakeQueryRepository shipAntiFakeQueryRepository;

    @Value("${mfs.printExpress.independentTransaction.switch:true}")
    private Boolean independentTransactionSwitch;

    @Resource
    private SelfOrderExpressRepository selfOrderExpressRepository;

    @Resource
    private WmsOutHandoverRepository outHandoverRepository;

    /**
     * 重新库存分配
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void partAllocateInv(String deliveryOrderCode) {
        boolean cancelLockFlag = false;
        boolean lockFlag = false;
        try {
            if (!distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_CANCEL, deliveryOrderCode)) {
                throw new WmsOperationException("请稍后库存分配");
            }
            cancelLockFlag = true;
            if (!distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_ALLOCATE, deliveryOrderCode)) {
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            lockFlag = true;
            InventoryAllocatedOrderPojo pojo = deliveryOrderCommonService.buildAllocateInventory(deliveryOrderCode);
            if (CollectionUtils.isEmpty(pojo.getInventoryAllocatedItemPojo())) {
                throw new WmsOperationException(WmsExceptionCode.UNWANTED_PART_ALLOCATE_INV);
            }
            DeliveryHeaderEntity deliveryHeaderEntity = deliveryOrderQueryService.queryDeliveryInfo(deliveryOrderCode);
            if (!WmsOutBoundStatusEnum.partAllocateInvList.contains(deliveryHeaderEntity.getStatus())) {
                throw new WmsOperationException(WmsExceptionCode.CURRENT_STATUS_NOT_PART_INV);
            }
            if (!StringUtils.isBlank(deliveryHeaderEntity.getLaunchNo())) {
                throw new WmsOperationException(WmsExceptionCode.LAUNCH_EMPTY_NOT_PART_INV);
            }
            if (deliveryHeaderEntity.getCommandStatus().equals(WmsOutCommandStatusEnum.CANCEL.getStatus())) {
                throw new WmsOperationException(WmsExceptionCode.BOUND_CANCELED);
            }
            if (!inventoryOperateService.checkInvAllocated(deliveryHeaderEntity.getType(), deliveryHeaderEntity.getBizType(), deliveryHeaderEntity.getDeliveryOrderCode())) {
                throw new WmsOperationException(WmsExceptionCode.INV_ALLOCATED_NOT_SUPPORT);
            }
            if (DeliveryHeaderUtils.ifGoodsFlow(deliveryHeaderEntity.getContainsGoodsFlow())) {
                throw new WmsOperationException("货品分流分层单据不支持手工分配");
            }
            /*判断出库单是否取消,如果未取消，先将出库单改为完全分配(采用乐观锁)*/
            this.updateAllocatedStatus(deliveryHeaderEntity);
            /*分配库存，并且修改出库单状态*/
            inventoryOperateService.allocatedInventory(pojo, false);

            //重新分配后 拦截重复发货给同一买家
            DeliveryUserEntity entity = deliveryUserService.queryDeliverUserByOrderCode(OperationUserContextHolder.getTenantCode(), deliveryHeaderEntity.getWarehouseCode(), deliveryOrderCode);
            if (entity != null && entity.getReceiverId() != null && entity.getReceiverId() > 0L) {
                RepeatDeliveryInterceptorInfo info = RepeatDeliveryInterceptorInfo.builder()
                        .warehouseCode(deliveryHeaderEntity.getWarehouseCode())
                        .tenantCode(deliveryHeaderEntity.getTenantCode())
                        .receiverId(entity.getReceiverId())
                        .deliveryOrderCode(deliveryOrderCode).build();
                deliveryInterceptService.repeatDeliveryInterceptor(info);
            }
        } catch (WmsOperationException | WmsException o) {
            log.warn("重新分配库存,request:{},error", deliveryOrderCode, o);
            throw new WmsOperationException(o.getCode(), o.getMessage());
        } catch (InterruptedException e) {
            log.error("interrupt, 重新分配库存,request:{},error", deliveryOrderCode, e);
            Thread.currentThread().interrupt();
            throw new WmsException(WmsExceptionCode.BOUND_OPERATING_SYSTEM_EXCEPTION);
        } catch (Exception e) {
            log.error("重新分配库存,request:{},error", deliveryOrderCode, e);
            throw new WmsException(WmsExceptionCode.BOUND_OPERATING_SYSTEM_EXCEPTION, e);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_ALLOCATE, deliveryOrderCode);
            }
            if (cancelLockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, deliveryOrderCode);
            }
        }
    }

    private void checkQTGreyFlag(String deliveryOrderCode,String deliveryType ){
        if(!WmsOutBoundTypeEnum.QLCK.getType().equals(deliveryType)){
            return;
        }
        DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if(deliveryExtraInfoDo == null){
            throw new WmsException("清退单出库附加信息为空");
        }
        if(NumberUtils.INTEGER_ZERO.equals(deliveryExtraInfoDo.getQtGreyFlag())){
            throw new WmsOperationException("历史清退单不允许取消分配");
        }

    }


    /**
     * 修改分配库存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyDeliveryAllocate(DeliveryModifyAllocatePojo pojo, List<String> matchUnifiedInventoryDetailNos, List<InvInventoryAllocatedEntity> inventoryAllocatedList) {
        log.info("deliveryModifyAllocatePojo-message:{}", JSON.toJSONString(pojo));
        boolean containsSnFlag = pojo.getDeliveryAllocate().stream().anyMatch(item -> InvManagementModeEnum.SN.getCode().equals(item.getInvManagementMode()));
        //修改出库单状态
        this.modifyDeliveryOrderStatusAndLockForAllocated(pojo.getDelivery(), containsSnFlag);
        //如果分配失败直接返回
        if (pojo.getDelivery().getDeliveryStatus().equals(WmsOutBoundStatusEnum.FAIL_ALLOCATE)) {
            return;
        }
        //修改出库单分配数量
        DeliveryDetailEntity updateDetailEntity;
        Set<String> detailNos = pojo.getDeliveryAllocate().stream().map(DeliveryDetailPojo::getDetailNo).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(detailNos)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY);
        }
        List<DeliveryDetailDo> deliveryDetailEntityList = deliveryDetailRepository.queryByDetailNos(new ArrayList<>(detailNos));
        deliveryDetailEntityList = deliveryDetailEntityList.stream().filter(item -> !WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(item.getCancelFlag())).collect(Collectors.toList());
        if (detailNos.size() != deliveryDetailEntityList.size()) {
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY);
        }
        Map<String, DeliveryDetailDo> deliveryDetailEntityMap =
                deliveryDetailEntityList.stream().collect(Collectors.toMap(DeliveryDetailDo::getDetailNo, a -> a, (k1, k2) -> k1));
        for (DeliveryDetailPojo deliveryDetailPojo : pojo.getDeliveryAllocate()) {
            if (deliveryDetailPojo.getAllocatedQty() > deliveryDetailEntityMap.get(deliveryDetailPojo.getDetailNo()).getPlanQty()) {
                throw new WmsException(WmsExceptionCode.PART_INV_NUM_ERROR);
            }
            updateDetailEntity = new DeliveryDetailEntity();
            updateDetailEntity.setId(deliveryDetailEntityMap.get(deliveryDetailPojo.getDetailNo()).getId());
            updateDetailEntity.setAllocatedQty(deliveryDetailPojo.getAllocatedQty());
            if (matchUnifiedInventoryDetailNos.contains(deliveryDetailPojo.getDetailNo())) {
                updateDetailEntity.setUnifiedInventory(UnifiedInventoryTypeEnum.UNIFIED_INVENTORY_REVERSE.getCode());
            }
            updateDetailEntity.setInvTag(deliveryDetailPojo.getInvTag());
            updateDetailEntity.setInvManagementMode(deliveryDetailPojo.getInvManagementMode());
            deliveryDetailMapper.updateById(updateDetailEntity);
        }

        // 单据完成分配情况下, 回写出库单明细
        if (pojo.getDelivery().getDeliveryStatus().equals(WmsOutBoundStatusEnum.WHOLE_ALLOCATE)) {
            writeBackDeliveryDetail(inventoryAllocatedList, deliveryDetailEntityList);
        }

    }

    public void writeBackDeliveryDetail(List<InvInventoryAllocatedEntity> inventoryAllocatedList, List<DeliveryDetailDo> deliveryDetailEntityList) {
        if (CollectionUtils.isEmpty(inventoryAllocatedList)) {
            return;
        }
        //是否存在SN管理库存
        boolean existSnFlag = inventoryAllocatedList.stream().anyMatch(item -> InvManagementModeEnum.SN.getCode().equals(item.getInvManagementMode()));
        if (!existSnFlag) {
            return;
        }

        Map<String, List<InvInventoryAllocatedEntity>> invGroupMap = inventoryAllocatedList.stream().collect(Collectors.groupingBy(InvInventoryAllocatedEntity::getShipmentDetailNo));

        for (DeliveryDetailDo deliveryDetailEntity : deliveryDetailEntityList) {
            if (!InvTagEnum.DEPOSIT_SKU.getCode().equals(deliveryDetailEntity.getInvTag())) {
                continue;
            }
            List<InvInventoryAllocatedEntity> invInventoryAllocatedEntities = invGroupMap.get(deliveryDetailEntity.getDetailNo());
            Map<String, List<InvInventoryAllocatedEntity>> map = invInventoryAllocatedEntities.stream().collect(Collectors.groupingBy(item -> item.getOriginalOrderCode() + item.getVenderCode() + item.getOriginalCountry() +
                    item.getTenantCode() + item.getQualityLevel() + item.getSkuId() + item.getBizType() + item.getOwnerCode()));
            if (map.size() == 1 ) {
                deliveryDetailRepository.updateSelectiveById(buildUpdateDeliveryDetailDo(deliveryDetailEntity, invInventoryAllocatedEntities));
            } else {
                int i = 0;
                List<DeliveryDetailDo> saveDetailList = new ArrayList<>();
                List<InvInventoryAllocatedDo> allocateUpdateList = new ArrayList<>();
                for (Map.Entry<String, List<InvInventoryAllocatedEntity>> entry : map.entrySet()) {
                    String newDetailNo = i == 0 ? deliveryDetailEntity.getDetailNo() : IdGenUtil.generateBusinessNo(SequenceTypeEnum.WMS_CK_DETAIL_CODE.getSequenceType());
                    saveDetailList.add(buildInsertDeliveryDetailDo(deliveryDetailEntity, entry.getValue(), newDetailNo));
                    allocateUpdateList.addAll(buildAllocateUpdateList(entry.getValue(), newDetailNo));
                    i++;
                }
                deliveryDetailRepository.deleteById(deliveryDetailEntity.getId());
                deliveryDetailRepository.batchSave(saveDetailList);
                inventoryAllocatedRepository.batchUpdate(allocateUpdateList);
            }
        }
    }

    private List<InvInventoryAllocatedDo> buildAllocateUpdateList(List<InvInventoryAllocatedEntity> allocatedList, String newDetailNo) {
        List<InvInventoryAllocatedDo> allocateUpdateList = new ArrayList<>();
        for (InvInventoryAllocatedEntity invInventoryAllocatedEntity : allocatedList) {
            InvInventoryAllocatedDo invInventoryAllocatedDo = new InvInventoryAllocatedDo();
            invInventoryAllocatedDo.setId(invInventoryAllocatedEntity.getId());
            invInventoryAllocatedDo.setShipmentDetailNo(newDetailNo);
            allocateUpdateList.add(invInventoryAllocatedDo);
        }
        return allocateUpdateList;
    }

    private DeliveryDetailDo buildUpdateDeliveryDetailDo(DeliveryDetailDo deliveryDetailEntity, List<InvInventoryAllocatedEntity> invInventoryAllocatedEntities) {
        DeliveryDetailDo updateDetailEntity = new DeliveryDetailDo();
        updateDetailEntity.setOwnerCode(invInventoryAllocatedEntities.get(0).getOwnerCode());
        updateDetailEntity.setBizType(invInventoryAllocatedEntities.get(0).getBizType());
        updateDetailEntity.setTargetEntryOrderCode(invInventoryAllocatedEntities.get(0).getOriginalOrderCode());
        updateDetailEntity.setVendorCode(invInventoryAllocatedEntities.get(0).getVenderCode());
        updateDetailEntity.setOriginalCountry(invInventoryAllocatedEntities.get(0).getOriginalCountry());
        updateDetailEntity.setUpdatedTime(new Date());
        updateDetailEntity.setInvTag(invInventoryAllocatedEntities.get(0).getInvTag());
        updateDetailEntity.setInvManagementMode(invInventoryAllocatedEntities.get(0).getInvManagementMode());
        updateDetailEntity.setId(deliveryDetailEntity.getId());
        return updateDetailEntity;
    }

    private DeliveryDetailDo buildInsertDeliveryDetailDo(DeliveryDetailDo deliveryDetailEntity, List<InvInventoryAllocatedEntity> allocatedList, String newDetailNo) {
        DeliveryDetailDo saveDetail = BeanUtil.deepCopy(deliveryDetailEntity, DeliveryDetailDo.class);
        saveDetail.setId(null);
        saveDetail.setSourceDetailNo(saveDetail.getDetailNo());
        saveDetail.setUpdatedTime(new Date());
        saveDetail.setOwnerCode(allocatedList.get(0).getOwnerCode());
        saveDetail.setBizType(allocatedList.get(0).getBizType());
        saveDetail.setTargetEntryOrderCode(allocatedList.get(0).getOriginalOrderCode());
        saveDetail.setVendorCode(allocatedList.get(0).getVenderCode());
        saveDetail.setOriginalCountry(allocatedList.get(0).getOriginalCountry());
        saveDetail.setPlanQty(allocatedList.stream().mapToInt(InvInventoryAllocatedEntity::getQty).sum());
        saveDetail.setAllocatedQty(saveDetail.getPlanQty());
        saveDetail.setDetailNo(newDetailNo);
        saveDetail.setInvTag(allocatedList.get(0).getInvTag());
        saveDetail.setInvManagementMode(allocatedList.get(0).getInvManagementMode());
        return saveDetail;
    }

    /**
     * 更新波次,为空不更新
     *
     * @param pojoList
     */
    @Override
    public void updateBatchLaunchNoByDeliveryOrderCode(List<DeliveryModifyLaunchNoPojo> pojoList) {
        if (CollectionUtils.isEmpty(pojoList)) {
            return;
        }
        Map<String, List<String>> deliveryOrderCodeMap = pojoList.stream().collect(Collectors.groupingBy(DeliveryModifyLaunchNoPojo::
                getLaunchNo, Collectors.mapping(DeliveryModifyLaunchNoPojo::getDeliveryOrderCode, Collectors.toList())));
        for (Map.Entry<String, List<String>> entry : deliveryOrderCodeMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            int updateSize = deliveryHeaderMapper.batchUpdateDeliveryHeaderWithNoLaunch(entry.getKey(),
                    entry.getValue(), OperationUserContextHolder.getTenantCode());
            if (updateSize != entry.getValue().size()) {
                log.warn("波次更新出库单失败，参数是[{}], [{}]", JSON.toJSONString(entry.getKey()), JSON.toJSONString(entry.getValue()));
                throw new WmsOperationException("创建波次更新出库单失败！");
            }
        }
    }

    /**
     * 更新出库单为空波次
     *
     * @param deliveryOrderCodes
     */
    @Override
    public void updateDeliveryOrderForEmptyLaunch(List<String> deliveryOrderCodes) {
        if (CollectionUtils.isEmpty(deliveryOrderCodes)) {
            return;
        }
        Collections.sort(deliveryOrderCodes);
        int updateSize = deliveryHeaderMapper.batchUpdateDeliveryHeader(StringUtils.EMPTY, deliveryOrderCodes, OperationUserContextHolder.getTenantCode());
        if (updateSize != deliveryOrderCodes.size()) {
            log.error("删除出库单波次为空失败，参数是 [{}]", JSON.toJSONString(deliveryOrderCodes));
            throw new WmsException("创建波次更新出库单失败！");
        }
    }


    /**
     * true --可以取消 false-不能取消
     * 出库单取消流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deliveryOrderCancelV2(String deliveryOrderCode) {
        return iDeliveryOrderCancelService.deliveryOrderCancelV3(deliveryOrderCode, null,false);
    }

    /**
     * 修改出库单的实际发货数量(发货最后一次调用)
     * 增加计划发货数量，部分分配可发货的单据（调拨、清退），需调整实际发货数量为出库单的计划数量
     */
    @Override
    public void handleDeliveryOrder(DeliveryShipPojo pojo) {
        try {
            if (StringUtils.isBlank(pojo.getDeliveryModifyHeader().getDeliveryOrderCode())) {
                throw new WmsException(WmsExceptionCode.Bill_RELATED_NULL);
            }
            //查询出库单
            DeliveryHeaderEntity headerEntity = deliveryOrderQueryService.queryDeliveryInfo(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());

            //出库单校验
            commonValidate(headerEntity);

            //构建出库单头更新对象
            DeliveryHeaderEntity deliveryHeaderEntity = buildDeliveryHeaderUpdateEntity(pojo, headerEntity);

            //更新出库单头
            updateDeliveryHeader(headerEntity, deliveryHeaderEntity);

            //更新出库单明细
            updateDeliveryDetail(pojo);

            //保存出库单状态记录
            deliveryOrderCommonService.saveDeliveryStatusRecord(headerEntity.getDeliveryOrderCode(), deliveryHeaderEntity.getStatus(), headerEntity.getWarehouseCode());

            //生成异常返架明细
            if (!WmsOutBoundTypeEnum.COMBINE_TYPE_LIST.contains(headerEntity.getType())
                    && pojo.getDeliveryModifyHeader().getReturnShelfStatus() != null
                    && pojo.getDeliveryModifyHeader().getReturnShelfStatus() > 0) {
                deliveryExceptionDetailGeneratorConsistencyExecutor.execute(headerEntity.getDeliveryOrderCode());
            }

            //出库完成，通知上游
            if (WmsOutBoundStatusEnum.outComplete.contains(pojo.getDeliveryModifyHeader().getStatus())) {
                handleCompleteEventWithType(pojo, headerEntity);
            }

        } catch (WmsOperationException w) {
            throw w;
        } catch (WmsException w) {
            log.error("修改出库单的实际发货数量,request:" + JSON.toJSONString(pojo), w);
            throw w;
        } catch (Exception e) {
            log.error("修改出库单的实际发货数量,request:" + JSON.toJSONString(pojo), e);
            throw new WmsException(WmsExceptionCode.BOUND_OPERATING_SYSTEM_EXCEPTION);
        }
    }

    private DeliveryHeaderEntity buildDeliveryHeaderUpdateEntity(DeliveryShipPojo pojo, DeliveryHeaderEntity headerEntity) {
        DeliveryHeaderEntity updateDeliveryHeaderEntity = new DeliveryHeaderEntity();
        updateDeliveryHeaderEntity.setTotalActualQty(pojo.getDeliveryModifyHeader().getTotalActualQty());
        updateDeliveryHeaderEntity.setStatus(pojo.getDeliveryModifyHeader().getStatus());
        if (WmsOutBoundStatusEnum.outComplete.contains(pojo.getDeliveryModifyHeader().getStatus())) {
            /*发货时间*/
            updateDeliveryHeaderEntity.setOutTime(new Date());
            if (WmsOutBoundTypeEnum.NOT_PRINT_EXPRESS_TYPE.contains(headerEntity.getType())){
                updateDeliveryHeaderEntity.setHandoverTime(updateDeliveryHeaderEntity.getOutTime());
            }
        }

        /**
         * 如果状态是出库完成, 判断是不是企业寄存虚拟出, 如果是, 直接更新交接时间
         */
        if (WmsOutBoundStatusEnum.OUTED.getStatus().equals(updateDeliveryHeaderEntity.getStatus()) && judgeQiYeVirtualShip(headerEntity)) {
            updateDeliveryHeaderEntity.setHandoverTime(new Date());
        }

        // 自主下单更改运单号之前，出库交接可能先操作，导致出库单上的出库交接时间没有，这里需要更新出库交接时间
        fillSelfOrderHandoverTime(headerEntity, updateDeliveryHeaderEntity);

        //合并发货和合并退货的返架交由取消模块处理
        if (!WmsOutBoundTypeEnum.COMBINE_TYPE_LIST.contains(headerEntity.getType())
                && pojo.getDeliveryModifyHeader().getReturnShelfStatus() != null
                && pojo.getDeliveryModifyHeader().getReturnShelfStatus() > 0) {
            updateDeliveryHeaderEntity.setReturnShelfStatus(pojo.getDeliveryModifyHeader().getReturnShelfStatus());
        }
        return updateDeliveryHeaderEntity;
    }

    /**
     * 自主下单更改运单号之前，出库交接可能先操作，导致出库单上的出库交接时间没有，这里需要更新出库交接时间
     * @param headerEntity
     * @param updateDeliveryHeaderEntity
     */
    private void fillSelfOrderHandoverTime(DeliveryHeaderEntity headerEntity, DeliveryHeaderEntity updateDeliveryHeaderEntity) {
        try {
            Date handoverTime = updateDeliveryHeaderEntity.getHandoverTime();
            // 如果前边已经设置了交接时间，这里就不用再设置了
            if (Objects.nonNull(handoverTime)) {
                return;
            }
            List<WmsPackDo> packDoList = wmsPackRepository.queryByDeliveryOrderCode(headerEntity.getDeliveryOrderCode(), headerEntity.getTenantCode());
            String expressCode;
            if (CollectionUtils.isEmpty(packDoList) || Strings.isBlank(expressCode = packDoList.get(0).getExpressCode())) {
                return;
            }
            // 如果不是自主下单的场景，不处理
            SelfOrderExpressDo selfOrderExpressDo = selfOrderExpressRepository.queryByExpressCode(expressCode, headerEntity.getTenantCode());
            if (Objects.isNull(selfOrderExpressDo)) {
                return;
            }

            // 如果还没有出库交接，不处理
            WmsOutHandoverDetailResponseModel wmsOutHandoverDetailModel = outHandoverRepository.queryHandOverDetail(selfOrderExpressDo.getExpressCode());
            if (Objects.isNull(wmsOutHandoverDetailModel)) {
                return;
            }
            log.info("fillSelfOrderHandoverTime, deliveryOrderCode: {}, expressCode: {}, handoverTime: {}",
                    headerEntity.getDeliveryOrderCode(), selfOrderExpressDo.getExpressCode(), wmsOutHandoverDetailModel.getCreateTime());
            updateDeliveryHeaderEntity.setHandoverTime(wmsOutHandoverDetailModel.getCreateTime());
        } catch (Exception e) {
            log.error("fillSelfOrderHandoverTime error, deliveryOrderCode: {}", headerEntity.getDeliveryOrderCode(), e);
        }
    }


    private void updateDeliveryHeader(DeliveryHeaderEntity headerEntity, DeliveryHeaderEntity updateDeliveryHeaderEntity) {
        UpdateWrapper<DeliveryHeaderEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(DeliveryHeaderEntity.COL_DELIVERY_ORDER_CODE, headerEntity.getDeliveryOrderCode());
        updateWrapper.ne(DeliveryHeaderEntity.COL_COMMAND_STATUS, WmsOutCommandStatusEnum.CANCEL.getStatus());
        int rows = deliveryHeaderMapper.update(updateDeliveryHeaderEntity, updateWrapper);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.BOUND_STATUS_CANCEL);
        }
    }

    private void commonValidate(DeliveryHeaderEntity headerEntity) {
        if (headerEntity == null) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        if (WmsOutCommandStatusEnum.CANCEL.getStatus().equals(headerEntity.getCommandStatus())) {
            throw new WmsException(WmsExceptionCode.BOUND_CANCELED);
        }
        if (WmsOutBoundStatusEnum.OUTED.getStatus().equals(headerEntity.getStatus())) {
            throw new WmsException(WmsExceptionCode.DELIVERY_SHIPPED);
        }
    }

    private boolean judgeQiYeVirtualShip(DeliveryHeaderEntity headerEntity) {
        if (!WmsBizTypeEnum.QI_YE_JI_CUN.getBizType().equals(headerEntity.getBizType())) {
            return false;
        }
        // 查询明细
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(
                headerEntity.getDeliveryOrderCode(), headerEntity.getTenantCode());

        List<String> uniqueCodeList = deliveryDetailDos.stream().map(
                DeliveryDetailDo::getUniqueCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 异常提报的虚拟出, 只有一个唯一码
        if (CollectionUtils.isEmpty(uniqueCodeList) || uniqueCodeList.size() > 1) {
            return false;
        }
        // 企业寄存的退货会创建退货申请单
        List<ReturnApplyRecordDo> returnApplyRecordList = returnApplyRecordRepository.selectByUniqueCode(uniqueCodeList.get(0));
        if (CollectionUtils.isEmpty(returnApplyRecordList)) {
            return false;
        }
        /*过滤出提报异常产生的申请退货的明细*/
        List<ReturnApplyRecordDo> virtualReturnRecordList = returnApplyRecordList.stream().filter(record ->
                ReturnPickFlagEnum.isVirtualReturnDelivery(record.getPickFlag())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(virtualReturnRecordList);

    }

    /**
     * 更新出库单明细
     * @param pojo
     */
    private void updateDeliveryDetail(DeliveryShipPojo pojo) {
        if (CollectionUtils.isEmpty(pojo.getDeliveryShipDetailPojoList())) {
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY);
        }
        Set<String> detailNos = pojo.getDeliveryShipDetailPojoList().stream().map(DeliveryShipDetailPojo::getDetailNo).collect(Collectors.toSet());
        List<DeliveryDetailEntity> deliveryDetailEntityList = deliveryOrderQueryService.queryBatchDeliveryDetailByDetailNos(detailNos);
        deliveryDetailEntityList = deliveryDetailEntityList.stream().filter(item -> !WmsDeliveryDetailCancelEnum.
                deliveryDetailIsCancel(item.getCancelFlag())).collect(Collectors.toList());
        if (detailNos.size() != deliveryDetailEntityList.size()) {
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY);
        }
        Map<String, DeliveryDetailEntity> deliveryDetailEntityMap =
                deliveryDetailEntityList.stream().collect(Collectors.toMap(DeliveryDetailEntity::getDetailNo, a -> a, (k1, k2) -> k1));
        Map<String, DeliveryDetailEntity> updateActualQtyMap = new HashMap<>(deliveryDetailEntityMap.size());
        for (DeliveryShipDetailPojo detailQtyPojo : pojo.getDeliveryShipDetailPojoList()) {
            if (detailQtyPojo.getActualQty() > deliveryDetailEntityMap.get(detailQtyPojo.getDetailNo()).getPlanQty()) {
                throw new WmsException(WmsExceptionCode.SHIPPING_NUM_ERROR);
            }
            //填充计划发货数量
            detailQtyPojo.setPlanQty(deliveryDetailEntityMap.get(detailQtyPojo.getDetailNo()).getPlanQty());

            DeliveryDetailEntity updateDeliveryDetailEntity;
            if (updateActualQtyMap.containsKey(detailQtyPojo.getDetailNo())) {
                updateDeliveryDetailEntity = updateActualQtyMap.get(detailQtyPojo.getDetailNo());
            } else {
                updateDeliveryDetailEntity = new DeliveryDetailEntity();
                updateDeliveryDetailEntity.setId(deliveryDetailEntityMap.get(detailQtyPojo.getDetailNo()).getId());
                updateDeliveryDetailEntity.setActualQty(deliveryDetailEntityMap.get(detailQtyPojo.getDetailNo()).getActualQty());
                if (updateDeliveryDetailEntity.getActualQty() == null) {
                    updateDeliveryDetailEntity.setActualQty(0);
                }
                updateActualQtyMap.put(detailQtyPojo.getDetailNo(), updateDeliveryDetailEntity);
            }

            updateDeliveryDetailEntity.setActualQty(detailQtyPojo.getActualQty()
                    + updateDeliveryDetailEntity.getActualQty());

        }
        updateActualQtyMap.forEach((key, value) -> deliveryDetailMapper.updateById(value));

    }


    /**
     * 出库完成，根据不同的业务类型和出库单类型做不同的处理
     * 1、门道租户：
     * 普货-平台预约（得物交易）：处理发货回传消息
     * 普货-交易出库（淘宝交易，取回代发，门道销售）：需要打面单，更新出库单状态为出库中
     * 普货-取回出库：走wms退供模块
     * 2、得物租户：
     * KOL仓：走wms退供模块
     * 普货-交易出库：通知ERP
     * 自营-交易出库：通知erp
     * 调拨出库：通知ofc,扣减sci
     * 清退出库：强制发货
     * @param pojo
     * @param headerEntity
     */
    private void handleCompleteEventWithType(DeliveryShipPojo pojo,DeliveryHeaderEntity headerEntity) {
        if (TenantEnum.MEN_DAO.getCode().equals(OperationUserContextHolder.getTenantCode())) {
            log.info("门道订单发货回传处理，deliveryOrderCode:{}", pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
            specialShippedHandleForMd(pojo, headerEntity);
        } else if (WmsOutboundBusinessUtils.isMFS(headerEntity.getBizType())) {
            log.info("MFS订单发货回传处理，deliveryOrderCode:{}", pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
            // 这里为什么跟门道分开，原因是后续MFS的需求可能会有变动
            specialShippedHandleForMfs(pojo, headerEntity);
        } else {
            log.info("非门道订单发货回传处理，deliveryOrderCode:{}", pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
            shippedHandlerForOther(pojo, headerEntity);
        }
    }

    private void specialShippedHandleForMfs(DeliveryShipPojo pojo,DeliveryHeaderEntity headerEntity) {
        String deliveryOrderType = pojo.getDeliveryModifyHeader().getDeliveryOrderType();
        // mfs && RCDJ类型, 自动打包下单
        if (Objects.equals(headerEntity.getType(), WmsOutBoundTypeEnum.RCDJ.getCode())) {
            createPackAndExpressForSEW(headerEntity);
        }
        if(Objects.equals(headerEntity.getType(), WmsOutBoundTypeEnum.DFJWCK.getCode())){
            shippingCommandService.createExpressPack(headerEntity.getDeliveryOrderCode());
        }
        if (!WmsOutBoundTypeEnum.DFCK.getType().equals(deliveryOrderType)
                &&!WmsOutBoundTypeEnum.QHCK.getType().equals(deliveryOrderType)
                && !WmsOutBoundTypeEnum.MFSQLCK.getType().equals(deliveryOrderType)){
            mfsShipRepository.shipNotify(headerEntity.getDeliveryOrderCode());
        }
        ShipEventParam shipEventParam = ShipEventParam.builder().deliveryOrderCode(pojo.getDeliveryModifyHeader().getDeliveryOrderCode()).build();
        deliveryShipEventProducer.publishShipEvent(shipEventParam);
        /*更新原始单据*/
        insertBackToOriginal(pojo, headerEntity.getWarehouseCode());
    }


    private void specialShippedHandleForMd(DeliveryShipPojo pojo,DeliveryHeaderEntity headerEntity) {
        String deliveryOrderType = pojo.getDeliveryModifyHeader().getDeliveryOrderType();
        String deliveryOrderCode = headerEntity.getDeliveryOrderCode();
        DeliveryLogisticDo deliveryLogisticDo = deliveryLogisticRepository.queryFirstOneByDeliveryOrderCode(deliveryOrderCode);
        boolean sendShipMsgFlag = false;
        if (WmsOutBoundTypeEnum.RCDJ.getType().equals(deliveryOrderType)){
            createPackAndExpressForSEW(headerEntity);
            //得物交易：平台预约出库
            sendShipMsgFlag = true;
        } else if (WmsOutBoundTypeEnum.JYCK.getType().equals(deliveryOrderType)){
            //门道销售出库，取回代发，淘宝交易，需要快递下单
            //出库单承运商信息
            //门道交易出库，如果出库单承运商信息是自填面单，会拦截不允许调用des下快递面单
            // 发货出库消息不要发送移动至自填面单处发送
            createPackAndExpress(headerEntity,deliveryLogisticDo);
            //发送出库消息
            if (Objects.nonNull(deliveryLogisticDo)&&
                    Objects.equals(ExpressChannelEnum.ZTMD.getCode(),deliveryLogisticDo.getCarrierCode())) {
                //门道自填面单需要判断是否已经有自填面单信息如果有自填面单信息则发送发货事件，否则不发送
                //存在手填面单的情况 必须得从包裹里面获取
                List<WmsPackDo> wmsPackDoList = wmsPackRepository.queryByDeliveryOrderCode(deliveryOrderCode);
                sendShipMsgFlag = wmsPackDoList.stream().anyMatch(i->StringUtils.isNotBlank(i.getExpressCode()));
            } else {
                sendShipMsgFlag = true;
            }
        }
        if (sendShipMsgFlag) {
            ShipEventParam shipEventParam = ShipEventParam.builder().deliveryOrderCode(pojo.getDeliveryModifyHeader().getDeliveryOrderCode()).build();
            deliveryShipEventProducer.publishShipEvent(shipEventParam);
        }
        /*更新原始单据*/
        insertBackToOriginal(pojo, headerEntity.getWarehouseCode());
    }

    private void createPackAndExpress(DeliveryHeaderEntity headerEntity,DeliveryLogisticDo deliveryLogisticDo) {
        shippingCommandService.createExpressPack(headerEntity.getDeliveryOrderCode());
        if (Objects.nonNull(deliveryLogisticDo)&&Objects.equals(ExpressChannelEnum.ZTMD.getCode(),deliveryLogisticDo.getCarrierCode())) {
            //门道自填面单不调用des下单
            return;
        }

        if (Objects.nonNull(deliveryLogisticDo) && Objects.equals(ExpressChannelEnum.SMQJ.getCode(), deliveryLogisticDo.getCarrierCode())) {
            //上门取件 打印小标签
            PrintExpressParam printExpressParam = new PrintExpressParam();
            printExpressParam.setDeliveryOrderCode(headerEntity.getDeliveryOrderCode());
            printExpressParam.setAutoMakeOrder(true);
            printExpressParam.setEscapeOrderStatusValidate(true);
            expressBillPrintService.printExpress(printExpressParam);
            return;
        }


        LogisticMakeParam logisticMakeParam = new LogisticMakeParam();
        logisticMakeParam.setDeliveryHeaderDo(BeanUtil.copy(headerEntity,DeliveryHeaderDo.class));
        LogisticsInfo logisticsInfo = customMakeOrderLogisticsService.makeOrder(logisticMakeParam);
        if (logisticsInfo == null) {
            throw new WmsException("门道发货调用tms下运单失败");
        }
    }

    /**
     * 发货只需要打个小面单
     * SEW
     *Small Express Waybill
     * @param headerEntity
     */
    private void createPackAndExpressForSEW(DeliveryHeaderEntity headerEntity) {
        if (independentTransactionSwitch) {
            shippingCommandService.createExpressPackIndependentTransaction(headerEntity.getDeliveryOrderCode());
            if (WmsOutBoundStatusEnum.pickComplete.contains(headerEntity.getStatus())) {
                deliveryHeaderRepository.updateStatusToPacking(headerEntity.getId());
            }
        } else {
            shippingCommandService.createExpressPack(headerEntity.getDeliveryOrderCode());
        }
        PrintExpressParam printExpressParam = new PrintExpressParam();
        printExpressParam.setDeliveryOrderCode(headerEntity.getDeliveryOrderCode());
        printExpressParam.setAutoMakeOrder(true);
        printExpressParam.setEscapeOrderStatusValidate(true);
        expressBillPrintService.printExpress(printExpressParam);
    }

    /**
     * Kol仓
     *
     * @param pojo
     * @param headerEntity
     */
    private void specialShippedHandleForKol(DeliveryShipPojo pojo, DeliveryHeaderEntity headerEntity) {
        shippingCommandService.createExpressPack(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
        /*更新原始单据*/
        insertBackToOriginal(pojo, headerEntity.getWarehouseCode());
        if (WmsOutBoundTypeEnum.WJCK.getType().equals(headerEntity.getType())) {
            kolShipExecutor.sendMessageToCreateEntryOrder(headerEntity);
        }
        List<DeliveryRelatedOrdersDo> deliveryRelatedOrdersDos = deliveryRelatedOrdersRepository.listByDeliveryOrder(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
        if (CollectionUtils.isEmpty(deliveryRelatedOrdersDos)){
            return;
        }
        DeliveryRelatedOrdersDo deliveryRelatedOrdersDo = deliveryRelatedOrdersDos.get(0);
        /*更新单据头*/
        wmsOutBillHeaderCommandRepository.updateFinishByOutBillNo(deliveryRelatedOrdersDo.getRelatedOrderCode(),WmsOutBillStatusEnum.FINISHED.getStatus());
        /*更新单据明细*/
        wmsOutBillDetailCommandRepository.updateStatusByOutBillNo(deliveryRelatedOrdersDo.getRelatedOrderCode(),WmsOutBillStatusEnum.FINISHED.getStatus());
    }

    private void shippedHandlerForOther(DeliveryShipPojo pojo,DeliveryHeaderEntity headerEntity) {
        List<DeliveryRelatedOrdersEntity> deliveryRelatedOrdersEntity =
                deliveryOrderQueryService.queryDeliveryRelatedOrderByOrderCode(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
        /**
         * 普通类型&自营普货
         */
        if (WmsOutboundBusinessUtils.needNoticeErp(headerEntity.getBizType(),headerEntity.getType())) {
            /*只有小仓库或者商研或自营通知erp*/
            String documentNo;
            String expressType="";
            Map<String,List<ShipAntiFakeDo>> antiFakeCodeMap = Maps.newHashMap();
            if (WmsOutBoundTypeEnum.ERP_MFS_JYCK_TYPE.contains(headerEntity.getType())) {
                /*1.交易回传relateOrderCode对应erp单号 2.由于内部单据传的是wms的单号*/
                documentNo = deliveryRelatedOrdersEntity.get(0).getRelatedOrderCode();
            } else {
                documentNo = pojo.getDeliveryModifyHeader().getDeliveryOrderCode();
            }
            String expressNo = IdGenUtil.generateExpressNo(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
            //自营的交易单，传交接单发货运单号
            if (WmsBizTypeEnum.SELF_BIZ_TYPE.contains(headerEntity.getBizType()) &&
                    WmsOutBoundTypeEnum.JYCK.getType().equals(headerEntity.getType())) {
                DeliveryHandoverHeaderDo headerDo = deliveryHandoverHeaderService.selectByUserIdAndStatus(OperationUserContextHolder.get().getUserId(), HandoverStatusEnums.PROCESS);
                expressNo = headerDo.getWaybillNo();
            }
            if (WmsOutboundBusinessUtils.nonPlatformPackShip(headerEntity.getType(), headerEntity.getBizType())) {
                List<WmsPackDo> wmsPackDos = wmsPackRepository.queryByDeliveryOrderCode(headerEntity.getDeliveryOrderCode());
                List<WmsLogisticsBillDo> logisticsBillDos = wmsLogisticsBillDoExtMapper.queryByDeliveryOrderCode(headerEntity.getDeliveryOrderCode(),OperationUserContextHolder.get().getTenantCode());
                if (CollectionUtils.isEmpty(wmsPackDos)) {
                    throw new WmsException("出库单没有包裹数据, 无法发货");
                }
                expressNo = wmsPackDos.get(0).getExpressCode();
                //获取物流公司类型
                if (CollectionUtils.isNotEmpty(logisticsBillDos)){
                    expressType=logisticsBillDos.get(0).getLogisticsCode();
                }

            }
            boolean ifNeedBindAntiFakeCode = deliveryOrderCommonService.ifNeedBindAntiFakeCode(pojo.getDeliveryModifyHeader().getDeliveryOrderCode(), null, OperationUserContextHolder.getTenantCode());
            if (ifNeedBindAntiFakeCode){
                List<ShipAntiFakeDo> results = shipAntiFakeQueryRepository.queryByDeliveryOrderCode(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
                antiFakeCodeMap = results.stream().collect(Collectors.groupingBy(x->x.getUniqueCode()));
            }

            if (WmsOutBoundTypeEnum.ERP_MFS_JYCK_TYPE.contains(headerEntity.getType())) {
                //交易出库和品牌直发类型需要同步调用ERP，其他走消息
                syncErp(pojo, headerEntity, documentNo, expressNo);
            } else {
                sendMessage(pojo, documentNo, expressNo,expressType, headerEntity.getWarehouseCode(), headerEntity.getOwnerCode(),antiFakeCodeMap);
            }
        }

        //调拨||跨园区调拨扣除sci库存
        if (WmsOutBoundTypeEnum.ALLOCATE_TYPE.contains(pojo.getDeliveryModifyHeader().getDeliveryOrderType())) {
            //wms调用sci扣减库存
            CisSubtractRequest deliveryRequest = cisSubtractRequestBuilder.buildCisSubRequest3(pojo);
            cisInventoryOperation.sendSubtractSciInventoryMsg(deliveryRequest);
            //ofc调用sci会被幂等
            if (WmsOutBoundTypeEnum.KYQDBCK.getType().equals(pojo.getDeliveryModifyHeader().getDeliveryOrderType())){
                //跨园区调拨给ofc发消息使用新的topic和tag(和寄存调拨用同一个)
                product.sendJCDBAllocateMessageToOfc(pojo);
            }else {
                product.sendAllocateMessageToOfc(pojo);
            }
        }
        /*如果是清退,需要强制发货*/
        if (WmsOutBoundTypeEnum.QLCK.getType().equals(headerEntity.getType())
                && WmsOutBoundStatusEnum.FORCE_OUTED.getStatus().equals(pojo.getDeliveryModifyHeader().getStatus())
                && pojo.getDeliveryModifyHeader().getTotalActualQty().equals(NumberUtils.INTEGER_ZERO)) {
            /*尝试强制发货*/
            boolean shipFlag = cleanUpOrderShipService.forceShip(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
            if (!shipFlag) {
                throw new WmsException(WmsExceptionCode.DELIVERY_NOT_ALLOW_FORCE_SHIP);
            }
        }
        //赔付出库 & 客退转寄存虚拟出库 & 免赔付出库 需要通知ofc
        if (WmsOutBoundTypeEnum.PFCK.getType().equals(headerEntity.getType())
                || WmsOutBoundTypeEnum.KT2JC_XNCK.getType().equals(headerEntity.getType())
                || WmsOutBoundTypeEnum.MPF_XNCK.getType().equals(headerEntity.getType())) {
            outboundMessageProducer.pfDeliveryShip2Ofc(headerEntity.getDeliveryOrderCode());
        }

        /*更新原始单据*/
        insertBackToOriginal(pojo, headerEntity.getWarehouseCode());
        /*更改内部单据状态*/
        if (WmsOutBoundTypeEnum.INSIDE_LIST.contains(headerEntity.getType())) {
            deliveryRelatedOrdersEntity.forEach(e -> oubApplyService.finish(e.getDeliveryOrderCode()));
        }
    }

    private void syncErp(DeliveryShipPojo pojo, DeliveryHeaderEntity headerEntity, String document, String expressNo) {
        //此处list不会为空
        DeliveryShipDetailPojo shipDetail = pojo.getDeliveryShipDetailPojoList().get(0);

        PlatformTradeOrderDeliverRequest syncErpRequest = new PlatformTradeOrderDeliverRequest();
        syncErpRequest.setWarehouse(headerEntity.getWarehouseCode());
        syncErpRequest.setDeliverTime(new Date());
        syncErpRequest.setDocumentNo(document);
        syncErpRequest.setExpressNo(expressNo);
        syncErpRequest.setRemark("");
        syncErpRequest.setQuantity(shipDetail.getActualQty());
        syncErpRequest.setTradeOrderNo(headerEntity.getDeliveryOrderCode());
        syncErpRequest.setUniqueCode(shipDetail.getUniqueCode());
        syncErpRequest.setQualityLevel(Integer.valueOf(shipDetail.getQualityLevel()));
        syncErpRequest.setRelationNo(shipDetail.getOriginalRelatedOrderCode());

        Result<Boolean> erpResult;
        try {
            log.info("交易出库同步调用ERP, 请求参数:{}", JSON.toJSONString(syncErpRequest));
            erpResult = orderApi.tradeOrderDeliver(syncErpRequest);
            log.info("交易出库同步调用ERP, 结果:{}", JSON.toJSONString(erpResult));
        } catch (Exception ex) {
            log.warn("交易出库同步调用ERP失败：请求参数:{}", JSON.toJSONString(syncErpRequest), ex);
            throw new WmsException("交易出库同步调用ERP失败");
        }
        if (!Result.SUCCESS_CODE.equals(erpResult.getCode())) {
            log.warn("交易出库同步调用ERP异常, 请求参数:{}, code:{}, message:{}", JSON.toJSONString(syncErpRequest), erpResult.getCode(), erpResult.getMsg());
            throw new WmsException("交易出库同步调用ERP异常：" + erpResult.getMsg());
        }
        //交易取消
        if (erpResult.getData() == null || !erpResult.getData()) {
            registerCancelTaskEvent(headerEntity.getDeliveryOrderCode(), headerEntity.getType());
            throw new WmsException("单据已取消,请返架");
        }
    }

    private void registerCancelTaskEvent(String deliveryOrderCode, String referenceType) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCompletion(int status) {
                log.info("执行发货任务取消动作");
                if (status != TransactionSynchronization.STATUS_COMMITTED) {
                    shippingCancelService.executeCancel(null, deliveryOrderCode, referenceType);
                }
            }
        });
    }


    private void updateDeliveryDetailAllocateQty(String deliveryOrderCode) {
        DeliveryDetailEntity entity = new DeliveryDetailEntity();
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        entity.setAllocatedQty(NumberUtils.INTEGER_ZERO);
        wrapper.eq(DeliveryDetailEntity.COL_DELIVERY_ORDER_CODE, deliveryOrderCode);
        wrapper.eq(DeliveryDetailEntity.COL_TENANT_CODE, OperationUserContextHolder.getTenantCode());
        deliveryDetailBaseService.update(entity, wrapper);
    }

    /**
     * 修改出库单状态(乐观锁)
     */
    @Override
    public void modifyDeliveryOrderStatusAndLock(DeliveryModifyStatusPojo pojo) {
        DeliveryHeaderDo entity = deliveryHeaderRepository.queryDeliveryInfo(pojo.getDeliveryOrderCode());
        if (entity == null) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        if (pojo.getDeliveryStatus() == null && pojo.getReturnShelfStatus() == null) {
            throw new WmsException("单据状态不能为空");
        }
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setDeliveryOrderCode(pojo.getDeliveryOrderCode());
        updateDeliveryHeaderDo.setTenantCode(entity.getTenantCode());
        updateDeliveryHeaderDo.setId(entity.getId());
        if (pojo.getDeliveryStatus() != null) {
            updateDeliveryHeaderDo.setStatus(pojo.getDeliveryStatus().getStatus());
        }
        if (pojo.getReturnShelfStatus() != null && pojo.getReturnShelfStatus().getStatus() > entity.getReturnShelfStatus()) {
            updateDeliveryHeaderDo.setReturnShelfStatus(pojo.getReturnShelfStatus().getStatus());
        }
        if (pojo.getCommandStatus() != null) {
            updateDeliveryHeaderDo.setCommandStatus(pojo.getCommandStatus());
        }
        updateDeliveryHeaderDo.setVersion(entity.getVersion());
        int rows = deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
        }
        addDeliveryOperateStatusLog(pojo.getDeliveryOrderCode(),
                entity.getWarehouseCode(), updateDeliveryHeaderDo.getStatus());
        // 如果是小仓库的话， 需要调用sci取消占用
        // 这里虽然上游都是取消状态下来，但是还是判断一下，防止其他人误用
        /*if(warehouseSmallProperties.isSmallWarehouse(entity.getWarehouseCode()) &&
                Objects.equals(pojo.getDeliveryStatus().getStatus(),WmsOutBoundStatusEnum.CANCEL.getStatus())){
            sciInventoryCancelExecutor.cancelInventory(entity.getDeliveryOrderCode());
        }*/

        //fp需要出列
        if (InterceptTypeEnum.FP.getCode().equals(entity.getInterceptType())) {
            //fp取消分配需要出列
            fpOrderOutProcessor.execute(entity, null,FpOrderOutEvenEnum.INV_ALLOCATION.getCode());
        }


    }


    @Override
    public void modifyDeliveryOrderStatusAndLockForAllocated(DeliveryModifyStatusPojo pojo, boolean containsSnFlag) {
        DeliveryHeaderDo entity = deliveryHeaderRepository.queryDeliveryInfo(pojo.getDeliveryOrderCode());
        if (entity == null) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        if (pojo.getDeliveryStatus() == null && pojo.getReturnShelfStatus() == null) {
            throw new WmsException("单据状态不能为空");
        }
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setDeliveryOrderCode(pojo.getDeliveryOrderCode());
        updateDeliveryHeaderDo.setTenantCode(entity.getTenantCode());
        updateDeliveryHeaderDo.setId(entity.getId());
        if (pojo.getDeliveryStatus() != null) {
            updateDeliveryHeaderDo.setStatus(pojo.getDeliveryStatus().getStatus());
        }
        if (pojo.getReturnShelfStatus() != null && pojo.getReturnShelfStatus().getStatus() > entity.getReturnShelfStatus()) {
            updateDeliveryHeaderDo.setReturnShelfStatus(pojo.getReturnShelfStatus().getStatus());
        }
        updateDeliveryHeaderDo.setVersion(entity.getVersion());
        if(Objects.nonNull(pojo.getLastAllocatedTime())){
            //最新分配时间不为空则记录此时间
            updateDeliveryHeaderDo.setLastAllocatedTime(pojo.getLastAllocatedTime());
        }
        updateDeliveryHeaderDo.setContainsSnFlag(containsSnFlag ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        int rows = deliveryHeaderRepository.updateStatus(updateDeliveryHeaderDo);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
        }
        if(Objects.equals(entity.getType(),WmsOutBoundTypeEnum.HBFHCK.getType())&&Objects.nonNull(pojo.getDeliveryStatus())){
            mergeShipmentReportRepository.updateStatusByShipmentNo(entity.getTenantCode(),entity.getDeliveryOrderCode(),updateDeliveryHeaderDo.getStatus());
        }
        addDeliveryOperateStatusLog(pojo.getDeliveryOrderCode(),
                entity.getWarehouseCode(), updateDeliveryHeaderDo.getStatus());
    }

    @Override
    public void updateShippingStatus(DeliveryModifyStatusPojo pojo) {
        DeliveryHeaderEntity entity = deliveryOrderQueryService.queryDeliveryInfo(pojo.getDeliveryOrderCode());
        if (entity == null) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        UpdateWrapper<DeliveryHeaderEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set(DeliveryHeaderEntity.COL_STATUS, pojo.getDeliveryStatus().getStatus());
        updateWrapper.eq(DeliveryHeaderEntity.COL_DELIVERY_ORDER_CODE, pojo.getDeliveryOrderCode());
        updateWrapper.ne(DeliveryHeaderEntity.COL_COMMAND_STATUS, WmsOutCommandStatusEnum.CANCEL.getStatus());
        int rows = deliveryHeaderMapper.update(null, updateWrapper);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.BOUND_STATUS_CANCEL);
        }
        deliveryOrderCommonService.saveDeliveryStatusRecord(
                pojo.getDeliveryOrderCode(),
                pojo.getDeliveryStatus().getStatus(),
                entity.getWarehouseCode());
    }


    /**
     * 拣货完成 修改出库单
     */
    @Override
    public void modifyDeliveryByPickStatus(DeliveryPickedPojo pojo) {
        DeliveryHeaderEntity entity = deliveryOrderQueryService.queryDeliveryInfo(pojo.getDeliveryOrderCode());
        if (entity == null) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        if (WmsOutBoundStatusEnum.pickComplete.contains(entity.getStatus())) {
            log.info("出库单状态已经拣货完成或者强制拣货完成:{}", JSON.toJSONString(pojo));
        } else {
            WmsOutBoundStatusEnum statusEnum;
            DeliveryHeaderEntity updateDeliveryHeaderEntity = new DeliveryHeaderEntity();
            updateDeliveryHeaderEntity.setVersion(entity.getVersion());
            updateDeliveryHeaderEntity.setId(entity.getId());
            /*销售出库类型 拣货强制完成 当拣货执行数量为0时需要将出库状态0并清除波次号和清除出库详情中的分配数量*/
            if (pojo.getIsForceComplete()) {
                updateDeliveryHeaderEntity.setStatus(WmsOutBoundStatusEnum.FORCE_PICKED.getStatus());
                if (pojo.getPickedNum() == 0 && entity.getType().equals(WmsOutBoundTypeEnum.JYCK.getCode())) {
                    /*清除出库详情中的分配数量*/
                    updateDeliveryDetailAllocateQty(pojo.getDeliveryOrderCode());
                    updateDeliveryHeaderEntity.setStatus(WmsOutBoundStatusEnum.INIT.getStatus());
                    updateDeliveryHeaderEntity.setLaunchNo("");
                }
                statusEnum = WmsOutBoundStatusEnum.FORCE_PICKED;
            } else {
                if (pojo.getPickedNum() == 0) {
                    throw new WmsException(WmsExceptionCode.PICK_STATUS_NUM_ATYPISM);
                }
                updateDeliveryHeaderEntity.setStatus(WmsOutBoundStatusEnum.PICKED.getStatus());
                statusEnum = WmsOutBoundStatusEnum.PICKED;
            }
            int rows = deliveryHeaderMapper.updateById(updateDeliveryHeaderEntity);
            if (rows <= 0) {
                throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
            }
            deliveryOrderCommonService.saveDeliveryStatusRecord(
                    pojo.getDeliveryOrderCode(),
                    statusEnum.getStatus(),
                    entity.getWarehouseCode()
            );
        }
    }

    private Boolean getSeedingFromDeposit(String deliveryOrderCode) {
        if (StringUtils.isEmpty(deliveryOrderCode)) {
            return false;
        }
        DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if (null != deliveryExtraInfoDo && StringUtils.isNotEmpty(deliveryExtraInfoDo.getFeature())) {
            String feature = deliveryExtraInfoDo.getFeature();
            DeliveryExtraInfoFeatureModel featureModel = JSON.parseObject(feature, DeliveryExtraInfoFeatureModel.class);
            if (null != featureModel) {
                return BooleanUtils.isTrue(featureModel.getSeedingFromDeposit());
            }
        }
        return false;
    }

    private void sendMessage(DeliveryShipPojo pojo, String documentNo,
                             String expressNo,String expressType, String warehouseCode, String ownerCode,Map<String,List<ShipAntiFakeDo>> antiFakeCodeMap) {
        SendMessage<WmsNoticeDto> sendMessage = new SendMessage<>();
        WmsNoticeDto message = new WmsNoticeDto();
        message.setNoticeId(pojo.getDeliveryModifyHeader().getDeliveryOrderCode() + "--" + CallBackTypeEnum.DELIVERY.getType());
        message.setNoticeType(WmsNoticeTypeEnums.DELIVER.getCode());
        WmsNoticeDto.Content deliveryContent = new WmsNoticeDto.Content();
        deliveryContent.setCreator(-1L);
        deliveryContent.setTime(new Date());
        String deliveryOrderCode = pojo.getDeliveryModifyHeader().getDeliveryOrderCode();
        Boolean seedingFromDeposit = getSeedingFromDeposit(deliveryOrderCode);
        deliveryContent.setSeedingFromDeposit(seedingFromDeposit);

        List<WmsNoticeDto.Document> deliveryDocumentList = new ArrayList<>();
        WmsNoticeDto.Document deliveryDocument = new WmsNoticeDto.Document();
        deliveryDocument.setDocumentNo(documentNo);
        //新增货主
        deliveryDocument.setOwnerCode(NumberUtils.toLong(ownerCode));
        List<WmsNoticeDto.DocumentDetail> deliverySkuListList = new ArrayList<>();
        deliveryDocument.setWarehouseCode(warehouseCode);
        if (CollectionUtils.isNotEmpty(pojo.getDeliveryShipDetailPojoList())) {
            for (DeliveryShipDetailPojo detailQtyPojo : pojo.getDeliveryShipDetailPojoList()) {
                //强制完成的明细不用通知erp
                if (detailQtyPojo.getActualQty() == 0) {
                    continue;
                }
                WmsNoticeDto.DocumentDetail deliverySkuList = new WmsNoticeDto.DocumentDetail();
                deliverySkuList.setExpire(detailQtyPojo.getExpire());
                deliverySkuList.setPlanQty(detailQtyPojo.getActualQty());
                deliverySkuList.setUniqueCode(detailQtyPojo.getUniqueCode());
                deliverySkuList.setRelationNo(detailQtyPojo.getEntryOrderCode());
                deliverySkuList.setSkuId(Long.valueOf(detailQtyPojo.getSkuId()));
                deliverySkuList.setQualityLevel(Integer.valueOf(detailQtyPojo.getQualityLevel()));
                deliverySkuList.setRelationNo(detailQtyPojo.getOriginalRelatedOrderCode());
                if (antiFakeCodeMap.containsKey(detailQtyPojo.getUniqueCode())){
                    List<String> antiFakeCodes = antiFakeCodeMap.get(detailQtyPojo.getUniqueCode()).stream().map(ShipAntiFakeDo::getAntiFakeCode).collect(Collectors.toList());
                    deliverySkuList.setAntiFakeCodes(antiFakeCodes);
                }
                deliverySkuListList.add(deliverySkuList);
            }

            deliveryDocument.setExpressNo(expressNo);
            deliveryDocument.setExpressType(expressType);
        } else {
            deliveryDocument.setExpressNo("");
            deliveryDocument.setExpressType("");
        }
        deliveryDocument.setDetails(deliverySkuListList);
        deliveryDocumentList.add(deliveryDocument);
        deliveryContent.setDocuments(deliveryDocumentList);
        message.setNoticeContent(deliveryContent);
        message.setWarehouse(warehouseCode);

        sendMessage.setMessageContent(message);
        sendMessage.setMessageKey(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
        sendMessage.setTag(WmsMessageQueueConstant.WMS_TO_ERP_CALL_BACK_TAG);
        sendMessage.setTopic(WmsMessageQueueConstant.WMS_TO_ERP_CALL_BACK_TOPIC);
        shippingConsistencyExecutor.execute(sendMessage);
    }

    @Override
    public void cancelLaunchUpdateDeliveryHeader(String launchNo) {
        DeliveryHeaderEntity headerEntity = new DeliveryHeaderEntity();
        headerEntity.setLaunchNo(StringUtils.EMPTY);
        DeliveryHeaderEntity updateHeader = new DeliveryHeaderEntity();
        updateHeader.setLaunchNo(launchNo);
        this.deliveryBaseService.update(headerEntity, new QueryWrapper<>(updateHeader));
    }

    /**
     * 插入原始单据溯源表
     */
    private void insertBackToOriginal(DeliveryShipPojo pojo, String warehouseCode) {
        List<BackToOriginalDo> backToOriginalEntityList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pojo.getDeliveryShipDetailPojoList())) {
            // 查询出收货人
            String deliveryOrderCode = pojo.getDeliveryModifyHeader().getDeliveryOrderCode();
            DeliveryUserEntity deliveryUser = deliveryUserService.queryDeliverUserByOrderCode(OperationUserContextHolder.getTenantCode(), warehouseCode, deliveryOrderCode);
            BackToOriginalDo entity;
            for (DeliveryShipDetailPojo shipDetailPojo : pojo.getDeliveryShipDetailPojoList()) {
                entity = new BackToOriginalDo();
                entity.setExpTime(shipDetailPojo.getExpire());
                entity.setMfgTime(shipDetailPojo.getMfgTime());
                entity.setEntryOrderCode(shipDetailPojo.getEntryOrderCode());
                entity.setSkuId(shipDetailPojo.getSkuId());
                entity.setSkuNum(shipDetailPojo.getActualQty());
                entity.setOutOrderCode(pojo.getDeliveryModifyHeader().getDeliveryOrderCode());
                entity.setOutOrderType(pojo.getDeliveryModifyHeader().getDeliveryOrderType());
                entity.setOriginalRelatedOrderCode(shipDetailPojo.getOriginalRelatedOrderCode());
                entity.setQualityLevel(shipDetailPojo.getQualityLevel());
                entity.setUniqueCode(shipDetailPojo.getUniqueCode());
                entity.setVendorCode(shipDetailPojo.getSupplierCode());
                entity.setVendorName(shipDetailPojo.getSupplierName());
                entity.setOwnerCode(shipDetailPojo.getOwnerCode());
                entity.setExpressCode(IdGenUtil.generateExpressNo(pojo.getDeliveryModifyHeader().getDeliveryOrderCode()));
                entity.setWarehouseCode(warehouseCode);
                entity.setFirstReceivedTime(shipDetailPojo.getFirstReceiveTime());
                entity.setReceiverId(deliveryUser != null ? deliveryUser.getReceiverId() : 0L);
                entity.setTenantCode(deliveryUser != null ? deliveryUser.getTenantCode() : OperationUserContextHolder.getTenantCode());
                entity.setProductionBatchNo(shipDetailPojo.getProductionBatchNo());
                entity.setOriginalCountry(shipDetailPojo.getOriginalCountry());
                backToOriginalEntityList.add(entity);
            }
            backToOriginalService.insertOriginalData(backToOriginalEntityList);
        }
    }


    /**
     * build 取消库存分配
     */
    private InventoryAllocatedCancelPojo buildCancelPartAllocate(String deliveryOrderCode,
                                                                 String deliveryDetailNo,
                                                                 String tenantCode,
                                                                 Boolean isDeleteAllocateRecord) {
        InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo = new InventoryAllocatedCancelPojo();
        if (StringUtils.isNotBlank(tenantCode)) {
            inventoryAllocatedCancelPojo.setTenantCode(tenantCode);
        } else {
            inventoryAllocatedCancelPojo.setTenantCode(OperationUserContextHolder.getTenantCode());
        }
        if (StringUtils.isNotBlank(deliveryDetailNo)) {
            inventoryAllocatedCancelPojo.setShipmentDetailNo(deliveryDetailNo);
        } else {
            inventoryAllocatedCancelPojo.setShipmentNo(deliveryOrderCode);
        }
        inventoryAllocatedCancelPojo.setIsDeleteAllocateRecord(isDeleteAllocateRecord);
        return inventoryAllocatedCancelPojo;
    }

    /**
     * 同步出库单明细中的ownerCode
     */
    @Override
    public void ownerDataFix() {
        QueryWrapper<DeliveryDetailEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(DeliveryDetailEntity.COL_DELETED, IsDeleteEnum.EFFECTIVE.getIsDelete());
        wrapper.groupBy(DeliveryDetailEntity.COL_DELIVERY_ORDER_CODE);
        List<DeliveryDetailEntity> deliveryDetailEntityList = deliveryDetailMapper.selectList(wrapper);
        DeliveryHeaderEntity headerEntity;
        for (DeliveryDetailEntity detailEntity : deliveryDetailEntityList) {
            /*更新DeliveryHeader*/
            headerEntity = new DeliveryHeaderEntity();
            QueryWrapper<DeliveryHeaderEntity> deliveryHeaderWrapper = new QueryWrapper<>();
            deliveryHeaderWrapper.eq(DeliveryHeaderEntity.COL_DELIVERY_ORDER_CODE, detailEntity.getDeliveryOrderCode());
            headerEntity.setOwnerCode(detailEntity.getOwnerCode());
            deliveryHeaderMapper.update(headerEntity, deliveryHeaderWrapper);
        }
    }

    @Autowired
    private FPOrderOutProcessor fpOrderOutProcessor;
    /**
     * 1.取消库存分配
     * 2.处理出库单头与明细相关内容
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInventoryAllocationAndDeliveryInfo(String deliveryOrderCode,
                                                            String deliveryDetailNo,
                                                            String tenantCode) {
        boolean tryLockFlag = false;
        try {
            DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
            if (Objects.isNull(deliveryHeaderDo)) {
                throw new WmsOperationException("出库单不存在");
            }
            if (cannotAllocateInventory(deliveryHeaderDo)) {
                throw new WmsOperationException("保税二销单/清退单/跨园区调拨单/虚拟出库单不允许取消分配");
            }
            if (BooleanUtils.toBoolean(deliveryHeaderDo.getContainsSnFlag())
                    && WmsOutBoundStatusEnum.WHOLE_ALLOCATE.getStatus().equals(deliveryHeaderDo.getStatus())) {
                throw new WmsOperationException("仓储托管的取回出库单不允许取消分配");
            }
            checkQTGreyFlag(deliveryOrderCode,deliveryHeaderDo.getType());
            if (!WmsOutBoundStatusEnum.CAN_CANCEL_ALLOCATE_LIST.contains(deliveryHeaderDo.getStatus())){
                throw new WmsOperationException("当前单据状态不允许取消分配");
            }
            if (WmsOutBoundStatusEnum.INIT.getStatus().equals(deliveryHeaderDo.getStatus())){
                throw new WmsOperationException("当前单据已经完成取消分配，无需重复取消！");
            }
            if(StringUtils.isNotBlank(deliveryHeaderDo.getLaunchNo())){
                throw new WmsOperationException("已组波次不允许取消分配");
            }
            tryLockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.CANCEL_ALLOCATED_LOCK, deliveryOrderCode);
            if (!tryLockFlag) {
                log.info("取消库存分配并发:{}", deliveryOrderCode);
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo =
                    buildCancelPartAllocate(deliveryOrderCode, deliveryDetailNo, tenantCode,Boolean.TRUE);
            Boolean flag = inventoryOperateService.cancelAllocatedInventory(inventoryAllocatedCancelPojo);
            if (flag) {
                if (StringUtils.isNotBlank(deliveryDetailNo)) {
                    /*取消其中一条明细*/
                    deliveryDetailRepository.updateDeliveryDetailAllocateQtyByDetailNo(deliveryDetailNo);
                } else {
                    /*取消所有详情*/
                    deliveryDetailRepository.updateDeliveryDetailAllocateQty(deliveryOrderCode);
                }
                /*更新deliveryHeader中status*/
                List<DeliveryDetailDo> deliveryDetailEntityList = deliveryDetailRepository.queryDeliveryDetailExcludeCancelByDeliveryCode(deliveryOrderCode);
                long count = deliveryDetailEntityList.stream().filter(item -> item.getAllocatedQty() > 0).count();
                DeliveryModifyStatusPojo pojo = new DeliveryModifyStatusPojo();
                pojo.setDeliveryOrderCode(deliveryOrderCode);
                if (count > 0) {
                    pojo.setDeliveryStatus(WmsOutBoundStatusEnum.PART_ALLOCATE);
                } else {
                    pojo.setDeliveryStatus(WmsOutBoundStatusEnum.INIT);
                }
                this.modifyDeliveryOrderStatusAndLock(pojo);

            }
            log.info("取消库存分配 request:{},result:{}", JSON.toJSON(inventoryAllocatedCancelPojo), flag);
            return flag;
        } catch (WmsOperationException w) {
            log.warn("取消库存分配异常:", w);
            throw w;
        }  catch (Exception e) {
            log.error("取消库存分配error:", e);
            throw e;
        } finally {
            if (tryLockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.CANCEL_ALLOCATED_LOCK, deliveryOrderCode);
            }
        }
    }

    /**
     * 不允许分配库存
     *
     * @param deliveryHeaderEntity
     * @return
     */
    private boolean cannotAllocateInventory(DeliveryHeaderDo deliveryHeaderEntity) {
        if (WmsOutBoundTypeEnum.NEED_DELIVERY_PERMISSION_TYPE.contains(deliveryHeaderEntity.getType())){
            return true;
        }
        if (WmsOutBoundTypeEnum.THCK.getType().equals(deliveryHeaderEntity.getType()) && (
                WmsBizTypeEnum.XIAN_HUO.getBizType().equals(deliveryHeaderEntity.getBizType()) ||
                        WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(deliveryHeaderEntity.getBizType()) ||
                        WmsBizTypeEnum.KUA_JING_XIAN_HUO.getBizType().equals(deliveryHeaderEntity.getBizType())
        )){
            return !isSpotAdditional(deliveryHeaderEntity) && !deliveryAllocateWarehouseConfig.isOverseaOrCrosseWarehouse(deliveryHeaderEntity.getWarehouseCode()) && !isAllocateMode(deliveryHeaderEntity.getDeliveryOrderCode());
        }
        return false;
    }

    private boolean isAllocateMode(String deliveryOrderCode) {
        try {
            DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
            if (Objects.isNull(deliveryExtraInfoDo)) {
                return false;
            }
            if (Objects.equals(deliveryExtraInfoDo.getAllocateMode(), DeliveryHeaderInventoryAllocateModeEnum.PRE_ALLOCATE.getValue())) {
                return true;
            }
        } catch (Exception e) {
            log.error("check isAllocateMode error, deliveryOrderCode:{}", deliveryOrderCode, e);
        }
        return false;
    }

    /**
     * 现货加价购
     *
     * @param delivery
     * @return
     */
    private boolean isSpotAdditional(DeliveryHeaderDo delivery) {
        return DeliveryTagV2Enum.getCodesByBitVal(delivery.getOrderTags()).contains(DeliveryTagV2Enum.XH_ADDITIONAL_ORDER.getCode());
    }



    /**
     * 取消库存分配
     */
    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInventoryAllocation(String deliveryOrderCode,
                                             String deliveryDetailNo,
                                             String tenantCode,
                                             Boolean isDeleteAllocateRecord) {
        boolean tryLockFlag = false;
        try {

            DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
            if (Objects.nonNull(deliveryHeaderDo) && WmsOutBoundTypeEnum.QLCK.getType().equals(deliveryHeaderDo.getType())) {
                throw new WmsException("清退单不允许取消分配");
            }

            tryLockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.CANCEL_ALLOCATED_LOCK, deliveryOrderCode);
            if (!tryLockFlag) {
                log.info("取消库存分配并发:{}", deliveryOrderCode);
                throw new WmsException(WmsExceptionCode.REPEAT_OPERATION);
            }

            InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo =
                    buildCancelPartAllocate(deliveryOrderCode, deliveryDetailNo, tenantCode,isDeleteAllocateRecord);


            Boolean flag = inventoryOperateService.cancelAllocatedInventory(inventoryAllocatedCancelPojo);
            log.info("取消库存分配 request:{},result:{}", JSON.toJSON(inventoryAllocatedCancelPojo), flag);
            return flag;

        } catch (WmsException exception) {
            log.error("取消库存分配error:", exception);
            throw exception;
        } finally {
            if (tryLockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.CANCEL_ALLOCATED_LOCK, deliveryOrderCode);
            }
        }
    }



    @Override
    public void modifyDeliveryReturnShelfStatus(DeliveryModifyReturnShelfStatusPojo pojo) {
        log.info("modifyDeliveryReturnShelfStatus-message:{}", JSON.toJSONString(pojo));
        DeliveryHeaderEntity entity = deliveryOrderQueryService.queryDeliveryInfo(pojo.getDeliveryOrderCode());
        if (entity == null) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        if (pojo.getReturnShelfStatus() == null) {
            throw new WmsException("单据返架状态不能为空");
        }
        DeliveryHeaderEntity updateDeliveryHeaderEntity = new DeliveryHeaderEntity();
        updateDeliveryHeaderEntity.setId(entity.getId());
        updateDeliveryHeaderEntity.setReturnShelfStatus(pojo.getReturnShelfStatus().getStatus());
        int rows = deliveryHeaderMapper.updateById(updateDeliveryHeaderEntity);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
        }
    }

    /**
     * 根据出库单状态为完全分配
     */
    private void updateAllocatedStatus(DeliveryHeaderEntity entity) {
        DeliveryHeaderEntity deliveryHeaderEntity = new DeliveryHeaderEntity();
        UpdateWrapper<DeliveryHeaderEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(DeliveryHeaderEntity.COL_ID, entity.getId());
        updateWrapper.ne(DeliveryHeaderEntity.COL_COMMAND_STATUS, WmsOutCommandStatusEnum.CANCEL.getStatus());
        deliveryHeaderEntity.setStatus(WmsOutBoundStatusEnum.WHOLE_ALLOCATE.getStatus());
        int rows = deliveryHeaderMapper.update(deliveryHeaderEntity, updateWrapper);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.BOUND_CANCELED);
        }
    }

    /**
     * 根据单据为拣货中
     */
    @Override
    public void updatePickStatus(DeliveryHeaderEntity entity, Integer status) {
        DeliveryHeaderEntity deliveryHeaderEntity = new DeliveryHeaderEntity();
        UpdateWrapper<DeliveryHeaderEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(DeliveryHeaderEntity.COL_ID, entity.getId());
        updateWrapper.ne(DeliveryHeaderEntity.COL_COMMAND_STATUS, WmsOutCommandStatusEnum.CANCEL.getStatus());
        deliveryHeaderEntity.setStatus(status);
        int rows = deliveryHeaderMapper.update(deliveryHeaderEntity, updateWrapper);
        if (rows <= 0) {
            throw new WmsException(WmsExceptionCode.BOUND_CANCELED);
        }
    }


    private void addDeliveryOperateStatusLog(String deliveryOrderCode,
                                             String warehouseCode,
                                             Integer status) {
        DeliveryStatusDo deliveryStatusDo = new DeliveryStatusDo();
        deliveryStatusDo.setStatus(status);
        deliveryStatusDo.setDeliveryOrderCode(deliveryOrderCode);
        deliveryStatusDo.setWarehouseCode(warehouseCode);
        deliveryStatusRepository.save(deliveryStatusDo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateDeliverTag(DeliveryHeaderDto deliveryHeaderDtoParam) {
        String deliveryOrderCode = deliveryHeaderDtoParam.getDeliveryOrderCode();
        //从数据库查询出订单tag
        DeliveryHeaderDo deliveryHeaderDo  = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        //排除小仓库现货出库单
        if(Objects.isNull(deliveryHeaderDo)
                ||Objects.equals(WmsBizTypeEnum.common.getBizType(),deliveryHeaderDo.getBizType())){
            //根据交易单号发货如果未查询到单据则，则根据交易单号查询明细反差出出库单信息
            deliveryHeaderDo = deliveryHeaderRepository.queryXHDeliveryInfoWithDeliveryDetail(deliveryOrderCode);
        }
        if (Objects.isNull(deliveryHeaderDo)){
            throw new WmsException("出库单不存在");
        }
        Long orderTags = deliveryHeaderDo.getOrderTags();
        deliveryHeaderDtoParam.setOldOrderTags(orderTags);
        deliveryHeaderDtoParam.setDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
        if(orderTags==1L){
            //标准就覆盖
            orderTags = 0L ;
        }
        Long newOrderTags = DeliveryTagV2Enum.getBitValByCodesAndOldValue(deliveryHeaderDtoParam.getTag(), orderTags);
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setOrderTags(newOrderTags);
        int updateResult = deliveryHeaderRepository.updateDeliverByCode(deliveryHeaderDtoParam,updateDeliveryHeaderDo);
        if(updateResult<1){
            throw new WmsException("更新订单标记失败");
        }
        return updateResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDeliveryPermission(String deliveryOrderCode,Integer sourceStatus, Integer targetStatus) {

        DeliveryPermissionUpdateParam updateParam = DeliveryPermissionUpdateParam.builder()
                .deliveryOrderCode(deliveryOrderCode)
                .sourceStatus(sourceStatus)
                .targetStatus(targetStatus)
                .build();

        int updateResult = deliveryPermissionRepository.updateAllocatedStatusWithLock(updateParam);
        Preconditions.checkBiz(updateResult == 1, "更新出库许可失败");
    }

    @Override
    public void updateDeliveryExtraInfo(DeliveryExtraInfoUpdateParam updateParam) {
        deliveryExtraInfoRepository.updateByCondition(updateParam);
    }

    @Override
    public void insertSelectiveDeliveryExtraInfo(DeliveryExtraInfoDo deliveryExtraInfoDo) {
        deliveryExtraInfoRepository.insertSelective(deliveryExtraInfoDo);
    }
}
