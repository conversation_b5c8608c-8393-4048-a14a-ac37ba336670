package com.poizon.scm.wms.service.shipping;

import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.WmsDeliveryDetailCancelEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2021/6/18
 */
@Service
@Slf4j
public class CombineDeliveryShipOrderService {

    @Autowired
    private ShipTaskQueryRepository shipTaskQueryRepository;

    @Autowired
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Transactional(rollbackFor = Exception.class)
    public void updateDeliveryHeaderIfNecessary(String deliveryOrderCode) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (WmsOutBoundStatusEnum.outCompletedStatus.contains(deliveryHeaderDo.getStatus())) {
            log.info("单据{}状态已经完成", deliveryOrderCode);
            return;
        }
        List<ShipTaskDo> shipTasks = shipTaskQueryRepository.findByReferenceNo(deliveryOrderCode);
        if (CollectionUtils.isEmpty(shipTasks)) {
            log.error("更新合并单{}状态的时候发现发货任务不存在", deliveryOrderCode);
            throw new WmsException(WmsExceptionCode.SHIP_TASK_NOT_EXIST);
        }
        ShipTaskDo task = shipTasks.get(0);
        log.info("单据{}实际发货数量{}", deliveryOrderCode, deliveryHeaderDo.getTotalActualQty());
        if (TaskStatusEnum.shipFinishedList.contains(task.getStatus())) {
            log.info("发货单{}的发货任务已经完成,需要更新单据状态{}", deliveryOrderCode, WmsOutBoundStatusEnum.OUTED.getStatus());
            DeliveryHeaderDo updated = new DeliveryHeaderDo();
            updated.setId(deliveryHeaderDo.getId());
            updated.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());
            updated.setOutTime(new Date());
            updated.setTotalActualQty(task.getTotalQty());
            deliveryHeaderRepository.updateById(updated);
        }else {
            log.info("发货单{}的发货任务还未完成,需要更新单据状态{}", deliveryOrderCode, WmsOutBoundStatusEnum.OUTING.getStatus());
            deliveryHeaderRepository.updateStatusByDeliveryCode(deliveryOrderCode,WmsOutBoundStatusEnum.OUTING.getStatus());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelUpdateDeliveryHeaderIfNecessary(String deliveryOrderCode) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (WmsOutBoundStatusEnum.outCompletedStatus.contains(deliveryHeaderDo.getStatus())) {
            log.info("单据状态已达终态:{}", deliveryOrderCode);
            return;
        }

        List<DeliveryDetailDo> deliveryDetailDoList =
                deliveryDetailRepository.queryDeliveryDetailByDeliveryCodeWithCancel(deliveryOrderCode);

        if (CollectionUtils.isEmpty(deliveryDetailDoList)) {
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY_ERROR);
        }

        List<DeliveryDetailDo> cancelList = deliveryDetailDoList.stream().filter(item ->
                WmsDeliveryDetailCancelEnum.CANCEL.getStatus().compareTo(item.getCancelFlag()) == 0).collect(Collectors.toList());

        // 明细全部取消，更新单据状态为取消状态
        if (CollectionUtils.isNotEmpty(cancelList) && cancelList.size() == deliveryDetailDoList.size()){
            log.info("发货单明细已全部取消,需要更新单据状态:{}", deliveryOrderCode);
            deliveryHeaderRepository.updateStatusByDeliveryCode(deliveryOrderCode, WmsOutBoundStatusEnum.CANCEL.getStatus());
        }
        //只取消了部分明细，剩下的可能是均已出库完成，此时要去查对应的发货任务状态来决定是否更新单据状态至终态
        else {
            //移除已取消的明细，留下其他出库明细，待确认是否均已出库完成
            deliveryDetailDoList.removeAll(cancelList);
            List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.queryByReferenceDetailNos(deliveryDetailDoList.stream().map(
                    DeliveryDetailDo::getDetailNo).distinct().collect(Collectors.toList()));
            //为取消的明细中筛选出出库完成的明细
            List<ShipTaskDetailDo> completeShipTaskDetailList = null;
            if (CollectionUtils.isNotEmpty(shipTaskDetailDos)) {
                completeShipTaskDetailList = shipTaskDetailDos.stream().filter(item ->
                        TaskStatusEnum.COMPLETE.getStatus().compareTo(item.getStatus().intValue()) == 0).collect(Collectors.toList());
            }
            //如果发现未取消的明细均已出库，则更新单据状态至出库完成终态
            if (CollectionUtils.isNotEmpty(completeShipTaskDetailList) && completeShipTaskDetailList.size() == shipTaskDetailDos.size()){
                log.info("发货单{}的发货任务已经完成,需要更新单据状态", deliveryOrderCode);
                DeliveryHeaderDo updated = new DeliveryHeaderDo();
                updated.setId(deliveryHeaderDo.getId());
                updated.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());
                updated.setOutTime(new Date());
                deliveryHeaderRepository.updateById(updated);
            }

        }

    }
}
