package com.poizon.scm.wms.service.shipping;

import com.poizon.scm.wms.adapter.outbound.exception.model.DeliveryCancelExceptionDetailDo;
import com.poizon.scm.wms.adapter.outbound.exception.repository.DeliveryCancelExceptionDetailRepository;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.domain.task.TaskFactory;
import com.poizon.scm.wms.pojo.task.operation.TaskOperationPojo;
import com.poizon.scm.wms.pojo.task.operation.modify.CancelTaskPojo;
import com.poizon.scm.wms.service.shipping.param.DeliveryCancelNotifyReturnShelfParam;
import com.poizon.scm.wms.service.shipping.producer.DeliveryCancelNotifyReturnShelfProducer;
import com.poizon.scm.wms.util.common.ReturnSalesCenterProperties;
import com.poizon.scm.wms.util.common.WarehouseSmallProperties;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2020/9/20 2:29 下午
 **/
@Slf4j
@Service
public class ShippingCancelService {

    @Autowired
    private TaskFactory<TaskOperationPojo> taskFactory;

    @Resource
    private DeliveryCancelExceptionDetailRepository deliveryCancelExceptionDetailRepository;

    @Resource
    private WarehouseSmallProperties warehouseSmallProperties;

    @Resource
    private ReturnSalesCenterProperties returnSalesCenterProperties;

    @Resource
    private DeliveryCancelNotifyReturnShelfProducer deliveryCancelNotifyReturnShelfProducer;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void executeCancel(String taskNo, String referenceNo, String referenceType) {
        //取消任务
        CancelTaskPojo cancelTaskPojo = new CancelTaskPojo();
        cancelTaskPojo.setTaskStatusEnum(TaskStatusEnum.CANCEL);
        cancelTaskPojo.setTaskTypeEnum(TaskTypeEnum.SHIP);
        cancelTaskPojo.setTaskNo(taskNo);
        cancelTaskPojo.setReferenceType(referenceType);
        cancelTaskPojo.setReferenceNo(referenceNo);
        taskFactory.process(cancelTaskPojo);

        //查询是否有取消明细
        List<DeliveryCancelExceptionDetailDo> list = deliveryCancelExceptionDetailRepository
                .queryByDeliveryOrder(referenceNo, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String warehouseCode = list.get(0).getWarehouseCode();
        if (warehouseSmallProperties.isSmallWarehouse(warehouseCode) || returnSalesCenterProperties.ifReturnSalesCenterSmallWarehouseBizType(warehouseCode, list.get(0).getBizType())) {
            //小仓库发货时发现取消通知库内生成返架任务
            deliveryCancelNotifyReturnShelf(list);
        }

    }

    private void deliveryCancelNotifyReturnShelf(List<DeliveryCancelExceptionDetailDo> exceptionDetailDos) {
        for (DeliveryCancelExceptionDetailDo detailDo : exceptionDetailDos) {
            deliveryCancelNotifyReturnShelfProducer.produce(
                    DeliveryCancelNotifyReturnShelfParam.builder()
                            .bizType(detailDo.getBizType())
                            .deliveryOrderCode(detailDo.getDeliveryOrderCode())
                            .operateUserId(OperationUserContextHolder.get().getUserId())
                            .qty(detailDo.getQty())
                            .requestId(detailDo.getUniqueCode())
                            .tradeOrderNo(detailDo.getDeliveryOrderCode())
                            .stationNo(OperationUserContextHolder.get().getStationNo())
                            .uniqueCode(detailDo.getUniqueCode())
                            .warehouseCode(detailDo.getWarehouseCode())
                            .tenantId(detailDo.getTenantId())
                            .build());
        }
    }
}
