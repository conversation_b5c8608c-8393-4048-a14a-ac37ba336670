package com.poizon.scm.wms.service.delivery.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.poizon.scm.pink.sdk.enums.YesOrNoEnum;
import com.poizon.scm.wms.adapter.apply.model.HandoverApplyHeaderDo;
import com.poizon.scm.wms.adapter.apply.repository.HandoverApplyHeaderRepository;
import com.poizon.scm.wms.adapter.inbound.entry.model.EntryHeaderDo;
import com.poizon.scm.wms.adapter.inbound.entry.repository.db.query.EntryHeaderRepository;
import com.poizon.scm.wms.adapter.inbound.received.model.ReceivedTaskDo;
import com.poizon.scm.wms.adapter.inbound.received.repository.db.query.ReceivedTaskQueryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.*;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.*;
import com.poizon.scm.wms.adapter.outbound.remark.model.MergeShipmentReportDo;
import com.poizon.scm.wms.adapter.outbound.report.repository.MergeShipmentReportRepository;
import com.poizon.scm.wms.adapter.upper.model.UpperHeaderDo;
import com.poizon.scm.wms.adapter.upper.repository.UpperHeaderRepository;
import com.poizon.scm.wms.api.dto.request.delivery.DeliveryAllocatedRequest;
import com.poizon.scm.wms.api.dto.response.base.BasTypeResponse;
import com.poizon.scm.wms.codeclean.TaskAccessCodeCleanConfig;
import com.poizon.scm.wms.codeclean.TaskAccessCodeCleanKey;
import com.poizon.scm.wms.common.enums.DeliveryHeaderInventoryAllocateModeEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.dao.entitys.DeliveryDetailEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryHeaderEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryStatusEntity;
import com.poizon.scm.wms.dao.mappers.DeliveryStatusMapper;
import com.poizon.scm.wms.domain.inventory.config.InventoryAllocateConfig;
import com.poizon.scm.wms.domain.outbound.report.OutboundOrderEvent;
import com.poizon.scm.wms.infra.batchhelper.BatchHelper;
import com.poizon.scm.wms.infra.outbound.report.mapper.MergeShipmentReportDoExtMapper;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedItemPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedOrderPojo;
import com.poizon.scm.wms.service.base.BasWarehouseService;
import com.poizon.scm.wms.service.delivery.config.DeliveryAllocateWarehouseConfig;
import com.poizon.scm.wms.service.delivery.query.DeliveryOrderQueryService;
import com.poizon.scm.wms.util.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeliveryOrderCommonService {
    @Autowired
    private DeliveryStatusMapper deliveryStatusMapper;
    @Autowired
    private DeliveryOrderQueryService deliveryOrderQueryService;
    @Resource
    private DeliveryLogisticRepository deliveryLogisticRepository;
    @Autowired
    DeliveryStatusRepository deliveryStatusRepository;
    @Resource
    private BatchHelper batchHelper;
    @Resource
    private MergeShipmentReportRepository mergeShipmentReportRepository;
    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Resource
    private DeliveryDetailRepository deliveryDetailRepository;
    @Resource
    private HandoverApplyHeaderRepository handoverApplyHeaderRepository;
    @Resource
    private BasWarehouseService basWarehouseService;
    @Resource
    private EntryHeaderRepository entryHeaderRepository;
    @Resource
    private UpperHeaderRepository upperHeaderRepository;
    @Resource
    private ReceivedTaskQueryRepository receivedTaskQueryRepository;
    @Resource
    private DeliveryExtraInfoRepository deliveryExtraInfoRepository;
    @Resource
    protected DeliveryAllocateWarehouseConfig deliveryAllocateWarehouseConfig;
    @Resource
    private InventoryAllocateConfig inventoryAllocateConfig;

    @Resource
    private TaskAccessCodeCleanConfig taskAccessCodeCleanConfig;

    /**
     * 拣货完成事件处理程序
     * @param outboundOrderEvent 出库单事件
     */
    public void outboundOrderPickedEventHandler(OutboundOrderEvent outboundOrderEvent){
        String shipmentOrderNo = outboundOrderEvent.getShipmentOrderNo();
        String tenantId = outboundOrderEvent.getTenantId();
        List<String> shipmentDetailNos = outboundOrderEvent.getShipmentDetailNos();
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(shipmentOrderNo,tenantId);
        if(Objects.isNull(deliveryHeader)){
            log.warn("拣货完成合并发货报表记录出库单为空");
            return;
        }
        for (String shipmentDetailNo:shipmentDetailNos){
            MergeShipmentReportDo mergeShipmentReport = mergeShipmentReportRepository
                    .selectMergeShipmentReportByShipmentNoAndDetailNo(tenantId,shipmentOrderNo,shipmentDetailNo);
            if(Objects.isNull(mergeShipmentReport)){
                log.warn("拣货完成合并发货报表记录不存在");
                return;
            }
            MergeShipmentReportDo mergeShipmentReportEdit = new MergeShipmentReportDo();
            mergeShipmentReportEdit.setId(mergeShipmentReport.getId());
            mergeShipmentReportEdit.setCollectPickTime(outboundOrderEvent.getOperationTime());
            mergeShipmentReportEdit.setStatus(deliveryHeader.getStatus());
            mergeShipmentReportRepository.updateByPrimaryKeySelective(mergeShipmentReportEdit);
            if(WmsOutBoundStatusEnum.pickComplete.contains(deliveryHeader.getStatus())){
                //如果是拣货完成状态则更新所有的明细为拣货完成
                mergeShipmentReportRepository.updateStatusByShipmentNo(tenantId,shipmentOrderNo,deliveryHeader.getStatus());
            }
        }
    }

    /**
     * 发货完成事件处理程序
     * @param outboundOrderEvent 出库单事件
     */
    public void outboundOrderShipedEventHandler(OutboundOrderEvent outboundOrderEvent){
        String shipmentOrderNo = outboundOrderEvent.getShipmentOrderNo();
        String tenantId = outboundOrderEvent.getTenantId();
        List<String> shipmentDetailNos = outboundOrderEvent.getShipmentDetailNos();
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(shipmentOrderNo,tenantId);
        if(Objects.isNull(deliveryHeader)){
            log.warn("发货完成合并发货报表记录出库单为空");
            return;
        }
        DeliveryLogisticDo deliveryLogisticDo = deliveryLogisticRepository.queryFirstOneByDeliveryOrderCode(shipmentOrderNo);

        for (String shipmentDetailNo:shipmentDetailNos){
            MergeShipmentReportDo mergeShipmentReport = mergeShipmentReportRepository
                    .selectMergeShipmentReportByShipmentNoAndDetailNo(tenantId,shipmentOrderNo,shipmentDetailNo);
            if(Objects.isNull(mergeShipmentReport)){
                log.warn("发货完成合并发货报表记录不存在");
                return;
            }
            MergeShipmentReportDo mergeShipmentReportEdit = new MergeShipmentReportDo();
            mergeShipmentReportEdit.setId(mergeShipmentReport.getId());
            mergeShipmentReportEdit.setCollectDeliveryTime(outboundOrderEvent.getOperationTime());
            mergeShipmentReportEdit.setCollectDeliveryUserId(outboundOrderEvent.getOperationUserId());
            mergeShipmentReportEdit.setCollectDeliveryUserName(outboundOrderEvent.getOperationUserName());
            mergeShipmentReportEdit.setStatus(deliveryHeader.getStatus());
            if(Objects.nonNull(deliveryLogisticDo)){
                mergeShipmentReportEdit.setExpressNo(deliveryLogisticDo.getExpressCode());
            }
            mergeShipmentReportRepository.updateByPrimaryKeySelective(mergeShipmentReportEdit);
            if(WmsOutBoundStatusEnum.outComplete.contains(deliveryHeader.getStatus())){
                //如果是发货完成状态则更新所有的明细为发货完成
                mergeShipmentReportRepository.updateStatusByShipmentNo(tenantId,shipmentOrderNo,deliveryHeader.getStatus());
            }
        }
    }


    /**
     * 出库单取消事件处理程序
     *
     * @param outboundOrderEvent 出库单事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void outboundOrderCancelEventHandler(OutboundOrderEvent outboundOrderEvent){
        String tenantId = outboundOrderEvent.getTenantId();
        String shipmentOrderNo = outboundOrderEvent.getShipmentOrderNo();
        List<String> shipmentDetailNos = outboundOrderEvent.getShipmentDetailNos();
        for (String shipmentDetailNo:shipmentDetailNos){
            MergeShipmentReportDo mergeShipmentReportDo = mergeShipmentReportRepository.selectMergeShipmentReportByShipmentNoAndDetailNo(tenantId,shipmentOrderNo,shipmentDetailNo);
            if(Objects.isNull(mergeShipmentReportDo)){
                continue;
            }
            mergeShipmentReportRepository.updateStatusByShipmentNoAndDetailNo(tenantId,shipmentOrderNo,shipmentDetailNo,
                    WmsOutBoundStatusEnum.CANCEL.getStatus());
        }
    }

    /**
     * 新增合并发货报表逻辑
     *
     * @param shipmentOrderNo 出库单号
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void outboundCreatedEventHandler(String shipmentOrderNo,String tenantId){
        List<MergeShipmentReportDo> mergeShipmentReports = mergeShipmentReportRepository.selectMergeShipmentReportByShipmentNo(tenantId,shipmentOrderNo);
        if(CollectionUtils.isNotEmpty(mergeShipmentReports)){
            log.warn("新增合并发货报表记录已存在");
            return;
        }
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(shipmentOrderNo,tenantId);
        if(Objects.isNull(deliveryHeader)){
            log.warn("新增合并发货报表记录出库单为空");
            return;
        }
        List<DeliveryDetailDo> deliveryDetails = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(shipmentOrderNo,tenantId);
        if(CollectionUtils.isEmpty(deliveryDetails)){
            log.warn("新增合并发货报表记录出库单明细为空");
            return;
        }
        //根据交易单号查询出合并的明细订单目的就是为了取订单创建时间
        Set<String> tradeOrderNoSet = deliveryDetails.stream().map(DeliveryDetailDo::getTradeOrderNo)
                .collect(Collectors.toSet());
        List<DeliveryHeaderDo> deliveryHeaderList = deliveryHeaderRepository.queryBatchDeliveryInfo(tradeOrderNoSet);
        //合并单明细订单的订单创建时间map
        Map<String, Date> tradeOrderCreateTimeMap = deliveryHeaderList.stream()
                .collect(Collectors.toMap(DeliveryHeaderDo::getDeliveryOrderCode, DeliveryHeaderDo::getOrderCreateTime,(key1 , key2)-> key2 ));

        List<MergeShipmentReportDo> mergeShipmentReportDos = new ArrayList<>();
        //保存后的出库单信息
        MergeShipmentReportDo mergeShipmentReport = null;
        for (DeliveryDetailDo deliveryDetail:deliveryDetails) {
            mergeShipmentReport = new MergeShipmentReportDo();
            mergeShipmentReport.setShipmentNo(deliveryDetail.getDeliveryOrderCode());
            mergeShipmentReport.setShipmentDetailNo(deliveryDetail.getDetailNo());
            mergeShipmentReport.setBizType(deliveryDetail.getBizType());
            mergeShipmentReport.setTenantId(deliveryDetail.getTenantCode());
            mergeShipmentReport.setStatus(deliveryHeader.getStatus());
            mergeShipmentReport.setOrderTags(deliveryHeader.getOrderTags());
            mergeShipmentReport.setOrderNo(deliveryDetail.getTradeOrderNo());
            mergeShipmentReport.setUniqueCode(deliveryDetail.getUniqueCode());
            mergeShipmentReport.setSkuId(deliveryDetail.getSkuId());
            mergeShipmentReport.setGoodsTitle(deliveryDetail.getGoodsTitle());
            mergeShipmentReport.setGoodsSpecs(deliveryDetail.getSpecs());
            String tradeOrderNo = deliveryDetail.getTradeOrderNo();
            Date tradeCreateTime = tradeOrderCreateTimeMap.get(tradeOrderNo);
            mergeShipmentReport.setOrderCreateTime(tradeCreateTime);
            mergeShipmentReport.setCollectWarehouseCode(deliveryHeader.getWarehouseCode());
            mergeShipmentReport.setCollectWarehouseName(deliveryHeader.getWarehouseName());
            mergeShipmentReport.setMergeShipmentOrderCreateTime(deliveryHeader.getCreatedTime());
            mergeShipmentReportDos.add(mergeShipmentReport);
            HandoverApplyHeaderDo handoverApplyHeader = handoverApplyHeaderRepository.selectByReferenceNo(deliveryDetail.getTradeOrderNo());
            if(Objects.nonNull(handoverApplyHeader)){
                mergeShipmentReport.setDepositWarehouseCode(handoverApplyHeader.getSourceWarehouseCode());
                BasTypeResponse<String> warehouse = basWarehouseService.warehouseName(handoverApplyHeader.getSourceWarehouseCode());
                mergeShipmentReport.setDepositWarehouseName(warehouse != null ? warehouse.getTypeDesc():"");
                mergeShipmentReport.setMergeHandoverTime(handoverApplyHeader.getCreateTime());
                mergeShipmentReport.setMergeHandoverUserId(handoverApplyHeader.getCreatedUserId());
                mergeShipmentReport.setMergeHandoverUserName(handoverApplyHeader.getCreatedRealName());
                //FIXME WYW 2025-04-07 20:32 后续可以删除下面的代码
                if (!taskAccessCodeCleanConfig.hitGray(TaskAccessCodeCleanKey.KEY_DeliveryOrderCommonService_outboundCreatedEventHandler)){
                    List<EntryHeaderDo> entryHeaders = entryHeaderRepository.queryByRelatedOrderList(Lists.newArrayList(deliveryDetail.getTradeOrderNo()));
                    if(CollectionUtils.isNotEmpty(entryHeaders)){
                        EntryHeaderDo entryHeader = entryHeaders.stream().filter(i->Objects.equals(i.getWarehouseCode(),deliveryHeader.getWarehouseCode())).findFirst().orElse(null);;
                        if(Objects.nonNull(entryHeader)){
                            String entryOrderCode = entryHeader.getEntryOrderCode();
                            ReceivedTaskDo receivedTask = receivedTaskQueryRepository.findByReferenceNoLimitOne(entryOrderCode);
                            if (Objects.nonNull(receivedTask)) {
                                mergeShipmentReport.setCollectReceiveTime(receivedTask.getEndTime());
                            }
                            List<UpperHeaderDo> upperHeaders = upperHeaderRepository.selectByReferenceNo(entryOrderCode);
                            if (CollectionUtils.isNotEmpty(upperHeaders)) {
                                UpperHeaderDo upperHeader = upperHeaders.get(0);
                                mergeShipmentReport.setCollectOnShelvesTime(upperHeader.getEndTime());
                            }
                        }
                    }
                }
            }else{
                mergeShipmentReport.setDepositWarehouseCode(deliveryHeader.getWarehouseCode());
                mergeShipmentReport.setDepositWarehouseName(deliveryHeader.getWarehouseName());
            }
        }
        batchHelper.batchExecute(MergeShipmentReportDoExtMapper.class,mergeShipmentReportDos,MergeShipmentReportDoExtMapper::insertSelective);
    }

    /**
     * 出库单状态明细
     */
    public void saveDeliveryStatusRecord(String deliveryOrderCode, Integer status, String warehouseCode) {
        DeliveryStatusEntity entity = new DeliveryStatusEntity();
        entity.setDeliveryOrderCode(deliveryOrderCode);
        entity.setStatus(status);
        entity.setWarehouseCode(warehouseCode);
        deliveryStatusMapper.insert(entity);
    }

    public void addDeliveryOperateStatusLog(String deliveryOrderCode,
                                             String warehouseCode,
                                             Integer status) {
        DeliveryStatusDo deliveryStatusDo = new DeliveryStatusDo();
        deliveryStatusDo.setStatus(status);
        deliveryStatusDo.setDeliveryOrderCode(deliveryOrderCode);
        deliveryStatusDo.setWarehouseCode(warehouseCode);
        deliveryStatusRepository.save(deliveryStatusDo);
    }

    /**
     * 根据出库单号锁定库存
     */
    public InventoryAllocatedOrderPojo buildAllocateInventory(String deliveryOrderCode) {
        DeliveryHeaderEntity deliveryHeaderEntity = deliveryOrderQueryService.queryDeliveryInfo(deliveryOrderCode);
        if (deliveryHeaderEntity == null) {
            throw new WmsException("单据不存在");
        }
        if (cannotAllocateInventory(deliveryHeaderEntity)){
            throw new WmsException("保税二销单据/清退单据/跨园区调拨单据/虚拟出库/个人寄存退货/分流分层退货单据不允许重新分配");
        }
        //校验清退单历史数据不允许手动分配
        checkQTGreyFlag(deliveryHeaderEntity.getDeliveryOrderCode(),deliveryHeaderEntity.getType());
        // 需要把取消的出库单明细给剔除出去
        List<DeliveryDetailEntity> entityList = deliveryOrderQueryService.queryDeliveryDetailByDeliveryCode(deliveryOrderCode);
        entityList = entityList.stream().filter(item -> !WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(item.getCancelFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(entityList)) {
            throw new WmsException("单据详情不存在");
        }
        InventoryAllocatedOrderPojo pojo = new InventoryAllocatedOrderPojo();
        List<InventoryAllocatedItemPojo> listPojo = new ArrayList<>();
        pojo.setShipmentNo(deliveryOrderCode);
        pojo.setTenantCode(deliveryHeaderEntity.getTenantCode());
        pojo.setTaskType(TaskTypeEnum.ALLOCATE.getTaskType());
        pojo.setTotalPlanQty(deliveryHeaderEntity.getTotalPlanQty());
        pojo.setOrderType(deliveryHeaderEntity.getType());
        pojo.setGlobalOrderFlag(DeliveryTagV2Enum.getByBitVal(deliveryHeaderEntity.getOrderTags()).contains(DeliveryTagV2Enum.GLOBAL_SHOPPING));
        for (DeliveryDetailEntity entity : entityList) {
            if ((entity.getPlanQty() - entity.getAllocatedQty()) <= 0) {
                log.info("当前不需要分配的出库详情:{}", JSON.toJSON(entity));
                continue;
            }
            InventoryAllocatedItemPojo inventoryAllocatedItemPojo = new InventoryAllocatedItemPojo();
            inventoryAllocatedItemPojo.setOperateQty(entity.getPlanQty() - entity.getAllocatedQty());
            if (StringUtils.isNotBlank(entity.getUniqueCode())) {
                inventoryAllocatedItemPojo.setUniqueCode(entity.getUniqueCode());
            }
            if (StringUtils.isNotBlank(entity.getOwnerCode())) {
                inventoryAllocatedItemPojo.setOwnerCode(entity.getOwnerCode());
            }
            if (StringUtils.isNotBlank(entity.getQualityLevel())) {
                inventoryAllocatedItemPojo.setQualityLevel(entity.getQualityLevel());
            }
            if (StringUtils.isNotBlank(entity.getTargetEntryOrderCode())) {
                inventoryAllocatedItemPojo.setOriginalOrderCode(entity.getTargetEntryOrderCode());
            }
            if (StringUtils.isNotBlank(deliveryHeaderEntity.getWarehouseCode())) {
                inventoryAllocatedItemPojo.setWarehouseCode(deliveryHeaderEntity.getWarehouseCode());
            }
            if (StringUtils.isNotBlank(entity.getVendorCode())) {
                inventoryAllocatedItemPojo.setVendorCode(entity.getVendorCode());
            }
            if(WmsOutBoundTypeEnum.QLCK.getType().equals(deliveryHeaderEntity.getType())
                    && WmsOutBoundSubTypeEnum.EXPIRY_FORCE_RETURN.getCode().equals(deliveryHeaderEntity.getSubType())){
                //清退出库 下架天数 强制效期清退才有值
                inventoryAllocatedItemPojo.setOutOffStockDay(entity.getOutOffStockDay());
            }
            // 调拨业务时候添加仓库维度
            if(StringUtils.isNotEmpty(entity.getLocationCode())){
                inventoryAllocatedItemPojo.setLocationCode(entity.getLocationCode());
            }
            if (StringUtils.isNotBlank(entity.getOriginalCountry())) {
                inventoryAllocatedItemPojo.setOriginalCountry(entity.getOriginalCountry());
            }
            // DQ上线时，加上了业务类型这个维度的库存维度
            inventoryAllocatedItemPojo.setBizType(entity.getBizType());
            inventoryAllocatedItemPojo.setTenantCode(entity.getTenantCode());
            inventoryAllocatedItemPojo.setSkuId(entity.getSkuId());
            inventoryAllocatedItemPojo.setShipmentDetailNo(entity.getDetailNo());
            inventoryAllocatedItemPojo.setOriginalCountry(entity.getOriginalCountry());
            inventoryAllocatedItemPojo.setInvTag(entity.getInvTag());

            if(BooleanUtils.isTrue(inventoryAllocateConfig.getEnableInventoryTypeAllocate())) {
                InventoryTypeEnum inventoryTypeEnum = InventoryTypeEnum.obtain(entity.getInventoryType());
                if(inventoryTypeEnum != null) {
                    inventoryAllocatedItemPojo.setInventoryType(inventoryTypeEnum.getCode());
                }else if(StringUtils.equals(deliveryHeaderEntity.getType(), WmsOutBoundTypeEnum.JYCK.getType())) {
                    inventoryAllocatedItemPojo.setInventoryType(InventoryTypeEnum.COMMON.getCode());
                }
            }

            listPojo.add(inventoryAllocatedItemPojo);
        }
        pojo.setInventoryAllocatedItemPojo(listPojo);
        return pojo;
    }

    private void checkQTGreyFlag(String deliveryOrderCode,String deliveryType ){
        if(!WmsOutBoundTypeEnum.QLCK.getType().equals(deliveryType)){
            return;
        }
        DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if(deliveryExtraInfoDo == null){
            throw new WmsException("清退单出库额外信息为空");
        }
        if(NumberUtils.INTEGER_ZERO.equals(deliveryExtraInfoDo.getQtGreyFlag())){
            throw new WmsOperationException("历史清退单不允许手动分配");
        }

    }

    /**
     * 不允许分配库存
     *
     * @param deliveryHeaderEntity
     * @return
     */
    public boolean cannotAllocateInventory(DeliveryHeaderEntity deliveryHeaderEntity) {
        if (WmsOutBoundTypeEnum.NEED_DELIVERY_PERMISSION_TYPE.contains(deliveryHeaderEntity.getType())){
            return true;
        }
        if (WmsOutBoundTypeEnum.THCK.getType().equals(deliveryHeaderEntity.getType()) && (
                WmsBizTypeEnum.XIAN_HUO.getBizType().equals(deliveryHeaderEntity.getBizType()) ||
                        WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(deliveryHeaderEntity.getBizType()) ||
                        WmsBizTypeEnum.KUA_JING_XIAN_HUO.getBizType().equals(deliveryHeaderEntity.getBizType())
        )){
            return !isSpotAdditional(deliveryHeaderEntity) && !deliveryAllocateWarehouseConfig.isOverseaOrCrosseWarehouse(deliveryHeaderEntity.getWarehouseCode())
                    && !isAllocateMode(deliveryHeaderEntity.getDeliveryOrderCode());
        }
        return false;
    }

    private boolean isAllocateMode(String deliveryOrderCode) {
        try {
            DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
            if (Objects.isNull(deliveryExtraInfoDo)) {
                return false;
            }
            if (Objects.equals(deliveryExtraInfoDo.getAllocateMode(), DeliveryHeaderInventoryAllocateModeEnum.PRE_ALLOCATE.getValue())) {
                return true;
            }
        } catch (Exception e) {
            log.error("check isAllocateMode error, deliveryOrderCode:{}", deliveryOrderCode, e);
        }
        return false;
    }

    /**
     * 现货加价购
     *
     * @param delivery
     * @return
     */
    private boolean isSpotAdditional(DeliveryHeaderEntity delivery) {
        return DeliveryTagV2Enum.getCodesByBitVal(delivery.getOrderTags()).contains(DeliveryTagV2Enum.XH_ADDITIONAL_ORDER.getCode());
    }


    /**
     * 根据出库单详情锁定库存
     */
    public InventoryAllocatedOrderPojo buildAllocateInventory(DeliveryAllocatedRequest request,
                                                                        DeliveryDetailEntity deliveryDetailEntity){
        DeliveryHeaderEntity deliveryHeaderEntity = deliveryOrderQueryService.queryDeliveryInfo(request.getDeliveryOrderCode());
        if (cannotAllocateInventory(deliveryHeaderEntity)){
            throw new WmsException("保税二销单据/清退单据/跨园区调拨单据/虚拟出库/个人寄存退货单据不允许重新分配");
        }
        //校验清退单历史数据不允许手动分配
        checkQTGreyFlag(deliveryHeaderEntity.getDeliveryOrderCode(),deliveryHeaderEntity.getType());

        if (Objects.equals(deliveryHeaderEntity.getType(),WmsOutBoundTypeEnum.THCK.getType())&&
                Objects.equals(WmsBizTypeEnum.GE_REN_JI_CUN.getBizType(),deliveryHeaderEntity.getBizType())) {
            throw new WmsOperationException("个人寄存退货，不允许库存分配");
        }
        InventoryAllocatedOrderPojo pojo = new InventoryAllocatedOrderPojo();
        List<InventoryAllocatedItemPojo> listPojo = new ArrayList<>();
        pojo.setShipmentNo(deliveryDetailEntity.getDeliveryOrderCode());
        pojo.setTenantCode(deliveryDetailEntity.getTenantCode());
        pojo.setTaskType(TaskTypeEnum.ALLOCATE.getTaskType());
        pojo.setOrderType(deliveryHeaderEntity.getType());
        pojo.setTotalPlanQty(deliveryHeaderEntity.getTotalPlanQty());
        for (DeliveryAllocatedRequest.InventoryAllocated inventoryAllocated:request.getInventoryList()){
            InventoryAllocatedItemPojo inventoryAllocatedItemPojo = new InventoryAllocatedItemPojo();
            inventoryAllocatedItemPojo.setOperateQty(inventoryAllocated.getAllocatedQty());
            inventoryAllocatedItemPojo.setRelativeInventoryNo(inventoryAllocated.getInventoryNo());
            if(StringUtils.isNotBlank(deliveryDetailEntity.getOwnerCode())){
                inventoryAllocatedItemPojo.setOwnerCode(deliveryDetailEntity.getOwnerCode());
            }
            if(StringUtils.isNotBlank(deliveryDetailEntity.getQualityLevel())){
                inventoryAllocatedItemPojo.setQualityLevel(deliveryDetailEntity.getQualityLevel());
            }
            if(StringUtils.isNotBlank(deliveryDetailEntity.getTargetEntryOrderCode())){
                inventoryAllocatedItemPojo.setOriginalOrderCode(deliveryDetailEntity.getTargetEntryOrderCode());
            }
            if(StringUtils.isNotBlank(deliveryDetailEntity.getWarehouseCode())){
                inventoryAllocatedItemPojo.setWarehouseCode(deliveryDetailEntity.getWarehouseCode());
            }
            if(StringUtils.isNotBlank(deliveryDetailEntity.getVendorCode())){
                inventoryAllocatedItemPojo.setVendorCode(deliveryDetailEntity.getVendorCode());
            }
            // 调拨需求,指定库位进行库存分配
            if(StringUtils.isNotBlank(deliveryDetailEntity.getLocationCode())){
                inventoryAllocatedItemPojo.setLocationCode(deliveryDetailEntity.getLocationCode());
            }
            inventoryAllocatedItemPojo.setTenantCode(deliveryDetailEntity.getTenantCode());
            inventoryAllocatedItemPojo.setSkuId(deliveryDetailEntity.getSkuId());
            inventoryAllocatedItemPojo.setShipmentDetailNo(deliveryDetailEntity.getDetailNo());
            inventoryAllocatedItemPojo.setBizType(deliveryDetailEntity.getBizType());
            if(WmsOutBoundTypeEnum.QLCK.getType().equals(deliveryHeaderEntity.getType())&&WmsOutBoundSubTypeEnum.EXPIRY_FORCE_RETURN.getCode().equals(deliveryHeaderEntity.getSubType())){
                //清退出库 下架天数 强制效期清退才有值
                inventoryAllocatedItemPojo.setOutOffStockDay(deliveryDetailEntity.getOutOffStockDay());
            }
            listPojo.add(inventoryAllocatedItemPojo);
        }
        pojo.setInventoryAllocatedItemPojo(listPojo);
        return pojo;

    }

    /**
     * 根据唯一码判断是否需要绑扣
     * @param uniqueCode
     * @param tenantCode
     * @return
     */
    public boolean ifNeedBindAntiFakeCode(String deliveryOrderCode, String uniqueCode, String tenantCode) {
        // 优先从明细上获取
        if (Strings.isNotBlank(uniqueCode)) {
            List<DeliveryDetailDo> deliveryDetailDoList = deliveryDetailRepository.queryDetailListByUniqueCode(uniqueCode, tenantCode);
            if (CollectionUtils.isNotEmpty(deliveryDetailDoList)
                    && Objects.equals(deliveryDetailDoList.get(0).getAntiFakeFlag(), YesOrNoEnum.YES.getId())) {
                return true;
            }
        } else {
            List<DeliveryDetailDo> deliveryDetailDoList = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(deliveryOrderCode, tenantCode);
            if (CollectionUtils.isNotEmpty(deliveryDetailDoList)) {
                boolean existAntiFakeFlag = deliveryDetailDoList.stream()
                        .anyMatch(deliveryDetailDo -> Objects.equals(deliveryDetailDo.getAntiFakeFlag(), YesOrNoEnum.YES.getId()));
                if (existAntiFakeFlag) {
                    return true;
                }
            }
        }

        // 兼容存量订单从单据扩展表上取
        DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryOrderCode);
        if (Objects.nonNull(deliveryExtraInfoDo) && Objects.equals(deliveryExtraInfoDo.getAntiFakeFlag(), AntiFakeFlagEnum.MES_ANTI_FAKE.getStatus())) {
            return true;
        }
        return false;
    }
}
