package com.poizon.scm.wms.domain.outbound.ship.processor.impl;

import com.dewu.doctor.annotation.ZsScan;
import com.poizon.scm.pink.sdk.enums.OperationLinkEnum;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.mes.param.MesDeliveryBeforeParam;
import com.poizon.scm.wms.adapter.outbound.mes.param.MesUserCommonParam;
import com.poizon.scm.wms.adapter.outbound.mes.result.MesDeliveryBeforeResult;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDetailDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.domain.outbound.ship.param.WmsDeliveryOrderShipmentParam;
import com.poizon.scm.wms.domain.outbound.ship.processor.AbsDeliveryOrderShipmentProcessor;
import com.poizon.scm.wms.domain.outbound.ship.processor.DeliveryOrderShipmentProcessor;
import com.poizon.scm.wms.domain.outbound.ship.result.WmsDeliveryOrderShipmentResult;
import com.poizon.scm.wms.service.log.PieceCountService;
import com.poizon.scm.wms.service.log.param.UserPieceCountItemReportBatchParams;
import com.poizon.scm.wms.service.log.param.UserPieceCountItemReportParams;
import com.poizon.scm.wms.util.enums.MesModuleEnums;
import com.poizon.scm.wms.util.enums.PieceCountReferenceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 地址可变的合并发货正向交易一单多包裹发货逻辑
 *
 * <AUTHOR>
 * @date 2023/11/29 21:16
 * @description 目前就合并发货 会出现单独的订单修改地址的情况
 * @Version 1.0
 */
@Slf4j
@Component("SHIPMENT_POSITIVE_ADDRESSABLE_SINGLEORDER_MULTIPACKAGE_PROCESSOR")
public class AddressableSingleOrderMultiPackageShipmentProcessor extends AbsDeliveryOrderShipmentProcessor implements DeliveryOrderShipmentProcessor {
    @Resource
    private PieceCountService pieceCountService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ZsScan(zsCases = {"zs_wms_jyck_ship_more"})
    public WmsDeliveryOrderShipmentResult shipment(WmsDeliveryOrderShipmentParam wmsDeliveryOrderShipmentParam) {
        DeliveryHeaderDo deliveryHeader = wmsDeliveryOrderShipmentParam.getDeliveryHeader();
        List<DeliveryDetailDo> deliveryDetails = wmsDeliveryOrderShipmentParam.getDeliveryDetailList();
        List<WmsPackDo> wmsPackList = wmsDeliveryOrderShipmentParam.getWmsPacks();
        List<WmsPackDetailDo> wmsPackDetails = wmsDeliveryOrderShipmentParam.getWmsPackDetails();
        //过滤出当前要包裹发货包裹出库单detail
        Set<String> deliveryDetailCodes = wmsPackDetails.stream().map(WmsPackDetailDo::getDeliveryDetailCode).collect(Collectors.toSet());
        List<DeliveryDetailDo> filterDeliveryDetails = deliveryDetails.stream().filter(item -> deliveryDetailCodes.contains(item.getDetailNo())).collect(Collectors.toList());
        wmsDeliveryOrderShipmentParam.setDeliveryDetailList(filterDeliveryDetails);
        //礼品使用情况
        MesDeliveryBeforeParam mesDeliveryBeforeParam = buildMesDeliveryBeforeParam(wmsDeliveryOrderShipmentParam);
        MesDeliveryBeforeResult mesDeliveryBeforeResult = deliveryBeforeNotify(mesDeliveryBeforeParam);
        boolean canShip = false;
        if (wmsDeliveryOrderShipmentParam.isDeliveryOrderLastShip() || wmsDeliveryOrderShipmentParam.isSameAddressLastShip()) {
            canShip = true;
        }
        //执行通用发货逻辑
        commonShipment(mesDeliveryBeforeResult, wmsDeliveryOrderShipmentParam, canShip);
        //合并发货计件
        pieceCount(deliveryHeader, wmsPackList, wmsPackDetails, wmsDeliveryOrderShipmentParam);
        //视频埋点
        notifyMesVideoOperateLog(deliveryDetails,wmsDeliveryOrderShipmentParam.getMesUserCommonParam(), OperationLinkEnum.RECHECK);
        //操作日志
        sendMesLogMessageAsync(deliveryHeader,deliveryDetails, wmsDeliveryOrderShipmentParam.getMesUserCommonParam());
        //发货通知mes
        WmsDeliveryOrderShipmentResult wmsDeliveryOrderShipmentResult = new WmsDeliveryOrderShipmentResult();
        //最后一件则提示前端跳转到扫描页
        wmsDeliveryOrderShipmentResult.setBackToScanPageFlag(wmsDeliveryOrderShipmentParam.isDeliveryOrderLastShip());
        wmsDeliveryOrderShipmentResult.setGray(Boolean.TRUE);
        return wmsDeliveryOrderShipmentResult;
    }

    /**
     * 计时计件
     *
     * @param deliveryHeader
     * @param wmsPackList
     * @param wmsPackDetails
     * @param wmsDeliveryOrderShipmentParam
     */
    @ZsScan(zsCases = {"zs_wms_jyck_ship_pieceCount_more"})
    public void pieceCount(DeliveryHeaderDo deliveryHeader,
                            List<WmsPackDo> wmsPackList,
                            List<WmsPackDetailDo> wmsPackDetails,
                            WmsDeliveryOrderShipmentParam wmsDeliveryOrderShipmentParam) {
        String statisticsPoint = "";
        //合并发货计时计件
        if (Objects.equals(wmsDeliveryOrderShipmentParam.getModuleCode(), MesModuleEnums.OUTBOUND_COMBINE_HD.getModuleCode())) {
            statisticsPoint = MesModuleEnums.OUTBOUND_COMBINE_HD.name();
        } else {
            statisticsPoint = MesModuleEnums.OUTBOUND_COMBINE.name();
        }
        UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParams = buildUserPieceCountItemReportBatchParams(deliveryHeader, statisticsPoint, wmsDeliveryOrderShipmentParam.getMesUserCommonParam(), wmsPackList, wmsPackDetails);
        pieceCountService.batchPieceCountMsg(userPieceCountItemReportBatchParams);
    }


    /**
     * 组装计件消息
     *
     * @param deliveryHeader
     * @param statisticsPoint
     * @param wmsPackList
     * @param wmsPackDetails
     * @return {@link UserPieceCountItemReportBatchParams}
     */
    protected UserPieceCountItemReportBatchParams buildUserPieceCountItemReportBatchParams(DeliveryHeaderDo deliveryHeader,
                                                                                           String statisticsPoint,
                                                                                           MesUserCommonParam mesUserCommonParam,
                                                                                           List<WmsPackDo> wmsPackList,
                                                                                           List<WmsPackDetailDo> wmsPackDetails) {
        Set<String> subWaybillNumberSet = wmsPackList.stream().map(WmsPackDo::getExpressCode).collect(Collectors.toSet());
        Map<String, List<WmsPackDo>> wmsPackMapGroupByPackWaybillNumber = wmsPackList.stream().collect(Collectors.groupingBy(WmsPackDo::getExpressCode));
        //自运单号分组
        Map<String, List<WmsPackDetailDo>> wmsPackDetailMapGroupByPackNo = wmsPackDetails.stream().collect(Collectors.groupingBy(WmsPackDetailDo::getPackNo));
        List<UserPieceCountItemReportParams> userPieceCountItemReportParamsList = new ArrayList<>();
        UserPieceCountItemReportBatchParams userPieceCountItemReportBatchParams = new UserPieceCountItemReportBatchParams();
        userPieceCountItemReportBatchParams.setBatchNo(deliveryHeader.getDeliveryOrderCode());
        userPieceCountItemReportBatchParams.setUserPieceCountItemReportParams(userPieceCountItemReportParamsList);
        UserPieceCountItemReportParams userPieceCountItemReportParams = null;
        List<UserPieceCountItemReportParams.LmsPieceCountDetail> pieceCountDetails = null;
        UserPieceCountItemReportParams.LmsPieceCountDetail lmsPieceCountDetail = null;
        for (String subWaybillNumber : subWaybillNumberSet) {
            List<WmsPackDo> wmsPackDos = wmsPackMapGroupByPackWaybillNumber.get(subWaybillNumber);
            int count = wmsPackDos.stream().mapToInt(wmsPackDo -> {
                List<WmsPackDetailDo> tempWmsPackDetails = wmsPackDetailMapGroupByPackNo.get(wmsPackDo.getPackNo());
                return tempWmsPackDetails.stream().mapToInt(WmsPackDetailDo::getQty).sum();
            }).sum();
            userPieceCountItemReportParams = new UserPieceCountItemReportParams();
            userPieceCountItemReportParams.setTenantId(deliveryHeader.getTenantCode());
            userPieceCountItemReportParams.setSignId(mesUserCommonParam.getSignId());
            userPieceCountItemReportParams.setUserId(mesUserCommonParam.getUserId());
            userPieceCountItemReportParams.setAssistMasterUserId(mesUserCommonParam.getUserId());
            userPieceCountItemReportParams.setUniqueKey(subWaybillNumber);
            userPieceCountItemReportParams.setStatisticsPoint(statisticsPoint);
            userPieceCountItemReportParams.setOperationTime(new Date());
            userPieceCountItemReportParams.setCount(count);
            userPieceCountItemReportParams.setCategoryId(NumberUtils.INTEGER_ZERO.toString());
            userPieceCountItemReportParams.setCategoryName("无商品类目");
            userPieceCountItemReportParams.setLevel3CategoryId("");
            userPieceCountItemReportParams.setLevel3CategoryName("");
            userPieceCountItemReportParams.setOperateWarehouseCode(mesUserCommonParam.getOperationRepositoryCode());
            userPieceCountItemReportParams.setReferenceType(PieceCountReferenceTypeEnum.OUTBOUND_EXPRESS.getCode());
            userPieceCountItemReportParams.setReferenceNo(subWaybillNumber);
            userPieceCountItemReportParams.setReferenceDetailNo(subWaybillNumber);
            userPieceCountItemReportParams.setAppId("PINK");
            pieceCountDetails = new ArrayList<>();
            lmsPieceCountDetail = new UserPieceCountItemReportParams.LmsPieceCountDetail();
            lmsPieceCountDetail.setExpressCode(subWaybillNumber);
            pieceCountDetails.add(lmsPieceCountDetail);
            userPieceCountItemReportParams.setDetailContents(pieceCountDetails);
            userPieceCountItemReportParamsList.add(userPieceCountItemReportParams);
        }
        return userPieceCountItemReportBatchParams;
    }
}
