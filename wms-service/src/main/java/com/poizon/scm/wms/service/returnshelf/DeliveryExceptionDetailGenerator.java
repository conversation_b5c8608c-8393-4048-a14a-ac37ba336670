package com.poizon.scm.wms.service.returnshelf;

import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.wms.adapter.container.model.ContainerDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.exception.model.DeliveryCancelExceptionDetailDo;
import com.poizon.scm.wms.adapter.outbound.exception.repository.DeliveryCancelExceptionDetailRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.returnshelf.model.DeliveryExceptionDetailDo;
import com.poizon.scm.wms.adapter.returnshelf.param.DeliveryExceptionDetailQueryParam;
import com.poizon.scm.wms.adapter.returnshelf.repository.DeliveryExceptionDetailRepository;
import com.poizon.scm.wms.api.enums.DeliveryCancelExceptionDetailStatusEnum;
import com.poizon.scm.wms.api.enums.DeliveryExceptionDetailStatusEnum;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.dao.entitys.DeliveryHeaderEntity;
import com.poizon.scm.wms.dao.entitys.InvInventoryEntity;
import com.poizon.scm.wms.domain.inner.container.instance.ContainerQueryService;
import com.poizon.scm.wms.domain.inner.exception.assembler.ExceptionCancelAssembler;
import com.poizon.scm.wms.domain.inner.exception.param.GenerateCancelDetailParam;
import com.poizon.scm.wms.domain.inner.exception.processor.DeliveryCancelExceptionDetailStatusChangeProcessor;
import com.poizon.scm.wms.domain.inner.exception.processor.ReturnShelfNotifyInnerUpdateProcessor;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.pojo.task.BaseDetailResultPojo;
import com.poizon.scm.wms.pojo.task.BaseTaskDetailPojo;
import com.poizon.scm.wms.pojo.task.param.TaskDetailQueryPojo;
import com.poizon.scm.wms.service.config.DeliveryCancelContainerConfig;
import com.poizon.scm.wms.service.delivery.query.DeliveryOrderQueryService;
import com.poizon.scm.wms.service.inventory.query.InventoryQueryService;
import com.poizon.scm.wms.service.shipping.param.DeliveryCancelNotifyReturnShelfParam;
import com.poizon.scm.wms.service.shipping.producer.DeliveryCancelNotifyReturnShelfProducer;
import com.poizon.scm.wms.service.task.TaskQueryService;
import com.poizon.scm.wms.util.enums.DeliveryReturnShelfStatusEnum;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.duapp.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Deacription 返架记录生成器
 * <AUTHOR>
 * @Date 2020/10/22 4:47 下午
 **/
@Service
@Slf4j
public class DeliveryExceptionDetailGenerator {

    @Autowired
    private DeliveryOrderQueryService deliveryOrderQueryService;
    @Autowired
    private DeliveryExceptionDetailRepository deliveryExceptionDetailRepository;
    @Autowired
    private DeliveryCancelExceptionDetailStatusChangeProcessor deliveryCancelExceptionDetailStatusChangeProcessor;
    @Autowired
    private TaskQueryService taskQueryService;
    @Autowired
    private InventoryQueryService inventoryQueryService;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private DeliveryCancelContainerConfig deliveryCancelContainerConfig;
    @Autowired
    private DeliveryCancelExceptionDetailRepository deliveryCancelExceptionDetailRepository;
    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    private ContainerQueryService containerQueryService;
    @Autowired
    private ReturnShelfNotifyInnerUpdateProcessor returnShelfNotifyInnerUpdateProcessor;
    @Resource
    private DeliveryCancelNotifyReturnShelfProducer deliveryCancelNotifyReturnShelfProducer;
    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Resource
    private OutboundMessageProducer outboundMessageProducer;

    /**
     * 根据订单号获需要返架的商品生成明细
     * <p>
     * 1.校验是否已经生成过明细
     * 2.获取已拣货未发货的记录
     * <p/>
     *
     * @param deliveryOrderCode 订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateDeliveryExceptionDetail(String deliveryOrderCode, String detailNo) {
        //加锁，避免并发重复操作
        boolean lockFlag = false;
        try {
            //1.校验订单的返架状态是否为待返架
            DeliveryHeaderEntity deliveryHeader = deliveryOrderQueryService.queryDeliveryInfo(deliveryOrderCode);
            //如果是取消件使用异常容器灰度仓则不生成异常明细 v   /
            //这个方法很多地方在调用，为了全局替换。故这里也加一下兜底
            if (deliveryCancelContainerConfig.isCancelOrderWhiteWarehouses(deliveryHeader.getWarehouseCode())) {
                if (deliveryHeader.getStatus().equals(WmsOutBoundStatusEnum.FORCE_OUTED.getStatus())) {
                    forceShipGenerate(deliveryOrderCode, deliveryHeader.getWarehouseCode(), deliveryHeader.getOrderCreateTime());
                } else {
                    generateDeliveryCancelExceptionDetailV2(deliveryOrderCode, detailNo);
                }
                return;
            }
            OperationUserContextHolder.buildDefaultUserContext(deliveryHeader.getWarehouseCode());
            if (!DeliveryReturnShelfStatusEnum.WAIT.getStatus().equals(deliveryHeader.getReturnShelfStatus())) {
                log.info("deliveryOrderCode={},returnShelfStatus={},not need generateDeliveryExceptionDetail", deliveryOrderCode, deliveryHeader.getReturnShelfStatus());
                return;
            }
            if (!distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_EXCEPTION_GENERATE_LOCK, deliveryOrderCode)) {
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            lockFlag = true;
            //2.根据订单号校验是否生成过
            DeliveryExceptionDetailQueryParam queryConditionParam = new DeliveryExceptionDetailQueryParam();
            queryConditionParam.setDeliveryOrderCode(deliveryOrderCode);
            queryConditionParam.setDetailNo(detailNo);
            List<DeliveryExceptionDetailDo> list = deliveryExceptionDetailRepository.findList(queryConditionParam);
            if (CollectionUtils.isNotEmpty(list)) {
                log.warn("returnShelfDetailDo deliveryOrderCode={},warehouseCode={} is exist",
                        deliveryOrderCode, deliveryHeader.getWarehouseCode());
                return;
            }
            //3.开始根据订单生成
            doGenerateDeliveryExceptionDetail(deliveryHeader, detailNo);

            //发送返架消息 。 目前返架这边后续都走发消息的模式  在灰度未完成的请看下   下面的返架都需要继续写入 后续 会删除。
            // 无需判断是否需要返架。 返架侧自己判断
            outboundMessageProducer.sendReturnShelfMessage(deliveryOrderCode, detailNo);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new WmsException("interrupt, 生成出库单异常明细失败", e);
        } catch (Exception ex) {
            log.error("生成出库单异常明细失败", ex);
            throw new WmsException("生成出库单异常明细失败", ex);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_EXCEPTION_GENERATE_LOCK, deliveryOrderCode);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void forceShipGenerate(String deliveryOrderCode, String warehouseCode, Date orderCreateTime) {
        //查询发货任务明细
        List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.queryByReferenceNo(deliveryOrderCode, warehouseCode, OperationUserContextHolder.getTenantCode());
        //过滤强制发货的明细
        List<ShipTaskDetailDo> forceShipList = shipTaskDetailDos.stream().filter(item -> item.getStatus().equals(TaskStatusEnum.FORCE_FINISH.getStatus().byteValue())).collect(Collectors.toList());
        log.info("forceShipList 1 {}", JsonUtils.serialize(forceShipList));
        if (CollectionUtils.isEmpty(forceShipList)) {
            log.warn("出库单没有强制发货完成的记录");
            return;
        }
        //加锁，避免并发重复操作
        boolean lockFlag = false;
        try {
            if (!distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_EXCEPTION_GENERATE_LOCK, deliveryOrderCode)) {
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            lockFlag = true;
            //根据订单号校验是否生成过
            List<DeliveryCancelExceptionDetailDo> list = deliveryCancelExceptionDetailRepository
                    .queryByDeliveryOrder(deliveryOrderCode, null);
            if (CollectionUtils.isNotEmpty(list)) {
                log.warn("该订单已存在DeliveryCancelExceptionDetailDo记录。 deliveryOrderCode={}", deliveryOrderCode);
                return;
            }

            log.info("forceShipList 2 {}", JsonUtils.serialize(forceShipList));
            //保存异常明细&通知库内生成返架任务
            saveExceptionDetailAndNotifyReturnShelf(orderCreateTime, forceShipList);
            log.info("forceShipList 3 {}", JsonUtils.serialize(forceShipList));
            for (ShipTaskDetailDo shipTaskDetailDo : forceShipList) {
                outboundMessageProducer.sendReturnShelfMessage(deliveryOrderCode, shipTaskDetailDo.getReferenceDetailNo());
            }
        } catch (WmsOperationException | WmsException ex) {
            throw ex;
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new WmsException("强制发货生成出库单异常明细失败，出库单号:" + deliveryOrderCode, ex);
        } catch (Exception ex) {
            throw new WmsException("强制发货生成出库单异常明细失败，出库单号:" + deliveryOrderCode, ex);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_EXCEPTION_GENERATE_LOCK, deliveryOrderCode);
            }
        }
    }

    private void saveExceptionDetailAndNotifyReturnShelf(Date orderCreateTime, List<ShipTaskDetailDo> forceShipList) {
        List<DeliveryCancelExceptionDetailDo> saveList = buildDeliveryCancelExceptionDos(forceShipList, orderCreateTime);
        log.info("saveList 1 {}", JsonUtils.serialize(saveList));
        deliveryCancelExceptionDetailRepository.batchSave(saveList);
        for (DeliveryCancelExceptionDetailDo detailDo : saveList) {
            deliveryCancelNotifyReturnShelfProducer.produce(
                    DeliveryCancelNotifyReturnShelfParam.builder()
                            .bizType(detailDo.getBizType())
                            .deliveryOrderCode(detailDo.getDeliveryOrderCode())
                            .operateUserId(OperationUserContextHolder.get().getUserId())
                            .qty(detailDo.getQty())
                            .requestId(detailDo.getUniqueCode())
                            .tradeOrderNo(detailDo.getDeliveryOrderCode())
                            .stationNo(OperationUserContextHolder.get().getStationNo())
                            .uniqueCode(detailDo.getUniqueCode())
                            .warehouseCode(detailDo.getWarehouseCode())
                            .tenantId(detailDo.getTenantId())
                            .deliveryOrderDetailNo(detailDo.getDeliveryOrderDetailNo())
                            .build());
        }
    }

    private List<DeliveryCancelExceptionDetailDo> buildDeliveryCancelExceptionDos(List<ShipTaskDetailDo> forceShipList, Date orderCreateTime) {
        List<DeliveryCancelExceptionDetailDo> list = new ArrayList<>();
        for (ShipTaskDetailDo shipTaskDetailDo : forceShipList) {
            DeliveryCancelExceptionDetailDo detailDo = new DeliveryCancelExceptionDetailDo();
            detailDo.setWarehouseCode(shipTaskDetailDo.getWarehouseCode());
            detailDo.setStatus(DeliveryCancelExceptionDetailStatusEnum.INIT.getStatus());
            detailDo.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.DELIVERY_EXCEPTION_DETAIL_NO.getSequenceType()));
            detailDo.setDeliveryOrderDetailNo(shipTaskDetailDo.getReferenceDetailNo());
            detailDo.setDeliveryOrderCode(shipTaskDetailDo.getReferenceNo());
            detailDo.setSkuId(shipTaskDetailDo.getSkuId());
            detailDo.setTenantId(OperationUserContextHolder.getTenantCode());
            detailDo.setQty(shipTaskDetailDo.getQty());
            detailDo.setOrderCreateTime(orderCreateTime);
            detailDo.setBarcode(Optional.ofNullable(shipTaskDetailDo.getBarcode()).orElse(StringUtils.EMPTY));
            OperationUserContext userContext = OperationUserContextHolder.get();
            detailDo.setCreatedUserId(userContext.getUserId());
            detailDo.setCreatedUserName(userContext.getUserName());
            detailDo.setCreatedRealName(userContext.getRealName());
            detailDo.setUpdatedUserId(userContext.getUserId());
            detailDo.setUpdatedUserName(userContext.getUserName());
            detailDo.setUpdatedRealName(userContext.getRealName());
            detailDo.setUniqueCode(Optional.ofNullable(shipTaskDetailDo.getUniqueCode()).orElse(StringUtils.EMPTY));
            detailDo.setVendorCode(Optional.ofNullable(shipTaskDetailDo.getVendorCode()).orElse(StringUtils.EMPTY));
            detailDo.setQualityLevel(shipTaskDetailDo.getQualityLevel());
            detailDo.setOwnerCode(shipTaskDetailDo.getOwnerCode());
            detailDo.setBizType(shipTaskDetailDo.getBizType().intValue());
            list.add(detailDo);
        }
        return list;
    }

    /**
     * 根据订单号获需要返架的商品生成明细
     * <p>
     * 1.校验是否已经生成过明细
     * 2.获取已拣货未发货的记录
     * <p/>
     *
     * @param deliveryOrderCode 订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateDeliveryCancelExceptionDetailV2(String deliveryOrderCode, @Nullable String detailNo) {
        //加锁，避免并发重复操作
        boolean lockFlag = false;
        try {
            //1.校验订单的返架状态是否为待返架
            DeliveryHeaderEntity deliveryHeader = deliveryOrderQueryService.queryDeliveryInfo(deliveryOrderCode);
            OperationUserContextHolder.buildDefaultUserContext(deliveryHeader.getWarehouseCode());
            if (!DeliveryReturnShelfStatusEnum.WAIT.getStatus().equals(deliveryHeader.getReturnShelfStatus())) {
                log.info("v2 deliveryOrderCode={},returnShelfStatus={},not need generateDeliveryExceptionDetail",
                        deliveryOrderCode, deliveryHeader.getReturnShelfStatus());
                return;
            }
            if (!distributeLockUtil.tryLockForBiz(LockEnum.DELIVERY_EXCEPTION_GENERATE_LOCK, deliveryOrderCode)) {
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            lockFlag = true;
            //2.根据订单号校验是否生成过
            List<DeliveryCancelExceptionDetailDo> list = deliveryCancelExceptionDetailRepository
                    .queryByDeliveryOrder(deliveryOrderCode, detailNo);
            if (CollectionUtils.isNotEmpty(list)) {
                log.warn("该订单已存在DeliveryCancelExceptionDetailDo记录。 deliveryOrderCode={},warehouseCode={}",
                        deliveryOrderCode, deliveryHeader.getWarehouseCode());
                return;
            }
            //3.开始根据订单生成
            doGenerateDeliveryCancelExceptionDetail(deliveryHeader, detailNo);

            //发送返架消息 。 目前返架这边后续都走发消息的模式  在灰度未完成的请看下   下面的返架都需要继续写入 后续 会删除。
            // 无需判断是否需要返架。 返架侧自己判断
            outboundMessageProducer.sendReturnShelfMessage(deliveryOrderCode, detailNo);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new WmsException("Interrupted, v2生成出库单异常明细失败，detailNo:" + detailNo, e);
        } catch (Exception ex) {
            throw new WmsException("v2生成出库单异常明细失败，detailNo:" + detailNo, ex);
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_EXCEPTION_GENERATE_LOCK, deliveryOrderCode);
            }
        }
    }

    /**
     * 根据订单生成
     *
     * @param deliveryHeader
     */
    private void doGenerateDeliveryExceptionDetail(DeliveryHeaderEntity deliveryHeader, String detailNo) {
        //获取 已拣货未发货的明细
        TaskDetailQueryPojo detailQueryPojo = new TaskDetailQueryPojo();
        detailQueryPojo.setReferenceNo(deliveryHeader.getDeliveryOrderCode());
        detailQueryPojo.setReferenceDetailNo(detailNo);
        detailQueryPojo.setTaskType(TaskTypeEnum.SHIP.getTaskType());
        detailQueryPojo.setWarehouseCode(deliveryHeader.getWarehouseCode());
        //先获取发货任务明细
        List<BaseTaskDetailPojo> shipTaskList = taskQueryService.findTaskDetail(detailQueryPojo);
        //有发货任务以发货任务为准没有则以拣货任务为准 拆行
        if (CollectionUtils.isNotEmpty(shipTaskList)) {
            addDetailByShipTaskDetail(deliveryHeader, shipTaskList);
        } else {
            //以拣货任务为准
            detailQueryPojo.setTaskType(TaskTypeEnum.PICK.getTaskType());
            List<BaseTaskDetailPojo> pickTaskList = taskQueryService.findTaskDetail(detailQueryPojo);
            if (CollectionUtils.isEmpty(pickTaskList)) {
                return;
            }
            addDetailByPickTaskDetail(deliveryHeader, pickTaskList);
        }
    }

    /**
     * 按照拣货任务明细生成
     *
     * @param pickTaskList
     */
    private void addDetailByPickTaskDetail(DeliveryHeaderEntity deliveryHeader, List<BaseTaskDetailPojo> pickTaskList) {
        pickTaskList = pickTaskList.stream().filter(baseTaskDetailPojo ->
                baseTaskDetailPojo.getOperationQty() > 0).collect(Collectors.toList());
        pickTaskList.forEach(baseTaskDetailPojo -> {
            List<BaseDetailResultPojo> pickResultList = baseTaskDetailPojo.getDetailResultPojoList();
            pickResultList.forEach(baseDetailResultPojo -> {
                //库存编号从任务执行结果里面获取
                DeliveryExceptionDetailDo detailDo = buildReturnShelfDetailDo(deliveryHeader,
                        baseTaskDetailPojo.getReferenceDetailNo(), baseDetailResultPojo.getInventoryNo(),
                        baseTaskDetailPojo.getSkuId(), baseTaskDetailPojo.getUniqueCode());
                deliveryExceptionDetailRepository.save(detailDo);
                //TODO 一致性框架
                returnShelfNotifyInnerUpdateProcessor.notifyInner(detailDo.getTenantCode(),detailDo.getDeliveryOrderCode(),detailDo.getDeliveryOrderDetailNo(),0);

            });
        });
    }

    /**
     * 生成取消件明细
     *
     * @param deliveryHeader
     * @param deliveryDetailNo
     */
    private void doGenerateDeliveryCancelExceptionDetail(DeliveryHeaderEntity deliveryHeader, String deliveryDetailNo) {
        List<DeliveryDetailDo> details = deliveryDetailRepository
                .queryByOrderOrDetailNo(deliveryHeader.getDeliveryOrderCode(), deliveryDetailNo);
        GenerateCancelDetailParam.DeliveryHeader header = ExceptionCancelAssembler.INSTANCE.toDeliveryHeader(deliveryHeader);
        details.forEach(detailDo -> deliveryCancelExceptionDetailStatusChangeProcessor.generateCancelDetail(
                GenerateCancelDetailParam
                        .builder()
                        .deliveryHeader(header)
                        .deliveryDetail(ExceptionCancelAssembler.INSTANCE.toDeliveryDetail(detailDo))
                        .build())
        );
    }


    /**
     * 根据发货任务明细生成
     *
     * @param shipTaskList
     */
    private void addDetailByShipTaskDetail(DeliveryHeaderEntity deliveryHeader, List<BaseTaskDetailPojo> shipTaskList) {
        //计划发货数量-实际发货数量大于0的
        shipTaskList = shipTaskList.stream().filter(baseTaskDetailPojo ->
                baseTaskDetailPojo.getQty() - baseTaskDetailPojo.getOperationQty() > 0).collect(Collectors.toList());
        shipTaskList.forEach(baseTaskDetailPojo -> {
            int needReturnTotal = baseTaskDetailPojo.getQty() - baseTaskDetailPojo.getOperationQty();
            for (int i = 0; i < needReturnTotal; i++) {
                DeliveryExceptionDetailDo detailDo = buildReturnShelfDetailDo(deliveryHeader,
                        baseTaskDetailPojo.getReferenceDetailNo(), baseTaskDetailPojo.getInventoryNo(),
                        baseTaskDetailPojo.getSkuId(), baseTaskDetailPojo.getUniqueCode());
                deliveryExceptionDetailRepository.save(detailDo);
                returnShelfNotifyInnerUpdateProcessor.notifyInner(detailDo.getTenantCode(),detailDo.getDeliveryOrderCode(),detailDo.getDeliveryOrderDetailNo(),0);
            }
        });
    }

    /**
     * 转换对象
     *
     * @param deliveryHeader
     * @param deliveryOrderDetailNo
     * @param inventoryNo
     * @param skuId
     * @param uniqueCode
     * @return
     */
    private DeliveryExceptionDetailDo buildReturnShelfDetailDo(DeliveryHeaderEntity deliveryHeader,
                                                               String deliveryOrderDetailNo, String inventoryNo,
                                                               String skuId, String uniqueCode) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        if (userContext == null) {
            OperationUserContextHolder.buildDefaultUserContext(deliveryHeader.getWarehouseCode());
            userContext = OperationUserContextHolder.get();
        }
        Long userId = userContext.getUserId();
        String userName = userContext.getUserName();
        DeliveryExceptionDetailDo detailDo = new DeliveryExceptionDetailDo();
        detailDo.setWarehouseCode(deliveryHeader.getWarehouseCode());
        detailDo.setStatus(DeliveryExceptionDetailStatusEnum.INIT.getStatus());
        detailDo.setCreatedUserId(userId);
        detailDo.setCreatedUserName(userName);
        detailDo.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.DELIVERY_EXCEPTION_DETAIL_NO.getSequenceType()));
        detailDo.setDeliveryOrderDetailNo(deliveryOrderDetailNo);
        detailDo.setDeliveryOrderCode(deliveryHeader.getDeliveryOrderCode());
        detailDo.setSkuId(skuId);
        detailDo.setTenantCode(deliveryHeader.getTenantCode());
        detailDo.setUniqueCode(uniqueCode);
        detailDo.setPlanQty(NumberUtils.INTEGER_ONE);
        detailDo.setOrderCreateTime(deliveryHeader.getOrderCreateTime());
        detailDo.setInventoryNo(inventoryNo);
        InvInventoryEntity invInventoryEntity = queryInvInventoryEntity(inventoryNo, deliveryHeader, deliveryOrderDetailNo);
        detailDo.setReceivedTime(invInventoryEntity.getReceivedTime());
        detailDo.setVendorCode(invInventoryEntity.getVendorCode());
        detailDo.setQualityLevel(invInventoryEntity.getQualityLevel());
        detailDo.setOwnerCode(invInventoryEntity.getOwnerCode());
        detailDo.setOriginalOrderCode(invInventoryEntity.getOriginalOrderCode());
        detailDo.setMfgTime(invInventoryEntity.getMfgTime());
        detailDo.setExpTime(invInventoryEntity.getExpTime());
        detailDo.setEntryOrderCode(invInventoryEntity.getEntryOrderCode());
        detailDo.setBizType(invInventoryEntity.getBizType());
        return detailDo;
    }

    private InvInventoryEntity queryInvInventoryEntity(String inventoryNo, DeliveryHeaderEntity deliveryHeader, String orderDetailNo) {
        //先暂存，执行完了再设置回去
        String tenantCode = TenantContext.getTenantId();
        try {
            TenantContext.setContextId(deliveryHeader.getTenantCode());
            return queryInvInventoryEntity0(inventoryNo, deliveryHeader, orderDetailNo);
        } finally {
            TenantContext.setContextId(tenantCode);
        }
    }

    private InvInventoryEntity queryInvInventoryEntity0(String inventoryNo, DeliveryHeaderEntity deliveryHeader, String orderDetailNo) {
        TenantContext.setContextId(deliveryHeader.getTenantCode());
        String orderCode = deliveryHeader.getDeliveryOrderCode();
        InvInventoryEntity invInventoryEntity = inventoryQueryService.queryInventoryDetail(OperationUserContextHolder.getTenantCode(), inventoryNo);
        if (Objects.nonNull(invInventoryEntity)) {
            return invInventoryEntity;
        }
        List<ContainerDetailDo> containerDetails = containerQueryService.queryUnCompletedContainerDetails(orderCode, orderDetailNo);
        if (CollectionUtils.isNotEmpty(containerDetails)) {
            ContainerDetailDo detail = containerDetails.get(0);
            inventoryNo = detail.getInventoryNo();
            log.info("从容器明细内查找到库存no. inventoryNo：{}, detailNo：{}", inventoryNo, detail.getDetailNo());
            invInventoryEntity = inventoryQueryService.queryInventoryDetail(OperationUserContextHolder.getTenantCode(), inventoryNo);
        }
        if (Objects.isNull(invInventoryEntity)) {
            throw new WmsException("deliveryHeaderCode =" + orderCode + "，inventoryNo=" + inventoryNo + " 生成异常返架明细失败,未找到库存");
        }
        return invInventoryEntity;
    }

}
