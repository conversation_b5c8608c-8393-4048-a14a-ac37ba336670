package com.poizon.scm.wms.service.inventory.search.modifier;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.query.SciInventoryAllocateDetail;
import com.poizon.scm.wms.adapter.inventory.repository.InvInventoryAllocatedRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.dao.curd.InventoryCurd;
import com.poizon.scm.wms.dao.entitys.InvInventoryEntity;
import com.poizon.scm.wms.domain.inventory.common.InventoryCommonOperation;
import com.poizon.scm.wms.domain.inventory.operate.InventoryAllocated;
import com.poizon.scm.wms.domain.inventory.translog.InvInventoryTransLog;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedCancelPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryBasePojo;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateServiceImpl;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2021/4/1
 */
@Slf4j
@Service
public class AllocatedModifierService {

    @Autowired
    private DistributeLockUtil distributeLockUtil;

    @Resource
    private InventoryAllocated inventoryAllocated;

    @Resource
    private  InvInventoryTransLog invInventoryTransLog;

    @Autowired
    private InventoryOperateServiceImpl inventoryOperateServiceImpl;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private InventoryCurd inventoryCurd;

    @Autowired
    private InvInventoryAllocatedRepository invInventoryAllocatedRepository;

    @Transactional(rollbackFor = Exception.class)
    public void modify(List<SciInventoryAllocateDetail> cisInventoryAllocatedRecordList, List<InvInventoryAllocatedDo> invInventoryAllocatedRecordList) {
        Map<String, List<InvInventoryAllocatedDo>> detailNoMapping = invInventoryAllocatedRecordList.stream().collect(Collectors.groupingBy(InvInventoryAllocatedDo::getShipmentDetailNo));
        Map<String, List<SciInventoryAllocateDetail>> referenceDetailMapping = cisInventoryAllocatedRecordList.stream().collect(Collectors.groupingBy(SciInventoryAllocateDetail::getReferenceDetailNo));
        for (Map.Entry<String, List<InvInventoryAllocatedDo>> entry : detailNoMapping.entrySet()) {
            String detailNo = entry.getKey();
            modifyDetail(detailNo, entry.getValue(), referenceDetailMapping.get(detailNo));
        }
    }

    /**
     * 更新库存分配明细
     *
     * @param detailNo
     * @param invInventoryAllocatedList   WMS库存分配记录
     * @param sciInventoryAllocateDetails SCI库存分配记录
     * @return
     */
    private void modifyDetail(String detailNo, List<InvInventoryAllocatedDo> invInventoryAllocatedList, List<SciInventoryAllocateDetail> sciInventoryAllocateDetails) {
        if (CollectionUtils.isEmpty(sciInventoryAllocateDetails)) {
            /*当前明细在SCI没有分配到库存,需要取消*/
            removeInventoryAllocated(invInventoryAllocatedList);
            modifyWmsActualAllocatedQty(detailNo, 0);
            return;
        }
        /*该明细在SCI分配的数量(该明细实际的分配数量)*/
        int sciTotalAllocatedQty = sciInventoryAllocateDetails.stream().mapToInt(SciInventoryAllocateDetail::getQty).sum();
        /*该明细在WMS分配的数量(需要修正的数量)*/
        int wmsTotalAllocatedQty = invInventoryAllocatedList.stream().mapToInt(InvInventoryAllocatedDo::getQty).sum();
        log.info("明细{}在sci的库存分配数量:{},在wms的库存分配数量:{}", detailNo, sciTotalAllocatedQty, wmsTotalAllocatedQty);
        if (sciTotalAllocatedQty > wmsTotalAllocatedQty) {
            throw new WmsException(WmsExceptionCode.SCI_INVENTORY_ALLOCATED_ERROR);
        }
        if (sciTotalAllocatedQty == wmsTotalAllocatedQty) {
            /*不需要修改该明细*/
            return;
        }
        /*剩余数量*/
        int remainSciQty = sciTotalAllocatedQty;
        for (InvInventoryAllocatedDo invInventoryAllocated : invInventoryAllocatedList) {
            Integer allocatedQty = invInventoryAllocated.getQty();
            if (remainSciQty == 0) {
                /*如果没有剩余数量了,那么剩下的分配记录都需要删除*/
                removeInventoryAllocated(Arrays.asList(invInventoryAllocated));
            } else if (allocatedQty > remainSciQty) {
                /*如果分配的数量大于了剩余数量,那么这个库存分配记录需要修改*/
                modifyInventoryAllocated(invInventoryAllocated, allocatedQty - remainSciQty);
                remainSciQty = 0;
            } else {
                remainSciQty = remainSciQty - allocatedQty;
            }
        }
        modifyWmsActualAllocatedQty(detailNo, sciTotalAllocatedQty);
    }

    /**
     * 修改明细的实际分配数量
     *
     * @param detailNo
     * @param actualQty
     */
    private void modifyWmsActualAllocatedQty(String detailNo, int actualQty) {
        /*修改明细实际分配数量*/
        int count = deliveryDetailRepository.updateActualAllocateQty(detailNo, actualQty);
        if (count != NumberUtils.INTEGER_ONE) {
            log.error("单据明细{}实际分配数量修改失败", detailNo);
            throw new WmsException(WmsExceptionCode.UPDATE_DELIVERY_DETAIL_ALLOCATED_QTY_ERROR);
        }
    }


    /**
     * 更新该条库存的分配数量和库存分配记录的数量
     *
     * @param invInventoryAllocated
     * @param diffQty               当前库存分配记录WMS多出来的数量
     */
    private void modifyInventoryAllocated(InvInventoryAllocatedDo invInventoryAllocated, int diffQty) {
        /*
         * 更新库存的分配数量
         * 更新库存分配记录的数量
         * */
        boolean lock = false;
        String lockName = InventoryCommonOperation.getInventoryLockName(invInventoryAllocated.getTenantCode(), invInventoryAllocated.getSourceInventoryNo());
        try {
            lock = distributeLockUtil.tryLock(lockName, 3);
            if (lock) {
                InvInventoryEntity inventoryEntity = inventoryCurd.findInventoryByInventoryNo(invInventoryAllocated.getTenantCode(), invInventoryAllocated.getSourceInventoryNo());
                InvInventoryEntity undoInventoryEntity = new InvInventoryEntity();
                BeanUtils.copyProperties(inventoryEntity, undoInventoryEntity);
                inventoryEntity.setAllocatedQty(inventoryEntity.getAllocatedQty() - diffQty);
                if (inventoryEntity.getAllocatedQty() < 0) {
                    throw new WmsException(WmsExceptionCode.INVENTORY_OPERATION_FAILED);
                }
                inventoryCurd.saveOrUpdate(inventoryEntity);
                InventoryBasePojo inventoryBasePojo = createInventoryBasePojo(invInventoryAllocated);
                invInventoryTransLog.insertInventoryTransLog(undoInventoryEntity, inventoryEntity, inventoryBasePojo, "", diffQty, null, null);
                int count = invInventoryAllocatedRepository.updateAllocatedQty(invInventoryAllocated.getAllocatedNo(), invInventoryAllocated.getQty() - diffQty);
                if (count != NumberUtils.INTEGER_ONE) {
                    throw new WmsException(WmsExceptionCode.UPDATE_INVENTORY_ALLOCATE_QTY_ERROR);
                }
            } else {
                /*获取不到锁直接抛出异常*/
                throw new WmsException(WmsExceptionCode.GET_LOCK_FAILED);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new WmsException("interrupt", e);
        } catch (Exception e) {
            throw new WmsException(e.getMessage());
        } finally {
            if (lock) {
                distributeLockUtil.release(lockName);
            }
        }
    }


    private InventoryBasePojo createInventoryBasePojo(InvInventoryAllocatedDo invInventoryAllocated) {
        InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
        inventoryBasePojo.setReferenceNo(invInventoryAllocated.getShipmentNo());
        inventoryBasePojo.setTenantCode(invInventoryAllocated.getTenantCode());
        inventoryBasePojo.setTaskType(TaskTypeEnum.RETURN_INVENTORY.getTaskType());
        return inventoryBasePojo;
    }


    /**
     * 删除该条库存分配记录
     *
     * @param invInventoryAllocatedList
     */
    private void removeInventoryAllocated(List<InvInventoryAllocatedDo> invInventoryAllocatedList) {
        Set<String> allocatedNoSet = invInventoryAllocatedList.stream().map(InvInventoryAllocatedDo::getAllocatedNo).collect(Collectors.toSet());
        /*继续走取消流程*/
        InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo = new InventoryAllocatedCancelPojo();
        inventoryAllocatedCancelPojo.setTenantCode(invInventoryAllocatedList.get(0).getTenantCode());
        inventoryAllocatedCancelPojo.setAllocatedNoList(Lists.newArrayList(allocatedNoSet));
        inventoryAllocatedCancelPojo.setShipmentNo(invInventoryAllocatedList.get(0).getShipmentNo());
        inventoryAllocated.cancelAllocatedInventory(inventoryAllocatedCancelPojo);
    }
}
