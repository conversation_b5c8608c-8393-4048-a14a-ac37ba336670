package com.poizon.scm.wms.service.shipping;

import com.poizon.scm.wms.adapter.inventory.param.InventorySubRpcParam;
import com.poizon.scm.wms.adapter.inventory.repository.rpc.InventoryRpcRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.domain.notify.notifyLms.processor.ShippingNotifyLmsProcessor;
import com.poizon.scm.wms.pojo.inventory.InventoryBasePojo;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateService;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @data 2021/6/21
 */
@Service
@Slf4j
public class CombineDeliveryShipService {

    @Autowired
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private CombineDeliveryShipTaskService combineDeliveryShipTaskService;

    @Autowired
    private InventoryOperateService inventoryOperateService;

    @Autowired
    private CombineDeliveryShipLogService combineDeliveryShipLogService;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;

    @Autowired
    private InventoryRpcRepository inventoryRpcRepository;
    @Autowired
    private ShippingNotifyLmsProcessor shippingNotifyLmsProcessor;


    /**
     * 按照任务明细发货
     *
     * @param warehouseCode     仓库
     * @param deliveryOrderCode 出库单
     * @param taskDetailNo      任务明细
     * @param operationQty      数量
     */
    @Transactional(rollbackFor = Exception.class)
    public void shipByTaskDetail(String warehouseCode, String deliveryOrderCode, String taskDetailNo, int operationQty, Map<String, List<String>> snListMap) {
        /*
         * 1、生成任务结果
         * 2、扣除库存
         * 3、更新任务明细
         * 4、更新任务如果任务完成的话
         * 5、添加日志
         * 6、异步回传pink操作日志
         */
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (Objects.isNull(deliveryHeader)) {
            log.error("发货单{}不存在", deliveryOrderCode);
            throw new WmsException("发货单不存在");
        }
        ShipTaskDetailDo taskDetail = shipTaskDetailQueryRepository.queryByDetailNo(taskDetailNo, warehouseCode, deliveryHeader.getTenantCode());
        if (Objects.isNull(taskDetail)) {
            log.error("发货任务明细{}不存在", taskDetailNo);
            throw new WmsException("发货任务明细不存在");
        }
        if (taskDetail.getQty() != operationQty) {
            log.error("发货任务明细{}的数量{}和发货数量{}不一致", taskDetailNo, taskDetail.getQty(), operationQty);
            throw new WmsException("发货任务明细的数量和发货数量不一致");
        }
        if (TaskStatusEnum.finishList.contains(taskDetail.getStatus().intValue())) {
            log.error("发货任务明细{}的已经完成,不能重复发货", taskDetailNo);
            throw new WmsException("发货任务明细的已经完成,不能重复发货");
        }
        /*写发货结果*/
        combineDeliveryShipTaskService.saveShipTaskResult(taskDetail.getInventoryNo(), taskDetail);
        /*更新任务明细状态和数量*/
        combineDeliveryShipTaskService.updateShipTaskDetail(taskDetail, operationQty);
        /*更新单据明细的数量*/
        deliveryDetailRepository.updateActualQtyByDetailNo(taskDetail.getReferenceDetailNo(),operationQty);
        /*更新任务头*/
        combineDeliveryShipTaskService.updateTaskHeadIfNecessary(warehouseCode, taskDetail.getTaskNo());
        /*插入日志*/
        combineDeliveryShipLogService.addLog(deliveryOrderCode, taskDetail.getReferenceDetailNo(), deliveryHeader.getType(), taskDetail.getTaskNo(), taskDetail.getUniqueCode(), warehouseCode);
        /*通知PINK*/
        combineDeliveryShipLogService.notifyPinkOperationLog(taskDetailNo, taskDetail.getBizType());
        //通知lms
        shippingNotifyLmsProcessor.notify(taskDetail);
    }

    /**
     * @param deliveryHeader
     * @param taskDetail
     * @return
     */
    private String subInventory(DeliveryHeaderDo deliveryHeader, ShipTaskDetailDo taskDetail,List<String> snList) {
        InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
        inventoryBasePojo.setTenantCode(taskDetail.getTenantCode());
        inventoryBasePojo.setReferenceNo(taskDetail.getReferenceNo());
        inventoryBasePojo.setReferenceType(deliveryHeader.getType());
        inventoryBasePojo.setOperateQty(taskDetail.getQty());
        inventoryBasePojo.setReferenceDetailNo(taskDetail.getReferenceDetailNo());
        inventoryBasePojo.setTaskDetailNo(taskDetail.getDetailNo());
        inventoryBasePojo.setTaskNo(taskDetail.getTaskNo());
        inventoryBasePojo.setRelativeInventoryNo(taskDetail.getInventoryNo());
        inventoryBasePojo.setSns(snList);
        //换成调库存rpc接口
        InventorySubRpcParam param = new InventorySubRpcParam();
        BeanUtils.copyProperties(inventoryBasePojo,param);
        return inventoryRpcRepository.subWmsInventoryRpc(param);
    }
}
