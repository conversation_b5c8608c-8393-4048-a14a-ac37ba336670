package com.poizon.scm.wms.third.receive.biz.out.executor;

import com.dewu.executor.annotation.Delay;
import com.dewu.executor.annotation.EventualConsistency;
import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.cis.api.client.InventoryOperateClient;
import com.poizon.scm.cis.api.enums.SciFromChannel;
import com.poizon.scm.cis.api.inventory.CisDimensionDto;
import com.poizon.scm.cis.api.inventory.CisInventoryDto;
import com.poizon.scm.cis.api.inventory.InTransitAddRequest;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.pojo.third.notify.request.out.NotifyOfcDeductSciRequest;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * sci增加在途的
 * <AUTHOR>
 * @data 2021/6/10
 */
@Slf4j
@Service
public class SciInventoryAddIntransitExecutor {

    @Autowired
    private InventoryOperateClient inventoryOperateClient;

    /**
     * 添加SCI在途库存
     * @param warehouseCode 集货仓库
     * 2021-08-04 版本之后可以删除
     */
    @EventualConsistency(label = "sciInventoryOperate", referenceNo = "#requestMessage.orderCode", delay = @Delay(delay = 30))
    public void addScInTransitInventory(String warehouseCode, NotifyOfcDeductSciRequest requestMessage) {
        InTransitAddRequest addRequest = buildInTransitAddRequest(warehouseCode,requestMessage);
        log.info("请求sci增加在途参数{}", JsonUtils.serialize(addRequest));
        Result<Boolean> result = inventoryOperateClient.addInTransit(addRequest);
        log.info("请求sci增加在途库存的结果:{}", JsonUtils.serialize(result));
        if (Objects.isNull(result) || !Result.SUCCESS_CODE.equals(result.getCode())) {
            throw new WmsException("交接发货调用SCI增加在途库存失败");
        }
    }

    /**
     * 添加SCI在途库存
     * @param warehouseCode 集货仓库
     */
    @EventualConsistency(label = "sciInventoryOperateV2", referenceNo = "#requestMessage.orderCode", delay = @Delay(delay = 30))
    public void addScInTransitInventoryV2(String warehouseCode, NotifyOfcDeductSciRequest requestMessage) {
        InTransitAddRequest addRequest = buildInTransitAddRequest(warehouseCode,requestMessage);
        log.info("请求sci增加在途参数{}", JsonUtils.serialize(addRequest));
        Result<Boolean> result = inventoryOperateClient.addInTransitV2(addRequest);
        log.info("请求sci增加在途库存的结果:{}", JsonUtils.serialize(result));
        if (Objects.isNull(result) || !Result.SUCCESS_CODE.equals(result.getCode())) {
            throw new WmsException("交接发货调用SCI增加在途库存失败");
        }
    }

    /**
     * 现货加价购交接集货仓添加SCI在途库存
     * @param warehouseCode 集货仓库
     */
    @EventualConsistency(label = "xhjjgAddScInTransitInventory", referenceNo = "#deliveryHeaderDo.deliveryOrderCode", delay = @Delay(delay = 30))
    public void xhjjgAddScInTransitInventory(DeliveryHeaderDo deliveryHeaderDo,
                                             List<DeliveryDetailDo> deliveryDetailDos,
                                             InventoryDo inventoryDo,
                                             String warehouseCode) {
        DeliveryDetailDo deliveryDetailDo = deliveryDetailDos.get(0);
        InTransitAddRequest addRequest = new InTransitAddRequest();
        addRequest.setOrderCode(deliveryDetailDo.getTradeOrderNo());
        addRequest.setOrderType(deliveryHeaderDo.getType());
        addRequest.setRequestId(deliveryDetailDo.getTradeOrderNo());
        addRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        addRequest.setFromChannel(SciFromChannel.WMS.name());
        List<CisInventoryDto> cisInventoryList = new ArrayList<>();
        CisDimensionDto cisDimension = null ;
        CisInventoryDto cisInventory = null;
        for(DeliveryDetailDo deliveryDetail:deliveryDetailDos){
            cisInventory = new CisInventoryDto();
            cisInventory.setQty(deliveryDetail.getPlanQty());
            cisDimension = new CisDimensionDto();
            cisDimension.setTenantCode(deliveryHeaderDo.getTenantCode());
            cisDimension.setWarehouseCode(warehouseCode);
            cisDimension.setBizType(deliveryDetail.getBizType());
            cisDimension.setOriginalOrderCode(inventoryDo.getOriginalOrderCode());
            cisDimension.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
            cisDimension.setQualityLevel(inventoryDo.getQualityLevel());
            cisDimension.setOwnerCode(inventoryDo.getOwnerCode());
            cisDimension.setSkuId(inventoryDo.getSkuId());
            cisDimension.setLotsNo(StringUtils.EMPTY);
            cisDimension.setExpTime(inventoryDo.getExpTime());
            cisDimension.setUniqueCode(inventoryDo.getUniqueCode());
            cisInventory.setCisDimensionDto(cisDimension);
            cisInventoryList.add(cisInventory);
        }
        addRequest.setQty(cisInventoryList.stream().mapToInt(CisInventoryDto::getQty).sum());
        addRequest.setCisInventoryDto(cisInventoryList);
        log.info("现货加价购请求sci增加在途参数{}", JsonUtils.serialize(addRequest));
        Result<Boolean> result = inventoryOperateClient.addInTransitV2(addRequest);
        log.info("现货加价购请求sci增加在途库存的结果:{}", JsonUtils.serialize(result));
        if (Objects.isNull(result) || !Result.SUCCESS_CODE.equals(result.getCode())) {
            throw new WmsException("现货加价购交接发货调用SCI增加在途库存失败");
        }
    }

    private InTransitAddRequest buildInTransitAddRequest(String warehouseCode, NotifyOfcDeductSciRequest requestMessage) {
        InTransitAddRequest addRequest = new InTransitAddRequest();
        addRequest.setOrderCode(requestMessage.getOrderCode());
        addRequest.setOrderType(requestMessage.getOrderType());
        addRequest.setRequestId(requestMessage.getOrderCode());
        addRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        addRequest.setFromChannel(SciFromChannel.WMS.name());
        List<CisInventoryDto> cisInventoryDto = createDetails(warehouseCode,requestMessage);
        addRequest.setQty(cisInventoryDto.stream().mapToInt(CisInventoryDto::getQty).sum());
        addRequest.setCisInventoryDto(cisInventoryDto);
        return addRequest;
    }

    private List<CisInventoryDto> createDetails(String warehouseCode, NotifyOfcDeductSciRequest requestMessage) {
        Integer bizType = requestMessage.getOrderLines().iterator().next().getBizType();
        if (WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(bizType)){
            return createForPersonalDeposit(warehouseCode,requestMessage.getOrderLines());
        }else {
            return createForEnterpriseDeposit(warehouseCode,requestMessage.getOrderLines());
        }
    }

    /**
     * 个人寄存
     * @param warehouseCode
     * @param orderLines
     * @return
     */
    private List<CisInventoryDto> createForPersonalDeposit(String warehouseCode, List<NotifyOfcDeductSciRequest.OrderLine> orderLines) {
        List<CisInventoryDto> cisInventoryList = new ArrayList<>();
        orderLines.forEach(orderLine -> {
            CisInventoryDto cisInventory = new CisInventoryDto();
            cisInventory.setQty(orderLine.getQty());
            CisDimensionDto cisDimension = createCisDimension(warehouseCode,orderLine);
            cisInventory.setCisDimensionDto(cisDimension);
            cisInventoryList.add(cisInventory);
        });
        return cisInventoryList;
    }

    /**
     * 企业和品牌
     * @param warehouseCode
     * @param orderLines
     * @return
     */
    private List<CisInventoryDto> createForEnterpriseDeposit(String warehouseCode, List<NotifyOfcDeductSciRequest.OrderLine> orderLines) {
        List<CisInventoryDto> cisInventoryList = new ArrayList<>();
        Map<String, List<NotifyOfcDeductSciRequest.OrderLine>> mapping = orderLines.stream().collect(Collectors.groupingBy(orderLine -> String.join("_",orderLine.getSkuId(),orderLine.getOwnerCode(),String.valueOf(orderLine.getBizType()),orderLine.getQualityLevel())));
        mapping.forEach((key,list)->{
            CisInventoryDto cisInventory = new CisInventoryDto();
            cisInventory.setQty(list.stream().mapToInt(NotifyOfcDeductSciRequest.OrderLine::getQty).sum());
            CisDimensionDto cisDimension = createCisDimension(warehouseCode,list.iterator().next());
            cisInventory.setCisDimensionDto(cisDimension);
            cisInventoryList.add(cisInventory);
        });
        return cisInventoryList;
    }

    /**
     * 库存维度
     *
     * @param warehouseCode
     * @param orderLine
     * @return
     */
    private CisDimensionDto createCisDimension(String warehouseCode, NotifyOfcDeductSciRequest.OrderLine orderLine) {
        CisDimensionDto cisDimension = new CisDimensionDto();
        cisDimension.setTenantCode(OperationUserContextHolder.getTenantCode());
        cisDimension.setWarehouseCode(warehouseCode);
        cisDimension.setBizType(orderLine.getBizType());
        cisDimension.setOriginalOrderCode(orderLine.getOriginalOrderCode());
        cisDimension.setFirstReceivedTime(orderLine.getFirstReceivedTime());
        cisDimension.setQualityLevel(orderLine.getQualityLevel());
        cisDimension.setOwnerCode(orderLine.getOwnerCode());
        cisDimension.setSkuId(orderLine.getSkuId());
        cisDimension.setLotsNo(StringUtils.EMPTY);
        cisDimension.setExpTime(orderLine.getExpTime());
        if (WmsBizTypeEnum.GE_REN_JI_CUN.getBizType().equals(orderLine.getBizType())){
            cisDimension.setUniqueCode(orderLine.getUniqueCode());
        }
        return cisDimension;
    }
}
