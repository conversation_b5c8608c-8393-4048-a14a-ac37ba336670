package com.poizon.scm.wms.service.delivery.operate.impl;

import com.poizon.scm.wms.adapter.commodity.model.rsp.SkuBasicRspDo;
import com.poizon.scm.wms.adapter.commodity.repository.ScpSkuRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.LocationConstant;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.domain.flow.rule.base.WmsFlowCodeEnum;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBill;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBillDetail;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/30
 * @desc
 */
@Slf4j
@Component
public class ShipTaskCreateService {

    @Autowired
    private ShipTaskCommandRepository shipTaskCommandRepository;

    @Autowired
    private ScpSkuRepository scpSkuRepository;

    @Autowired
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Transactional(rollbackFor = Exception.class)
    public void create(WmsDeliveryBill delivery) {
        log.info("开始创建发货任务");
        ShipTaskDo shipTaskDo = buildShipTaskHeader(delivery);
        int thResult = shipTaskCommandRepository.save(shipTaskDo);
        Preconditions.checkBiz(thResult == 1, WmsExceptionCode.TASK_HEADER_SAVE_FAIL);
        /*构建发货明细*/
        List<ShipTaskDetailDo> shipTaskDetails = buildShipTaskDetails(shipTaskDo, delivery);
        shipTaskDetailCommandRepository.batchInsert(shipTaskDetails);
        log.info("结束创建发货任务");

    }

    private ShipTaskDo buildShipTaskHeader(WmsDeliveryBill delivery) {
        OperationUserContext user = OperationUserContextHolder.get();
        Date now = new Date();
        String referenceType = delivery.getOrderType();
        String referenceNo = delivery.getDeliveryOrderCode();
        String warehouseCode = delivery.getWarehouseCode();
        int totalQty = delivery.getTotalOrderLines();

        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        taskDo.setFlowCode(WmsFlowCodeEnum.TAKE_BACK_OUT.getFlowCode());
        taskDo.setReferenceType(referenceType);
        taskDo.setReferenceNo(referenceNo);
        taskDo.setStatus(TaskStatusEnum.INIT.getStatus());
        taskDo.setWarehouseCode(warehouseCode);
        taskDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setOperationUserId(user.getUserId());
        taskDo.setOperationRealName(user.getRealName());
        taskDo.setOperationUserName(user.getUserName());
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        taskDo.setCreatedUserId(user.getUserId());
        taskDo.setCreatedUserName(user.getUserName());
        taskDo.setCreatedRealName(user.getRealName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        taskDo.setVersion(NumberUtils.INTEGER_ZERO);
        taskDo.setDeleted(NumberUtils.INTEGER_ZERO);
        return taskDo;
    }

    private List<ShipTaskDetailDo> buildShipTaskDetails(ShipTaskDo shipTask, WmsDeliveryBill delivery) {
        Set<String> uniqueCode = delivery.getWmsDeliveryBillDetails().stream().map(WmsDeliveryBillDetail::getUniqueCode).collect(Collectors.toSet());
        Map<String, InventoryDo> inventoryMap = new HashMap<>();
        List<InventoryDo> inventoryDos = inventoryRepository.findByUniqueCodes(uniqueCode, OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isNotEmpty(inventoryDos)) {
            inventoryMap = inventoryDos.stream().collect(Collectors.toMap(InventoryDo::getUniqueCode, Function.identity()));
        }
        List<ShipTaskDetailDo> shipDetailList = new ArrayList<>();
        OperationUserContext userContext = OperationUserContextHolder.get();
        Map<String, InventoryDo> finalInventoryMap = inventoryMap;

        Set<String> skuIdSet = delivery.getWmsDeliveryBillDetails().stream().map(WmsDeliveryBillDetail::getSkuId).collect(Collectors.toSet());
        List<SkuBasicRspDo> skuBasicRspDos = scpSkuRepository.listScpSkuBySkuIds(OperationUserContextHolder.getTenantCode(), skuIdSet);
        Map<String, SkuBasicRspDo> skuMap = skuBasicRspDos.stream().collect(Collectors.toMap(SkuBasicRspDo::getSkuId, Function.identity(), (o1, o2) -> o1));

        delivery.getWmsDeliveryBillDetails().forEach(detail -> {
            ShipTaskDetailDo shipTaskDetail = new ShipTaskDetailDo();
            InventoryDo inventoryDo = finalInventoryMap.get(detail.getUniqueCode());
            if (inventoryDo != null) {
                shipTaskDetail.setMfgTime(inventoryDo.getMfgTime());
                shipTaskDetail.setExpTime(inventoryDo.getExpTime());
                shipTaskDetail.setOriginalOrderCode(inventoryDo.getOriginalOrderCode());
                shipTaskDetail.setBatchNo(inventoryDo.getBatchNo());
                shipTaskDetail.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
                shipTaskDetail.setPoNo(inventoryDo.getOriginalOrderCode());
                shipTaskDetail.setOriginalDetailNo(inventoryDo.getOriginalOrderCode());
                shipTaskDetail.setInventoryNo(inventoryDo.getInventoryNo());
                shipTaskDetail.setVendorCode(inventoryDo.getVendorCode());
                shipTaskDetail.setEntryOrderCode(inventoryDo.getEntryOrderCode());
            }

            shipTaskDetail.setTaskNo(shipTask.getTaskNo());
            shipTaskDetail.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            shipTaskDetail.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            shipTaskDetail.setBizType(detail.getBizType().byteValue());
            shipTaskDetail.setStatus(NumberUtils.INTEGER_ZERO.byteValue());
            shipTaskDetail.setReferenceNo(delivery.getDeliveryOrderCode());
            shipTaskDetail.setReferenceType(delivery.getOrderType());
            shipTaskDetail.setReferenceDetailNo(detail.getOrderLineNo());


            shipTaskDetail.setContainerCode("");
            shipTaskDetail.setQty(detail.getPlanQty());
            shipTaskDetail.setLocationCode(LocationConstant.PICK_DOCK);
            shipTaskDetail.setOperationQty(NumberUtils.INTEGER_ZERO);

            shipTaskDetail.setWarehouseCode(delivery.getWarehouseCode());
            shipTaskDetail.setTenantCode(OperationUserContextHolder.getTenantCode());
            shipTaskDetail.setOwnerCode(detail.getOwnerCode());

            shipTaskDetail.setBarcode(detail.getBarCode());
            shipTaskDetail.setUniqueCode(detail.getUniqueCode());
            shipTaskDetail.setSkuId(detail.getSkuId());
            shipTaskDetail.setQualityLevel(detail.getQualityLevel());
            shipTaskDetail.setUom("");
            shipTaskDetail.setAllocatedNo("");
            shipTaskDetail.setFlowCode(WmsFlowCodeEnum.TAKE_BACK_OUT.getFlowCode());
            shipTaskDetail.setCreatedUserId(userContext.getUserId());
            shipTaskDetail.setCreatedUserName(userContext.getUserName());
            shipTaskDetail.setCreatedRealName(userContext.getRealName());
            shipTaskDetail.setCreatedTime(new Date());
            shipTaskDetail.setUpdatedUserId(userContext.getUserId());
            shipTaskDetail.setUpdatedUserName(userContext.getUserName());
            shipTaskDetail.setUpdatedRealName(userContext.getRealName());
            shipTaskDetail.setUpdatedTime(new Date());
            shipTaskDetail.setVersion(NumberUtils.INTEGER_ZERO);
            shipTaskDetail.setDeleted(NumberUtils.INTEGER_ZERO);

            fillSkuInfo(detail, shipTaskDetail, skuMap.get(shipTaskDetail.getSkuId()));
            shipDetailList.add(shipTaskDetail);
        });
        return shipDetailList;
    }

    private void fillSkuInfo(WmsDeliveryBillDetail deliveryDetail, ShipTaskDetailDo shipTaskDetail, SkuBasicRspDo skuCommonRspDomain) {
        if (skuCommonRspDomain == null) {
            log.error("sku不存在:{}", shipTaskDetail.getSkuId());
            if (WmsBizTypeEnum.JIAN_BIE.getBizType().equals(deliveryDetail.getBizType())) {
                shipTaskDetail.setGoodsTitle(deliveryDetail.getItemTitle());
                shipTaskDetail.setGoodsPic(deliveryDetail.getItemPic());
                shipTaskDetail.setGoodsArticleNumber(deliveryDetail.getItemArticleNumber());
                shipTaskDetail.setSpecs(deliveryDetail.getSpecs());
                shipTaskDetail.setBarcode(deliveryDetail.getBarCode());
            } else {
                throw new WmsException("sku不存在");
            }
        } else {
            shipTaskDetail.setSpecs(StringUtils.isNotBlank(skuCommonRspDomain.getPropertyText()) ? skuCommonRspDomain.getPropertyText() : "");
            shipTaskDetail.setGoodsTitle(StringUtils.isNotBlank(skuCommonRspDomain.getSpuName()) ? skuCommonRspDomain.getSpuName() : "");
            shipTaskDetail.setGoodsPic(StringUtils.isNotBlank(skuCommonRspDomain.getSkuLogoUrl()) ? skuCommonRspDomain.getSkuLogoUrl() : "");
            shipTaskDetail.setGoodsArticleNumber(StringUtils.isNotBlank(skuCommonRspDomain.getArtNoMain()) ? skuCommonRspDomain.getArtNoMain() : "");
            shipTaskDetail.setBarcode(StringUtils.isNotBlank(skuCommonRspDomain.getBarcode()) ? skuCommonRspDomain.getBarcode() : "");
        }
    }
}
