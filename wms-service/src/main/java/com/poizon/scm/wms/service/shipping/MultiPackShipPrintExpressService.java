package com.poizon.scm.wms.service.shipping;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.ofc.OfcServiceRepository;
import com.poizon.scm.wms.adapter.ofc.model.CategoryFlowMatchWarehouseParam;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsLogisticsBillRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackRepository;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.api.dto.response.logistics.MultiPackPrintInfo;
import com.poizon.scm.wms.api.enums.WmsQualityLevelEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.outbound.returngoods.entity.LogisticsInfo;
import com.poizon.scm.wms.domain.outbound.returngoods.service.ExpressBillPrintService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.WmsPackService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.PrintExpressParam;
import com.poizon.scm.wms.domain.outbound.worklog.WmsOutboundWorkLogService;
import com.poizon.scm.wms.domain.outbound.worklog.enums.WorkLogTypeEnum;
import com.poizon.scm.wms.domain.outbound.worklog.param.OperationLogParam;
import com.poizon.scm.wms.service.shipping.param.MultiPackShipPrintExpressParam;
import com.poizon.scm.wms.service.shipping.param.WmsPackLimitation;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundSubTypeEnum;
import com.poizon.scm.wms.util.enums.WmsPackStatusEnum;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.DateUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.poizon.scm.wms.common.enums.LockEnum.WMS_PACK_LOCK;

/**
 * wms发货新 v2版本：支持信任交接多包裹打面单
 */
@Component
@Slf4j
@Service
public class MultiPackShipPrintExpressService {
    @Resource
    private ExpressBillPrintService expressBillPrintService;
    @Resource
    private WmsLogisticsBillRepository logisticsBillRepository;
    @Resource
    private WmsPackRepository packRepository;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private WmsOutboundWorkLogService wmsOutboundWorkLogService;

    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Resource
    private OfcServiceRepository ofcServiceRepository;

    @Resource
    private ScpWarehouseRepository scpWarehouseRepository;

    @Resource
    private WmsPackService wmsPackService;

    private final static String  DEPOSITORY = "寄售";

    private final static String NO_QUALITY_LEVEL = "未质检";

    @Value("${categoryFlow.independentTransaction.switch:false}")
    private Boolean categoryFlowIndependentTransactionSwitch;

    @Transactional(rollbackFor = Exception.class)
    public List<MultiPackPrintInfo> printExpress(MultiPackShipPrintExpressParam param) {
        List<String> lockedPack = new ArrayList<>();
        boolean lockFlag = false;
        try {
            lockedPack = lockForPack(param);
            lockFlag = true;
            //加锁后再查一下包裹
            WmsPackDo wmsPackDo = checkPackAgain(param);
            // 打印面单触发按类目流向出库寻场景仓
            if (categoryFlowIndependentTransactionSwitch) {
                // 这里使用独立事务，否则outbound面单重构更新包裹的逻辑会和这里产生死锁问题
                wmsPackService.categoryFlowMatchWarehouse(wmsPackDo);
            } else {
                categoryFlowMatchWarehouse(wmsPackDo);
            }
            //这里不再跟新出库单为装箱完成，包裹没有封箱状态
            //打单（包含更新包裹）
            LogisticsInfo logisticsInfo = print(wmsPackDo,param);

            //sendOperateLog(buildOperateLog(param.getWmsPackDetailDos(),remark),param.getPackNo());
            //日志中心
            OperationLogParam operationLogParam = OperationLogParam.builder()
                    .orderNo(param.getPackNos())
                    .workLogTypeEnum(WorkLogTypeEnum.PACK_NO)
                    .build();
            wmsOutboundWorkLogService.operateLog(operationLogParam);
            // 打印面单后查询更新后的包裹信息
            WmsPackDo wmsPackDo1 = packRepository.queryByPackNo(wmsPackDo.getPackNo());
            // ... 这样写保险点
            wmsPackDo = Objects.nonNull(wmsPackDo1) ? wmsPackDo1 : wmsPackDo;
            return buildMultiPackPrintInfo(logisticsInfo,wmsPackDo);
        } catch (Exception e) {
            log.error("打面单失败:", e);
            throw new WmsException("打面单失败，请稍后重试");
        } finally {
            if (lockFlag) {
                releaseLockForPack(lockedPack);
            }
        }
    }

    /**
     * 按类目流向出库寻仓,
     * @param wmsPackDo
     * @return
     */
    public void categoryFlowMatchWarehouse(WmsPackDo wmsPackDo) {
        // 无限制码信息不寻仓
        if (StringUtils.isBlank(wmsPackDo.getLimitationCode())) {
            return;
        }
        // 有流向信息，无目标仓信息则认为是按流向调拨出，第一次打印面单时需进行寻仓
        WmsPackLimitation limitation = JSON.parseObject(wmsPackDo.getLimitationCode(), WmsPackLimitation.class);
        if (!(StringUtils.isBlank(limitation.getTargetWarehouseName()) && StringUtils.isNotBlank(limitation.getFlowName()))) {
            return;
        }
        // 查询包裹组所有信息构建寻仓参数
        List<WmsPackDo> wmsPackDos = packRepository.queryByGroupPackNo(
                wmsPackDo.getTenantCode(), wmsPackDo.getWarehouseCode(), wmsPackDo.getGroupPackNo()
        );
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(wmsPackDo.getDeliveryOrderCode());
        WmsPackDo firstWmsPackDo = wmsPackDos.iterator().next();
        CategoryFlowMatchWarehouseParam param = new CategoryFlowMatchWarehouseParam();
        param.setWarehouseCode(firstWmsPackDo.getWarehouseCode());
        param.setGroupPackNo(firstWmsPackDo.getGroupPackNo());
        param.setFlowCode(deliveryHeaderDo.getFlowCode());
        param.setQty(wmsPackDos.stream().mapToInt(WmsPackDo::getGoodsQty).sum());
        String depositWarehouseCode = ofcServiceRepository.categoryFlowMatchWarehouse(param);
        ScpWarehouseDo depositWarehouseDo = scpWarehouseRepository.queryByCodeAndTenant(depositWarehouseCode, wmsPackDo.getTenantCode());
        if (depositWarehouseDo == null) {
            log.error("OFC寻仓返回数据异常，目标仓不存在，warehouseCode:{}", depositWarehouseCode);
            throw new WmsException("OFC寻仓返回数据异常，目标仓不存在");
        }
        // 构建包裹目标仓更新参数
        List<WmsPackDo> updateWmsPackDos = new ArrayList<>(wmsPackDos.size());
        wmsPackDos.forEach(item -> {
            WmsPackLimitation itemLimitation = JSON.parseObject(item.getLimitationCode(), WmsPackLimitation.class);
            if(StringUtils.isNotBlank(itemLimitation.getTargetWarehouseName())) {
                log.error("寻仓更新目标仓信息，包裹已存在目标仓 {}", JsonUtils.serialize(item));
            }
            itemLimitation.setTargetWarehouse(depositWarehouseCode);
            itemLimitation.setTargetWarehouseName(depositWarehouseDo.getWarehouseName());
            WmsPackDo updateWmsPackDo = new WmsPackDo();
            updateWmsPackDo.setId(item.getId());
            updateWmsPackDo.setLimitationCode(JSON.toJSONString(itemLimitation));
            updateWmsPackDos.add(updateWmsPackDo);
        });
        packRepository.batchUpdatePack(updateWmsPackDos);
    }

    private List<MultiPackPrintInfo> buildMultiPackPrintInfo(LogisticsInfo logisticsInfo,WmsPackDo wmsPackDo){
        WmsPackLimitation limitation = JSON.parseObject(wmsPackDo.getLimitationCode(), WmsPackLimitation.class);

        Boolean transferDb = false;
        String deliveryOrderCode = wmsPackDo.getDeliveryOrderCode();
        String bizTypeStr = null;
        if (StringUtils.isNotBlank(deliveryOrderCode)) {
            DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
            if (null!=deliveryHeaderDo) {
                transferDb = WmsOutBoundSubTypeEnum.TRANSFER_DB.getCode().equals(deliveryHeaderDo.getSubType());
                Integer bizType = deliveryHeaderDo.getBizType();
                if(WmsBizTypeEnum.BIZ_OF_35.contains(bizType)){
                    bizTypeStr = DEPOSITORY;
                }
                else {
                    bizTypeStr = WmsBizTypeEnum.getDescByBizType(bizType);
                }
            }
        }
        List<MultiPackPrintInfo> printInfos = new ArrayList<>();
        for (LogisticsInfo.SubExpress n : logisticsInfo.getSubExpress()) {
            MultiPackPrintInfo info = new MultiPackPrintInfo();
            info.setSubExpressCode(n.getExpressCode());
            info.setTotalQty(n.getGoodsQty());
            info.setPrintDateTime(DateUtils.formatDate(new Date(),DateUtils.FORMAT_TIME));
            info.setPrintUserRealName(OperationUserContextHolder.get().getRealName());
            info.setBizType(bizTypeStr);
            String qualityLevelName = WmsQualityLevelEnum.getDescByCode(limitation.getQualityLevel());
            if(StringUtils.isBlank(qualityLevelName)){
                qualityLevelName = NO_QUALITY_LEVEL;
            }
            info.setQualityLevel(qualityLevelName);
            info.setTargetWarehouseName(limitation.getTargetWarehouseName());
            info.setTransferDb(transferDb);
            printInfos.add(info);
        }
        return printInfos;
    }

    /**
     * 批量加锁
     * @param param
     * @return
     */
    private List<String> lockForPack(MultiPackShipPrintExpressParam param) {
        List<String> lockedList = new ArrayList<>();
        try {
            for (String packNo : param.getPackNos()) {
                boolean lockFlag = distributeLockUtil.tryLockForBiz(WMS_PACK_LOCK, packNo);
                if (!lockFlag) {
                    // 加锁失败直接报异常
                    log.warn("lock pack uniqueCode error, uniqueCode is [{}]", packNo);
                    throw new WmsOperationException("装箱唯一码加锁失败");
                }
                lockedList.add(packNo);
            }
            return lockedList;
        } catch (InterruptedException e) {
            log.error("装箱唯一码获取锁失败,未获取到锁", param.getPackNos());
            Thread.currentThread().interrupt();
            throw new WmsException("请稍后重试");
        } finally {
            if (lockedList.size() != param.getPackNos().size()) {
                lockedList.forEach(uniqueCode -> distributeLockUtil.releaseLockForBiz(LockEnum.WMS_PACK_LOCK, uniqueCode));
            }
        }

    }

    /**
     * 批量释放锁
     * @param packNos
     */
    private void releaseLockForPack(List<String> packNos) {
        if (CollectionUtils.isEmpty(packNos)) {
            log.error("release lock for pack is error, empty uniqueCode");
            return;
        }
        packNos.forEach(item -> {
            distributeLockUtil.releaseLockForBiz(LockEnum.
                    WMS_PACK_LOCK, item);
        });
    }




    private LogisticsInfo print(WmsPackDo wmsPackDo,MultiPackShipPrintExpressParam param) {
        //出库单任意取一个
        String deliveryOrderCode = wmsPackDo.getDeliveryOrderCode();
        //下单，返回打印面单信息
        PrintExpressParam printExpressParam = new PrintExpressParam();
        printExpressParam.setDeliveryOrderCode(deliveryOrderCode);
        if (!BooleanUtils.isTrue(param.getPrintAllPack())) {
            List<String> packNos = param.getPackNos();
            if (CollectionUtils.isNotEmpty(packNos) && packNos.size() == 1) {
                printExpressParam.setCurrentPackNo(wmsPackDo.getPackNo());
            }
        }
        printExpressParam.setSubExpressCode("");
        printExpressParam.setAutoMakeOrder(true);
        //不校验出库单状态，因为没封箱
        printExpressParam.setEscapeOrderStatusValidate(true);
        printExpressParam.setGroupPackNo(wmsPackDo.getGroupPackNo());
        return expressBillPrintService.printExpress(printExpressParam);
    }

    private WmsPackDo checkPackAgain(MultiPackShipPrintExpressParam param){
        //加锁后再查一下包裹,包裹明细非空是否校验
        Map<String, WmsPackDo> stringWmsPackDoMap = packRepository.queryByPackNos(new HashSet<>(param.getPackNos()));
        if(stringWmsPackDoMap == null || stringWmsPackDoMap.size()<1 || stringWmsPackDoMap.size() != param.getPackNos().size()){
            throw new WmsException("包裹不存在，不能打单");
        }
        WmsPackDo wmsPackDo = stringWmsPackDoMap.get(param.getPackNos().get(0));
        if(!wmsPackDo.getGroupPackNo().equals(param.getGroupPackNo())){
            throw new WmsException("组包裹号不正确，不能打单");
        }
        //如果已打单，必须存在物流信息
        List<WmsLogisticsBillDo> logisticsBillDoList = logisticsBillRepository.queryByDeliveryOrderCode(wmsPackDo.getDeliveryOrderCode());
        if (WmsPackStatusEnum.PRINTED.getStatus().equals(wmsPackDo.getPackStatus()) && CollectionUtils.isEmpty(logisticsBillDoList)) {
            //如果包裹是已打单, 且非tms下单则不能打印面单
            throw new WmsOperationException("线下生成的面单, 无法重新打印面单");
        }
        return wmsPackDo;

    }

}
