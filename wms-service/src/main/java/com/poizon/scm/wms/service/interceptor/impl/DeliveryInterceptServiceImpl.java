package com.poizon.scm.wms.service.interceptor.impl;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.ofc.gw.order.OfcQueryOrderInfoBySubOrderNoReq;
import com.poizon.scm.ofc.gw.order.OfcTradeSubOrderInfo;
import com.poizon.scm.ofc.sdk.api.dubbo.OrderHaService;
import com.poizon.scm.wms.adapter.inbound.inbound.model.BillSkuUcInfoDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryUserDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryUserRepository;
import com.poizon.scm.wms.adapter.outbound.model.BackToOriginalDo;
import com.poizon.scm.wms.api.enums.InterceptTypeEnum;
import com.poizon.scm.wms.api.enums.WmsInboundTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.dao.curd.InventoryCurd;
import com.poizon.scm.wms.dao.entitys.InvInventoryAllocatedEntity;
import com.poizon.scm.wms.dao.mappers.DeliveryHeaderMapper;
import com.poizon.scm.wms.domain.inbound.entry.service.query.BillSkuUcInfoQueryService;
import com.poizon.scm.wms.domain.outbound.outbound.entity.RepeatDeliveryInterceptorInfo;
import com.poizon.scm.wms.service.backToOriginal.BackToOriginalService;
import com.poizon.scm.wms.service.interceptor.IDeliveryInterceptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;

/**
 * <AUTHOR> Zhang
 * @date 2020/6/27 3:02 下午
 * @description
 */
@Service
@Slf4j
public class DeliveryInterceptServiceImpl implements IDeliveryInterceptService {

    @Autowired
    private BackToOriginalService backToOriginalService;
    @Autowired
    private BillSkuUcInfoQueryService billSkuUcInfoQueryService;
    @Autowired
    InventoryCurd inventoryCurd;
    @Autowired
    Executor asyncServiceExecutor;
    @Autowired
    private DeliveryHeaderMapper deliveryHeaderMapper;

    @Autowired
    private DeliveryUserRepository deliveryUserRepository;

    @Value("#{'${switch.intercept.delivery:SH99}'.split(',')}")
    private List<String> interceptWarehouses;

    @Autowired
    private OrderHaService orderHaService;

    @Override
    public boolean isRepeatDeliveryInterceptor(Long receiverId, String uniqueCode, String warehouseCode, String tenantCode) {
        log.info("isRepeatDeliveryInterceptor receiverId={},uniqueCode={}", receiverId, uniqueCode);
        if (StringUtils.isBlank(uniqueCode) || receiverId == null) {
            log.warn("缺失参数，返回false");
            return false;
        }
        boolean intercept = false;
        // 1、验证商品来源，如果orderCode表示不是客退入库的，那就没毛病
        List<BillSkuUcInfoDo> ucInfoList = billSkuUcInfoQueryService.queryInfoByUniqueCodeAndType(uniqueCode, WmsInboundTypeEnum.KTRK.getCode(), tenantCode);
        String orderCode = CollectionUtils.isNotEmpty(ucInfoList) ? ucInfoList.get(0).getRelatedOrderCode() : "";
        // 2、验证用户是否曾经购买过这件商品
        if (StringUtils.isNotBlank(orderCode)) {
            // 是退货入的小仓库，需要验证是否发货同一个买家
            BackToOriginalDo backInfo = this.getBackInfo(orderCode, warehouseCode, tenantCode);
            if (backInfo == null) {
                OfcQueryOrderInfoBySubOrderNoReq orderInfoBySubOrderNoReq = new OfcQueryOrderInfoBySubOrderNoReq();
                orderInfoBySubOrderNoReq.setSubOrderNo(orderCode);
                // 小仓库的历史里面没有记录，需要从交易查下有无购买记录
                Result<OfcTradeSubOrderInfo> orderRequest = orderHaService.ofcFindBySubOrderNo(orderInfoBySubOrderNoReq);
                log.info("订单查询返回：{}", JSON.toJSONString(orderRequest));
                OfcTradeSubOrderInfo orderInfo = orderRequest.getData();
                intercept = orderInfo != null && receiverId.equals(orderInfo.getBuyerId());
            } else {
                if (receiverId.equals(backInfo.getReceiverId())) {
                    // 之前有发货过
                    intercept = true;
                } else {
                    log.info("重复发货同一买家递归检查...receiverId:{}, uniqueCode:{}", receiverId, backInfo.getUniqueCode());
                    String lastUniqueCode = backInfo.getUniqueCode();
                    intercept = this.isRepeatDeliveryInterceptor(receiverId, lastUniqueCode, warehouseCode, tenantCode);
                }
            }
        }
        log.info("拦截完成，结果是isIntercept:{}", intercept);
        return intercept;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repeatDeliveryInterceptor(final RepeatDeliveryInterceptorInfo info) {
        if(Objects.isNull(info)){
            log.error("拦截重复出库单参数错误。");
            return;
        }
        // 不需要拦截 则直接返回
        boolean isNeedIntercept = interceptWarehouses.contains(info.getWarehouseCode());
        // 这里是补加的逻辑需要查询下是否有收货用户
        DeliveryUserDo deliveryUserDo = deliveryUserRepository.selectByDeliveryOrderCode(info.getDeliveryOrderCode(), info.getTenantCode());
        if (null == deliveryUserDo) {
            log.info("出库单[{}] 查询不到收货人发货人记录！", JSON.toJSONString(info));
            return;
        }
        info.setReceiverId(deliveryUserDo.getReceiverId());
        if(!isNeedIntercept) {
            return;
        }
        try {
            asyncServiceExecutor.execute(() -> {
                try {
                    Long receiverId = info.getReceiverId();
                    String shipmentNo = info.getDeliveryOrderCode();
                    String tenantCode = info.getTenantCode();
                    if(!com.google.common.base.Objects.equal(tenantCode, OperationUserContextHolder.getTenantCode())){
                        log.warn("tenant-check:RepeatDeliveryInterceptorInfo中tenant:{}和上下问中tenant：{}不一致"
                                ,info.getTenantCode(),OperationUserContextHolder.getTenantCode());
                    }
                    if(StringUtils.isBlank(tenantCode)){
                        tenantCode = OperationUserContextHolder.getTenantCode();
                    }
                    boolean isRepeatDelivery = false;
                    List<InvInventoryAllocatedEntity> inventoryAllocatedEntityList = inventoryCurd.queryAllocateRecordList(tenantCode, shipmentNo);
                    for (InvInventoryAllocatedEntity a : inventoryAllocatedEntityList) {
                        isRepeatDelivery = this.isRepeatDeliveryInterceptor(receiverId, a.getUniqueCode(), a.getWarehouseCode(), tenantCode);
                        if (isRepeatDelivery) {
                            break;
                        }
                    }
                    if (isRepeatDelivery) {
                        deliveryHeaderMapper.updateInterceptTypeByOrderCode(shipmentNo, InterceptTypeEnum.FM.getCode(), tenantCode);
                    }
                } catch (Exception e) {
                    log.error("重复发货同一买家拦截出现异常222！为了避免库存分配失败，这里吃掉异常。", e);
                }
            });
        } catch (Exception e) {
            log.error("重复发货同一买家拦截出现异常111！为了避免库存分配失败，这里吃掉异常。", e);
        }
    }

    private BackToOriginalDo getBackInfo(String orderCode, String warehouseCode, String tenantCode) {
        List<BackToOriginalDo> backList = backToOriginalService.queryByOutOrderCode(orderCode, warehouseCode, tenantCode);
        this.fbiWarning(backList.size(), orderCode);
        if (CollectionUtils.isNotEmpty(backList)) {
            return backList.get(0);
        } else {
            return null;
        }
    }


    private void fbiWarning(int size, String orderCode) {
        if (size > 1) {
            log.warn("back_to_original查到了多条数据，不正常。为保持业务流转，取第一条！size:{}, relateOrderCode:{}", size, orderCode);
        }
    }


}
