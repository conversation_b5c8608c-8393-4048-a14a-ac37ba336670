package com.poizon.scm.wms.domain.outbound.ship;

import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailResultCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.api.enums.SequenceTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskCreateParam;
import com.poizon.scm.wms.domain.outbound.ship.entity.param.create.ShipTaskDetailParam;
import com.poizon.scm.wms.pojo.inventory.InventoryBasePojo;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateService;
import com.poizon.scm.wms.service.inventory.query.InventoryQueryService;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @Date 2023/12/19 11:20
 * @Description 兼容库存不存在创建操作发货任务，后续会废弃
 */
@Component
@Slf4j
public class ShipTaskNothingnessCommandService {
    @Resource
    private ShipTaskCommandRepository shipTaskCommandRepository;
    @Resource
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;
    @Resource
    private InventoryQueryService inventoryQueryService;
    @Resource
    private DistributeLockUtil distributeLockUtil;
    @Resource
    private ShipTaskQueryRepository shipTaskQueryRepository;
    @Resource
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;
    @Autowired
    private InventoryOperateService inventoryOperateService;
    @Autowired
    private ShipTaskDetailResultCommandRepository repository;
    /**
     * 库存存在不存在都支持创建并发货完成
     *
     * @param param
     * @return
     */
    public void createAndShipTaskForInvNothingness(ShipTaskCreateParam param) {
        String deliveryOrderCode = param.getHeader().getReferenceNo();
        String expressNo = param.getHeader().getExpressNo();
        boolean lockFlag = false;
        try {
            if (distributeLockUtil.tryLockForBiz(LockEnum.SHIP_TASK_CREATE_LOCK, param.getHeader().getReferenceNo())) {
                lockFlag = true;
                /*发货任务幂等校验*/
                List<ShipTaskDo> shipTaskDos = queryShipTaskByReferenceNo(deliveryOrderCode);
                // 发货任务是否存在
                boolean shipTaskExist = CollectionUtils.isNotEmpty(shipTaskDos);
                if (shipTaskExist) {
                    // 发货任务
                    ShipTaskDo shipTaskDo = shipTaskDos.get(0);
                    // 发货任务是否完成
                    boolean shipTaskComplete = CollectionUtils.isNotEmpty(shipTaskDos.stream()
                            .filter(entity -> TaskStatusEnum.COMPLETE.getStatus().equals(entity.getStatus()))
                            .collect(Collectors.toList()));
                    if (shipTaskComplete) {
                        log.info("出库单{}发货任务已经执行。", deliveryOrderCode);
                        return;
                    }
                    // 执行发货任务
                    executeShipTask(shipTaskDo, expressNo);
                } else {
                    // 创建并执行发货任务
                    ShipTaskDo taskDo = saveShipTask(param);
                    saveShipTaskDetail(param, taskDo);
                    executeShipTask(taskDo, expressNo);
                }
            }
        } catch (InterruptedException ex) {
            log.error("发货任务创建加锁错误", ex);
            Thread.currentThread().interrupt();
            throw new WmsException("创建发货任务失败");
        } catch (Exception ex) {
            log.error("发货任务创建加锁错误", ex);
            throw new WmsException("创建发货任务失败");
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SHIP_TASK_CREATE_LOCK, param.getHeader().getReferenceNo());
            }
        }
    }
    /**
     * 幂等发货任务是否存在
     *
     * @param deliveryOrderCode
     * @return
     */
    private List<ShipTaskDo> queryShipTaskByReferenceNo(String deliveryOrderCode) {
        List<ShipTaskDo> currentShipTaskDoList = shipTaskQueryRepository.findByReferenceNo(deliveryOrderCode)
                .stream()
                .filter(entity -> !TaskStatusEnum.CANCEL.getStatus().equals(entity.getStatus()))
                .collect(Collectors.toList());
        return currentShipTaskDoList;
    }
    /**
     * 执行发货任务
     *
     * @param shipTaskDo
     */
    public void executeShipTask(ShipTaskDo shipTaskDo, String expressNo) {
        if (null == shipTaskDo) {
            return;
        }
        String taskNo = shipTaskDo.getTaskNo();
        String warehouseCode = shipTaskDo.getWarehouseCode();
        String tenantCode = shipTaskDo.getTenantCode();
        // 要查一遍发货任务，发货明细
        List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.
                queryByTaskNo(taskNo, warehouseCode, tenantCode);
        if (CollectionUtils.isEmpty(shipTaskDetailDos)) {
            log.error("发货任务异常：发货任务[{}]没有明细", taskNo);
            return;
        }
        ShipTaskDetailDo updateShipTaskDetail = buildUpdateShipTaskDetailList(shipTaskDetailDos);
        ShipTaskDo updateShipTask = buildUpdateShipTask(shipTaskDo);
        // 扣库存
        // 更新任务头
        shipTaskCommandRepository.updateSelectiveUseVersionLock(updateShipTask);
        // 更新任务明细
        shipTaskDetailCommandRepository.updateTaskDetailsByTaskNo(updateShipTaskDetail);
        // 写入任务结果
        List<ShipTaskDetailResultDo> taskDetailResultDos = buildShipTaskDetailResult(updateShipTask, shipTaskDetailDos, expressNo);
        repository.batchSave(taskDetailResultDos);
    }
    private String subInv(ShipTaskDetailDo shipTaskDetailDo, String expressNo) {
        /* 现货扣库存 */
        InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
        inventoryBasePojo.setTenantCode(Optional.ofNullable(shipTaskDetailDo)
                .map(ShipTaskDetailDo::getTenantCode).orElse(OperationUserContextHolder.getTenantCode()));
        inventoryBasePojo.setReferenceNo(Optional.ofNullable(shipTaskDetailDo)
                .map(ShipTaskDetailDo::getUniqueCode).orElse(shipTaskDetailDo.getUniqueCode()));
        inventoryBasePojo.setReferenceType(shipTaskDetailDo.getReferenceType());
        inventoryBasePojo.setOperateQty(1);
        inventoryBasePojo.setReferenceDetailNo(Optional.ofNullable(shipTaskDetailDo)
                .map(ShipTaskDetailDo::getDetailNo).orElse(shipTaskDetailDo.getReferenceDetailNo()));
        inventoryBasePojo.setTaskDetailNo(Optional.ofNullable(shipTaskDetailDo)
                .map(ShipTaskDetailDo::getDetailNo).orElse(shipTaskDetailDo.getUniqueCode()));
        inventoryBasePojo.setTaskNo(Optional.ofNullable(shipTaskDetailDo)
                .map(ShipTaskDetailDo::getTaskNo).orElse(shipTaskDetailDo.getUniqueCode()));
        inventoryBasePojo.setRelativeInventoryNo(shipTaskDetailDo.getInventoryNo());
        inventoryBasePojo.setExpressNo(expressNo);
        String inventoryNo = inventoryOperateService.ship(inventoryBasePojo);
        if (StringUtils.isBlank(inventoryNo)) {
            log.error("发货明细{}扣减库存失败", Optional.ofNullable(shipTaskDetailDo)
                    .map(ShipTaskDetailDo::getDetailNo).orElse(shipTaskDetailDo.getUniqueCode()));
            throw new WmsException("发货明细扣减库存失败");
        }
        return inventoryNo;
    }
    private ShipTaskDo buildUpdateShipTask(ShipTaskDo shipTaskDo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        ShipTaskDo upShipTask = new ShipTaskDo();
        upShipTask.setTaskNo(shipTaskDo.getTaskNo());
        upShipTask.setStatus(TaskStatusEnum.COMPLETE.getStatus());
        upShipTask.setWarehouseCode(shipTaskDo.getWarehouseCode());
        upShipTask.setTenantCode(shipTaskDo.getTenantCode());
        upShipTask.setOperationUserId(Optional.ofNullable(userContext.getUserId()).orElse(0L));
        upShipTask.setOperationUserName(Optional.ofNullable(userContext.getUserName()).orElse(StringUtils.EMPTY));
        upShipTask.setOperationRealName(Optional.ofNullable(userContext.getRealName()).orElse(StringUtils.EMPTY));
        upShipTask.setUpdatedUserId(Optional.ofNullable(userContext.getUserId()).orElse(0L));
        upShipTask.setUpdatedUserName(Optional.ofNullable(userContext.getUserName()).orElse(StringUtils.EMPTY));
        upShipTask.setUpdatedRealName(Optional.ofNullable(userContext.getRealName()).orElse(StringUtils.EMPTY));
        upShipTask.setUpdatedTime(new Date());
        upShipTask.setVersion(shipTaskDo.getVersion());
        upShipTask.setTotalQty(shipTaskDo.getTotalQty());
        return upShipTask;
    }
    private ShipTaskDetailDo buildUpdateShipTaskDetailList(List<ShipTaskDetailDo> shipTaskDetailDos) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        ShipTaskDetailDo originDetail = shipTaskDetailDos.get(0);
        ShipTaskDetailDo shipTaskDetailDo = new ShipTaskDetailDo();
        shipTaskDetailDo.setTaskNo(originDetail.getTaskNo());
        shipTaskDetailDo.setTaskType(originDetail.getTaskType());
        shipTaskDetailDo.setStatus(TaskStatusEnum.COMPLETE.getStatus().byteValue());
        shipTaskDetailDo.setTenantCode(originDetail.getTenantCode());
        shipTaskDetailDo.setOperationQty(originDetail.getQty());
        shipTaskDetailDo.setUpdatedUserId(Optional.ofNullable(userContext.getUserId()).orElse(0L));
        shipTaskDetailDo.setUpdatedUserName(Optional.ofNullable(userContext.getUserName()).orElse(StringUtils.EMPTY));
        shipTaskDetailDo.setUpdatedRealName(Optional.ofNullable(userContext.getRealName()).orElse(StringUtils.EMPTY));
        shipTaskDetailDo.setUpdatedTime(new Date());
        return shipTaskDetailDo;
    }
    private List<ShipTaskDetailResultDo> buildShipTaskDetailResult(ShipTaskDo shipTaskDo, List<ShipTaskDetailDo> shipTaskDetailDos, String expressNo) {
        List<ShipTaskDetailResultDo> taskDetailResultDos = Lists.newArrayList();
        OperationUserContext userContext = OperationUserContextHolder.get();
        ShipTaskDetailResultDo shipTaskDetailResultDo = null;
        for (ShipTaskDetailDo shipTaskDetailDo : shipTaskDetailDos) {
            shipTaskDetailResultDo = new ShipTaskDetailResultDo();
            shipTaskDetailResultDo.setResultNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL_RESULT.getSequenceType()));
            shipTaskDetailResultDo.setTaskNo(shipTaskDo.getTaskNo());
            shipTaskDetailResultDo.setTaskDetailNo(shipTaskDetailDo.getDetailNo());
            shipTaskDetailResultDo.setTaskType(shipTaskDetailDo.getTaskType());
            shipTaskDetailResultDo.setBarcode(shipTaskDetailDo.getBarcode());
            shipTaskDetailResultDo.setUniqueCode(shipTaskDetailDo.getUniqueCode());
            shipTaskDetailResultDo.setSkuId(shipTaskDetailDo.getSkuId());
            shipTaskDetailResultDo.setOwnerCode(shipTaskDetailDo.getOwnerCode());
            shipTaskDetailResultDo.setVendorCode(shipTaskDetailDo.getVendorCode());
            shipTaskDetailResultDo.setContainerCode(shipTaskDetailDo.getContainerCode());
            shipTaskDetailResultDo.setWarehouseCode(shipTaskDetailDo.getWarehouseCode());
            shipTaskDetailResultDo.setTenantCode(shipTaskDetailDo.getTenantCode());
            shipTaskDetailResultDo.setLocationCode(shipTaskDetailDo.getLocationCode());
            shipTaskDetailResultDo.setPlanQty(shipTaskDetailDo.getQty());
            shipTaskDetailResultDo.setOperationQty(shipTaskDetailDo.getQty());
            shipTaskDetailResultDo.setMfgTime(shipTaskDetailDo.getMfgTime());
            shipTaskDetailResultDo.setExpTime(shipTaskDetailDo.getExpTime());
            shipTaskDetailResultDo.setQuality((byte)0);
            shipTaskDetailResultDo.setRemark("");
            shipTaskDetailResultDo.setQualityLevel(shipTaskDetailDo.getQualityLevel());
            shipTaskDetailResultDo.setInventoryNo("");
            if (StringUtils.isNotBlank(shipTaskDetailDo.getInventoryNo())) {
                String inventoryNo = subInv(shipTaskDetailDo, expressNo);
                shipTaskDetailResultDo.setInventoryNo(inventoryNo);
            }
            shipTaskDetailResultDo.setOperationUserId(Optional.ofNullable(userContext.getUserId()).orElse(0L));
            shipTaskDetailResultDo.setOperationUserName(Optional.ofNullable(userContext.getUserName()).orElse(StringUtils.EMPTY));
            shipTaskDetailResultDo.setOperationRealName(Optional.ofNullable(userContext.getRealName()).orElse(StringUtils.EMPTY));
            shipTaskDetailResultDo.setCreatedUserId(Optional.ofNullable(userContext.getUserId()).orElse(0L));
            shipTaskDetailResultDo.setCreatedUserName(Optional.ofNullable(userContext.getUserName()).orElse(StringUtils.EMPTY));
            shipTaskDetailResultDo.setCreatedRealName(Optional.ofNullable(userContext.getRealName()).orElse(StringUtils.EMPTY));
            shipTaskDetailResultDo.setCreatedTime(new Date());
            shipTaskDetailResultDo.setUpdatedUserId(Optional.ofNullable(userContext.getUserId()).orElse(0L));
            shipTaskDetailResultDo.setUpdatedUserName(Optional.ofNullable(userContext.getUserName()).orElse(StringUtils.EMPTY));
            shipTaskDetailResultDo.setUpdatedRealName(Optional.ofNullable(userContext.getRealName()).orElse(StringUtils.EMPTY));
            shipTaskDetailResultDo.setUpdatedTime(new Date());
            shipTaskDetailResultDo.setVersion(NumberUtils.INTEGER_ZERO);
            shipTaskDetailResultDo.setDeleted(NumberUtils.INTEGER_ZERO);
            shipTaskDetailResultDo.setOriginalOrderCode(shipTaskDetailDo.getOriginalOrderCode());
            taskDetailResultDos.add(shipTaskDetailResultDo);
        }
        return taskDetailResultDos;
    }
    private void saveShipTaskDetail(ShipTaskCreateParam param, ShipTaskDo taskDo) {
        List<String> inventoryNos = param.getDetail().stream().map(ShipTaskDetailParam::getInventoryNo).collect(Collectors.toList());
        Map<String, InventoryDo> inventoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inventoryNos)) {
            inventoryMap = inventoryQueryService.queryInventoryDoByInventoryNos(inventoryNos);
        }
        // 创建发货任务明细
        List<ShipTaskDetailDo> shipTaskDetailList = new ArrayList<>();
        Map<String, InventoryDo> finalInventoryMap = inventoryMap;
        param.getDetail().forEach(pickTD -> {
            ShipTaskDetailDo detailDo = new ShipTaskDetailDo();
            detailDo.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            detailDo.setTaskNo(taskDo.getTaskNo());
            detailDo.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            detailDo.setBizType(pickTD.getBizType().byteValue());
            detailDo.setStatus(TaskStatusEnum.INIT.getStatus().byteValue());
            detailDo.setReferenceNo(taskDo.getReferenceNo());
            detailDo.setReferenceType(taskDo.getReferenceType());
            detailDo.setReferenceDetailNo(pickTD.getReferenceDetailNo());
            detailDo.setInventoryNo(pickTD.getInventoryNo());
            detailDo.setQty(pickTD.getQty());
            detailDo.setOperationQty(NumberUtils.INTEGER_ZERO);
            InventoryDo inventoryDo = finalInventoryMap.getOrDefault(pickTD.getInventoryNo(),new InventoryDo());
            detailDo.setContainerCode(Optional.ofNullable(inventoryDo.getContainerCode()).orElse(StringUtils.EMPTY));
            detailDo.setPoNo(Optional.ofNullable(inventoryDo.getOriginalOrderCode()).orElse(StringUtils.EMPTY));
            detailDo.setEntryOrderCode(Optional.ofNullable(inventoryDo.getEntryOrderCode()).orElse(StringUtils.EMPTY));
            detailDo.setVendorCode(Optional.ofNullable(inventoryDo.getVendorCode()).orElse(StringUtils.EMPTY));
            detailDo.setFirstReceivedTime(Optional.ofNullable(inventoryDo.getFirstReceivedTime()).orElse(new Date()));
            detailDo.setOriginalDetailNo(pickTD.getOriginalDetailNo());
            detailDo.setWarehouseCode(pickTD.getWarehouseCode());
            detailDo.setTenantCode(OperationUserContextHolder.getTenantCode());
            detailDo.setOwnerCode(pickTD.getOwnerCode());
            detailDo.setBarcode(pickTD.getBarcode());
            detailDo.setUniqueCode(pickTD.getUniqueCode());
            detailDo.setSkuId(pickTD.getSkuId());
            detailDo.setGoodsTitle(pickTD.getGoodsTitle());
            detailDo.setGoodsPic(pickTD.getGoodsPic());
            detailDo.setGoodsArticleNumber(pickTD.getGoodsArticleNumber());
            detailDo.setQualityLevel(pickTD.getQualityLevel());
            detailDo.setSpecs(pickTD.getSpecs());
            detailDo.setUom(pickTD.getUom());
            detailDo.setLocationCode(pickTD.getLocationCode());
            detailDo.setAllocatedNo(pickTD.getAllocatedNo());
            detailDo.setFlowCode(pickTD.getFlowCode());
            OperationUserContext userContext = OperationUserContextHolder.get();
            detailDo.setCreatedUserId(userContext.getUserId());
            detailDo.setCreatedUserName(userContext.getUserName());
            detailDo.setCreatedRealName(userContext.getRealName());
            detailDo.setMfgTime(pickTD.getMfgTime());
            detailDo.setExpTime(pickTD.getExpTime());
            detailDo.setOriginalOrderCode(pickTD.getOriginalOrderCode());
            detailDo.setBatchNo(Optional.ofNullable(inventoryDo.getBatchNo()).orElse(StringUtils.EMPTY));
            detailDo.setProductionBatchNo(Optional.ofNullable(inventoryDo.getProductionBatchNo()).orElse(StringUtils.EMPTY));
            shipTaskDetailList.add(detailDo);
        });
        int tdListResult = shipTaskDetailCommandRepository.batchSave(shipTaskDetailList);
        Preconditions.checkBiz(tdListResult > 0, WmsExceptionCode.TASK_DETAIL_SAVE_FAIL);
    }
    private ShipTaskDo saveShipTask(ShipTaskCreateParam param) {
        int totalQty = param.getDetail().stream().mapToInt(ShipTaskDetailParam::getQty).sum();
        // 创建发货任务头
        OperationUserContext user = OperationUserContextHolder.get();
        Date now = new Date();
        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        taskDo.setFlowCode(param.getHeader().getFlowCode());
        taskDo.setReferenceType(param.getHeader().getReferenceType());
        taskDo.setReferenceNo(param.getHeader().getReferenceNo());
        taskDo.setStatus(TaskStatusEnum.INIT.getStatus());
        taskDo.setWarehouseCode(user.getWarehouseCode());
        taskDo.setTenantCode(user.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setOperationUserId(user.getUserId());
        taskDo.setOperationRealName(user.getRealName());
        taskDo.setOperationUserName(user.getUserName());
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        taskDo.setCreatedUserId(user.getUserId());
        taskDo.setCreatedUserName(user.getUserName());
        taskDo.setCreatedRealName(user.getRealName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        taskDo.setVersion(NumberUtils.INTEGER_ZERO);
        taskDo.setDeleted(NumberUtils.INTEGER_ZERO);
        int thResult = shipTaskCommandRepository.save(taskDo);
        Preconditions.checkBiz(thResult == 1, WmsExceptionCode.TASK_HEADER_SAVE_FAIL);
        return taskDo;
    }
}