package com.poizon.scm.wms.service.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.bas.locationLimit.LocationLimitRepository;
import com.poizon.scm.wms.adapter.common.BasLocationRepository;
import com.poizon.scm.wms.adapter.common.model.BasAreaDo;
import com.poizon.scm.wms.adapter.common.model.BasLocationDo;
import com.poizon.scm.wms.adapter.common.model.BasLocationParam;
import com.poizon.scm.wms.adapter.common.model.req.*;
import com.poizon.scm.wms.adapter.common.model.rsp.LocationPropertyDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.sys.model.SysDictDo;
import com.poizon.scm.wms.adapter.sys.repository.SysDictRepository;
import com.poizon.scm.wms.adapter.task.model.BasWorkZoneDo;
import com.poizon.scm.wms.adapter.task.repository.BasWorkZoneRepository;
import com.poizon.scm.wms.api.command.common.request.LocationInitRequest;
import com.poizon.scm.wms.api.dto.request.base.*;
import com.poizon.scm.wms.api.dto.request.reportDownload.ReportCreateRequest;
import com.poizon.scm.wms.api.dto.response.base.BasLocationResponse;
import com.poizon.scm.wms.api.dto.response.base.BasTypeResponse;
import com.poizon.scm.wms.api.dto.response.report.ReportCenterResponse;
import com.poizon.scm.wms.api.enums.*;
import com.poizon.scm.wms.api.query.inventory.response.LocationInventoryResponse;
import com.poizon.scm.wms.bas.api.command.locationLimit.response.BasLocationLimitResponse;
import com.poizon.scm.wms.common.IExcelExportService;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constants.SimpleStatus;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.*;
import com.poizon.scm.wms.dao.entitys.InvInventoryEntity;
import com.poizon.scm.wms.domain.bas.BasWorkZoneQueryService;
import com.poizon.scm.wms.domain.bas.entity.param.BasWorkZoneDetailParam;
import com.poizon.scm.wms.infra.common.mapper.BasLocationDoExtMapper;
import com.poizon.scm.wms.infra.task.mapper.BasWorkZoneDoExtMapper;
import com.poizon.scm.wms.service.base.param.PickSeqParam;
import com.poizon.scm.wms.service.base.processor.LocationChangeProcessor;
import com.poizon.scm.wms.service.inventory.query.InventoryQueryService;
import com.poizon.scm.wms.service.reportDownload.ReportDownloadService;
import com.poizon.scm.wms.service.sys.SysDictService;
import com.poizon.scm.wms.service.sys.SysDictV2Service;
import com.poizon.scm.wms.util.enums.LocationIsMultiItemEnum;
import com.poizon.scm.wms.util.enums.LocationIsMultiSkuEnum;
import com.poizon.scm.wms.util.enums.LocationPropertyEnum;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import com.poizon.scm.wms.util.util.PagingObjectUtils;
import com.poizon.scm.wms.util.util.RegexUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.util.util.ArrayUtils.groupApply;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2020/4/24 4:52 下午
 **/
@Slf4j
@Component
public class BasLocationServiceImpl implements BasLocationService {

    @Autowired
    private BasWarehouseService basWarehouseService;
    @Autowired
    private BasAreaService basAreaService;
    @Autowired
    private InventoryQueryService inventoryQueryService;
    @Autowired
    private SysDictService sysDictService;

    @Autowired
    private BasLocationRepository basLocationRepository;

    @Autowired
    private BasLocationDoExtMapper basLocationDoExtMapper;

    @Autowired
    private IExcelExportService excelExportService;

    @Autowired
    private BasWorkZoneDoExtMapper basWorkZoneDoExtMapper;
    @Autowired
    private LocationChangeProcessor locationChangeProcessor;
    @Autowired
    private BasWorkZoneQueryService workZoneQueryService;

    @Autowired
    private BasWorkZoneRepository basWorkZoneRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private ReportDownloadService reportDownloadService;

    @Autowired
    @Qualifier("asyncServiceExecutor")
    Executor asyncServiceExecutor;

    @Autowired
    private SysDictRepository sysDictRepository;

    @Resource
    private SysDictV2Service sysDictV2Service;

    @Autowired
    private LocationLimitRepository locationLimitRepository;


    public static final String SPECIAL_SYMBOL = "——";

    public static final String COMMON_SYMBOL = ",";

    private static final int MAX_LOCATION_NUM = 99;

    private static final int MAX_MULTI_SKU_NUM = 99;

    private static final int MAX_MULTI_ITEM_NUM = 99;

    private static final Integer MAX_BATCH_SIZE = 1000;

    private static final Integer MAX_QUERY_INV_SIZE = 200;

    /**
     * 错误数据返回导出头
     */
    private static final String[] ERROR_REPORT_HEADER = {"location_code", "is_active", "warehouse_code", "area_code", "location_type", "picking_seq_num", "put_away_seq_num", "pass_num"
            , "level_num", "length", "width", "height", "row_num", "column_num", "location_specs", "is_virtual", "location_property",
            "is_multi_sku",
            "is_multi_item",
            "备注信息"};

    private static final String[] HEADER = {"库位", "修改时间", "修改人", "修改后库位类型", "修改前库位类型", "修改后作业库区", "修改前作业库区", "修改前货位最大件数", "修改后货位最大件数", "成功/失败"};


    @Override
    public List<String> getPassNumList(String warehouseCode, String areaCode) {
        List<String> areaCodeList = Arrays.asList(areaCode.split(COMMON_SYMBOL));

        ArrayList<String> passNumList = new ArrayList<>(50);

        //组合成 XX库区——XX通道的格式
        for (String areaCodeItem : areaCodeList) {
            List<String> passNumberList = basLocationRepository.passNumList(warehouseCode, areaCodeItem);
            for (String passNum : passNumberList) {
                passNumList.add(areaCodeItem + SPECIAL_SYMBOL + passNum);
            }
        }

        return passNumList;
    }

    @Override
    public List<String> getLevelNumList(LevelNumListRequest request) {
        ArrayList<String> resultList = new ArrayList<>(50);
        Map<String, List<String>> passNumsMap = processData(request.getPassNum());
        Map<String, List<String>> columnNumMap = processData(request.getColumnNum());

        for (Map.Entry<String, List<String>> entry : columnNumMap.entrySet()) {
            LevelNumQueryParam levelNumQueryParam = new LevelNumQueryParam();
            levelNumQueryParam.setWarehouseCode(request.getWarehouseCode());
            levelNumQueryParam.setAreaCode(entry.getKey());
            levelNumQueryParam.setPassNums(passNumsMap.get(entry.getKey()));
            levelNumQueryParam.setColumnNums(entry.getValue());
            levelNumQueryParam.setTenantCode(OperationUserContextHolder.get().getTenantCode());
            List<String> levelNumList = basLocationRepository.levelNumList(levelNumQueryParam);
            for (String levelNum : levelNumList) {
                resultList.add(entry.getKey() + SPECIAL_SYMBOL + levelNum);
            }
        }
        return resultList;
    }

    @Override
    public List<String> getColumnNumList(ColumnNumListRequest request) {

        ArrayList<String> resultList = new ArrayList<>(50);
        Map<String, List<String>> passNumsMap = processData(request.getPassNum());
        //组合成 XX库区——XX通道的格式

        for (Map.Entry<String, List<String>> entry : passNumsMap.entrySet()) {
            ColumnNumQueryParam columnNumQueryParam = new ColumnNumQueryParam();
            columnNumQueryParam.setWarehouseCode(request.getWarehouseCode());
            columnNumQueryParam.setAreaCode(entry.getKey());
            columnNumQueryParam.setPassNums(entry.getValue());
            columnNumQueryParam.setTenantCode(OperationUserContextHolder.get().getTenantCode());
            List<String> columnNumNumList = basLocationRepository.columnNumList(columnNumQueryParam);
            for (String columnNum : columnNumNumList) {
                resultList.add(entry.getKey() + SPECIAL_SYMBOL + columnNum);
            }
        }
        return resultList;
    }

    protected Map<String, List<String>> processData(String data) {
        List<String> list;
        Map<String, List<String>> resultMap = new HashMap<>();
        if (StringUtils.isNotBlank(data)) {
            list = Arrays.asList(data.split(COMMON_SYMBOL));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                list.forEach(e -> {
                    List<String> item = Arrays.asList(e.split(SPECIAL_SYMBOL));
                    if (resultMap.containsKey(item.get(0))) {
                        resultMap.get(item.get(0)).add(item.get(1));
                    } else {
                        List<String> dataList = new ArrayList<>();
                        dataList.add(item.get(1));
                        resultMap.put(item.get(0), dataList);
                    }

                });
            }
        }
        return resultMap;
    }

    @Override
    public BasLocationResponse basLocationDetail(Long id) {
        BasLocationDo entity = detailById(id);
        if (entity != null) {
            BasLocationResponse response = new BasLocationResponse();
            BeanUtils.copyProperties(entity, response);
            BasTypeResponse warehouse = basWarehouseService.warehouseName(response.getWarehouseCode());
            if (warehouse != null) {
                response.setWarehouseName(warehouse.getTypeDesc());
            }
            BasAreaDo area = basAreaService.areaDetail(response.getWarehouseCode(), response.getAreaCode());
            if (area != null) {
                response.setAreaName(area.getAreaName());
            }
            if (entity.getWorkZoneCode() != null && entity.getWorkZoneCode().equals("")) {
//                BasWorkZoneDo basWorkZoneDo = basWorkZoneDoExtMapper.selectBasWorkZoneDoByZoneCode("SH09-001");
                BasWorkZoneDo basWorkZoneDo = basWorkZoneDoExtMapper.selectBasWorkZoneDoByZoneCode(entity.getWorkZoneCode());
                if (basWorkZoneDo != null) {
                    response.setWorkZoneName(basWorkZoneDo.getWorkZoneName());
                }
            }
            response.setLocationSpecsDesc(BasLocationSpecsEnum.getDescByCode(response.getLocationSpecs()));
            try {
                response.setLocationTypeDesc(sysDictService.getDictName(entity.getTenantCode(), response.getWarehouseCode(), DictTypeEnum.LOCATION_CODE_TYPE, response.getLocationType()));
            }catch (Exception e){
                log.error("字典查询存储类型出错",e);
            }
            response.setLocationPropertyDesc(LocationPropertyEnum.getMessage(response.getLocationProperty().byteValue()));
            //查询库位限制
            BasLocationLimitResponse basLocationLimitResponse=locationLimitRepository.queryLocationLimitByLocationCode(OperationUserContextHolder.getTenantCode(),response.getWarehouseCode(),response.getLocationCode());
            if (basLocationLimitResponse!=null){
                response.setLimitNo(basLocationLimitResponse.getLimitNo());
                response.setLimitName(basLocationLimitResponse.getLimitName());
            }

            response.setIsForTemporaryStorageDesc(Objects.equals(response.getIsForTemporaryStorage(), (byte)1) ? "是" : "否");
            return response;
        }
        return null;
    }

    @Override
    public BasLocationDo detailById(Long id) {
        if (id != null) {
            return basLocationRepository.selectById(id);
        }
        return null;
    }

    @Override
    public PagingObject<BasLocationDo> locationList(BasLocationQueryRequest request) {
        LocationQueryParam locationQueryParam = createCondition(request);
        int count = basLocationRepository.selectCountByQueryParam(locationQueryParam);
        if (count <= 0) {
            return PagingObjectUtils.emptyPagingObject(locationQueryParam);
        }
        locationQueryParam.setStartNum((locationQueryParam.getPageNum() - 1) * locationQueryParam.getPageSize());
        List<BasLocationDo> list = basLocationRepository.selectByQueryParam(locationQueryParam);
        return PagingObjectUtils.convertPagingObject(list, locationQueryParam, count);
    }


    @Override
    public List<BasLocationDo> selectLocationList(BasLocationQueryRequest request) {
        LocationQueryParam locationQueryParam = createCondition(request);
        List<BasLocationDo> list = basLocationRepository.selectListByQueryParam(locationQueryParam);
        return list;
    }


    @Override
    public List<String> selectlocationBySection(BasLocationSectionQueryRequest request) {
        String tenantCode = OperationUserContextHolder.get().getTenantCode();
        if (StringUtils.isEmpty(tenantCode)||StringUtils.isEmpty(request.getWarehouseCode())|| StringUtils.isEmpty(request.getAreaCode())) {
            throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY);
        }
        log.info("查询库位selectlocationBySection入参{}",JSON.toJSONString(request));
        List<String> list = basLocationRepository.selectlocationBySection(tenantCode, 0, request.getWarehouseCode(),request.getAreaCode()
                ,request.getStartPassNum(),request.getEndPassNum(),request.getStartColumnNum(),request.getEndColumnNum(),request.getStartLevelNum(),request.getEndLevelNum());
        return list;
    }

    @Override
    public BasLocationDo detailByCode(String tenantCode,String warehouseCode, String locationCode) {
        if (StringUtils.isNotBlank(warehouseCode)
                && StringUtils.isNotBlank(locationCode)) {
            LocationQueryParam locationQueryParam = new LocationQueryParam();
            locationQueryParam.setWarehouseCode(warehouseCode);
            locationQueryParam.setLocationCode(locationCode);
            locationQueryParam.setIsActive(1);
            locationQueryParam.setTenantCode(tenantCode);
            return basLocationRepository.selectOneByParam(locationQueryParam);
        }
        return null;
    }

    @Override
    public BasLocationDo detailByCode(String warehouseCode, String locationCode) {
        if (StringUtils.isNotBlank(warehouseCode)
                && StringUtils.isNotBlank(locationCode)) {
            LocationQueryParam locationQueryParam = new LocationQueryParam();
            locationQueryParam.setWarehouseCode(warehouseCode);
            locationQueryParam.setLocationCode(locationCode);
            locationQueryParam.setIsActive(1);
            locationQueryParam.setTenantCode(OperationUserContextHolder.getTenantCode());
            return basLocationRepository.selectOneByParam(locationQueryParam);
        }
        return null;
    }

    @Override
    public BasLocationDo getBerthLocationByCode(String warehouseCode, String locationCode) {
        if (StringUtils.isNotBlank(warehouseCode)
                && StringUtils.isNotBlank(locationCode)) {
            LocationQueryParam locationQueryParam = new LocationQueryParam();
            locationQueryParam.setWarehouseCode(warehouseCode);
            locationQueryParam.setLocationCode(locationCode);
            locationQueryParam.setIsActive(1);
            locationQueryParam.setLocationProperty(BasLocationPropertiesEnum.CONTAINER_BERTH_LOCATION.getCode());
            locationQueryParam.setTenantCode(OperationUserContextHolder.getTenantCode());
            return basLocationRepository.selectOneByParam(locationQueryParam);
        }
        return null;
    }

    @Override
    public boolean containByCode(String warehouseCode, String locationCode) {
        if (StringUtils.isNotBlank(warehouseCode)
                && StringUtils.isNotBlank(locationCode)) {
            return detailByCode(warehouseCode, locationCode) != null;
        }

        return false;
    }

    @Override
    public BasLocationDo queryDetailByLocationCode(String warehouseCode, String locationCode) {
        if (StringUtils.isNotBlank(warehouseCode)
                && StringUtils.isNotBlank(locationCode)) {
            return basLocationRepository.selectAllByWarehouseCodeAndLocationCode(warehouseCode, locationCode);
        }
        return null;
    }

    @Override
    public List<BasLocationDo> areaDetailByCodes(String warehouseCode, Set<String> locationCodes) {
        if (StringUtils.isNotBlank(warehouseCode)
                && CollectionUtils.isNotEmpty(locationCodes)) {
            return basLocationRepository.selectByCodes(warehouseCode, locationCodes);
        }
        return null;
    }

    @Override
    public Integer add(BasLocationRequest request) {
        if (request.getWorkZoneCode().equals("") || request.getWorkZoneCode() == null) {
            throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY);
        }
        convertToUppercase(request);
        checkWarehouse(request.getWarehouseCode());
        checkArea(request.getWarehouseCode(), request.getAreaCode(), request.getLocationProperty());

        Byte locationProperty = request.getLocationProperty();
        String locationType = request.getLocationType();
        if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty && locationType.equals(BasLocationTypeEnum.ABNORMAL_REPORTING.getCode())) {
            throw new WmsOperationException(WmsExceptionCode.TEMPORARY_STORAGE_ABNORMAL_REPORTING_CONFLICT);
        }

        checkLocation(request.getWarehouseCode(), request.getLocationCode());
        checkLocationType(request.getLocationType(), request.getLocationProperty());
        BasLocationDo entity = new BasLocationDo();
        initEntity(entity);

        Integer maxSkuNum = request.getMaxSkuNum();
        Integer isMultiSku = request.getIsMultiSku();

        if (isMultiSku != null && isMultiSku == 1 && maxSkuNum != null && maxSkuNum != 1) {
            throw new WmsOperationException("当不允许sku混放时, 最大SKU数只能是1");
        }
        if (maxSkuNum == null) {
            if (isMultiSku != null && isMultiSku == 1) {
                entity.setMaxSkuNum(1);
            } else {
                entity.setMaxSkuNum(0);
            }
        } else {
            entity.setMaxSkuNum(maxSkuNum);
        }


        BeanUtils.copyProperties(request, entity);
        entity.setMaxSkuNum(maxSkuNum);
        //体积立方厘米转换为立方米
        BigDecimal decimal = new BigDecimal("1000000");
        entity.setVolume(entity.getHeight()
                .multiply(entity.getLength())
                .multiply(entity.getWidth()).divide(decimal));
        //1期默认允许混放
        // v1.69 是否允许混放批次和商品 由页面传入
        entity.buildMaxItemNumNonNull(MAX_MULTI_ITEM_NUM);
        Integer result= basLocationRepository.save(entity);
        locationChangeProcessor.sendActiveMsg(entity.getLocationCode(),entity.getWarehouseCode(),entity.getAreaCode());
        return result;
    }

    private Integer parseMaxSkuNum(String maxSkuNumStr) {
        Integer maxSkuNum;
        try {
            maxSkuNum = StringUtils.isNotBlank(maxSkuNumStr) ? null : Integer.parseInt(maxSkuNumStr);
        } catch (Exception e) {
            throw new WmsOperationException("最大SKU数必须是整数类型");
        }
        if (maxSkuNum != null && maxSkuNum < 0) {
            throw new WmsOperationException("最大SKU数不能小于0");
        }
        return maxSkuNum;
    }

    //库位统一转成大写
    private void convertToUppercase(BasLocationRequest request) {
        if (request.getLocationCode()!=null){
            request.setLocationCode(request.getLocationCode().toUpperCase());
        }
    }

    private void convertToUppercase(LocationData request) {
        if (request.getLocationCode()!=null){
            request.setLocationCode(request.getLocationCode().toUpperCase());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<LocationData> locationDatas) {
        if (CollectionUtils.isNotEmpty(locationDatas)) {
            List<BasLocationDo> locationDos = new ArrayList<>(locationDatas.size());
            Date date = new Date();
            OperationUserContext userContext = OperationUserContextHolder.get();
            Set<String> warehouses = new HashSet<>();
            Map<String, BasAreaDo> areaDoMap = new HashMap<>();
            Set<String> workZones = new HashSet<>();
            Set<String> importLocationCodes = new HashSet<>();

            locationDatas.forEach(locationData -> {
                convertToUppercase(locationData);
                locationData.checkInsertParam();
                checkLocationType(locationData.getLocationType(), locationData.getLocationProperty());
                // 判断作业库区是否在该仓库真实存在
                checkWorkAndWare(locationData.getWorkZoneCode(), locationData.getWarehouseCode());
                // 判断是否存在唯一码冲突的问题
                checkImportLocationIsRepeated(
                        locationData.getLocationCode(),
                        locationData.getWarehouseCode(),
                        userContext.getTenantCode(),
                        importLocationCodes
                );
                if (!warehouses.contains(locationData.getWarehouseCode())) {
                    checkWarehouse(locationData.getWarehouseCode());
                    warehouses.add(locationData.getWarehouseCode());
                }
                checkArea(areaDoMap, locationData.getWarehouseCode(), locationData.getAreaCode(), locationData.getLocationProperty());
                //检查作业库区
                if (!workZones.contains(locationData.getWorkZoneCode())) {
                    checkWorkZoneCode(locationData.getWorkZoneCode());
                    workZones.add(locationData.getWorkZoneCode());
                }
                Byte locationProperty = locationData.getLocationProperty();
                String locationType = locationData.getLocationType();
                if (!StringUtils.isEmpty(locationType)) {
                    if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty && locationType.equals(BasLocationTypeEnum.ABNORMAL_REPORTING.getCode())) {
                        throw new WmsOperationException(WmsExceptionCode.TEMPORARY_STORAGE_ABNORMAL_REPORTING_CONFLICT);
                    }
                }
                BasLocationDo locationDo = new BasLocationDo();
                locationDo.setWorkZoneCode(locationData.getWorkZoneCode());
                locationDo.setMaxCommodityNum(locationData.checkAndGetMaxCommodityNum());
                locationDo.setLocationCode(locationData.getLocationCode());
                locationDo.setWarehouseCode(locationData.getWarehouseCode());
                locationDo.setAreaCode(locationData.getAreaCode());
                locationDo.setColumnNum(locationData.getColumnNum());
                locationDo.setCreatedRealName(userContext.getRealName());
                locationDo.setCreatedTime(date);
                locationDo.setCreatedUserId(userContext.getUserId());
                locationDo.setCreatedUserName(userContext.getUserName());
                locationDo.setDeleted(0);
                locationDo.setHeight(new BigDecimal(locationData.getHeight()));
                if (locationData.getIsActive() != null) {
                    locationDo.setIsActive(locationData.getIsActive().intValue());
                }
                locationDo.setIsMultiItem(
                        Objects.isNull(locationData.getIsMultiItem())
                                ? NumberUtils.INTEGER_ZERO
                                : locationData.getIsMultiItem()
                );
                locationDo.setIsMultiSku(
                        Objects.isNull(locationData.getIsMultiSku())
                                ? NumberUtils.INTEGER_ZERO
                                : locationData.getIsMultiSku()
                );
                locationDo.setIsVirtual(locationData.getIsVirtual());
                locationDo.setLength(new BigDecimal(locationData.getLength()));
                locationDo.setLevelNum(locationData.getLevelNum());
                locationDo.setLocationSpecs(locationData.getLocationSpecs());
                locationDo.setLocationState(0);
                locationDo.setLocationType(locationData.getLocationType());
//                locationDo.setMaxItemNum(99);
//                locationDo.setMaxSkuNum(99);
                // v1.69 是否允许混放批次和商品 由页面传入
                locationDo.buildMaxItemNumNonNull(MAX_MULTI_ITEM_NUM);
                locationDo.buildMaxSkuNumNonNull(MAX_MULTI_SKU_NUM);
                locationDo.setPassNum(locationData.getPassNum());
                locationDo.setPickingSeqNum(locationData.getPickingSeqNum());
                locationDo.setPutAwaySeqNum(locationData.getPutAwaySeqNum());
                locationDo.setRowNum(locationData.getRowNum());
                locationDo.setTenantCode(userContext.getTenantCode());
                locationDo.setUpdatedRealName(userContext.getRealName());
                locationDo.setUpdatedTime(date);
                locationDo.setUpdatedUserId(userContext.getUserId());
                locationDo.setVolume(new BigDecimal(locationData.getVolume()));
                locationDo.setUpdatedUserName(userContext.getUserName());
                locationDo.setWidth(new BigDecimal(locationData.getWidth()));
                locationDo.setLocationProperty(locationData.getLocationProperty());
                locationDo.setIsForTemporaryStorage(Objects.equals("是", locationData.getIsForTemporaryStorageDesc()) ? (byte)1 : (byte)0);
                locationDos.add(locationDo);
            });
            for (int i = 0; i < locationDatas.size(); i += 1000) {
                List<BasLocationDo> list = CollUtil.sub(locationDos, i, i + 1000);
                basLocationRepository.batchInsert(list);
            }
        } else {
            throw new WmsOperationException(WmsExceptionCode.UPLOAD_ERROR_FILE_IS_NULL);
        }
    }


    /**
     * 判断作业库区是否在该仓库真实存在
     *
     * @param workZoneCode
     * @param warehouseCode
     */
    private void checkWorkAndWare(String workZoneCode, String warehouseCode) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        BasWorkZoneDo basWorkZoneDo = basWorkZoneRepository.selectByWorkZoneCode(warehouseCode
                , userContext.getTenantCode()
                , workZoneCode);
        if (basWorkZoneDo == null) {
            throw new WmsOperationException(WmsExceptionCode.WORK_ZONE_NOT_NOT_IN_WAREHOUSE);
        }
        if (basWorkZoneDo.getIsActive() == null || basWorkZoneDo.getIsActive() != 1) {
            throw new WmsOperationException(WmsExceptionCode.WORK_ZONE_NOT_ACTIVE);
        }
    }

    /**
     * 判断是否存在唯一码冲突的问题
     *
     * @param locationCode
     * @param warehouseCode
     * @param tenantCode
     */
    private void checkImportLocationIsRepeated(
            String locationCode,
            String warehouseCode,
            String tenantCode,
            Set<String> importLocationCodes) {
        String locationCodeUniqueKey = locationCode + warehouseCode;
        if (importLocationCodes.contains(locationCodeUniqueKey)) {
            throw new WmsOperationException(
                    WmsExceptionCode.IMPORT_LOCATION_REPETITION,
                    "重复库位：" + locationCode
            );
        }
        importLocationCodes.add(locationCodeUniqueKey);
        int nums = basLocationRepository.selectUniqueConflict(locationCode, warehouseCode, tenantCode);
        if (nums != 0) {
            log.info("LocationIsRepeated locationCode= {} warehouseCode = {} tenantCode = {}"
                    , locationCode, warehouseCode, tenantCode);
            throw WmsException.placeholderNew(WmsExceptionCode.THIS_LOCATION_EXIST, locationCode);
        }
    }





    @Override
    public ExcelCallbackBody<LocationData> importUpdate(List<LocationData> locationDataList) {
        HashMap<String, Set<String>> locationCodeMap = new HashMap<>();
        for (LocationData locationData : locationDataList) {
            convertToUppercase(locationData);
            Set<String> subSet = locationCodeMap.computeIfAbsent(locationData.getWarehouseCode(), (key)->new HashSet<>());
            subSet.add(locationData.getLocationCode());
        }

        OperationUserContext userContext = OperationUserContextHolder.get();
        //工具过滤类构建
        LocationDataBathUpdateUtilsV2 utils = LocationDataBathUpdateUtilsV2.builder()
                .errorMap(new HashMap<>())
                .areaDoMap(new HashMap<>())
                .validationMap(new HashMap<>())
                .notExistAreas(new HashSet<>())
                .workZones(new HashSet<>())
                .warehouseCodes(buildWarehouseCodeListV2(locationCodeMap.keySet()))
                .build();

        for (LocationData x : locationDataList) {
            try {
                if (!checkAndFillV2(utils, x, locationCodeMap)) {
                    continue;
                }
                BasLocationDo locationDo = x.toBasLocationDo(userContext);
                //目前没找到合适的批量update方法
                BasLocationDo basLocationDo=basLocationDoExtMapper.selectAllByCodeAndWarehouseCode(locationDo.getWarehouseCode(),locationDo.getLocationCode(),locationDo.getTenantCode());

                if (basLocationDoExtMapper.updateByCode(locationDo) <= 0) {
                    utils.getErrorMap().put(x, WmsExceptionCode.DATA_ERROR);
                    x.setMessage(WmsExceptionCode.DATA_ERROR.getMessage());
                }else {
                    locationChangeProcessor.sendMsg(basLocationDo.getIsActive(),locationDo.getIsActive(),basLocationDo.getLocationCode(),basLocationDo.getWarehouseCode(),basLocationDo.getAreaCode());
                }

            } catch (Exception ex) {
                //记录错误继续执行
                log.error("批量导入发生错误 -> {} ",JSON.toJSONString(x), ex);
                x.setMessage(WmsExceptionCode.DATA_ERROR.getMessage());
            }
        }
        Map<LocationData, WmsExceptionCode> errorMap = utils.getErrorMap();
        for (LocationData locationData : locationDataList) {
            WmsExceptionCode wmsExceptionCode = errorMap.get(locationData);
            if (wmsExceptionCode == null) {
                locationData.setMessage("成功");
            } else {
                locationData.setMessage(wmsExceptionCode.getMessage());
            }
        }
        //如果有异常的信息 则调用远程下载中心
        int errorCount = utils.getErrorCount();
        ExcelCallbackBody<LocationData> excelCallbackBody = new ExcelCallbackBody<>();
        excelCallbackBody.setLineList(locationDataList);
        excelCallbackBody.setFailCount(errorCount);
        excelCallbackBody.setTotalCount(locationDataList.size());
        return excelCallbackBody;
    }



    /**
     * 批量修改库位
     *
     * @param locationDatas   修改入参
     * @param locationCodeMap 仓库和库位的集合
     * @return 是否需要导出 如果需要导出则返回类型有值
     */
    @Override
    public ReportCenterResponse batchUpdate(List<LocationData> locationDatas, HashMap<String, Set<String>> locationCodeMap) {
        log.info("batchUpdate->start -> locationCodeMap :{},locationDatas :{}", locationCodeMap, locationDatas);
        ReportCenterResponse response = null;
        OperationUserContext userContext = OperationUserContextHolder.get();
        //工具过滤类构建
        LocationDataBathUpdateUtils utils = LocationDataBathUpdateUtils.builder()
                .errorMap(buildErrorMap())
                .areas(new HashSet<>())
                .validationMap(new HashMap<>())
                .notExistAreas(new HashSet<>())
                .workZones(new HashSet<>())
                .warehouseCodes(buildWarehouseCodeList(locationCodeMap.keySet()))
                .build();
        List<BasLocationDo> locationDos = new ArrayList<>(locationDatas.size());
        for (LocationData x : locationDatas) {
            try {
                if (!checkAndFill(utils, x, locationCodeMap)) {continue;}
                checkArea(x.getWarehouseCode(),x.getAreaCode(),x.getLocationProperty());
                BasLocationDo locationDo = x.toBasLocationDo(userContext);
                //目前没找到合适的批量update方法
                BasLocationDo basLocationDo=basLocationDoExtMapper.selectAllByCodeAndWarehouseCode(locationDo.getWarehouseCode(),locationDo.getLocationCode(),locationDo.getTenantCode());
                if (basLocationDo==null||basLocationDoExtMapper.updateByCode(locationDo) <= 0) {
                    utils.getErrorMap().get(WmsExceptionCode.DATA_ERROR).add(x);
                }else {
                    locationChangeProcessor.sendMsg(basLocationDo.getIsActive(),locationDo.getIsActive(),basLocationDo.getLocationCode(),basLocationDo.getWarehouseCode(),basLocationDo.getAreaCode());
                }
            } catch (Exception ex) {
                //记录错误继续执行
                log.error("批量导入发生错误 -> {} ", ex);
                utils.getErrorMap().get(WmsExceptionCode.DATA_ERROR).add(x);
            }
        }
        //如果有异常的信息 则调用远程下载中心
        int errorCount = utils.getErrorCount();
        log.info("数据筛选完毕-> 异常数据条数：{},待修改条数 {}", errorCount, locationDos.size());
        if (errorCount > 0) {
            ReportCreateRequest reportCreateRequest = reportDownloadService.buildReportDownloadRequest("库位信息列表", "wms", "批量导入库位异常数据导出" + DateUtils.dateToString(new Date(), DateUtils.FORMAT_TIME3));
            response = excelExportService.triggerAsyncDownload(() -> {
                        List<Object[]> result = new ArrayList<>(errorCount);
                        for (Map.Entry<WmsExceptionCode, List<LocationData>> entry : utils.getErrorMap().entrySet()) {
                            List<Object[]> objects = reportObjectsV2(entry.getValue(), entry.getKey().getMessage());
                            if (CollectionUtils.isNotEmpty(objects)){
                                result.addAll(objects);
                            }
                        }
                        return result;
                    },
                    "批量导入库位异常数据导出" + DateUtils.dateToString(new Date(), DateUtils.FORMAT_TIME3), "库位信息", ERROR_REPORT_HEADER, reportCreateRequest);
            log.info("生成下载链接返回{}", response);
        }
        return response;
    }


    @Override
    public Integer update(BasLocationRequest request) {
        if (request.getWorkZoneCode().equals("") || request.getWorkZoneCode() == null) {
            throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY);
        }
        convertToUppercase(request);
        checkWarehouse(request.getWarehouseCode());
        checkArea(request.getWarehouseCode(), request.getAreaCode(),request.getLocationProperty());
        checkIsActive(request.getLocationCode(), request.getIsActive(), request.getWarehouseCode());
        checkLocationType(request.getLocationType(), request.getLocationProperty());
        BasLocationDo entity = basLocationRepository.selectById(request.getId());
        if (entity != null) {
            initUpdate(entity);

            Integer isMultiSku = request.getIsMultiSku();
            Integer maxSkuNum = request.getMaxSkuNum();
            isMultiSku = isMultiSku == null ? entity.getIsMultiSku() : isMultiSku;
            maxSkuNum = maxSkuNum == null ? entity.getMaxSkuNum() : maxSkuNum;
            if (isMultiSku != null && isMultiSku == 1 && maxSkuNum != null && maxSkuNum != 1) {
                throw new WmsOperationException("当不允许sku混放时, 最大SKU数只能是1");
            }
            BeanUtils.copyProperties(request, entity, "locationCode");
            entity.setMaxSkuNum(maxSkuNum);
            //体积立方厘米转换为立方米
            BigDecimal decimal = new BigDecimal("1000000");
            entity.setVolume(entity.getHeight()
                    .multiply(entity.getLength())
                    .multiply(entity.getWidth()).divide(decimal));
            // v1.69 是否允许混放批次和商品 由页面传入
            entity.buildMaxItemNumNonNull(MAX_MULTI_ITEM_NUM);
        } else {
            throw new WmsException(WmsExceptionCode.LOCATION_NOT_EXIST);
        }
        String sourceArea=entity.getAreaCode();
        Integer sourceIsActive=entity.getIsActive();

        Byte locationProperty = request.getLocationProperty();
        locationProperty = locationProperty == null ? entity.getLocationProperty() : locationProperty;
        String locationType = request.getLocationType();
        locationType = StringUtils.isEmpty(locationType) ? entity.getLocationType() : locationType;
        if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty && locationType.equals(BasLocationTypeEnum.ABNORMAL_REPORTING.getCode())) {
            throw new WmsOperationException(WmsExceptionCode.TEMPORARY_STORAGE_ABNORMAL_REPORTING_CONFLICT);
        }

        entity.setTenantCode(OperationUserContextHolder.getTenantCode());
        Integer result= basLocationRepository.updateById(entity);
        //发送库位变动消息
        locationChangeProcessor.sendMsg(sourceIsActive,entity.getIsActive(),entity.getLocationCode(),entity.getWarehouseCode(),sourceArea);
        return result;
    }

    @Override
    public List<Object[]> reportList(BasLocationQueryRequest request) {
        BasLocationParam basLocationDo = convertBasLocationDo(request);
        int size = 0;
        int pageSize = 1000;
        Long lastId = null;
        List<Object[]> resultList = new ArrayList<>();
        do {
            List<BasLocationDo> basLocationDos = basLocationRepository.selectPageById(basLocationDo, lastId, pageSize);
            size = basLocationDos.size();
            if (size > 0) {
                lastId = basLocationDos.get(size - 1).getId();
            }
            List<Object[]> rows = reportObjects(basLocationDos);
            resultList.addAll(rows);
        } while (size == pageSize);
        return resultList;
    }

    /**
     * 转换为导出数据格式
     *
     * @param basLocationDos
     * @return
     */
    private List<Object[]> reportObjects(List<BasLocationDo> basLocationDos) {
        Map<String, String> warehouseNames = new HashMap<>(10);
        List<Object[]> results = new ArrayList<>();
        Set<String> wordZoneCode = basLocationDos.stream().map(BasLocationDo::getWorkZoneCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> warehouseCode = basLocationDos.stream().map(BasLocationDo::getWarehouseCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<BasWorkZoneDetailParam> basWorkZoneDetailParams = workZoneQueryService.selectByWarehouseZoneCodes(warehouseCode, wordZoneCode);
        Map<String, Map<String, String>> workZoneWareMap = basWorkZoneDetailParams.stream().collect(Collectors.groupingBy(BasWorkZoneDetailParam::getWorkZoneCode, Collectors.toMap(BasWorkZoneDetailParam::getWarehouseCode, BasWorkZoneDetailParam::getWorkZoneName)));

        Set<String> areCodes = basLocationDos.stream().map(BasLocationDo::getAreaCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<BasAreaDo> basAreaDos = basAreaService.areaDetail(warehouseCode, areCodes);
        Map<String, Map<String, String>> areaWarehouseMap = basAreaDos.stream().collect(Collectors.groupingBy(BasAreaDo::getAreaCode, Collectors.toMap(BasAreaDo::getWarehouseCode, BasAreaDo::getAreaName, (o, n) -> o)));

        Map<String, Map<String, String>> locationTypeMapMap = new HashMap<>();
        for (String s : warehouseCode) {
            Map<String, String> locationTypeMap = sysDictService.dictToMap(OperationUserContextHolder.get().getTenantCode(), s, DictTypeEnum.LOCATION_CODE_TYPE);
            if (locationTypeMap != null) {
                locationTypeMapMap.put(s, locationTypeMap);
            }
        }

        if (CollectionUtils.isNotEmpty(basLocationDos)) {
            for (BasLocationDo entity : basLocationDos) {
                List<Object> listObjs = new ArrayList<>();
                listObjs.add(entity.getLocationCode());
                Map<String, String> locationTypeMap = locationTypeMapMap.get(entity.getWarehouseCode());
                String locationTypeDesc = locationTypeMap == null ? null : locationTypeMap.get(entity.getLocationType());
                locationTypeDesc = locationTypeDesc == null ? "" : locationTypeDesc;
                listObjs.add(locationTypeDesc);
                Byte isForTemporaryStorage = entity.getIsForTemporaryStorage();
                listObjs.add(Objects.equals(isForTemporaryStorage, (byte)1) ? "是" : "否");
                if (!warehouseNames.containsKey(entity.getWarehouseCode())) {
                    BasTypeResponse<String> response = basWarehouseService.warehouseName(entity.getWarehouseCode());
                    if (response != null) {
                        warehouseNames.put(response.getTypeCode(), response.getTypeDesc());
                    }
                }
                listObjs.add(warehouseNames.get(entity.getWarehouseCode()));
                String areaCode = entity.getAreaCode();
                if (areaWarehouseMap.containsKey(areaCode) && areaWarehouseMap.get(areaCode).containsKey(entity.getWarehouseCode())) {
                    listObjs.add(areaWarehouseMap.get(areaCode).get(entity.getWarehouseCode()));
                } else {
                    listObjs.add(null);
                }
                listObjs.add(entity.getLocationState() == 0 ? "正常" : "锁定");
                listObjs.add(BasLocationSpecsEnum.getDescByCode(entity.getLocationSpecs()));
                listObjs.add(LocationPropertyEnum.getMessage(entity.getLocationProperty().byteValue()));
                listObjs.add(entity.getLength());
                listObjs.add(entity.getWidth());
                listObjs.add(entity.getHeight());
                listObjs.add(entity.getVolume());
                listObjs.add(entity.getPassNum());
                listObjs.add(entity.getRowNum());
                listObjs.add(entity.getColumnNum());
                listObjs.add(entity.getLevelNum());
                listObjs.add(entity.getPickingSeqNum());
                listObjs.add(entity.getPutAwaySeqNum());
                listObjs.add(entity.getIsActive() == 1 ? "否" : "是");
                listObjs.add(entity.getMaxCommodityNum());
                String workZoneCode = entity.getWorkZoneCode();
                if (workZoneWareMap.containsKey(workZoneCode) && workZoneWareMap.get(workZoneCode).containsKey(entity.getWarehouseCode())) {
                    listObjs.add(workZoneWareMap.get(workZoneCode).get(entity.getWarehouseCode()));
                } else {
                    listObjs.add(null);
                }
                listObjs.add(LocationIsMultiSkuEnum.getDescByStatus(entity.getIsMultiSku()));
                listObjs.add(LocationIsMultiItemEnum.getDescByStatus(entity.getIsMultiItem()));
                results.add(listObjs.toArray());
            }

        }
        return results;
    }

    /**
     * v2版本根据类型不同转化成obj不同 新增了备注字段 主要用于描述批量导入的错误信息
     *
     * @return
     */
    private List<Object[]> reportObjectsV2(List<LocationData> list, String remake) {
        log.info("{} -> 错误类型开始转换，条数{}", remake, list.size());
        List<Object[]> result = null;
        if (CollectionUtils.isNotEmpty(list)) {
            result = new ArrayList<>(list.size());
            for (LocationData entity : list) {
                List<Object> listObjs = new ArrayList<>();
                listObjs.add(entity.getLocationCode());
                listObjs.add(entity.getIsActive());
                listObjs.add(entity.getWarehouseCode());
                listObjs.add(entity.getAreaCode());
                listObjs.add(entity.getLocationType());
                listObjs.add(entity.getPickingSeqNum());
                listObjs.add(entity.getPutAwaySeqNum());
                listObjs.add(entity.getPassNum());
                listObjs.add(entity.getLevelNum());
                listObjs.add(entity.getLength());
                listObjs.add(entity.getWidth());
                listObjs.add(entity.getHeight());
                listObjs.add(entity.getRowNum());
                listObjs.add(entity.getColumnNum());
                listObjs.add(entity.getLocationSpecs());
                listObjs.add(entity.getIsVirtual());
                listObjs.add(entity.getLocationProperty());
                listObjs.add(entity.getIsMultiSku());
                listObjs.add(entity.getIsMultiItem());
                listObjs.add(remake);
                result.add(listObjs.toArray());
            }
        }
        return result;
    }

    private void checkWarehouse(String warehouseCode) {
        //检查用户插入的仓库是否是该用户有权限的仓库
        checkAuthWarehouse(warehouseCode);
        BasTypeResponse entity = basWarehouseService.warehouseName(warehouseCode);
        if (entity == null) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_NOT_EXIST);
        }
    }

    private void checkAuthWarehouse(String warehouseCode) {
        OperationUserContext operationUserContext = OperationUserContextHolder.get();
        List<String> warehouseCodes = operationUserContext.getWarehouses().stream().
                map(OperationUserContext.UserWarehouse::getWarehouseCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseCodes)) {
            throw new WmsException(WmsExceptionCode.AUTHENTICATION_WAREHOUSE_IS_NULL);
        }
        if (!warehouseCodes.contains(warehouseCode)) {
            throw new WmsException(WmsExceptionCode.AUTHENTICATION_WAREHOUSE_IS_NULL);
        }
    }


    /**
     * 构建仓库code
     */
    private List<String> buildWarehouseCodeList(Set<String> codes) {
        log.info("buildWarehouseCodeList -> start -> {}", codes);
        //当前仓库校验完毕集合
        List<BasTypeResponse<String>> warehouseBasTypeResponse = basWarehouseService.warehouseName(codes);
        List<String> result = CollectionUtils.isNotEmpty(warehouseBasTypeResponse) ? warehouseBasTypeResponse.stream().map(x -> x.getTypeCode()).collect(Collectors.toList()) :
                new ArrayList<>();
        return result;
    }

    /**
     * 构建仓库code
     */
    private List<String> buildWarehouseCodeListV2(Set<String> codes) {
        if (codes == null ) {
            return Collections.emptyList();
        }
        OperationUserContext operationUserContext = OperationUserContextHolder.get();
        List<String> warehouseCodes = operationUserContext.getWarehouses().stream().
                map(OperationUserContext.UserWarehouse::getWarehouseCode).collect(Collectors.toList());
        log.info("buildWarehouseCodeListV2  codes = {} warehouseCodes = {}", codes, warehouseCodes);
        return codes.stream().filter(warehouseCodes::contains).collect(Collectors.toList());
    }


    private Map<WmsExceptionCode, List<LocationData>> buildErrorMap() {
        log.info("buildBatchUpdateErrorMap -> start");
        Map<WmsExceptionCode, List<LocationData>> errorMap = new HashMap<>();
        errorMap.put(WmsExceptionCode.WAREHOUSE_NOT_EXIST, new ArrayList<>());
        errorMap.put(WmsExceptionCode.LOCATION_NOT_EXIST, new ArrayList<>());
        errorMap.put(WmsExceptionCode.AREA_NOT_EXIST, new ArrayList<>());
        errorMap.put(WmsExceptionCode.LOCATION_SAVE_TYPE_ERROR, new ArrayList<>());
        errorMap.put(WmsExceptionCode.PARAMS_ERROR, new ArrayList<>());
        errorMap.put(WmsExceptionCode.DATA_ERROR, new ArrayList<>());
        errorMap.put(WmsExceptionCode.WORK_ZONE_NOT_EXIST, new ArrayList<>());
        errorMap.put(WmsExceptionCode.LOCATION_HAS_CANT_EN_ACTIVE, new ArrayList<>());
        return errorMap;
    }

    private void fillValidationMap(LocationData x, HashMap<String, Set<String>> locationCodeMap, HashMap<String, Set<String>> validationMap, String tenantCode) {
        if (CollectionUtils.isNotEmpty(locationCodeMap.get(x.getWarehouseCode()))) {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("warehouseCode", x.getWarehouseCode());
            paramMap.put("locationCodes", locationCodeMap.get(x.getWarehouseCode()));
            paramMap.put("tenantCode", tenantCode);
            Set<String> list = basLocationDoExtMapper.selectCodeByCode(paramMap);
            validationMap.put(x.getWarehouseCode(), list);
        }
    }

    /**
     * 检查workZoneCode是否存在
     *
     * @param workZoneCode
     */
    private void checkWorkZoneCode(String workZoneCode) {
        if (StringUtils.isBlank(workZoneCode)) {
            throw new WmsException(WmsExceptionCode.WORK_ZONE_IS_EMPTY);
        }
        int count = basWorkZoneDoExtMapper.selectCountByZoneCode(workZoneCode);
        if (count == 0) {
            throw new WmsException(WmsExceptionCode.WORK_ZONE_NOT_EXIST);
        }
    }

    public BasAreaDo checkArea(Map<String, BasAreaDo> areaDoMap, String warehouseCode, String areaCode, Byte locationProperty) {
        BasAreaDo basAreaDo = areaDoMap.get(warehouseCode.concat(areaCode));
        if (basAreaDo == null) {
            basAreaDo = basAreaService.areaDetail(warehouseCode, areaCode);
            if (basAreaDo == null || !basAreaDo.getWarehouseCode().equals(warehouseCode)) {
                throw new WmsException(WmsExceptionCode.AREA_NOT_EXIST);
            }
            areaDoMap.put(warehouseCode.concat(areaCode), basAreaDo);
        }

        //暂存库位必须在暂存库区
        if (locationProperty != null) {
            if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() == locationProperty
                    &&(!BasAreaTypeEnum.TEMPORARY_STORAGE.getCode().equals(basAreaDo.getAreaType()))){
                throw new WmsException("暂存库位必须在暂存库区");
            }
            if ((LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty )
                    &&(BasAreaTypeEnum.TEMPORARY_STORAGE.getCode().equals(basAreaDo.getAreaType()))) {
                throw new WmsException("暂存库区下只能新建暂存库位");
            }
        }
        return basAreaDo;
    }

    @Override
    public BasAreaDo checkArea(String warehouseCode, String areaCode, Byte locationProperty) {
        BasAreaDo basAreaDo = basAreaService.areaDetail(warehouseCode, areaCode);
        if (basAreaDo == null || !basAreaDo.getWarehouseCode().equals(warehouseCode)) {
            throw new WmsException(WmsExceptionCode.AREA_NOT_EXIST);
        }

        //暂存库位必须在暂存库区
        if (locationProperty != null) {
            if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() == locationProperty
                    &&(!BasAreaTypeEnum.TEMPORARY_STORAGE.getCode().equals(basAreaDo.getAreaType()))){
                throw new WmsException("暂存库位必须在暂存库区");
            }
            if ((LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty )
                    &&(BasAreaTypeEnum.TEMPORARY_STORAGE.getCode().equals(basAreaDo.getAreaType()))) {
                throw new WmsException("暂存库区下只能新建暂存库位");
            }
        }
        return basAreaDo;
    }

    private void checkLocationType(String locationType, Byte locationProperty) {
        //如果是存储库位，库位类型不能为冻结库位
        if (locationProperty == LocationPropertyEnum.STORAGE_LOCATION.getType() && BasAreaTypeEnum.FREEZE.getCode().equals(locationType)) {
            throw new WmsOperationException(WmsExceptionCode.LOCATION_SAVE_TYPE_ERROR);
        }
    }

    private void checkLocation(String warehouseCode, String locationCode) {
        boolean bl = RegexUtils.regexYingWen(locationCode);
        if (!bl) {
            throw new WmsOperationException(WmsExceptionCode.LOCATION_ADD_CHINESE_CHAR_ERROR, locationCode);
        }
        int count = basLocationRepository.selectUniqueConflict(locationCode, warehouseCode, OperationUserContextHolder.getTenantCode());
        if (count > 0) {
            throw new WmsOperationException(WmsExceptionCode.LOCATION_EXIST);
        }
    }

    /**
     * 验证库位是否正在使用
     *
     * @param locationCode
     * @param isActive
     */
    private void checkIsActive(String locationCode, Integer isActive, String warehouseCode) {
        if (isActive == 0) {
            OperationUserContext context = OperationUserContextHolder.get();
            List<InvInventoryEntity> list = inventoryQueryService.queryInventoryList(context.getTenantCode(), warehouseCode, locationCode);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new WmsException(WmsExceptionCode.AREA_NOT_IS_ACTIVE);
            }
        }
    }

    public BasLocationParam convertBasLocationDo(BasLocationQueryRequest request) {
        BasLocationParam basLocationDo = new BasLocationParam();
        basLocationDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        if (request.getIsMultiItem() != null) {
            basLocationDo.setIsMultiItem(request.getIsMultiItem());
        }
        if (request.getIsMultiSku() != null) {
            basLocationDo.setIsMultiSku(request.getIsMultiSku());
        }
        basLocationDo.setAreaCode(request.getAreaCode());
        basLocationDo.setLocationCode(request.getLocationCode());
        basLocationDo.setLocationType(request.getLocationType());
        basLocationDo.setWarehouseCode(request.getWarehouseCode());
        if (request.getIsActive() != null) {
            basLocationDo.setIsActive(request.getIsActive());
        }
        if (request.getLocationProperty() != null) {
            basLocationDo.setLocationProperty(request.getLocationProperty().byteValue());
        }
        basLocationDo.setLocationPropertyList(LocationPropertyEnum.getCommonLocationProperty());
        basLocationDo.setIsVirtual(0);
        return basLocationDo;
    }

    private LocationQueryParam createCondition(BasLocationQueryRequest request) {
        LocationQueryParam locationQueryParam = new LocationQueryParam();
        if (request.getIsMultiItem() != null) {
            locationQueryParam.setIsMultiItem(request.getIsMultiItem());
        }
        if (request.getIsMultiSku() != null) {
            locationQueryParam.setIsMultiSku(request.getIsMultiSku());
        }
        if (StringUtils.isNotBlank(request.getAreaCode())) {
            locationQueryParam.setAreaCode(request.getAreaCode());
        }
        if (StringUtils.isNotBlank(request.getLocationCode())) {
            locationQueryParam.setLocationCode(request.getLocationCode());
        }
        if (StringUtils.isNotBlank(request.getLocationType())) {
            locationQueryParam.setLocationType(request.getLocationType());
        }
        locationQueryParam.setWarehouseCodeList(WarehouseUtils.getAuthWarehouseCodes(request.getWarehouseCode()));
        if (request.getIsActive() != null) {
            locationQueryParam.setIsActive(request.getIsActive());
        }
        if (request.getLocationProperty() != null) {
            locationQueryParam.setLocationProperty(request.getLocationProperty());
        }
        if (StringUtils.isNotBlank(request.getLocationCodeLike())) {
            locationQueryParam.setLocationCodeLike(request.getLocationCodeLike());
        }
        locationQueryParam.setIsForTemporaryStorage(request.getIsForTemporaryStorage());
        locationQueryParam.setIsVirtual(0);
        locationQueryParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        locationQueryParam.setPageNum(request.getPageNum());
        locationQueryParam.setPageSize(request.getPageSize());
        locationQueryParam.setOrders(request.getOrders());
        return locationQueryParam;

    }

    private boolean checkAndFill(LocationDataBathUpdateUtils utils, LocationData x, HashMap<String, Set<String>> locationCodeMap) {
        if (!utils.checkParameter(x) || !utils.checkWarehouseCode(x) || !utils.checkLocationProperty(x)) {
            return Boolean.FALSE;
        }
        //如果当前库位和仓库没有检查 校验当前仓库下的所有库位
        if (!utils.getValidationMap().containsKey(x.getWarehouseCode())){
            fillValidationMap(x, locationCodeMap, utils.getValidationMap(), OperationUserContextHolder.get().getTenantCode());
        }
        if (!utils.checkLocationCode(x)){
            return Boolean.FALSE;
        }
        //校验库区
        String concatAreaCode = x.getWarehouseCode().concat(x.getAreaCode());
        if (!utils.checkAreaCode(concatAreaCode, x)){
            return Boolean.FALSE;
        }
        if (!utils.getAreas().contains(concatAreaCode)) {
            BasAreaDo basAreaEntity = basAreaService.areaDetail(x.getWarehouseCode(), x.getAreaCode());
            if (basAreaEntity == null) {
                utils.getErrorMap().get(WmsExceptionCode.WAREHOUSE_NOT_EXIST).add(x);
                utils.getNotExistAreas().add(concatAreaCode);
                return Boolean.FALSE;
            }
            utils.getAreas().add(concatAreaCode);
        }

        OperationUserContext userContext = OperationUserContextHolder.get();
        //校验作业库区
        if (!utils.getWorkZones().contains(x.getWorkZoneCode())) {
            BasWorkZoneDo basWorkZoneDo = basWorkZoneRepository.selectByWorkZoneCode(x.getWarehouseCode()
                    , userContext.getTenantCode()
                    , x.getWorkZoneCode());
            if (basWorkZoneDo != null) {
                utils.getWorkZones().add(x.getWorkZoneCode());
            } else {
                utils.getErrorMap().get(WmsExceptionCode.WORK_ZONE_NOT_EXIST).add(x);
                return Boolean.FALSE;
            }
        }

        Integer qty = utils.getInventoryQtyMap().get(x.getLocationCode());
        if (x.getIsActive() ==0 && qty != null && qty != 0) {
            utils.getErrorMap().get(WmsExceptionCode.LOCATION_HAS_CANT_EN_ACTIVE).add(x);
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }



    private boolean checkAndFillV2(LocationDataBathUpdateUtilsV2 utils,
                                   LocationData locationData,
                                   HashMap<String, Set<String>> locationCodeMap) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        if (!utils.checkParameter(locationData)
                || !utils.checkWarehouseCode(locationData)
                || !utils.checkLocationProperty(locationData)) {
            return Boolean.FALSE;
        }

        BasLocationDo locationDoNow = basLocationDoExtMapper.selectAllByCodeAndWarehouseCode(locationData.getWarehouseCode()
                , locationData.getLocationCode()
                , userContext.getTenantCode());

        if (locationDoNow == null) {
            utils.getErrorMap().put(locationData, WmsExceptionCode.LOCATION_NOT_EXIST);
            return Boolean.FALSE;
        }

        if (locationDoNow.getIsActive() == 1 && locationData.getIsActive() != 1) {
            int qty = getLocationInvQty(locationDoNow, userContext.getTenantCode());
            if (qty != 0) {
                utils.getErrorMap().put(locationData, WmsExceptionCode.LOCATION_HAS_INV_CANT_EN_ACTIVE);
                return Boolean.FALSE;
            }
        }

        //校验库区
        String concatAreaCode = locationData.getWarehouseCode().concat(locationData.getAreaCode());
        if (!utils.checkAreaCode(concatAreaCode, locationData)){
            return Boolean.FALSE;
        }
        Map<String, BasAreaDo> areaDoMap = utils.getAreaDoMap();
        BasAreaDo basAreaDo = areaDoMap.get(concatAreaCode);
        if (basAreaDo == null) {
            basAreaDo = basAreaService.areaDetail(locationData.getWarehouseCode(), locationData.getAreaCode());
            if (basAreaDo == null) {
                utils.getErrorMap().put(locationData, WmsExceptionCode.AREA_NOT_EXIST);
                utils.getNotExistAreas().add(concatAreaCode);
                return Boolean.FALSE;
            }
            areaDoMap.put(concatAreaCode, basAreaDo);
        }
        Byte locationProperty = locationData.getLocationProperty();
        //暂存库位必须在暂存库区
        if (locationProperty != null) {
            if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() == locationProperty
                    &&(!BasAreaTypeEnum.TEMPORARY_STORAGE.getCode().equals(basAreaDo.getAreaType()))){
                utils.getErrorMap().put(locationData, WmsExceptionCode.TEMPORARY_STORAGE_LOCATION_AREA_CONFLICT);
                return Boolean.FALSE;
            }
            if ((LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty )
                    &&(BasAreaTypeEnum.TEMPORARY_STORAGE.getCode().equals(basAreaDo.getAreaType()))) {
                utils.getErrorMap().put(locationData, WmsExceptionCode.TEMPORARY_STORAGE_AREA_LOCATION_CONFLICT);
                return Boolean.FALSE;
            }
        }

        locationProperty = locationProperty == null ? locationDoNow.getLocationProperty() : locationProperty;
        String locationType = locationData.getLocationType();
        if (!StringUtils.isEmpty(locationType)) {
            if (LocationPropertyEnum.TEMPORARY_STORAGE.getType() != locationProperty && locationType.equals(BasLocationTypeEnum.ABNORMAL_REPORTING.getCode())) {
                utils.getErrorMap().put(locationData, WmsExceptionCode.TEMPORARY_STORAGE_ABNORMAL_REPORTING_CONFLICT);
                return Boolean.FALSE;
            }
        }

        //校验作业库区
        if (!utils.getWorkZones().contains(locationData.getWorkZoneCode())) {
            BasWorkZoneDo basWorkZoneDo = basWorkZoneRepository.selectByWorkZoneCode(locationData.getWarehouseCode()
                    , userContext.getTenantCode()
                    , locationData.getWorkZoneCode());
            if (basWorkZoneDo != null) {
                utils.getWorkZones().add(locationData.getWorkZoneCode());
            } else {
                utils.getErrorMap().put(locationData, WmsExceptionCode.WORK_ZONE_NOT_EXIST);
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }



    private int getLocationInvQty (BasLocationDo basLocationDo
            , String tenantCode) {

        List<String> locationCodeList = Collections.singletonList(basLocationDo.getLocationCode());
        LocationInventoryResponse locationInventoryResponse = inventoryQueryService.queryLocationInventory(basLocationDo.getWarehouseCode()
                , locationCodeList
                , tenantCode);
        if (locationInventoryResponse == null) {
            return 0;
        }
        List<LocationInventoryResponse.LocationInfo> locationInfoList = locationInventoryResponse.getLocationInfoList();

        Map<String, Integer> onHandQtyMap = new HashMap<>();
        for (LocationInventoryResponse.LocationInfo locationInfo : locationInfoList) {
            onHandQtyMap.put(locationInfo.getLocationCode(), locationInfo.getQty());
        }

        Integer qty = onHandQtyMap.get(basLocationDo.getLocationCode());
        if (qty == null) {
            return 0;
        }
        return qty;

    }


    @Override
    public List<BasLocationDo> getAllLocation(String warehouseCode, Integer isActive) {
        LocationWarehouseQueryParam param = new LocationWarehouseQueryParam();
        param.setIsActive(isActive);
        param.setWarehouseCode(warehouseCode);
        param.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        param.setIsVirtual(0);
        param.setPageSize(1000);

        Long id = null;

        List<BasLocationDo> result = new ArrayList<>();
        while (true) {
            param.setId(id);
            List<BasLocationDo> basLocationDos = basLocationRepository.batchQueryPage(param);
            if (org.springframework.util.CollectionUtils.isEmpty(basLocationDos)) {
                break;
            }
            result.addAll(basLocationDos);
            id = basLocationDos.get(basLocationDos.size() - 1).getId();
        }
        return result;
    }

    @Override
    public Integer locationCountByAreaCode(String warehouseCode, String areaCode) {
        return basLocationRepository.selectCountByWarehouseAndArea(warehouseCode, areaCode, OperationUserContextHolder.getTenantCode());
    }

    @Override
    public List<BasLocationDo> getLocationByPassNum(String warehouseCode, String areaCode, List<String> passNums, List<String> levelNums, List<String> columnNums) {
        LocationPassNumQueryParam param = new LocationPassNumQueryParam();
        param.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        param.setIsActive(SimpleStatus.YES.getCode());
        param.setAreaCode(areaCode);
        param.setPassNums(passNums);
        param.setLevelNums(levelNums);
        param.setColumnNums(columnNums);
        param.setWarehouseCode(warehouseCode);
        return basLocationRepository.selectLocationByPassNum(param);
    }

    @Override
    public List<String> getLocationCodesByPassNum(String warehouseCode, String areaCode, List<String> passNums, List<String> levelNums, List<String> columnNums) {
        List<BasLocationDo> basLocationDos = this.getLocationByPassNum(warehouseCode, areaCode, passNums, levelNums, columnNums);
        List<String> locations = basLocationDos.stream().map(e -> e.getLocationCode()).collect(Collectors.toList());
        return locations;
    }


    /**
     * 获取库区名称
     *
     * @param warehouseCode
     * @param locationCode
     * @param areaMap
     * @return
     */
    @Override
    public String getAreaName(String warehouseCode, String locationCode, Map<String, BasAreaDo> areaMap) {
        BasLocationDo location = this.detailByCode(warehouseCode, locationCode);
        if (location == null || StringUtils.isBlank(location.getAreaCode())) {
            return "";
        }
        BasAreaDo areaEntity = areaMap.get(location.getAreaCode());
        if (areaEntity == null) {
            return "";
        }
        return areaEntity.getAreaName();
    }

    @Override
    public Boolean mixLocationByBizType(BasLocationDo locationEntity, Integer bizType) {
        //默认未进行混放
        Boolean isMix = false;
        if (null == locationEntity || StringUtils.isBlank(locationEntity.getLocationCode())
                || null == bizType) {
            log.error("库存BizType为空, locationEntity -> {}, bizType -> {}",
                    JSON.toJSON(locationEntity), JSON.toJSONString(bizType));
            throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY);
        } else {
            InvInventoryEntity inventoryEntity = inventoryQueryService.queryFirstInvByLocationCode(OperationUserContextHolder.getTenantCode(), locationEntity.getWarehouseCode(), locationEntity.getLocationCode());
            if (null != inventoryEntity) {
                if (null == inventoryEntity.getBizType()) {
                    log.warn("库存BizType为空, inventoryEntity -> [{}]", JSON.toJSONString(inventoryEntity));
                    throw new WmsOperationException(WmsExceptionCode.SYSTEM_ERROR);
                }
                if ((WmsBizTypeEnum.UNIQUE_CODE_TYPE.contains(bizType) && WmsBizTypeEnum.NOT_UNIQUE_CODE_TYPE.contains(inventoryEntity.getBizType())
                        || (WmsBizTypeEnum.NOT_UNIQUE_CODE_TYPE.contains(bizType) && WmsBizTypeEnum.UNIQUE_CODE_TYPE.contains(inventoryEntity.getBizType())))) {
                    // 两者进行混放了
                    isMix = true;
                }
            }
        }
        return isMix;
    }

    @Nullable
    @Override
    public Map<String, String> locationAreaMap(String warehouseCode, Set<String> locations) {
        List<BasLocationDo> locationList = this.areaDetailByCodes(warehouseCode, locations);
        if (CollectionUtils.isEmpty(locationList)) {
            return new HashMap<>();
        }
        return locationList.stream().collect(Collectors.toMap(BasLocationDo::getLocationCode, BasLocationDo::getAreaCode, (value1, value2) -> value2));
    }

    /**
     * 完成初步校验之后异步
     */
    @Override
    public void updateStorageWorkZone(BasLocationWorkZoneStorageRequest request) {
        log.info("updateStorageWorkZone request:{}", JSON.toJSONString(request));
        // 分批修改库位信息
        String warehouseCode = request.getWarehouseCode();
        String areaCode = request.getAreaCode();
        String minLevelNum = request.getMinLevelNum();
        String maxLevelNum = request.getMaxLevelNum();
        // 如果层数大于1层或者查询所有层。更新数量不超过5000
        if ((Strings.isBlank(minLevelNum) || Strings.isBlank(maxLevelNum))
                || (Integer.parseInt(maxLevelNum) - Integer.parseInt(minLevelNum)) > 1) {
            int count = basLocationRepository.findCountByWarehouseAreaLevel(warehouseCode, areaCode, minLevelNum, maxLevelNum, request.getMinPassNum(), request.getMaxPassNum());
            if (count > 5000) {
                throw new WmsOperationException(WmsExceptionCode.LOCATION_CHANGE_ON_LIMIT);
            }
            if (count == 0) {
                throw new WmsOperationException("没有满足条件的库位");
            }
        }

        // 校验更新结果是否有数据
        List<BasLocationDo> list = basLocationRepository.selectBasLocationByUpdateParam(request.getWarehouseCode()
                , request.getAreaCode()
                , OperationUserContextHolder.getTenantCode()
                , request.getMinLevelNum()
                , request.getMaxLevelNum()
                , request.getMinPassNum()
                , request.getMaxPassNum()
                , null);
        if (CollectionUtils.isEmpty(list)) {
            throw new WmsOperationException("待更新数据为空");
        }

        // 获取结果生成Excel，上传下载中心
        String s = JSONUtil.toJsonStr(request);
        ReportCreateRequest reportCreateRequest = reportDownloadService.buildReportDownloadRequest("批量修改库位信息", "wms", s);
        excelExportService.triggerAsyncDownload(() -> batchUpdateLocation(request, list),
                "批量修改库位信息" + DateUtils.dateToString(new Date(), DateUtils.FROMAT_DATE), "批量修改库位信息", HEADER, reportCreateRequest);

    }

    private List<Object[]> batchUpdateLocation(BasLocationWorkZoneStorageRequest request, List<BasLocationDo> list) {
        return doBatchUpdateLocation(request, list);
    }


    /**
     * 批量异步执行
     * 更新库位数据，更新结果上传下载中心
     */
    public List<Object[]> doBatchUpdateLocation(BasLocationWorkZoneStorageRequest request, List<BasLocationDo> list) {
        /*根据条件查询所有的需要更新的库位信息,最多有两万多条数据需要处理,需要分组查询数据*/
        List<BasLocationDo> basLocationDoList = new ArrayList<>(MAX_BATCH_SIZE);
        basLocationDoList.addAll(list);
        Long id = list.get(list.size() - 1).getId();
        OperationUserContext userContext = OperationUserContextHolder.get();
        // 继续滚动查询数据
        while (true) {
            list = basLocationRepository.selectBasLocationByUpdateParam(request.getWarehouseCode()
                    , request.getAreaCode()
                    , OperationUserContextHolder.getTenantCode()
                    , request.getMinLevelNum()
                    , request.getMaxLevelNum()
                    , request.getMinPassNum()
                    , request.getMaxPassNum()
                    , id);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            // 重置下一个起始位id
            id = list.get(list.size() - 1).getId();
            basLocationDoList.addAll(list);
        }
        // 数据为空下载空表头
        if (CollectionUtils.isEmpty(basLocationDoList)) {
            return Collections.emptyList();
        }

        // 更新失败的数据
        Map<Long, String> errorMessageMap = new HashMap<>();
        if (request.getLocationType() != null) {
            Map<String, Integer> onHandQtyMap =  getOnHandQtyMap(basLocationDoList
                    , request.getWarehouseCode(), userContext.getTenantCode());
            for (BasLocationDo locationDo : basLocationDoList) {
                Integer qty = onHandQtyMap.get(locationDo.getLocationCode());
                boolean hasInv = qty != null && qty != 0;
                boolean locationTypeChanged = !locationDo.getLocationType().equals(request.getLocationType());
                if (hasInv && locationTypeChanged) {
                    errorMessageMap.put(locationDo.getId(), "该库位中有库存，不可修改库位类型");
                }
            }
        }

        //待更新数据，分组更新
        List<Long> ids = basLocationDoList
                .stream()
                .filter(x -> !errorMessageMap.containsKey(x.getId()))
                .map(BasLocationDo::getId).collect(Collectors.toList());

        Map<Long,BasLocationDo> locationIdMap=basLocationDoList.stream().collect(Collectors.toMap(BasLocationDo::getId, o -> o));

        List<List<Long>> idList = Lists.partition(ids, MAX_BATCH_SIZE);
        if (CollectionUtils.isNotEmpty(idList)) {
            idList.forEach(entity -> {
                try {
                    basLocationRepository.updateBasLocationByParam(request.getMaxCommodityNum()
                            , request.getWorkZoneCode()
                            , userContext.getRealName()
                            , userContext.getUserName()
                            , userContext.getUserId()
                            , request.getLocationType()
                            , entity);
                    //更新完成发送库位变动消息
                    List<BasLocationDo> batchMsglist=new ArrayList<BasLocationDo>();
                    for (Long updateId:entity){
                        BasLocationDo msgLocation=locationIdMap.get(updateId);
                        if (msgLocation!=null){
                            batchMsglist.add(msgLocation);
                        }
                    }
                    locationChangeProcessor.sendBatchMsg(batchMsglist,LocationChangeProcessor.UPDATE);

                } catch (Exception e) {
                    // 更新失败异常吃掉，记录失败记录
                    for (Long errorId : entity) {
                        errorMessageMap.put(errorId, e.getMessage());
                    }
                }
            });
        }
        Date now = new Date();
        // 设置是否成功
        List<LocationUpdateParam> resultList = basLocationDoList.stream().map(item -> {
            LocationUpdateParam locationUpdateParam = new LocationUpdateParam();
            locationUpdateParam.setLocationCode(item.getLocationCode());
            if (request.getMaxCommodityNum() != null) {
                locationUpdateParam.setMaxCommodityNum(item.getMaxCommodityNum());
            }
            if (request.getWorkZoneCode() != null) {
                locationUpdateParam.setWorkZoneCode(item.getWorkZoneCode());
            }
            if (request.getLocationType() != null) {
                String locationTypeDesc = getLocationTypeDesc(userContext.getTenantCode(), request.getWarehouseCode(), item.getLocationType());
                locationUpdateParam.setLocationType(locationTypeDesc);
            }
            String errorMessage = errorMessageMap.get(item.getId());
            if (errorMessage != null ) {
                locationUpdateParam.setSuccessFlag(errorMessage);
            } else {
                String afterLocationTypeDesc = getLocationTypeDesc(userContext.getTenantCode(), request.getWarehouseCode()
                        , request.getLocationType());
                locationUpdateParam.setAfterLocationType(afterLocationTypeDesc);
                locationUpdateParam.setAfterWorkZoneCode(request.getWorkZoneCode());
                locationUpdateParam.setAfterMaxCommodityNum(request.getMaxCommodityNum());
                locationUpdateParam.setSuccessFlag("成功");
            }
            locationUpdateParam.setUpdateTime(now);
            locationUpdateParam.setUpdateRealName(userContext.getRealName());
            return locationUpdateParam;
        }).collect(Collectors.toList());
        //  导出数据，上传下载中心
        return toReportList(resultList);
    }


    private  String getLocationTypeDesc(String tenantCode, String warehouseCode, String locationType) {
        Map<String, String> locationTypeDescMap = sysDictV2Service.getDictMap(tenantCode, warehouseCode, DictTypeEnum.LOCATION_CODE_TYPE);
        String locationTypeDesc = locationTypeDescMap.get(locationType);
        if (locationTypeDesc == null) {
            return "未能识别的库位类型";
        }
        return locationTypeDesc;
    }


    private Map<String, Integer> getOnHandQtyMap(List<BasLocationDo> basLocationDoList
            , String warehouseCode
            , String tenantCode) {
        List<List<BasLocationDo>>  basLocationDoListList = Lists.partition(basLocationDoList, MAX_QUERY_INV_SIZE);
        Map<String, Integer> onHandQtyMap = new HashMap<>(basLocationDoList.size());
        for (List<BasLocationDo> subLocationDoList : basLocationDoListList) {
            List<String> locationCodeList = subLocationDoList
                    .stream().map(BasLocationDo::getLocationCode)
                    .collect(Collectors.toList());
            LocationInventoryResponse locationInventoryResponse = inventoryQueryService.queryLocationInventory(warehouseCode
                    , locationCodeList
                    , tenantCode);
            List<LocationInventoryResponse.LocationInfo> locationInfoList = locationInventoryResponse.getLocationInfoList();
            for (LocationInventoryResponse.LocationInfo locationInfo : locationInfoList) {
                onHandQtyMap.put(locationInfo.getLocationCode(), locationInfo.getQty());
            }
        }
        return onHandQtyMap;
    }


    private List<Object[]> toReportList(List<LocationUpdateParam> locationUpdateParamList) {
        List<Object[]> resultList = new ArrayList<>();
        locationUpdateParamList.forEach(item -> {
            List<Object> list = new ArrayList<>();
            list.add(item.getLocationCode());
            list.add(DateUtils.dateToString(item.getUpdateTime(), DateUtils.FORMAT_TIME));
            list.add(item.getUpdateRealName());
            list.add(item.getAfterLocationType());
            list.add(item.getLocationType());
            list.add(item.getAfterWorkZoneCode());
            list.add(item.getWorkZoneCode());
            list.add(item.getMaxCommodityNum());
            list.add(item.getAfterMaxCommodityNum());
            list.add(item.getSuccessFlag());
            resultList.add(list.toArray());
        });
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchModifyPickSeq(PickSeqParam pickSeqParam) {
        // 原始库位信息
        List<Map<String, Object>> originLocationList = new ArrayList<>();
        // 更改库位信息
        List<BasLocationDo> modifyLocationList = new ArrayList<>();
        List<BasLocationDo> locationDos = this.basLocationRepository.findByWarehouseAndArea(
                pickSeqParam.getWarehouseCode(), pickSeqParam.getAreaCode(), pickSeqParam.getTenantCode());

        if (CollectionUtils.isEmpty(locationDos)) {
            log.info("需要修改拣货顺序的库位为空,pickSeqParam -> [{}]", JSON.toJSONString(pickSeqParam));
        }
        /*按照通道进行分组*/
        Map<String, List<BasLocationDo>> passLocationMap = locationDos.stream().collect(Collectors.groupingBy(BasLocationDo::getPassNum));

        /*按照通道进行升序排序*/
        List<String> locationSortPass = passLocationMap.keySet().stream()
                .sorted(Comparator.comparing((entity -> Integer.parseInt(entity)))).collect(Collectors.toList());
        log.info("通道进行升序, locationPass -> {}", JSON.toJSONString(locationSortPass));

        /*遍历通道*/
        for (int i = 1; i <= locationSortPass.size(); i++) {
            String passCode = locationSortPass.get(i - 1);
            /*通道下的库位列表*/
            List<BasLocationDo> passLocationList = passLocationMap.get(passCode);
            /*通道下的库位按照列进行分组*/
            Map<String, List<BasLocationDo>> columnMap = passLocationList.stream()
                    .collect(Collectors.groupingBy(entity -> String.format("%02d", Integer.parseInt(entity.getColumnNum()))));
            /*通道内的库位进行排序*/
            List<String> columnSortCodes = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(pickSeqParam.getItems()) && !pickSeqParam.getForcePass()) {
                /*按照指定的通道更新拣货路径*/
                Map<String, PickSeqParam.Item> itemPaasMap = pickSeqParam.getItems().stream()
                        .collect(Collectors.toMap(entity -> entity.getPassCode(),
                                Function.identity(), (oldValue, newValue) -> oldValue));

                PickSeqParam.Item itemConfig = itemPaasMap.get(passCode);
                if (null == itemConfig) {
                    continue;
                }
                if (NumberUtils.INTEGER_ONE.equals(itemConfig.getSortRule())) {
                    // 1-升序
                    columnSortCodes = columnMap.keySet().stream().sorted()
                            .collect(Collectors.toList());
                } else if (NumberUtils.INTEGER_ZERO.equals(itemConfig.getSortRule())) {
                    // 0-降序
                    columnSortCodes = columnMap.keySet().stream().sorted(Comparator.reverseOrder())
                            .collect(Collectors.toList());
                } else {
                    throw new WmsException(WmsExceptionCode.SYSTEM_ERROR);
                }
            } else if (CollectionUtils.isEmpty(pickSeqParam.getItems()) && pickSeqParam.getForcePass()) {
                if (i % 2 == 1) {
                    // 列升序处理
                    columnSortCodes = columnMap.keySet().stream().sorted()
                            .collect(Collectors.toList());
                } else {
                    // 列降序处理
                    columnSortCodes = columnMap.keySet().stream().sorted(Comparator.reverseOrder())
                            .collect(Collectors.toList());
                }
            } else {
                // 其他情况
                throw new WmsException("配置错误");
            }

            /*遍历列*/
            /*全量库位刷新拣货路径*/
            Integer columnSeqNum = 1;
            for (String columnSortCode : columnSortCodes) {
                // 同一列的库位信息,设置相同的拣货顺序
                List<BasLocationDo> columnLocationDos = columnMap.get(columnSortCode);
                if (CollectionUtils.isEmpty(columnLocationDos)) {
                    throw new WmsException(WmsExceptionCode.SYSTEM_ERROR);
                }
                columnSeqNum = columnSeqNum + 1;
                BasLocationDo samSeqNumLocation = columnLocationDos.get(0);
                /*库位规则, 库区 + 通道 + 列 + 层*/
                String newSeqNum = new StringBuilder(samSeqNumLocation.getAreaCode())
                        .append(String.format("%02d", Integer.parseInt(samSeqNumLocation.getPassNum())))
                        .append(String.format("%02d", columnSeqNum))
                        .append(String.format("%02d", Integer.parseInt(samSeqNumLocation.getLevelNum())))
                        .toString();

                /*遍历列下面的库位*/
                for (BasLocationDo entity : columnLocationDos) {
                    BasLocationDo basLocationDo = new BasLocationDo();
                    basLocationDo.setId(entity.getId());
                    basLocationDo.setLocationCode(entity.getLocationCode());
                    basLocationDo.setPickingSeqNum(newSeqNum);
                    modifyLocationList.add(basLocationDo);

                    Map<String, Object> temp = new HashMap<>();
                    temp.put("id", entity.getId());
                    temp.put("locationCode", entity.getLocationCode());
                    temp.put("pickingSeqNum", entity.getPickingSeqNum());
                    originLocationList.add(temp);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(modifyLocationList)) {
            log.info("准备更新库位信息, modifyLocationList size -> [{}]", modifyLocationList.size());
            List<List<Map<String, Object>>> partitionList = Lists.partition(originLocationList, 500);

            log.info("---------- start 记录库位原始信息 ----------");
            partitionList.forEach(entity -> {
                log.info(JSON.toJSONString(entity));
            });
            log.info("---------- end 记录库位原始信息 ----------");
            int size = this.basLocationRepository.modifyPickSeq(modifyLocationList);
            if (modifyLocationList.size() != size) {
                throw new WmsException("更新拣货序号失败");
            }
        }
    }

    @Override
    public Map<String, LocationPropertyDo> queryLocationProperty(String warehouseCode, Set<String> locationCodes) {
        if (CollectionUtils.isEmpty(locationCodes)) {
            return Collections.emptyMap();
        }
        List<LocationPropertyDo> locationPropertyDo = groupApply(Lists.newArrayList(locationCodes), 100, (l) -> basLocationRepository.queryLocationProperty(warehouseCode, l));
        if (CollectionUtils.isEmpty(locationPropertyDo)) {
            return Collections.emptyMap();
        }
        return locationPropertyDo.stream().collect(Collectors.toMap(LocationPropertyDo::getLocationCode, Function.identity()));
    }

    @Override
    public List<BasLocationDo> queryDetailByLocationCodeList(String warehouseCode, Set<String> locationCodeList) {

        if (CollectionUtils.isEmpty(locationCodeList)) {
            return Collections.emptyList();
        }
        return basLocationRepository.selectAllByWarehouseCodeAndLocationCodeList(warehouseCode, locationCodeList);
    }

    private void initEntity(BasLocationDo basLocationDo) {
        OperationUserContext context = OperationUserContextHolder.get();
        basLocationDo.setCreatedTime(new Date());
        basLocationDo.setCreatedUserId(context.getUserId());
        basLocationDo.setUpdatedUserId(context.getUserId());
        basLocationDo.setCreatedRealName(context.getRealName());
        basLocationDo.setUpdatedRealName(context.getRealName());
        basLocationDo.setCreatedUserName(context.getUserName());
        basLocationDo.setTenantCode(context.getTenantCode());
    }

    private void initUpdate(BasLocationDo basLocationDo) {
        OperationUserContext context = OperationUserContextHolder.get();
        basLocationDo.setUpdatedRealName(context.getRealName());
        basLocationDo.setUpdatedUserId(context.getUserId());
        basLocationDo.setUpdatedUserName(context.getUserName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer initVirtualLocationCode(LocationInitRequest locationInitRequest){
        String locationCode = locationInitRequest.getLocationCode();
        String warehouseCode = locationInitRequest.getWarehouseCode();
        String tenantCode = locationInitRequest.getTenantCode();
        String locationCodeName = locationInitRequest.getLocationCodeName();
        BasLocationDo basLocationDo = buildBasLocation(locationCode, warehouseCode,
                tenantCode, 0, 1, "102");
        basLocationRepository.save(basLocationDo);
        sysDictRepository.insertSelective(buildSysDict("adjustForbidLocationCode",locationCodeName,
                locationCode,warehouseCode,tenantCode));
        return 1;
    }



    //参考开仓代码
    private BasLocationDo buildBasLocation(String locationCode, String warehouseCode, String tenantCode,Integer
            locationProperty,Integer isVirtual, String passNum) {
        BasLocationDo basLocationDo = new BasLocationDo();
        basLocationDo.setLocationCode(locationCode);
        basLocationDo.setIsActive(1);
        basLocationDo.setWarehouseCode(warehouseCode);
        basLocationDo.setAreaCode(StringUtils.EMPTY);
        basLocationDo.setLocationType(StringUtils.EMPTY);
        basLocationDo.setTenantCode(tenantCode);
        basLocationDo.setPickingSeqNum(StringUtils.EMPTY);
        basLocationDo.setPutAwaySeqNum(StringUtils.EMPTY);
        basLocationDo.setPassNum(passNum);
        basLocationDo.setLevelNum(StringUtils.EMPTY);
        basLocationDo.setLength(BigDecimal.valueOf(0.00));
        basLocationDo.setWidth(BigDecimal.valueOf(0.00));
        basLocationDo.setHeight(BigDecimal.valueOf(0.00));
        basLocationDo.setVolume(BigDecimal.valueOf(0.000000));
        basLocationDo.setRowNum(StringUtils.EMPTY);
        basLocationDo.setColumnNum(StringUtils.EMPTY);
        basLocationDo.setIsMultiSku(0);
        basLocationDo.setIsMultiItem(0);
//        basLocationDo.setLastTakeStockDate(DateUtils.parseDate("0000-00-00",DateUtils.FORMAT_DATE));
        basLocationDo.setMaxSkuNum(9999999);
        basLocationDo.setMaxItemNum(0);
        basLocationDo.setLocationState(0);
        basLocationDo.setLocationSpecs(StringUtils.EMPTY);
        OperationUserContext userContext = OperationUserContextHolder.get();
        basLocationDo.setCreatedUserId(userContext.getUserId());
        basLocationDo.setCreatedRealName(userContext.getRealName());
        basLocationDo.setCreatedTime(new Date());
        basLocationDo.setUpdatedUserId(userContext.getUserId());
        basLocationDo.setUpdatedRealName(userContext.getRealName());
        basLocationDo.setUpdatedUserName(userContext.getUserName());
        basLocationDo.setUpdatedTime(new Date());
        basLocationDo.setDeleted(0);
        basLocationDo.setCreatedUserName(userContext.getUserName());
        basLocationDo.setIsVirtual(isVirtual);
        basLocationDo.setLocationProperty(locationProperty.byteValue());
        return basLocationDo;
    }

    private SysDictDo buildSysDict(String type, String dictName, String dictValue, String warehouseCode, String tenantCode) {
        SysDictDo sysDictDo = new SysDictDo();
        sysDictDo.setType(type);
        sysDictDo.setDictName(dictName);
        sysDictDo.setDictValue(dictValue);
        OperationUserContext userContext = OperationUserContextHolder.get();

        sysDictDo.setCreatedUserId(userContext.getUserId());
        sysDictDo.setCreatedRealName(userContext.getRealName());
        sysDictDo.setCreatedTime(new Date());
        sysDictDo.setUpdatedUserId(userContext.getUserId());
        sysDictDo.setUpdatedRealName(userContext.getRealName());
        sysDictDo.setUpdatedUserName(userContext.getUserName());
        sysDictDo.setUpdatedTime(new Date());
        sysDictDo.setTenantCode(tenantCode);
        sysDictDo.setWarehouseCode(warehouseCode);
        sysDictDo.setRecordVersion(0L);
        return sysDictDo;

    }

    @Override
    public void sendActiveMsg(List<LocationData> list){
        List<BasLocationDo> target=BeanUtil.copyListProperties(list,BasLocationDo.class);
        locationChangeProcessor.sendBatchMsg(target,LocationChangeProcessor.ACTIVE);
    }

}

