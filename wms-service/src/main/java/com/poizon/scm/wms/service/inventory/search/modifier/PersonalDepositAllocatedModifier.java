package com.poizon.scm.wms.service.inventory.search.modifier;

import com.poizon.scm.wms.adapter.inventory.model.InvInventoryAllocatedDo;
import com.poizon.scm.wms.adapter.inventory.query.SciInventoryAllocateDetail;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.domain.inventory.operate.InventoryAllocated;
import com.poizon.scm.wms.pojo.inventory.InventoryAllocatedCancelPojo;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 个人寄存库存分配修改器
 *
 * <AUTHOR>
 * @data 2021/4/1
 */
@Slf4j
@Service
public class PersonalDepositAllocatedModifier implements InventoryAllocatedModifier {

    @Autowired
    InventoryAllocated inventoryAllocated;

    @Autowired
    DeliveryDetailRepository deliveryDetailRepository;

    @Override
    public int bizType() {
        return WmsBizTypeEnum.GE_REN_JI_CUN.getBizType();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(String referenceNo, List<SciInventoryAllocateDetail> cisInventoryAllocatedRecordList, List<InvInventoryAllocatedDo> invInventoryAllocatedRecordList) {
        /*
         * 个人寄存需要通过唯一码校对库存分配记录
         * */
        /*sci库存唯一码库存分配记录*/
        List<String> sciAllocatedUniqueCodeList = cisInventoryAllocatedRecordList.stream().map(SciInventoryAllocateDetail::getUniqueCode).collect(Collectors.toList());
        /*单据明细和数量*/
        Map<String, Integer> orderDetailMappingQty = invInventoryAllocatedRecordList.stream().collect(Collectors.groupingBy(InvInventoryAllocatedDo::getShipmentDetailNo, Collectors.summingInt(InvInventoryAllocatedDo::getQty)));
        /*待取消的库存分配记录*/
        List<InvInventoryAllocatedDo> waitingCancelAllocatedRecordList = invInventoryAllocatedRecordList.stream().filter(invInventoryAllocatedRecord -> !sciAllocatedUniqueCodeList.contains(invInventoryAllocatedRecord.getUniqueCode())).collect(Collectors.toList());
        /*理论上这里不可能为空*/
        if (CollectionUtils.isEmpty(waitingCancelAllocatedRecordList)) {
            throw new WmsException("修改wms库存分配记录失败,数据有问题");
        }
        /*<清退单明细编号,待取消分配记录>*/
        Map<String, List<InvInventoryAllocatedDo>> waitingCancelAllocatedMapping = waitingCancelAllocatedRecordList.stream().collect(Collectors.groupingBy(InvInventoryAllocatedDo::getShipmentDetailNo));
        waitingCancelAllocatedMapping.forEach((orderDetailNo, allocatedRecordList) -> {
            modifyDetail(orderDetailNo, orderDetailMappingQty.get(orderDetailNo), allocatedRecordList);
        });
    }

    /**
     * @param orderDetailNo       单据明细
     * @param oldAllocatedQty     wms库存分配数量
     * @param allocatedRecordList wms待取消的分配记录
     */
    private void modifyDetail(String orderDetailNo, Integer oldAllocatedQty, List<InvInventoryAllocatedDo> allocatedRecordList) {
        int waitingCancelQty = 0;
        List<String> allocatedNo = new ArrayList<>();
        for (InvInventoryAllocatedDo record : allocatedRecordList) {
            waitingCancelQty += record.getQty();
            allocatedNo.add(record.getAllocatedNo());
        }
        removeInventoryAllocated(allocatedRecordList.get(0).getTenantCode(), allocatedNo);
        /*修改明细实际分配数量*/
        int count = deliveryDetailRepository.updateActualAllocateQty(orderDetailNo, oldAllocatedQty - waitingCancelQty);
        if (count != NumberUtils.INTEGER_ONE) {
            log.error("单据明细{}实际分配数量修改失败", orderDetailNo);
            throw new WmsException(WmsExceptionCode.UPDATE_DELIVERY_DETAIL_ALLOCATED_QTY_ERROR);
        }
    }


    /**
     * 删除该条库存分配记录
     *
     * @param waitingCancelAllocatedNo
     */
    private void removeInventoryAllocated(String tenantCode, List<String> waitingCancelAllocatedNo) {
        /*继续走取消流程*/
        InventoryAllocatedCancelPojo inventoryAllocatedCancelPojo = new InventoryAllocatedCancelPojo();
        inventoryAllocatedCancelPojo.setTenantCode(tenantCode);
        inventoryAllocatedCancelPojo.setAllocatedNoList(waitingCancelAllocatedNo);
        inventoryAllocated.cancelAllocatedInventory(inventoryAllocatedCancelPojo);
    }
}
