package com.poizon.scm.wms.third.receive.biz.out.ship;

import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.api.enums.TenantEnum;
import com.poizon.scm.wms.common.constants.LocationConstant;
import com.poizon.scm.wms.domain.outbound.customerreturn.CustomerReturnVirtualShipService;
import com.poizon.scm.wms.pojo.third.notify.base.task.BaseTaskDetailRequest;
import com.poizon.scm.wms.pojo.third.receive.request.TaskReceiveRequest;
import com.poizon.scm.wms.third.receive.biz.out.ship.base.AbstractShipProcessor;
import com.poizon.scm.wms.util.enums.WmsDeliveryDetailCancelEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客退发货-目前只有客退虚拟出库部分场景(商品未上架)会走到该发货模型
 * <AUTHOR>
 * @date 2023/5/8 3:59 PM:55
 * @description
 **/
@Service
public class CustomerReturnShipProcessor extends AbstractShipProcessor {
    @Autowired
    private InventoryRepository inventoryRepository;
    @Resource
    private CustomerReturnVirtualShipService customerReturnVirtualShipService;

    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Override
    public List<String> getTenantIdList() {
        return TenantEnum.dwAllTenantList;
    }

    @Override
    public Boolean matchProcessor(DeliveryHeaderDo deliveryHeaderDo) {
        return deliveryHeaderDo.getType().equals(WmsOutBoundTypeEnum.KTHXNCK.getType());
    }

    @Override
    public void handle(TaskReceiveRequest receiveRequest, DeliveryHeaderDo deliveryHeaderDo) {
        BaseTaskDetailRequest taskDetailRequest = receiveRequest.getTaskDetails().get(0);
        InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(taskDetailRequest.getUniqueCode());
        //如果库存不存在或者库存在收货暂存库位上
        if(inventoryDo != null && inventoryDo.getLocationCode().equals(LocationConstant.RECEIVE_DOCK)){
            List<DeliveryDetailDo> deliveryDetailDoList = deliveryDetailRepository.queryByDeliveryOrderCodeAndStatus(receiveRequest.getOrderCode(), taskDetailRequest.getUniqueCode(), WmsDeliveryDetailCancelEnum.NORMAL);
            DeliveryDetailDo deliveryDetailDo = deliveryDetailDoList.get(0);
            customerReturnVirtualShipService.shipToOfc(deliveryDetailDo, inventoryDo);
            customerReturnVirtualShipService.shipToPink(deliveryDetailDo, deliveryHeaderDo);
            customerReturnVirtualShipService.innerShip(deliveryDetailDo);
        }
    }
}
