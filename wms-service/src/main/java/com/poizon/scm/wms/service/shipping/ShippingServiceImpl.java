package com.poizon.scm.wms.service.shipping;
import java.util.Date;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.wms.adapter.common.model.TaskDetailDo;
import com.poizon.scm.wms.adapter.container.model.ContainerInstanceDo;
import com.poizon.scm.wms.adapter.handover.model.DeliveryHandoverDetailDo;
import com.poizon.scm.wms.adapter.handover.model.DeliveryHandoverHeaderDo;
import com.poizon.scm.wms.adapter.inbound.rpcinventory.repository.WmsInventoryQueryApiRpcRepository;
import com.poizon.scm.wms.adapter.inner.biz.unique.model.WarehouseUniqueLifeCycleDo;
import com.poizon.scm.wms.adapter.inner.biz.unique.repository.db.WarehouseUniqueLifeCycleCommandRepository;
import com.poizon.scm.wms.adapter.inner.biz.unique.repository.db.WarehouseUniqueLifeCycleQueryRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.model.SnBindOperateParam;
import com.poizon.scm.wms.adapter.inventory.model.TempInvDo.SnInfo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.inventory.repository.SnCodeRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryExtraInfoDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryLogisticDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryExtraInfoRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryLogisticRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.command.DeliveryLogisticCommandRepository;
import com.poizon.scm.wms.adapter.outbound.grey.repository.IGreyRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickTaskDetailResultQueryRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDetailDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackDetailRepository;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipAntiFakeDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDetailResultDo;
import com.poizon.scm.wms.adapter.outbound.ship.model.ShipTaskDo;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.command.ShipTaskDetailResultCommandRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipAntiFakeQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskDetailResultQueryRepository;
import com.poizon.scm.wms.adapter.outbound.ship.repository.query.ShipTaskQueryRepository;
import com.poizon.scm.wms.adapter.outbound.sn.model.ShipSnBindRelationDo;
import com.poizon.scm.wms.adapter.outbound.sn.model.repository.ShipSnBindRelationRepository;
import com.poizon.scm.wms.adapter.third.pink.OperateItemInfoRepository;
import com.poizon.scm.wms.api.command.ship.request.ShipBySnRequest;
import com.poizon.scm.wms.api.command.ship.request.TaskDetailNoShipRequest;
import com.poizon.scm.wms.api.command.ship.response.BarcodeShipResponse;
import com.poizon.scm.wms.api.command.ship.response.HandoverDetailResponse;
import com.poizon.scm.wms.api.dto.response.commodity.CommodityExtendResponse;
import com.poizon.scm.wms.api.dto.response.receive.TaskItemDetailResponse;
import com.poizon.scm.wms.api.enums.*;
import com.poizon.scm.wms.common.WmsResult;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.utils.GetPropertyHelper;
import com.poizon.scm.wms.common.utils.IdGenUtil;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.dao.entitys.DeliveryDetailEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryHeaderEntity;
import com.poizon.scm.wms.dao.entitys.DeliveryRelatedOrdersEntity;
import com.poizon.scm.wms.domain.apply.HandoverApplyService;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.domain.notify.notifyLms.processor.ShippingNotifyLmsProcessor;
import com.poizon.scm.wms.domain.notify.notifyPinkLog.TaskFinishNotifyPinkLog;
import com.poizon.scm.wms.domain.outbound.container.lpn.ShipTakeOutContainerService;
import com.poizon.scm.wms.domain.outbound.delivery.DeliverySNService;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.outbound.producer.entity.OutboundDto;
import com.poizon.scm.wms.domain.outbound.report.OutboundOrderEvent;
import com.poizon.scm.wms.domain.outbound.returngoods.ExpressShipService;
import com.poizon.scm.wms.domain.outbound.returngoods.WmsPackShipService;
import com.poizon.scm.wms.domain.outbound.returngoods.entity.LogisticsInfo;
import com.poizon.scm.wms.domain.outbound.returngoods.service.WmsPackService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.logistic.SelfTakeLogisticsService;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.LogisticMakeParam;
import com.poizon.scm.wms.domain.outbound.returngoods.service.param.SealBoxParam;
import com.poizon.scm.wms.domain.outbound.ship.processor.ShipAntiFakeCheckHandler;
import com.poizon.scm.wms.domain.outbound.worklog.WmsOutboundWorkLogService;
import com.poizon.scm.wms.domain.outbound.worklog.enums.WmsLogActionEnum;
import com.poizon.scm.wms.domain.outbound.worklog.enums.WorkLogTypeEnum;
import com.poizon.scm.wms.domain.outbound.worklog.param.OperationLogParam;
import com.poizon.scm.wms.domain.ship.event.message.ShipEventParam;
import com.poizon.scm.wms.domain.ship.event.producer.DeliveryShipEventProducer;
import com.poizon.scm.wms.domain.strategy.StrategyConfigCodeEnum;
import com.poizon.scm.wms.domain.strategy.WmsStrategyFactory;
import com.poizon.scm.wms.domain.task.TaskFactory;
import com.poizon.scm.wms.domain.task.biz.ship.ShipCommonHandler;
import com.poizon.scm.wms.infra.outbound.ship.mapper.ShipTaskDetailDoExtMapper;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipHeaderPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryBasePojo;
import com.poizon.scm.wms.pojo.log.OperateLogPojo;
import com.poizon.scm.wms.pojo.task.BaseTaskDetailPojo;
import com.poizon.scm.wms.pojo.task.BaseTaskPojo;
import com.poizon.scm.wms.pojo.task.TaskHeaderPojo;
import com.poizon.scm.wms.pojo.task.TaskPojo;
import com.poizon.scm.wms.pojo.task.operation.TaskOperationPojo;
import com.poizon.scm.wms.pojo.task.operation.modify.BaseExecuteDetailPojo;
import com.poizon.scm.wms.pojo.task.operation.modify.ExecuteTaskPojo;
import com.poizon.scm.wms.pojo.task.operation.modify.ForceFinishTaskPojo;
import com.poizon.scm.wms.pojo.task.param.CodeQueryPojo;
import com.poizon.scm.wms.pojo.third.notify.base.task.BaseTaskDetailRequest;
import com.poizon.scm.wms.pojo.third.notify.request.base.OutboundOperateLogRequest;
import com.poizon.scm.wms.pojo.third.notify.request.out.NotifyOfcDeductSciRequest;
import com.poizon.scm.wms.pojo.third.receive.request.TaskReceiveRequest;
import com.poizon.scm.wms.service.apply.OubApplyService;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.config.SNConfig;
import com.poizon.scm.wms.service.delivery.common.DeliveryOrderCommonService;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.delivery.query.DeliveryOrderQueryService;
import com.poizon.scm.wms.service.haandover.DeliveryHandoverDetailService;
import com.poizon.scm.wms.service.haandover.DeliveryHandoverHeaderService;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateService;
import com.poizon.scm.wms.service.log.OperateLogService;
import com.poizon.scm.wms.service.merchant.ScpMerchantService;
import com.poizon.scm.wms.service.notifyPink.NotifyPinkService;
import com.poizon.scm.wms.service.sys.SysDictService;
import com.poizon.scm.wms.service.task.TaskHandlerService;
import com.poizon.scm.wms.service.task.TaskQueryService;
import com.poizon.scm.wms.third.receive.biz.out.HandoverDeliveryService;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.util.WmsOutboundBusinessUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.athena.api.enums.SnOperateTypeEnum;
import com.shizhuang.athena.api.enums.SnTypeEnum;
import com.shizhuang.athena.api.response.query.InventoryResponse;
import com.shizhuang.duapp.erp.api.definition.OrderApi;
import com.shizhuang.duapp.erp.api.model.request.PlatformTradeOrderDeliverRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.util.enums.TaskStatusEnum.*;
import static org.springframework.util.DigestUtils.md5DigestAsHex;

/**
 * @Author: dwq
 * @Date: 2020/5/12 11:51 上午
 */
@Slf4j
@Service
public class ShippingServiceImpl implements ShippingService {
    @Autowired
    private ShipTaskQueryRepository shipTaskQueryRepository;

    @Autowired
    private ShipTaskCommandRepository shipTaskCommandRepository;

    @Autowired
    private ShipTaskDetailResultCommandRepository shipTaskDetailResultCommandRepository;
    @Autowired
    private ShipTaskDetailResultQueryRepository shipTaskDetailResultQueryRepository;
    @Autowired
    private PickTaskDetailQueryRepository pickTaskDetailQueryRepository;
    @Autowired
    private PickTaskDetailResultQueryRepository pickTaskDetailResultQueryRepository;

    @Autowired
    private ShipTaskDetailQueryRepository shipTaskDetailQueryRepository;

    @Autowired
    private ShipTaskDetailCommandRepository shipTaskDetailCommandRepository;
    @Autowired
    private DeliveryOrderQueryService deliveryOrderQueryService;
    @Autowired
    private TaskQueryService taskQueryService;
    @Autowired
    private TaskFactory<TaskOperationPojo> taskFactory;
    @Autowired
    private OperateLogService operateLogService;
    @Autowired
    private TaskHandlerService taskHandlerService;
    @Autowired
    private DeliveryOrderModifyService deliveryOrderModifyService;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private ScpMerchantService scpMerchantService;
    @Autowired
    private ShippingCancelService shippingCancelService;
    @Autowired
    private DeliveryHandoverHeaderService deliveryHandoverHeaderService;
    @Autowired
    private DeliveryHandoverDetailService deliveryHandoverDetailService;
    @Autowired
    private ICommodityQueryV2Service iCommodityQueryV2Service;
    @Autowired
    private InventoryRepository inventoryRepository;
    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    private DeliveryOrderCommonService deliveryOrderCommonService;
    @Autowired
    private OubApplyService oubApplyService;
    @Autowired
    private BatchShipAsyncConsistencyExecute batchShipAsyncConsistencyExecute;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private TaskFinishNotifyPinkLog taskFinishNotifyPinkLog;
    @Resource
    private OrderApi orderApi;

    @Autowired
    private CombineDeliveryShipService combineDeliveryShipService;

    @Autowired
    private CombineDeliveryShipTaskService combineDeliveryShipTaskService;

    @Autowired
    private CombineDeliveryShipOrderService combineDeliveryShipOrderService;

    @Autowired
    private WmsInventoryQueryApiRpcRepository inventoryQueryApiRpcRepository;
    @Autowired
    private InventoryOperateService inventoryOperateService;
    @Autowired
    private WarehouseUniqueLifeCycleQueryRepository warehouseUniqueLifeCycleQueryRepository;
    @Autowired
    private WarehouseUniqueLifeCycleCommandRepository warehouseUniqueLifeCycleCommandRepository;
    @Resource
    private OutboundMessageProducer outboundMessageProducer;
    @Autowired
    private DeliveryLogisticCommandRepository deliveryLogisticCommandRepository;
    @Resource
    private ShipAntiFakeCheckHandler shipAntiFakeCheckHandler;
    @Autowired
    private OperateItemInfoRepository operateItemInfoRepository;
    @Autowired
    private WmsStrategyFactory<Void> wmsStrategyFactory;
    @Resource
    private DeliveryLogisticRepository deliveryLogisticRepository;
    @Resource
    private DeliveryShipEventProducer deliveryShipEventProducer;

    @Resource
    private WmsPackService wmsPackService;
    @Resource
    private SelfTakeLogisticsService selfTakeLogisticsService;
    @Resource
    private ExpressShipService expressShipService;
    @Resource
    private WmsPackShipService wmsPackShipService;
    @Resource
    private DeliveryExtraInfoRepository deliveryExtraInfoRepository;
    @Resource
    private WmsPackDetailRepository wmsPackDetailRepository;

    @Autowired
    private ShippingNotifyLmsProcessor shippingNotifyLmsProcessor;
    @Autowired
    private DeliverySNService deliverySNService;

    @Autowired
    private ShipCommonHandler shipCommonHandler;

    @Autowired
    private ShipSnBindRelationRepository shipSnBindRelationRepository;

    @Autowired
    private ShipTakeOutContainerService shipTakeOutContainerService;

    private static final Integer MAX_BATCH_SAVE_SIZE = 500;
    @Resource
    private SNConfig snConfig;
    @Resource
    private ShippingService shippingService;
    @Autowired
    private HandoverDeliveryService handoverDeliveryService;

    @Autowired
    private ShipTaskDetailDoExtMapper shipTaskDetailDoExtMapper;
    @Autowired
    private SnCodeRepository snCodeRepository;

    @Autowired
    private WmsOutboundBusinessUtils wmsOutboundBusinessUtils;

    @Autowired
    private WmsOutboundWorkLogService wmsOutboundWorkLogService;

    @Autowired
    private IGreyRepository greyRepository;
    @Autowired
    private ShipAntiFakeQueryRepository shipAntiFakeQueryRepository;

    @Autowired
    private HandoverApplyService handoverApplyService;

    @Autowired
    private NotifyPinkService notifyPinkService;

    @Value("${batch.ship.black.warehouseCodes:}")
    private List<String> blackWarehouseCodes;


    //这是一个临时开关后续灰度完成后不需要了
    @Value("${ship.batch.sns.inv.switch:true}")
    private boolean snsSwitch;

    private static final Long SYSTEM_USER_ID = 1L;

    private static final String DEFAULT_SUCCESS_MESSAGE = "操作成功";
    private static final String KYQDB_SUCCESS_MESSAGE = "正在执行中, 请稍候进行查询";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> executeShipping(String referenceNo, String uniqueCode, Boolean useLock) {
        TaskPojo taskPojo = getShipTask(uniqueCode,referenceNo);
        return executeShipping(referenceNo, taskPojo.getTaskDetailPojoList().get(0), 1, useLock);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> executeShippingByTaskDetailNo(TaskDetailNoShipRequest request) {
        TaskPojo taskPojo = getTaskPojo(request.getDeliveryOrderNo(), request.getTaskDetailNo());
        if(snConfig.isSN(OperationUserContextHolder.get().getWarehouseCode(),taskPojo.getTaskDetailPojoList().get(0).getBizType())){
            throw new WmsOperationException("请升级最新版pink，再进行操作");
        }
        /**
         * 得物租户：自营仓验证非交易订单才能使用按订单发货
         * 门道租户：门道仓取回单需通过【WMS】退供模块进行发货
         */
        wmsStrategyFactory.getWmsStrategy(OperationUserContextHolder.get().getWarehouseCode(),
                StrategyConfigCodeEnum.SHIP_DETAIL_STRATEGY,OperationUserContextHolder.get().getWarehouseCode()).
                apply(request.getDeliveryOrderNo());
        return executeShipping(request.getDeliveryOrderNo(), taskPojo.getTaskDetailPojoList().get(0), request.getOperationQty(), true);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> shipBySnNo(ShipBySnRequest request) {
        TaskPojo taskPojo = getTaskPojo(request.getDeliveryOrderNo(), request.getTaskDetailNo());
        /**
         * 得物租户：自营仓验证非交易订单才能使用按订单发货
         * 门道租户：门道仓取回单需通过【WMS】退供模块进行发货
         */
        wmsStrategyFactory.getWmsStrategy(OperationUserContextHolder.get().getWarehouseCode(),
                        StrategyConfigCodeEnum.SHIP_DETAIL_STRATEGY,OperationUserContextHolder.get().getWarehouseCode()).
                apply(request.getDeliveryOrderNo());
        Result<String> result = executeShipping(request.getDeliveryOrderNo(), taskPojo.getTaskDetailPojoList().get(0), 1, false);
        //sn 模式
        processSnBusiness(taskPojo,request.getSnNo());
        return result;
    }


    /**
     * 处理sn业务
     */
    private void processSnBusiness(TaskPojo shipTask,String snNo){
        BaseTaskDetailPojo detailTask = shipTask.getTaskDetailPojoList().get(0);
        DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDetail(detailTask.getReferenceDetailNo());
        if(!snConfig.isSN(OperationUserContextHolder.get().getWarehouseCode(),detailTask.getBizType(), deliveryDetailDo.getInvManagementMode(), deliveryDetailDo.getQualityLevel())){
            throw new WmsOperationException("非SN模式，不支持扫SN码发货");
       }
        SnInfo snInfo = snCodeRepository.getSkuInfoOfSnCode(OperationUserContextHolder.getTenantCode(), snNo);
       //校验sn码是否可用
        validateSNInfo(snInfo,detailTask);
        //sn码绑定
        snCodeRepository.bind(buildSnBindOperateParam(snInfo,detailTask));
        //保存发货sn码记录
        shipSnBindRelationRepository.insert(buildShipSnBindRelationDo(snInfo,detailTask));
    }

    /**
     * 构建绑定报文
     * @param snInfo
     * @param detailPojo
     * @return
     */
    private SnBindOperateParam buildSnBindOperateParam(SnInfo snInfo,BaseTaskDetailPojo detailPojo){
        SnBindOperateParam snBindOperateParam = new SnBindOperateParam();
        SnBindOperateParam.SnBindOperateItem item= new SnBindOperateParam.SnBindOperateItem();
        item.setBindType(SnOperateTypeEnum.BIND.getCode());
        item.setOperateUserId(String.valueOf(OperationUserContextHolder.get().getUserId()));
        item.setUniqueCode(snInfo.getSn());
        item.setOperateUserName(OperationUserContextHolder.get().getUserName());
        item.setTenantCode(OperationUserContextHolder.getTenantCode());
        item.setOrderCode(detailPojo.getReferenceNo());
        item.setOrderDetailCode(detailPojo.getReferenceDetailNo());
        item.setOrderType(detailPojo.getReferenceType());
        snBindOperateParam.setItems(Collections.singletonList(item));
        return snBindOperateParam;
    }
    /**
     * 构建发货sn绑定记录
     * @param snInfo
     * @param detailPojo
     * @return
     */
    private ShipSnBindRelationDo buildShipSnBindRelationDo(SnInfo snInfo ,BaseTaskDetailPojo detailPojo){
        OperationUserContext user = OperationUserContextHolder.get();
        ShipSnBindRelationDo shipSnBindRelationDo = new ShipSnBindRelationDo();
        shipSnBindRelationDo.setBatchNo(snInfo.getBatchNo());
        shipSnBindRelationDo.setShipTaskNo(detailPojo.getTaskNo());
        shipSnBindRelationDo.setCreatedRealName(user.getRealName());
        shipSnBindRelationDo.setCreatedUserId(user.getUserId());
        shipSnBindRelationDo.setCreatedUserName(user.getUserName());
        shipSnBindRelationDo.setTenantId(OperationUserContextHolder.getTenantCode());
        shipSnBindRelationDo.setSnNo(snInfo.getSn());
        shipSnBindRelationDo.setWarehouseCode(user.getWarehouseCode());
        shipSnBindRelationDo.setShipTaskDetailNo(detailPojo.getDetailNo());
        return shipSnBindRelationDo;
    }
    /**
     * 校验sn码是否可用
     * @param snInfo
     */
    private void validateSNInfo(SnInfo snInfo, BaseTaskDetailPojo shipTaskDetail) {
        if (snInfo == null) {
            throw new WmsOperationException("SN信息不存在");
        }
        if (!snInfo.getStatus().equals(SNStatusEnum.IN_STOCK.getCode())) {
            throw new WmsOperationException("SN已被绑定");
        }
        if (!snInfo.getSkuId().equals(shipTaskDetail.getSkuId())) {
            throw new WmsOperationException("请扫描正确的sku进行装箱");
        }

        ShipTaskDetailDo shipTaskDetailDo = shipTaskDetailDoExtMapper.queryByDetailNo(shipTaskDetail.getDetailNo(), shipTaskDetail.getTenantCode(), shipTaskDetail.getWarehouseCode());
        if (!shipTaskDetailDo.getBatchNo().equals(snInfo.getBatchNo()))  {
            throw new WmsOperationException("存在SN商品不属于当前出库单");
        }

    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> executeShippingByTaskDetailNoV2(TaskDetailNoShipRequest request) {
        TaskPojo taskPojo = getTaskPojo(request.getDeliveryOrderNo(), request.getTaskDetailNo());
        return executeShipping(request.getDeliveryOrderNo(), taskPojo.getTaskDetailPojoList().get(0), request.getOperationQty(), false);
    }

    private Result<String> executeShipping(String referenceNo, BaseTaskDetailPojo pojo, int operationQty, boolean useLock) {

        if (pojo == null) {
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
        }
        DeliveryHeaderEntity delivery = deliveryOrderQueryService.queryDeliveryInfo(pojo.getReferenceNo());
        if (null == delivery) {
            throw new WmsException(WmsExceptionCode.BILL_EMPTY);
        }
        if (!delivery.isFinishPickOrOuting()) {
            //未完成拣货
            throw new WmsOperationException(WmsExceptionCode.PICK_NOT_FINISHED);
        }

        DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDeliveryDetailByOrderCodeAndDetailNo(referenceNo, pojo.getReferenceDetailNo());

        if (null == deliveryDetailDo) {
            throw new WmsOperationException(WmsExceptionCode.BILL_EMPTY);
        }

        boolean lockFlag = false;
        try {
            if (useLock && !distributeLockUtil.tryLockForBiz(LockEnum.SHIP_EXECUTE_LOCK, referenceNo)) {
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            lockFlag = true;

            if (delivery.isCanceled() || WmsDeliveryDetailCancelEnum.deliveryDetailIsCancel(deliveryDetailDo.getCancelFlag())) {
                //单据已取消，需要返架
                shippingCancelService.executeCancel(pojo.getTaskNo(), delivery.getDeliveryOrderCode(), delivery.getType());
                throw new WmsOperationException(WmsExceptionCode.BILL_CANCELED_NEED_BACK);
            }
            checkTaskStatus(pojo);
            //校验商品是否需要拍照&绑扣
            shipAntiFakeCheckHandler.checkByUniqueCode(referenceNo,pojo.getUniqueCode());

            //校验前端传入单据号与任务单号是否一致
            if (!referenceNo.equals(pojo.getReferenceNo())) {
                log.warn("入参referenceNo与系统查询的referenceNo不一致;"
                                + "referenceNo:{},taskReferenceNo:{}"
                        , referenceNo, pojo.getReferenceNo());
                throw new WmsOperationException(WmsExceptionCode.PARAMS_ERROR);
            }
            if (operationQty > pojo.getQty() - pojo.getOperationQty()) {
                throw new WmsOperationException(WmsExceptionCode.OPERATION_QTY_MAX_ERROR);
            }
            execute(pojo.getTaskNo(),
                    pojo.getDetailNo(), pojo.getUniqueCode(), pojo.getInventoryNo(), EXECUTING, operationQty);
            //通知lms
            ShipTaskDetailDo shipTaskDetailDo = new ShipTaskDetailDo();
            BeanUtils.copyProperties(pojo,shipTaskDetailDo);
            shipTaskDetailDo.setBizType(pojo.getBizType().byteValue());
            //这里发货执行后的返回值不好取,给个null
            if (!OperationUserContextHolder.get().getUserId().equals(SYSTEM_USER_ID)) {
                shippingNotifyLmsProcessor.notify(shipTaskDetailDo);
            }
            //添加日志
            addLog(referenceNo, pojo.getDetailNo(), pojo.getReferenceType(), pojo.getTaskNo(), pojo.getUniqueCode(), pojo.getWarehouseCode());
            /*异步回传pink操作日志*/
            notifyPinkOperationLog(pojo.getDetailNo(), pojo.getBizType());
            //日志中心
            OperationLogParam operationLogParam = OperationLogParam.builder()
                    .orderNo(Lists.newArrayList(deliveryDetailDo.getDetailNo()))
                    .workLogTypeEnum(WorkLogTypeEnum.OUTBOUND_DETAIL_NO)
                    .qty(operationQty)
                    .wmsLogActionEnum(WmsLogActionEnum.WMS_COMMON_SHIP_SCAN)
                    .build();
            wmsOutboundWorkLogService.operateLogWithConsistency(operationLogParam);
        } catch (WmsOperationException | WmsException e) {
            throw e;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new WmsException("发货失败");
        } catch (Exception e) {
            log.error("发货失败", e);
            throw new WmsException("发货失败");
        } finally {
            if (useLock && lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SHIP_EXECUTE_LOCK, referenceNo);
            }
        }
        return WmsResult.success(pojo.getSkuId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<TaskItemDetailResponse> executeShipping(String uniqueCode,Boolean checkBindAntiFakeCode) {
        TaskPojo taskPojo = getTaskPojo(uniqueCode);
        if (taskPojo == null) {
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
        }
        checkReferenceType(taskPojo.getTaskHeaderPojo().getReferenceType());
        Boolean needBindAndCode = checkBindAntiFakeCode && needBindAntiFakeCode(uniqueCode,taskPojo.getTaskHeaderPojo().getReferenceNo());
        Result<String> result = null;
        if(!needBindAndCode){
            result = executeShipping(taskPojo.getTaskHeaderPojo().getReferenceNo(), uniqueCode, true);

            // 记录操作日志
            OperationUserContext operationUserContext = OperationUserContextHolder.get();
            if (null != operationUserContext) {
                OutboundOperateLogRequest logRequest = buildOutboundOperationLogRequest(uniqueCode, operationUserContext);
                notifyPinkService.notifyPinkOperationLogByUniqueCode(logRequest);
            }
        }
        if (needBindAndCode || result.getCode() == Result.SUCCESS_CODE) {
            TaskItemDetailResponse response = new TaskItemDetailResponse();
            if (CollectionUtils.isNotEmpty(taskPojo.getTaskDetailPojoList()) && taskPojo.getTaskDetailPojoList().size() == 1) {
                BeanUtils.copyProperties(taskPojo.getTaskDetailPojoList().get(0), response);
            } else {
                log.error("数据异常：{}", JSONUtil.toJsonStr(taskPojo));
                throw new WmsException(WmsExceptionCode.DATA_ERROR);
            }
            String qualityLevelName = sysDictService.getDictName(OperationUserContextHolder.get().getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL, response.getQualityLevel());
            response.setQualityLevel(qualityLevelName);
            response.setOwnerName(scpMerchantService.getNameByCode(response.getOwnerCode()));
            response.setNeedBindAntiFakeCode(needBindAndCode);
            response.setDeliveryOrderCode(taskPojo.getTaskHeaderPojo().getReferenceNo());
            //构建标签信息
            buildOperateItemInfo(response,taskPojo.getTaskHeaderPojo().getReferenceType());
            return WmsResult.success(getCommodityAppendix(response));
        } else {
            return WmsResult.error(result.getMsg(), result.getCode());
        }

    }

    /**
     * 构建操作日志报文
     * @param uniqueCode
     * @param operationUserContext
     * @return
     */
    private OutboundOperateLogRequest buildOutboundOperationLogRequest(String uniqueCode, OperationUserContext operationUserContext) {
        OutboundOperateLogRequest logRequest = new OutboundOperateLogRequest();
        logRequest.setUniqueCode(uniqueCode);
        logRequest.setOperateType(NotifyPinkOperationTypeEnum.SHIP_TASK.getPinkType());
        logRequest.setOperateId(operationUserContext.getUserId());
        logRequest.setOperateName(operationUserContext.getRealName());
        logRequest.setWarehouseCode(operationUserContext.getWarehouseCode());
        logRequest.setTenantCode(operationUserContext.getTenantCode());
        logRequest.setOperateTimeDate(new Date());
        return logRequest;
    }

    /**
     * 构建标签信息
     * @param response
     */
    private void buildOperateItemInfo(TaskItemDetailResponse response,String referenceType) {
        if (StringUtils.isNotBlank(response.getUniqueCode()) && WmsOutBoundTypeEnum.JYCK.getType().equals(referenceType)) {
            Map<String, List<String>> itemInfoMap = operateItemInfoRepository.getInfo(Sets.newHashSet(response.getUniqueCode()));
            List<String> itemInfoList = itemInfoMap.get(response.getUniqueCode());
            if (CollectionUtils.isNotEmpty(itemInfoList)) {
                response.setOperateItemInfoResps(itemInfoList);
            }
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskItemDetailResponse executeShipByTaskDetail(TaskDetailDo detailDo) {
        BaseTaskDetailPojo detailPojo = new BaseTaskDetailPojo();
        BeanUtils.copyProperties(detailDo, detailPojo);
        DeliveryHandoverHeaderDo headerDo = addHandoverHeader();
        DeliveryHandoverDetailDo handoverDetail = addHandoverDetail(detailPojo, headerDo, NumberUtils.INTEGER_ONE);
        executeShipping(detailPojo.getReferenceNo(), detailPojo, 1, false);

        TaskItemDetailResponse response = new TaskItemDetailResponse();
        BeanUtils.copyProperties(detailPojo, response);
        String qualityLevelName = sysDictService.getDictName(OperationUserContextHolder.get().getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL, response.getQualityLevel());
        response.setQualityLevel(qualityLevelName);
        response.setOwnerName(scpMerchantService.getNameByCode(response.getOwnerCode()));
        response.setExtraCode(handoverDetail.getExtraCode());
        response.setStandardCode(handoverDetail.getStandardCode());
        response.setDeliveryOrderCode(handoverDetail.getDeliveryOrderCode());
        return getCommodityAppendix(response);
    }


    /**
     * 构建交接单商品信息
     *
     * @return
     */
    @Override
    public BarcodeShipResponse buildHandoverDetailInfos() {
        List<HandoverDetailResponse> list = new ArrayList<>();
        List<DeliveryHandoverDetailDo> result = deliveryHandoverDetailService.selectByUserIdAndStatus(OperationUserContextHolder.get().getUserId(), HandoverStatusEnums.PROCESS);
        if (CollectionUtils.isNotEmpty(result)) {
            Set<String> ownerCodes = new HashSet<>();
            Map<String, List<HandoverDetailResponse>> skuMaps = new HashMap<>();
            result.forEach(e -> {
                HandoverDetailResponse detailResponse = new HandoverDetailResponse();
                detailResponse.setHandoverNo(e.getHandoverNo());
                detailResponse.setDeliveryOrderCode(e.getDeliveryOrderCode());
                detailResponse.setExtraCode(e.getExtraCode());
                detailResponse.setGoodsArticleNumber(e.getGoodsArticleNumber());
                detailResponse.setGoodsTitle(e.getGoodsTitle());
                detailResponse.setOperationQty(e.getOperationQty());
                detailResponse.setSkuId(e.getSkuId());
                detailResponse.setSpecs(e.getSpecs());
                detailResponse.setStandardCode(e.getStandardCode());
                detailResponse.setUniqueCode(e.getUniqueCode());
                detailResponse.setWarehouseCode(e.getWarehouseCode());
                DeliveryDetailEntity deliveryDetailInfo = deliveryOrderQueryService.queryDeliveryDetailByDetailNo(e.getDeliveryDetailCode());
                String qualityLevelName = sysDictService.getDictName(OperationUserContextHolder.get().getWarehouseCode(), DictTypeEnum.QUALITY_LEVEL, e.getQualityLevel());
                detailResponse.setQualityLevelDesc(qualityLevelName);
                detailResponse.setOwnerCode(deliveryDetailInfo.getOwnerCode());
                ownerCodes.add(detailResponse.getOwnerCode());
                if (skuMaps.containsKey(detailResponse.getSkuId())) {
                    skuMaps.get(detailResponse.getSkuId()).add(detailResponse);
                } else {
                    List<HandoverDetailResponse> responses = new ArrayList<>();
                    responses.add(detailResponse);
                    skuMaps.put(detailResponse.getSkuId(), responses);
                }

            });
            List<SkuCommonRspDomain> commodityInfos = iCommodityQueryV2Service.querySkuCommonInfoListBySkuIds(OperationUserContextHolder.getTenantCode(), skuMaps.keySet());

            Map<String, SkuCommonRspDomain> commodityMaps = commodityInfos.stream().collect(Collectors.toMap(SkuCommonRspDomain::getSkuId, Function.identity()));

            skuMaps.forEach((key, value) -> {
                SkuCommonRspDomain skuCommonRspDomain = commodityMaps.get(key);
                if (skuCommonRspDomain != null) {

                    value.forEach(e1 -> {
                        try {
                            List<CommodityExtendResponse> extList = JSON.parseArray(skuCommonRspDomain.getAnnex(), CommodityExtendResponse.class);
                            if (CollectionUtils.isNotEmpty(extList)) {
                                e1.setExtendsList(extList);
                                StringBuffer stringBuffer = new StringBuffer();
                                extList.forEach(e -> {
                                    if (StringUtils.isNotBlank(e.getLabel())) {
                                        stringBuffer.append(",").append(e.getLabel());
                                    }
                                });
                                if (stringBuffer.length() > 0) {
                                    e1.setLables(stringBuffer.toString().substring(1));
                                }
                                e1.setGoodsPic(GetPropertyHelper.getString(skuCommonRspDomain::getSkuLogoUrl));
                            }
                        } catch (Exception e) {
                            log.warn("商品信息转换错误,info:{}", skuCommonRspDomain.getAnnex(), e);
                        }
                    });
                }
                list.addAll(value);
            });
            Map<String, String> ownerCodesMap = scpMerchantService.batchQueryName(ownerCodes);
            list.forEach(e -> {
                e.setOwnerCodeDesc(MapUtils.getString(ownerCodesMap, e.getOwnerCode(), null));
            });
        }
        BarcodeShipResponse response = new BarcodeShipResponse();
        response.setCommitHandover(1);
        if (CollectionUtils.isNotEmpty(list)) {
            response.setHandoverDetailResponses(list);
        }
        return response;
    }

    private DeliveryHandoverHeaderDo addHandoverHeader() {
        return deliveryHandoverHeaderService.add(HandoverStatusEnums.PROCESS);
    }

    private DeliveryHandoverDetailDo addHandoverDetail(BaseTaskDetailPojo baseTaskDetailPojo, DeliveryHandoverHeaderDo headerDo, int operationQty) {
        DeliveryHandoverDetailDo detailDo = buildDeliveryHandoverDetailDo(baseTaskDetailPojo, headerDo, operationQty);
        deliveryHandoverDetailService.add(detailDo);
        return detailDo;
    }

    private DeliveryHandoverDetailDo buildDeliveryHandoverDetailDo(BaseTaskDetailPojo baseTaskDetailPojo, DeliveryHandoverHeaderDo headerDo, int operationQty) {
        SkuCommonRspDomain commodityInfo = iCommodityQueryV2Service.querySkuCommonInfoBySkuId(OperationUserContextHolder.getTenantCode(), baseTaskDetailPojo.getSkuId());
        DeliveryHandoverDetailDo detailDo = new DeliveryHandoverDetailDo();
        detailDo.setHandoverNo(headerDo.getHandoverNo());
        detailDo.setDeliveryOrderCode(baseTaskDetailPojo.getReferenceNo());
        detailDo.setExtraCode(Optional.ofNullable(commodityInfo.getExtraCode()).orElse(""));
        detailDo.setDeliveryOrderCode(baseTaskDetailPojo.getReferenceNo());
        detailDo.setGoodsArticleNumber(Optional.ofNullable(commodityInfo.getArtNoMain()).orElse(""));
        detailDo.setGoodsTitle(Optional.ofNullable(baseTaskDetailPojo.getGoodsTitle()).orElse(""));
        detailDo.setHandoverDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.HANDOVER_DETAIL_NO.getSequenceType()));
        detailDo.setOperationQty(operationQty);
        detailDo.setSkuId(baseTaskDetailPojo.getSkuId());
        detailDo.setSpecs(Optional.ofNullable(baseTaskDetailPojo.getSpecs()).orElse(""));
        detailDo.setStandardCode(Optional.ofNullable(commodityInfo.getStandCode()).orElse(""));
        detailDo.setWaybillNo(headerDo.getWaybillNo());
        detailDo.setDeliveryDetailCode(baseTaskDetailPojo.getReferenceDetailNo());
        detailDo.setInventoryNo(baseTaskDetailPojo.getInventoryNo());
        detailDo.setQualityLevel(baseTaskDetailPojo.getQualityLevel());
        detailDo.setUniqueCode(Optional.ofNullable(baseTaskDetailPojo.getUniqueCode()).orElse(""));
        detailDo.setWarehouseCode(baseTaskDetailPojo.getWarehouseCode());
        detailDo.setTenantId(baseTaskDetailPojo.getTenantCode());

        OperationUserContext context = OperationUserContextHolder.get();
        detailDo.setCreatedRealName(context.getRealName());
        Date currentDate = new Date();
        detailDo.setCreatedTime(currentDate);
        detailDo.setCreatedUserId(context.getUserId());
        detailDo.setCreatedUserName(context.getUserName());
        detailDo.setUpdatedRealName(context.getRealName());
        detailDo.setUpdatedTime(currentDate);
        detailDo.setUpdatedUserId(context.getUserId());
        detailDo.setUpdatedUserName(context.getUserName());

        return detailDo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void finishByReferenceNo(@NotNull String referenceNo) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(referenceNo);
        if (deliveryHeaderDo == null) {
            throw new WmsOperationException(WmsExceptionCode.NO_DATA);
        }
        if (!wmsOutboundBusinessUtils.forbidBatchShip(deliveryHeaderDo.getType(),
                deliveryHeaderDo.getBizType(), deliveryHeaderDo.getWarehouseCode())) {
            throw new WmsOperationException("不支持强制发货");
        }
        BaseTaskPojo queryPojo = new BaseTaskPojo();
        queryPojo.setReferenceNo(referenceNo);
        queryPojo.setReferenceType(deliveryHeaderDo.getType());
        queryPojo.setType(TaskTypeEnum.SHIP.getTaskType());
        queryPojo.setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
        TaskHeaderPojo task = taskQueryService.getTaskHeader(queryPojo);
        boolean isFinish = true;
        if (WmsOutBoundStatusEnum.FORCE_PICKED.getStatus().equals(deliveryHeaderDo.getStatus())
                || WmsOutBoundStatusEnum.FAIL_ALLOCATE.getStatus().equals(deliveryHeaderDo.getStatus())) {
            if (task == null) {
                isFinish = false;
                DeliveryShipPojo modifyQtyPojo = new DeliveryShipPojo();
                DeliveryShipHeaderPojo headerQtyPojo = new DeliveryShipHeaderPojo();
                headerQtyPojo.setDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
                headerQtyPojo.setStatus(WmsOutBoundStatusEnum.FORCE_OUTED.getStatus());
                headerQtyPojo.setTotalActualQty(0);
                modifyQtyPojo.setDeliveryModifyHeader(headerQtyPojo);
                deliveryOrderModifyService.handleDeliveryOrder(modifyQtyPojo);
            }
        } else if (WmsOutBoundStatusEnum.PICKED.getStatus().equals(deliveryHeaderDo.getStatus())
                || WmsOutBoundStatusEnum.OUTING.getStatus().equals(deliveryHeaderDo.getStatus())) {
            if (task == null) {
                throw new WmsOperationException(WmsExceptionCode.NO_DATA);
            }
        } else {
            throw new WmsOperationException(WmsExceptionCode.BOUND_STATUS_ERROR);
        }
        if (isFinish) {
            if (!EXECUTING.getStatus().equals(task.getStatus()) && !INIT.getStatus().equals(task.getStatus())) {
                throw new WmsOperationException(WmsExceptionCode.BOUND_STATUS_SUCCESS);
            }
            ForceFinishTaskPojo finishTaskPojo = new ForceFinishTaskPojo();
            finishTaskPojo.setTaskStatusEnum(FORCE_FINISH);
            finishTaskPojo.setTaskTypeEnum(TaskTypeEnum.SHIP);
            finishTaskPojo.setReferenceNo(referenceNo);
            finishTaskPojo.setReferenceType(deliveryHeaderDo.getType());
            finishTaskPojo.setTaskNo(task.getTaskNo());
            taskFactory.process(finishTaskPojo);
        }
    }

    private TaskPojo getTaskPojo(String referenceNo, String detailNo) {
        return getTaskPojo(null, detailNo, referenceNo);
    }

    /**
     * 唯一码和任务明细编号可以二选一填
     *
     * @param uniqueCode
     * @return
     */
    @Override
    public TaskPojo getTaskPojo(String uniqueCode) {
        return getTaskPojo(uniqueCode, null, null);
    }

    private TaskPojo getTaskPojo(String uniqueCode, String detailNo, String referenceNo) {
        CodeQueryPojo pojo = new CodeQueryPojo();
        pojo.setUniqueCode(uniqueCode);
        pojo.setReferenceNo(referenceNo);
        pojo.setDetailNo(detailNo);
        pojo.setType(TaskTypeEnum.SHIP.getTaskType());
        pojo.setStatus(NumberUtils.INTEGER_ONE);
        OperationUserContext context = OperationUserContextHolder.get();
        pojo.setWarehouseCode(context.getWarehouseCode());
        List<TaskPojo> taskPojoList = taskQueryService.findTasks(pojo);
        if (CollectionUtils.isEmpty(taskPojoList)) {
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
        }
        if (taskPojoList.size() > 1) {
            throw new WmsException(WmsExceptionCode.TASK_SYSTEM_ERROR);
        }
        return taskPojoList.get(0);
    }

    private TaskPojo getShipTask(String uniqueCode, String referenceNo) {
        CodeQueryPojo pojo = new CodeQueryPojo();
        pojo.setUniqueCode(uniqueCode);
        pojo.setReferenceNo(referenceNo);
        pojo.setType(TaskTypeEnum.SHIP.getTaskType());
        pojo.setStatus(NumberUtils.INTEGER_ZERO);
        OperationUserContext context = OperationUserContextHolder.get();
        pojo.setWarehouseCode(context.getWarehouseCode());
        List<TaskPojo> taskPojoList = taskQueryService.findTasks(pojo);
        if (CollectionUtils.isEmpty(taskPojoList)) {
            throw new WmsOperationException(WmsExceptionCode.TASK_IS_EMPTY);
        }
        if (taskPojoList.size() > 1) {
            throw new WmsException(WmsExceptionCode.TASK_SYSTEM_ERROR);
        }
        TaskPojo taskPojo = taskPojoList.get(0);
        if (finishList.contains(taskPojo.getTaskHeaderPojo().getStatus())) {
            throw new WmsOperationException(WmsExceptionCode.SHIP_FINISHED_ERROR);
        }
        return taskPojo;
    }


    private void checkTaskStatus(BaseTaskDetailPojo detailPojo) {
        Integer status = detailPojo.getStatus();
        if (!(status.equals(EXECUTING.getStatus())
                || status.equals(INIT.getStatus()))) {
            throw new WmsOperationException(WmsExceptionCode.BOUND_DETAIL_STATUS_SUCCESS);
        }
    }

    private TaskItemDetailResponse getCommodityAppendix(TaskItemDetailResponse response) {
        taskHandlerService.buildCommodityAppendix(response);
        return response;
    }

    /**
     * 按商品发货校验出库单类型为交易出库
     *
     * @param referenceType
     */
    private void checkReferenceType(String referenceType) {
        //按商品发货校验出库单类型为交易出库、客退寄存出库
        if (!WmsOutBoundTypeEnum.STORAGE_TYPE_LIST.contains(referenceType)) {
            throw new WmsOperationException(WmsExceptionCode.BOUND_TYPE_ERROR);
        }
    }

    private boolean needBindAntiFakeCode(String uniqueCode, String deliveryOrderCode){
        boolean hitGray = greyRepository.isHitWmsShippingBindMesAntiFakeCode();
        if (!hitGray) {
            return  false;
        }
        boolean ifNeedBindAntiFakeCode = deliveryOrderCommonService.ifNeedBindAntiFakeCode(deliveryOrderCode, uniqueCode, OperationUserContextHolder.getTenantCode());
        if(!ifNeedBindAntiFakeCode) {
            return false;
        }
        List<ShipAntiFakeDo> antiFakeDos = shipAntiFakeQueryRepository.queryByUniqueCode(deliveryOrderCode,uniqueCode);
        if (CollectionUtils.isNotEmpty(antiFakeDos)){
            return false;
        }
        return true;
    }

    private void addLog(String referenceNo, String detailNo, String deliveryType, String taskNo, String uniqueCode, String warehouseCode) {
        OperateLogPojo logPojo = OperateLogPojo.builder()
                .operateTypeEnum(OperateTypeEnum.SHIPPING_TASK_EXECUTOR).referenceNo(referenceNo)
                .qty(1).referenceType(deliveryType)
                .taskDetailNo(detailNo)
                .taskNo(taskNo)
                .uniqueCode(uniqueCode)
                .warehouseCode(warehouseCode).build();
        operateLogService.addLog(logPojo);
    }

    private BaseTaskPojo execute(String taskNo, String taskDetailNo, String uniqueCode, String inventoryNo, TaskStatusEnum statusEnum, int operationQty) {
        OperationUserContext context = OperationUserContextHolder.get();
        ExecuteTaskPojo operationPojo = new ExecuteTaskPojo();
        operationPojo.setTaskStatusEnum(statusEnum);
        operationPojo.setTaskTypeEnum(TaskTypeEnum.SHIP);
        BaseExecuteDetailPojo detailPojo = new BaseExecuteDetailPojo();
        detailPojo.setUniqueCode(uniqueCode);
        detailPojo.setTaskNo(taskNo);
        detailPojo.setDetailNo(taskDetailNo);
        detailPojo.setWarehouseCode(context.getWarehouseCode());
        detailPojo.setOperationQty(operationQty);
        detailPojo.setInventoryNo(inventoryNo);
        operationPojo.setExecuteDetailPojo(detailPojo);
        BaseTaskPojo result = taskFactory.process(operationPojo);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public NotifyOfcDeductSciRequest createTaskAndShip(TaskReceiveRequest receiveRequest) {
        String warehouseCode = receiveRequest.getTaskDetails().get(0).getWarehouseCode();
        String referenceNo = receiveRequest.getOrderCode();
        // 幂等在createShipTask内部
        List<ShipTaskDetailDo> shipDetailList = this.createShipTask(receiveRequest);
        // 查询库存信息，先查，发货可能库存就删掉了
        List<String> invNoList = shipDetailList.stream().map(ShipTaskDetailDo::getInventoryNo).collect(Collectors.toList());
        Map<String, InventoryDo> inventoryMap = inventoryRepository.queryInventoryDoMap(OperationUserContextHolder.getTenantCode(), invNoList, warehouseCode);
        inventoryMap = inventoryMap != null ? inventoryMap : Maps.newHashMap();
        // 然后循环发货
        shipDetailList.forEach(shipTaskDetail -> {
            TaskDetailNoShipRequest shiParam = new TaskDetailNoShipRequest();
            try {
                shiParam.setTaskDetailNo(shipTaskDetail.getDetailNo());
                shiParam.setDeliveryOrderNo(referenceNo);
                shiParam.setOperationQty(shipTaskDetail.getQty());
                this.executeShippingByTaskDetailNoV2(shiParam);
                /*处理出箱*/
                takeOutFromContainerIfNecessary(shipTaskDetail);
                /*发送发货完成消息*/
                sendOutShipMessage(shipTaskDetail);
            } catch (Exception e) {
                log.error("pinpai ship task error, param is [{}]", JSON.toJSONString(shiParam), e);
                throw e;
            }
        });
        List<String> deliveryDetailNos = shipDetailList.stream().map(ShipTaskDetailDo::getReferenceDetailNo).collect(Collectors.toList());
        List<DeliveryDetailDo> deliveryDetailList = deliveryDetailRepository.listByDetailNoList(deliveryDetailNos);
        // 组装消息对象返回
        return this.buildNotifyOfcDeductSciRequest(shipDetailList, deliveryDetailList, inventoryMap);
    }

    /**
     * 发送发货完成的消息
     *
     * @param shipTaskDetail
     */
    private void sendOutShipMessage(ShipTaskDetailDo shipTaskDetail) {
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(shipTaskDetail.getReferenceNo(),shipTaskDetail.getTenantCode());
        if(Objects.equals(WmsOutBoundTypeEnum.HBFHCK.getType(),deliveryHeader.getType())){
            //增加合并发货报表特殊逻辑处理
            OutboundOrderEvent outboundOrderEvent = new OutboundOrderEvent();
            outboundOrderEvent.setTenantId(deliveryHeader.getTenantCode());
            outboundOrderEvent.setShipmentOrderNo(deliveryHeader.getDeliveryOrderCode());
            outboundOrderEvent.setOperationTime(shipTaskDetail.getUpdatedTime());
            outboundOrderEvent.setOperationUserId(shipTaskDetail.getUpdatedUserId());
            outboundOrderEvent.setOperationUserName(shipTaskDetail.getUpdatedRealName());
            outboundOrderEvent.setShipmentDetailNos(Lists.newArrayList(shipTaskDetail.getReferenceDetailNo()));
            outboundOrderEvent.setEventType(OutboundEventEnum.SHIPED_EVENT.name());
            outboundMessageProducer.sendShipmentOrderEvent(outboundOrderEvent);
        }
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository.queryDeliveryDetailByDeliveryCode(shipTaskDetail.getReferenceNo(),shipTaskDetail.getTenantCode());
        if(CollectionUtils.isEmpty(deliveryDetailDos)){
            log.warn("发货单明细为空，不发送事件{}",shipTaskDetail.getReferenceNo());
            return;
        }
    }

    /**
     * 箱明细出箱
     *
     * @param shipTaskDetail
     */
    private void takeOutFromContainerIfNecessary(ShipTaskDetailDo shipTaskDetail) {
        log.info("执行发货任务明细出箱:{}",JsonUtils.serialize(shipTaskDetail));
        shipTakeOutContainerService.takeOutContainerIfNeed(shipTaskDetail);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String batchShipByTaskNo(String taskNo) {
        ShipTaskDo taskDo = shipTaskQueryRepository.findByTaskNo(taskNo, null, OperationUserContextHolder.getTenantCode());
        if (taskDo == null) {
            throw new WmsException(WmsExceptionCode.TASK_IS_EMPTY);
        }
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(taskDo.getReferenceNo(), taskDo.getTenantCode());
        if (deliveryHeaderDo == null) {
            throw new WmsException(WmsExceptionCode.DELIVERY_NOT_EXIST);
        }

        if (YesOrNoEnum.YES.getCode().equals(deliveryHeaderDo.getContainsSnFlag())) {
            throw new WmsException("仓储托管出库单不支持批量发货");
        }

        if(CollectionUtils.isNotEmpty(blackWarehouseCodes) && blackWarehouseCodes.contains(taskDo.getWarehouseCode())){
            throw new WmsException("该仓库暂不支持批量发货功能");
        }
        if(WmsOutBoundTypeEnum.REFOUND_CENTER_OUT_TYPE_LIST.contains(taskDo.getReferenceType())){
            throw new WmsException("该单据不支持批量发货，请前往wms发货新进行发货");
        }
        if (!unCompleteList.contains(taskDo.getStatus())) {
            throw new WmsOperationException(WmsExceptionCode.TASK_SUCCESS);
        }
        if (taskDo.getReferenceType().equals(WmsOutBoundTypeEnum.KYQDBCK.getCode())) {
            CompletableFuture.runAsync(() -> executeKyqdbShip(taskDo));
            return KYQDB_SUCCESS_MESSAGE;
        }
        List<ShipTaskDetailDo> details = shipTaskDetailQueryRepository.queryByTaskNo(taskNo, taskDo.getWarehouseCode(), taskDo.getTenantCode());
        if (CollectionUtils.isEmpty(details)) {
            throw new WmsOperationException(WmsExceptionCode.TASK_SUCCESS);
        }
        //未完成的发货明细数量
        long count = details.stream().filter(e -> unCompleteList.contains(e.getStatus().intValue())).count();
        //3 执行任务明细更新，并判断更新的任务明细数量是否一致
        updateTaskDetail(taskNo, count);
        //4 更新发货任务头信息
        updateTaskHead(taskDo);
        //5保存任务执行结果
        saveTaskResult(details);
        //6 更新单据头和明细信息
        updateOrderInfo(details, taskDo);
        // 添加交接单(根据条件)
        DeliveryHandoverHeaderDo handoverHeader = addDeliveryHandoverInfo(details, taskDo);
        // 记录操作日志
        addLog(taskDo.getReferenceNo(), null, taskDo.getReferenceType(), taskNo, null, taskDo.getWarehouseCode());
        //11 异步执行库存操作
        BatchShipContext context = new BatchShipContext();
        context.setTaskNo(taskNo);
        if (handoverHeader != null) {
            context.setWaybillNo(handoverHeader.getWaybillNo());
        }
        //小仓库和商研 && 交易出库同步调用ERP
        boolean needCancel = false;
        if (WmsBizTypeEnum.SB_BIZ_TYPE.contains(details.get(0).getBizType().intValue()) && WmsOutBoundTypeEnum.JYCK.getType().equals(taskDo.getReferenceType())) {
            Result<Boolean> erpResult = callErp(taskDo, details.get(0), Optional.ofNullable(handoverHeader).map(DeliveryHandoverHeaderDo::getWaybillNo).orElse(null));
            //交易取消
            needCancel = erpResult.getData() == null || !erpResult.getData();
        }
        //订单已取消, 注册任务取消事件
        if (needCancel) {
            registerCancelTaskEvent(taskDo);
            throw new WmsException("单据已取消,请返架");
        }
        batchShipAsyncConsistencyExecute.execute(context);
        return DEFAULT_SUCCESS_MESSAGE;
        //11-1 循环扣件库存
        //11-3 插入原始订单信息
        //11-3 循环单个发送发货通知外部系统
        //11-4 循环将操作日志发送给pink
    }

    private void executeKyqdbShip(ShipTaskDo taskDo) {
        OperationUserContextHolder.get().setUserId(SYSTEM_USER_ID);
        OperationUserContextHolder.get().setWarehouseCode(taskDo.getWarehouseCode());
        //过滤
        boolean lockFlag = false;
        try {
            if (!distributeLockUtil.tryLockForBiz(LockEnum.SHIP_TASK_BATCH_EXECUTE_LOCK, taskDo.getTaskNo())) {
                throw new WmsOperationException(WmsExceptionCode.REPEAT_OPERATION);
            }
            lockFlag = true;
            List<ShipTaskDetailDo> shipTaskDetailDos = shipTaskDetailQueryRepository.queryByTaskNo(taskDo.getTaskNo(),
                    taskDo.getWarehouseCode(), taskDo.getTenantCode());
            for (ShipTaskDetailDo shipTaskDetailDo : shipTaskDetailDos) {
                if (COMPLETE.getStatus().byteValue() == shipTaskDetailDo.getStatus()) {
                    continue;
                }
                shippingService.executeShippingByTaskDetailNoV2(buildRequest(shipTaskDetailDo));
                Thread.sleep(200);
            }
        } catch (WmsOperationException ex) {
            throw ex;
        } catch (InterruptedException ex) {
            log.warn("Interrupted!", ex);
            Thread.currentThread().interrupt();
        } finally {
            OperationUserContextHolder.clear();
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SHIP_TASK_BATCH_EXECUTE_LOCK, taskDo.getTaskNo());
            }
        }
    }

    private TaskDetailNoShipRequest buildRequest(ShipTaskDetailDo shipTaskDetailDo) {
        TaskDetailNoShipRequest request = new TaskDetailNoShipRequest();
        request.setDeliveryOrderNo(shipTaskDetailDo.getReferenceNo());
        request.setTaskDetailNo(shipTaskDetailDo.getDetailNo());
        request.setOperationQty(shipTaskDetailDo.getQty()-shipTaskDetailDo.getOperationQty());
        return request;
    }

    private void registerCancelTaskEvent(ShipTaskDo taskDo) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCompletion(int status) {
                log.info("执行发货任务取消动作");
                if (status != TransactionSynchronization.STATUS_COMMITTED) {
                    shippingCancelService.executeCancel(null, taskDo.getReferenceNo(), taskDo.getReferenceType());
                }
            }
        });
    }

    private Result<Boolean> callErp(ShipTaskDo taskDo, ShipTaskDetailDo detailDo, String expressNo) {
        Result<Boolean> erpResult;
        PlatformTradeOrderDeliverRequest param = buildErpParam(taskDo, detailDo, expressNo);
        try {
            erpResult = orderApi.tradeOrderDeliver(param);
        } catch (Exception ex) {
            log.warn("交易出库同步调用ERP失败：请求参数:{}", JSON.toJSONString(param), ex);
            throw new WmsException("交易出库同步调用ERP失败");
        }
        if (!Result.SUCCESS_CODE.equals(erpResult.getCode())) {
            log.warn("交易出库同步调用ERP异常, 请求参数:{}, code:{}, message:{}", JSON.toJSONString(param), erpResult.getCode(), erpResult.getMsg());
            throw new WmsException("交易出库同步调用ERP异常：" + erpResult.getMsg());
        }
        return erpResult;

    }

    private PlatformTradeOrderDeliverRequest buildErpParam(ShipTaskDo taskDo, ShipTaskDetailDo detailDo, String expressNo) {
        List<DeliveryRelatedOrdersEntity> relatedOrdersList = deliveryOrderQueryService.queryDeliveryRelatedOrderByOrderCode(taskDo.getReferenceNo());
        String documentNo = relatedOrdersList.get(0).getRelatedOrderCode();
        PlatformTradeOrderDeliverRequest param = new PlatformTradeOrderDeliverRequest();
        param.setWarehouse(taskDo.getWarehouseCode());
        param.setDeliverTime(new Date());
        param.setDocumentNo(documentNo);
        param.setExpressNo(expressNo);
        param.setRemark("");
        param.setQuantity(detailDo.getOperationQty());
        param.setTradeOrderNo(taskDo.getReferenceNo());
        param.setUniqueCode(detailDo.getUniqueCode());
        param.setQualityLevel(Integer.valueOf(detailDo.getQualityLevel()));
        param.setRelationNo(detailDo.getOriginalOrderCode());
        return param;
    }


    /**
     * 保存任务执行结果
     *
     * @param detailDos
     */
    private void saveTaskResult(List<ShipTaskDetailDo> detailDos) {
        List<ShipTaskDetailDo> list = detailDos.stream().filter(e -> unCompleteList.contains(e.getStatus().intValue())).collect(Collectors.toList());
        OperationUserContext operationUserContext = OperationUserContextHolder.get();
        List<ShipTaskDetailResultDo> resultDos = new ArrayList<>(detailDos.size());
        for (ShipTaskDetailDo detailDo : list) {
            ShipTaskDetailResultDo detailResultDo = new ShipTaskDetailResultDo();
            detailResultDo.setDeleted(NumberUtils.INTEGER_ZERO);
            detailResultDo.setTaskNo(detailDo.getTaskNo());
            if (org.apache.commons.lang3.StringUtils.isBlank(detailDo.getTaskType()) || null == TaskTypeEnum.getTaskType(detailDo.getTaskType())) {
                throw new WmsException(WmsExceptionCode.TASK_TYPE_IS_EMPTY);
            }
            detailResultDo.setTaskType(detailDo.getTaskType());
            detailResultDo.setTaskDetailNo(detailDo.getDetailNo());

            // 商品基础信息
            detailResultDo.setBarcode(detailDo.getBarcode());
            detailResultDo.setUniqueCode(detailDo.getUniqueCode());
            detailResultDo.setSkuId(detailDo.getSkuId());
            detailResultDo.setQualityLevel(detailDo.getQualityLevel());
            detailResultDo.setOwnerCode(detailDo.getOwnerCode());
            detailResultDo.setVendorCode(detailDo.getVendorCode());
            detailResultDo.setTenantCode(detailDo.getTenantCode());
            detailResultDo.setWarehouseCode(detailDo.getWarehouseCode());

            detailResultDo.setInventoryNo(detailDo.getInventoryNo());

            //原始单号&效期
            detailResultDo.setMfgTime(detailDo.getMfgTime());
            detailResultDo.setExpTime(detailDo.getExpTime());
            detailResultDo.setOriginalOrderCode(detailDo.getOriginalOrderCode());

            /*第一次执行*/
            String taskDetailResultNo = IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL_RESULT.getSequenceType());
            detailResultDo.setResultNo(taskDetailResultNo);
            detailResultDo.setOperationQty(detailDo.getQty() - Optional.ofNullable(detailDo.getOperationQty()).orElse(0));

            // 操作人信息
            Date currentDate = new Date();
            detailResultDo.setVersion(0);
            detailResultDo.setCreatedTime(currentDate);
            detailResultDo.setUpdatedTime(currentDate);
            detailResultDo.setCreatedUserId(operationUserContext.getUserId());
            detailResultDo.setCreatedUserName(operationUserContext.getUserName());
            detailResultDo.setCreatedRealName(operationUserContext.getRealName());
            detailResultDo.setUpdatedUserId(operationUserContext.getUserId());
            detailResultDo.setUpdatedUserName(operationUserContext.getUserName());
            detailResultDo.setUpdatedRealName(operationUserContext.getRealName());
            detailResultDo.setOperationUserId(operationUserContext.getUserId());
            detailResultDo.setOperationUserName(operationUserContext.getUserName());
            detailResultDo.setOperationRealName(operationUserContext.getRealName());
            resultDos.add(detailResultDo);
        }
        shipTaskDetailResultCommandRepository.batchSave(resultDos);
    }

    /**
     * 添加交接单信息
     *
     * @param taskDetailDos
     */
    private DeliveryHandoverHeaderDo addDeliveryHandoverInfo(List<ShipTaskDetailDo> taskDetailDos, ShipTaskDo taskDo) {
        if (BizTypeEnum.ZI_YING_PU_HUO.getBizType().equals(taskDetailDos.get(0).getBizType().intValue())
                && taskDo.getReferenceType().equals(WmsOutBoundTypeEnum.JYCK.getType())) {
            DeliveryHandoverHeaderDo headerDo = deliveryHandoverHeaderService.addCompletedHandoverHeader(taskDo.getWarehouseCode());
            List<DeliveryHandoverDetailDo> handoverDetailDos = new ArrayList<>();
            for (ShipTaskDetailDo detailDo : taskDetailDos) {
                if (unCompleteList.contains(detailDo.getStatus().intValue())) {
                    BaseTaskDetailPojo detailPojo = new BaseTaskDetailPojo();
                    BeanUtils.copyProperties(detailDo, detailPojo);
                    DeliveryHandoverDetailDo handoverDetail = buildDeliveryHandoverDetailDo(detailPojo, headerDo, detailDo.getQty() - Optional.ofNullable(detailDo.getOperationQty()).orElse(0));
                    handoverDetailDos.add(handoverDetail);
                }
            }
            deliveryHandoverDetailService.batchInsert(handoverDetailDos);
            return headerDo;

        }
        return null;
    }

    /**
     * 更新单据头和明细信息
     *
     * @param taskDetailDos
     * @param taskDo
     */
    private void updateOrderInfo(List<ShipTaskDetailDo> taskDetailDos, ShipTaskDo taskDo) {
        int headerTotalQty = 0;
        Map<String, Integer> detailTotalQtyMap = new HashMap<>();
        //实际出库数量求和
        for (ShipTaskDetailDo taskDetailDo : taskDetailDos) {
            headerTotalQty += taskDetailDo.getQty();
            Integer detailQty = MapUtils.getInteger(detailTotalQtyMap, taskDetailDo.getReferenceDetailNo(), 0);
            detailQty += taskDetailDo.getQty();
            detailTotalQtyMap.put(taskDetailDo.getReferenceDetailNo(), detailQty);
        }
        //更新单据头
        int count = deliveryHeaderRepository.updateOutedStatus(taskDo.getReferenceNo(), headerTotalQty);
        if (count != NumberUtils.INTEGER_ONE) {
            log.error("更新单据状态异常referenceNo:{}", taskDo.getReferenceNo());
            throw new WmsException(WmsExceptionCode.BOUND_STATUS_SUCCESS);
        }
        //更新单据明细
        detailTotalQtyMap.forEach((key, value) -> {
            int num = deliveryDetailRepository.updateActualQtyByDetailNo(key, value);
            if (num == 0) {
                log.error("更新单据明细异常referenceDetailNo:{},value:{}", key, value);
                throw new WmsException(WmsExceptionCode.BOUND_STATUS_SUCCESS);
            }
        });
        //更新内部单据
        if (WmsOutBoundTypeEnum.INSIDE_LIST.contains(taskDo.getReferenceType())) {
            oubApplyService.finish(taskDo.getReferenceNo());
        }
        deliveryOrderCommonService.saveDeliveryStatusRecord(
                taskDo.getReferenceNo(),
                WmsOutBoundStatusEnum.OUTED.getStatus(),
                taskDo.getWarehouseCode());

    }

    /**
     * 更新任务头信息
     *
     * @param taskDo
     */
    private void updateTaskHead(ShipTaskDo taskDo) {
        ShipTaskDo update = new ShipTaskDo();
        OperationUserContext context = OperationUserContextHolder.get();
        update.setTaskNo(taskDo.getTaskNo());
        update.setType(taskDo.getType());
        update.setOperationUserId(context.getUserId());
        update.setOperationUserName(context.getUserName());
        update.setOperationRealName(context.getRealName());
        update.setUpdatedUserId(context.getUserId());
        update.setUpdatedUserName(context.getUserName());
        update.setUpdatedRealName(context.getRealName());
        update.setTenantCode(context.getTenantCode());
        update.setVersion(taskDo.getVersion());
        if (taskDo.getStatus().equals(INIT.getStatus())) {
            update.setStartTime(new Date());
        }
        update.setEndTime(new Date());
        update.setStatus(COMPLETE.getStatus());
        Integer count = shipTaskCommandRepository.completeTaskByTaskNo(update);
        if (!NumberUtils.INTEGER_ONE.equals(count)) {
            throw new WmsOperationException(WmsExceptionCode.TASK_STATUS_EXECUTE);
        }
    }

    /**
     * 更新任务明细
     */
    private void updateTaskDetail(String taskNo, long detailSize) {
        Integer count = shipTaskDetailCommandRepository.modifyDetailStatusByTaskNo(taskNo);
        if (count == null || count != detailSize) {
            throw new WmsOperationException(WmsExceptionCode.TASK_STATUS_EXECUTE);
        }
    }


    /**
     * 注意：该方法会生成所有明细的发货任务
     *
     * @param receiveRequest
     * @param containerInstances
     * @return
     */
    @Nullable
    private List<ShipTaskDetailDo> createShipTask(TaskReceiveRequest receiveRequest) {
        BaseTaskDetailRequest baseTaskDetailRequest = receiveRequest.getTaskDetails().get(0);
        String warehouseCode = baseTaskDetailRequest.getWarehouseCode();
        String referenceNo = receiveRequest.getOrderCode();
        List<ShipTaskDetailDo> shipTDList = null;
        boolean lockShipCreate = false;
        try {
            lockShipCreate = distributeLockUtil.tryLockForBizWithLeaseTime(LockEnum.SHIP_TASK_CREATE_V2_LOCK, referenceNo);
            Preconditions.checkBizV2(lockShipCreate, WmsExceptionCode.GET_LOCK_FAILED);
            List<ShipTaskDo> existTask = shipTaskQueryRepository.findByReferenceNo(referenceNo);
            if (CollectionUtils.isNotEmpty(existTask)) {
                // 判断是否已创建发货任务
                log.warn("发货消息重复消费！");
                Preconditions.checkBiz(INIT.getStatus().equals(existTask.get(0).getStatus()), WmsExceptionCode.SHIP_TASK_NOT_INIT);
                // 如果有，返回任务明细
                shipTDList = shipTaskDetailQueryRepository.queryByReferenceNo(referenceNo, warehouseCode, OperationUserContextHolder.getTenantCode());

                if (CollectionUtils.isNotEmpty(shipTDList)) {
                    // 过滤取消的明细
                    shipTDList = shipTDList.stream().filter(t -> !TaskDetailStatusEnum.CANCEL.getStatus().equals(Integer.valueOf(t.getStatus()))).collect(Collectors.toList());
                }

            } else {
                log.info("箱实例为空,将通过拣货任务明细生成单据{}的发货任务", referenceNo);
                shipTDList = createShipTaskByPickTaskDetail(baseTaskDetailRequest, referenceNo);
            }
        } catch (InterruptedException e) {
            log.warn("发货创建出库任务并发，抢锁失败，上游可能发送了重复消息！", e);
            Thread.currentThread().interrupt();
        } finally {
            if (lockShipCreate) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SHIP_TASK_CREATE_V2_LOCK, referenceNo);
            }
        }
        return shipTDList;
    }

    /**
     * 通过拣货明细结果生成发货任务
     *
     * @param baseTaskDetailRequest
     * @param referenceNo
     * @return
     */
    private List<ShipTaskDetailDo> createShipTaskByPickTaskDetail(BaseTaskDetailRequest baseTaskDetailRequest, String referenceNo) {
        // 收到发货消息，根据拣货任务，创建一套发货任务以及明细
        // 查询拣货任务明细，先查拣货明细再查头，因为拣货里面的referenceNo是批次号。因为一个单据号可能会有多个任务，这里只取成功的。
        List<PickTaskDetailDo> pickTDList = pickTaskDetailQueryRepository.findTaskByReferenceNosAndStatus(Collections.singletonList(referenceNo), finishList);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(pickTDList), WmsExceptionCode.PICK_TASK_DETAIL_NOT_FOUND);

        // 查询操作明细，从中获取location_code、inventory_no，qty要合并
        Set<String> detailNoSet = pickTDList.stream().map(PickTaskDetailDo::getDetailNo).collect(Collectors.toSet());
        List<PickTaskDetailResultDo> resultDoList = pickTaskDetailResultQueryRepository.queryByTaskDetailNoList(Lists.newArrayList(detailNoSet));

        // 创建发货任务头
        ShipTaskDo shipTask = this.buildTaskEntity(pickTDList);
        int thResult = shipTaskCommandRepository.save(shipTask);
        Preconditions.checkBiz(thResult == 1, WmsExceptionCode.TASK_HEADER_SAVE_FAIL);

        // 创建发货任务明细
        List<ShipTaskDetailDo> shipTDList = this.buildTaskDetailList(shipTask.getTaskNo(), pickTDList, resultDoList, baseTaskDetailRequest);
        int tdListResult = shipTaskDetailCommandRepository.batchSave(shipTDList);
        Preconditions.checkBiz(tdListResult > 0, WmsExceptionCode.TASK_DETAIL_SAVE_FAIL);
        return shipTDList;
    }

    private ShipTaskDo buildTaskEntity(List<PickTaskDetailDo> taskDetailDoList) {
        String flowCode = taskDetailDoList.get(0).getFlowCode();
        String referenceType = taskDetailDoList.get(0).getReferenceType();
        String referenceNo = taskDetailDoList.get(0).getReferenceNo();
        int totalQty = taskDetailDoList.stream().mapToInt(PickTaskDetailDo::getOperationQty).sum();
        return getTaskDo(flowCode, referenceType, referenceNo, totalQty);
    }

    private ShipTaskDo getTaskDo(String flowCode, String referenceType, String referenceNo, int totalQty) {
        OperationUserContext user = OperationUserContextHolder.get();
        Date now = new Date();

        ShipTaskDo taskDo = new ShipTaskDo();
        taskDo.setTaskNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_HEADER.getSequenceType()));
        taskDo.setType(TaskTypeEnum.SHIP.getTaskType());
        taskDo.setFlowCode(flowCode);
        taskDo.setReferenceType(referenceType);
        taskDo.setReferenceNo(referenceNo);
        taskDo.setStatus(INIT.getStatus());
        taskDo.setWarehouseCode(user.getWarehouseCode());
        taskDo.setTenantCode(user.getTenantCode());
        taskDo.setPriority(NumberUtils.INTEGER_ONE);
        taskDo.setOperationUserId(user.getUserId());
        taskDo.setOperationRealName(user.getRealName());
        taskDo.setOperationUserName(user.getUserName());
        taskDo.setStartTime(now);
        taskDo.setEndTime(now);
        taskDo.setTotalQty(totalQty);
        taskDo.setInitialTotalQty(totalQty);
        taskDo.setMutex(NumberUtils.INTEGER_ZERO);
        taskDo.setCreatedUserId(user.getUserId());
        taskDo.setCreatedUserName(user.getUserName());
        taskDo.setCreatedRealName(user.getRealName());
        taskDo.setCreatedTime(now);
        taskDo.setUpdatedTime(now);
        taskDo.setVersion(NumberUtils.INTEGER_ZERO);
        taskDo.setDeleted(NumberUtils.INTEGER_ZERO);
        return taskDo;
    }

    private List<ShipTaskDetailDo> buildTaskDetailList(String taskNo, List<PickTaskDetailDo> tdList, List<PickTaskDetailResultDo> resultDoList, BaseTaskDetailRequest baseTaskDetailRequest) {
        List<ShipTaskDetailDo> shipTaskDetailList = new ArrayList<>();
        String userName = baseTaskDetailRequest.getUserName();
        String realName = baseTaskDetailRequest.getRealName();
        long userId = baseTaskDetailRequest.getUserId();
        Date now = new Date();

        // 拷贝操作副本
        List<PickTaskDetailDo> tempTdList = new ArrayList<>();
        // 获取任务明细对象和result对象的笛卡尔积
        for (PickTaskDetailDo td : tdList) {
            for (PickTaskDetailResultDo rd : resultDoList) {
                if (StringUtils.equals(td.getDetailNo(), rd.getTaskDetailNo())) {
                    PickTaskDetailDo tempTd = new PickTaskDetailDo();
                    BeanUtils.copyProperties(td, tempTd);
                    // 任务要取result里面的相关信息，这里进行赋值，方便操作。
                    tempTd.setInventoryNo(rd.getInventoryNo());
                    tempTd.setOperationQty(rd.getOperationQty());
                    tempTd.setContainerCode(rd.getContainerCode());
                    tempTd.setLocationCode(rd.getLocationCode());
                    tempTdList.add(tempTd);
                }
            }
        }
        // 然后按照referenceDetailNo和inventoryNo进行合并
        Map<String, List<PickTaskDetailDo>> cleanRepeatMap = tempTdList.stream().collect(Collectors.groupingBy(x -> x.getReferenceDetailNo() + "_" + x.getInventoryNo()));
        final List<PickTaskDetailDo> finalTdList = new ArrayList<>();
        cleanRepeatMap.forEach((k, v) -> {
            // 合并操作数
            v.get(0).setOperationQty(v.stream().mapToInt(PickTaskDetailDo::getOperationQty).sum());
            finalTdList.add(v.get(0));
        });
        log.info("合并前数量：{}，合并后数量：{}.", tdList.size(), finalTdList.size());
        cleanRepeatMap.clear();
        tempTdList.clear();

        List<String> inventoryNos = finalTdList.stream().map(PickTaskDetailDo::getInventoryNo).distinct().collect(Collectors.toList());

        Map<String, InventoryResponse> inventoryMap = inventoryQueryApiRpcRepository.queryInvMapByInventoryNoList(inventoryNos);

        finalTdList.forEach(pickTD -> {
            ShipTaskDetailDo shipTD = new ShipTaskDetailDo();
            shipTD.setDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.TASK_DETAIL.getSequenceType()));
            shipTD.setTaskNo(taskNo);
            shipTD.setTaskType(TaskTypeEnum.SHIP.getTaskType());
            shipTD.setBizType(pickTD.getBizType().byteValue());
            shipTD.setStatus(NumberUtils.INTEGER_ZERO.byteValue());
            shipTD.setReferenceNo(pickTD.getReferenceNo());
            shipTD.setReferenceType(pickTD.getReferenceType());
            shipTD.setReferenceDetailNo(pickTD.getReferenceDetailNo());
            shipTD.setInventoryNo(pickTD.getInventoryNo());
            shipTD.setContainerCode(pickTD.getContainerCode());
            shipTD.setQty(pickTD.getOperationQty());
            shipTD.setOperationQty(NumberUtils.INTEGER_ZERO);
            shipTD.setPoNo(pickTD.getPoNo());
            shipTD.setOriginalDetailNo(pickTD.getOriginalDetailNo());
            shipTD.setWarehouseCode(pickTD.getWarehouseCode());
            shipTD.setTenantCode(pickTD.getTenantCode());
            shipTD.setOwnerCode(pickTD.getOwnerCode());
            shipTD.setVendorCode(pickTD.getVendorCode());
            shipTD.setBarcode(pickTD.getBarcode());
            shipTD.setUniqueCode(pickTD.getUniqueCode());
            shipTD.setSkuId(pickTD.getSkuId());
            shipTD.setGoodsTitle(pickTD.getGoodsTitle());
            shipTD.setGoodsPic(pickTD.getGoodsPic());
            shipTD.setGoodsArticleNumber(pickTD.getGoodsArticleNumber());
            shipTD.setQualityLevel(pickTD.getQualityLevel());
            shipTD.setSpecs(pickTD.getSpecs());
            shipTD.setUom(pickTD.getUom());
            shipTD.setLocationCode(pickTD.getLocationCode());
            shipTD.setAllocatedNo(pickTD.getAllocatedNo());
            shipTD.setFlowCode(pickTD.getFlowCode());
            shipTD.setCreatedUserId(userId);
            shipTD.setCreatedUserName(userName);
            shipTD.setCreatedRealName(realName);
            shipTD.setCreatedTime(now);
            shipTD.setUpdatedUserId(userId);
            shipTD.setUpdatedUserName(userName);
            shipTD.setUpdatedRealName(realName);
            shipTD.setUpdatedTime(now);
            shipTD.setVersion(NumberUtils.INTEGER_ZERO);
            shipTD.setDeleted(NumberUtils.INTEGER_ZERO);
            shipTD.setMfgTime(pickTD.getMfgTime());
            shipTD.setExpTime(pickTD.getExpTime());
            shipTD.setOriginalOrderCode(pickTD.getOriginalOrderCode());
            shipTD.setBatchNo(pickTD.getInvBatchNo());
            shipTD.setEntryOrderCode(pickTD.getEntryOrderCode());
            shipTD.setProductionBatchNo(pickTD.getProductionBatchNo());
            shipTD.setFirstReceivedTime(Optional.ofNullable(inventoryMap.get(pickTD.getInventoryNo())).map(InventoryResponse::getFirstReceivedTime).orElse(null));
            shipTaskDetailList.add(shipTD);
        });
        return shipTaskDetailList;
    }

    private NotifyOfcDeductSciRequest buildNotifyOfcDeductSciRequest(List<ShipTaskDetailDo> shipDetailList, List<DeliveryDetailDo> deliveryDetailList, Map<String, InventoryDo> inventoryMap) {

        int bizType = shipDetailList.get(0).getBizType();
        String orderCode = shipDetailList.get(0).getReferenceNo();
        String orderType = shipDetailList.get(0).getReferenceType();

        NotifyOfcDeductSciRequest request = new NotifyOfcDeductSciRequest();
        request.setBizType(bizType);
        request.setOrderCode(orderCode);
        request.setOrderType(orderType);
        List<NotifyOfcDeductSciRequest.OrderLine> orderLines = new ArrayList<>();
        request.setOrderLines(orderLines);

        for (ShipTaskDetailDo taskDetail : shipDetailList) {
            NotifyOfcDeductSciRequest.OrderLine orderLine = new NotifyOfcDeductSciRequest.OrderLine();
            orderLine.setUniqueCode(taskDetail.getUniqueCode());
            orderLine.setOwnerCode(taskDetail.getOwnerCode());
            orderLine.setSkuId(taskDetail.getSkuId());
            orderLine.setQualityLevel(taskDetail.getQualityLevel());
            orderLine.setWarehouseCode(taskDetail.getWarehouseCode());
            /*增加单据明细编号,一单多次发货的时候要根据明细对SCI进行幂等*/
            orderLine.setOrderDetailCode(taskDetail.getReferenceDetailNo());
            DeliveryDetailDo deliveryDetailDo = deliveryDetailList.stream()
                    .filter(item -> item.getDetailNo().equals(taskDetail.getReferenceDetailNo())).findAny().orElse(null);
            if (deliveryDetailDo == null) {
                log.warn("出库单明细[{}]不存在", taskDetail.getReferenceDetailNo());
            } else {
                orderLine.setTradeOrderNo(deliveryDetailDo.getTradeOrderNo());
            }
            InventoryDo inventoryDo = inventoryMap.get(taskDetail.getInventoryNo());
            if (inventoryDo != null) {
                orderLine.setFirstReceivedTime(inventoryDo.getFirstReceivedTime());
                orderLine.setOriginalOrderCode(inventoryDo.getOriginalOrderCode());
                orderLine.setOriginalCountry(inventoryDo.getOriginalCountry());
                orderLine.setExpTime(inventoryDo.getExpTime());
                orderLine.setOriginalCountry(inventoryDo.getOriginalCountry());
                orderLine.setVendorCode(inventoryDo.getVendorCode());
                orderLine.setInvTag(inventoryDo.getInvTag());
            } else {
                log.error("库存属性未找到，可能有问题！要当心！");
            }
            orderLine.setQty(taskDetail.getQty());
            orderLine.setPlanQty(deliveryDetailDo.getPlanQty());
            orderLine.setBizType(taskDetail.getBizType().intValue());
            orderLine.setOrderCode(orderCode);
            orderLine.setOriginalBizType(deliveryDetailDo.getOriginalBizType());
            orderLines.add(orderLine);
        }
        return request;
    }

    @Override
    public void notifyPinkOperationLog(String taskDetailNo, int bizType) {
        if (WmsBizTypeEnum.SEND_PINK_OPERATION_CODE_TYPE.contains(bizType)) {
            List<ShipTaskDetailResultDo> taskDetailResultDos = shipTaskDetailResultQueryRepository.queryByDetailNos(Collections.singletonList(taskDetailNo));
            if (CollectionUtils.isEmpty(taskDetailResultDos)) {
                log.info("待回传pink操作日志对应的明细不存在,taskDetailNo:" + taskDetailNo);
                return;
            }
            List<String> resultNos = taskDetailResultDos.stream().map(ShipTaskDetailResultDo::getResultNo).collect(Collectors.toList());
            resultNos.forEach(e -> {
                //如果是个人寄存或企业寄存，发消息到wms-admin-interfaces，由wms-admin-interfaces调pink接口记录操作日志
                taskFinishNotifyPinkLog.notifyPink(e, TaskTypeEnum.SHIP.getTaskType(), bizType);
            });
        }
    }

    /**
     * 一单多发场景
     *
     * @param receiveRequest
     * @param containerInstances
     * @return
     */
    @Override
    public NotifyOfcDeductSciRequest shipForMultipleSpilt(TaskReceiveRequest receiveRequest) {
        String deliveryOrderCode = receiveRequest.getOrderCode();
        boolean lockFlag = false;
        try {
            /*多次发货可能和取消并发*/
            lockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.DELIVERY_CANCEL, deliveryOrderCode);
            if (!lockFlag) {
                log.warn("单据{}发货和取消并发", deliveryOrderCode);
                throw new WmsException(WmsExceptionCode.DELIVERY_CANCEL_SYNC);
            }
            return shipSupportMultipleSpilt(receiveRequest);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new WmsException(WmsExceptionCode.DELIVERY_CANCEL_SYNC);
        } catch (Exception e) {
            log.error("合并发货单{}发货失败", deliveryOrderCode, e);
            throw e;
        } finally {
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.DELIVERY_CANCEL, deliveryOrderCode);
            }
        }
    }

    /**
     * 新接入的新品来样、维修一出二出，欧洲买手店直发
     * 客制二出发货,简单的发货处理只是简单更新出库单状态
     * @param deliveryHeader
     */
    @Override
    public void simpleShipment(DeliveryHeaderDo deliveryHeader){
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setId(deliveryHeader.getId());
        updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());
        updateDeliveryHeaderDo.setOutTime(new Date());
        updateDeliveryHeaderDo.setTotalActualQty(1);
        deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
        deliveryDetailRepository.updateActualQtyByDeliveryOrderCodeExcludeCancel(deliveryHeader.getTenantCode(), deliveryHeader.getDeliveryOrderCode());
    }

    /**
     * 新接入的新品来样、维修一出二出，
     * 客制二出发货,简单的发货处理只是简单更新出库单状态
     * @param deliveryOrderCode
     */
    @Override
    public void simpleShipment(String deliveryOrderCode){
        DeliveryHeaderDo headerDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if(Objects.isNull(headerDo)){
            headerDo = deliveryHeaderRepository.queryXHDeliveryInfoWithDeliveryDetail(deliveryOrderCode);
        }
        if(Objects.nonNull(headerDo)){
            simpleShipment(headerDo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationWarehouseShip(TaskReceiveRequest receiveRequest) {
        List<BaseTaskDetailRequest> detailRequestList = receiveRequest.getTaskDetails();
        BaseTaskDetailRequest detailRequest = detailRequestList.get(NumberUtils.INTEGER_ZERO);
        //一、查询出库单信息
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(receiveRequest.getOrderCode());
        deliveryHeaderDo = replaceSmallWarehouseXHDeliveryHeader(deliveryHeaderDo,receiveRequest.getOrderCode());
        if(Objects.isNull(deliveryHeaderDo)){
            log.error("操作仓发货, 出库单[{}]不存", receiveRequest.getOrderCode());
            return;
        }
        //二、查询出库单明细信息
        List<DeliveryDetailDo> deliveryDetailDos = deliveryDetailRepository
                .queryDeliveryDetailExcludeCancelByDeliveryCode(deliveryHeaderDo.getDeliveryOrderCode());
        if(CollectionUtils.isEmpty(deliveryDetailDos)){
            log.error("操作仓发货,明细不存在或已取消", receiveRequest.getOrderCode());
            return;
        }
        //三、使用出库单明细信息扣库存
        DeliveryDetailDo deliveryDetailDo = deliveryDetailDos.get(NumberUtils.INTEGER_ZERO);
        InventoryDo inventoryDo = inventoryRepository.queryInventoryDoByUniCode(detailRequest.getUniqueCode());
        if (Objects.isNull(inventoryDo) || inventoryDo.getBizType().compareTo(receiveRequest.getBizType()) != 0) {
            log.warn("操作仓发货, 唯一码[{}]对应的库存行不存在", detailRequest.getUniqueCode());
        } else if (!isSameWarehouseConsolidation(deliveryDetailDo)){
            InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
            inventoryBasePojo.setTenantCode(Optional.ofNullable(deliveryDetailDo)
                    .map(DeliveryDetailDo::getTenantCode).orElse(OperationUserContextHolder.getTenantCode()));
            inventoryBasePojo.setReferenceNo(Optional.ofNullable(deliveryDetailDo)
                    .map(DeliveryDetailDo::getDeliveryOrderCode).orElse(detailRequest.getUniqueCode()));
            inventoryBasePojo.setReferenceType(receiveRequest.getOrderType());
            inventoryBasePojo.setOperateQty(1);
            inventoryBasePojo.setReferenceDetailNo(Optional.ofNullable(deliveryDetailDo)
                    .map(DeliveryDetailDo::getDetailNo).orElse(detailRequest.getUniqueCode()));
            inventoryBasePojo.setTaskDetailNo(Optional.ofNullable(deliveryDetailDo)
                    .map(DeliveryDetailDo::getDetailNo).orElse(detailRequest.getUniqueCode()));
            inventoryBasePojo.setTaskNo(Optional.ofNullable(deliveryDetailDo)
                    .map(DeliveryDetailDo::getDetailNo).orElse(detailRequest.getUniqueCode()));
            inventoryBasePojo.setRelativeInventoryNo(inventoryDo.getInventoryNo());
            inventoryBasePojo.setExpressNo(detailRequest.getExpressCode());
            String inventoryNo = inventoryOperateService.ship(inventoryBasePojo);
            if (StringUtils.isBlank(inventoryNo)) {
                log.error("发货明细{}扣减库存失败", Optional.ofNullable(deliveryDetailDo)
                        .map(DeliveryDetailDo::getDetailNo).orElse(detailRequest.getUniqueCode()));
                throw new WmsException("发货明细扣减库存失败");
            }
        }
        //四、出库单存在, 更新出库单头已发货状态
        DeliveryHeaderDo updateDeliveryHeaderDo = new DeliveryHeaderDo();
        updateDeliveryHeaderDo.setId(deliveryHeaderDo.getId());
        if (Boolean.TRUE.equals(receiveRequest.getNeedHandover())) {
            /*并且单据状态改成集货出库*/
            updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.COLLECTION_OUT.getStatus());
        }else{
            updateDeliveryHeaderDo.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());
        }
        updateDeliveryHeaderDo.setOutTime(new Date());
        updateDeliveryHeaderDo.setTotalActualQty(1);
        int deliveryHeaderRows = deliveryHeaderRepository.updateById(updateDeliveryHeaderDo);
        if (deliveryHeaderRows <= NumberUtils.INTEGER_ZERO) {
            throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
        }
        // 更新出库单明细actualQty
        deliveryDetailRepository.updateActualQtyByDetailNo(deliveryDetailDo.getDetailNo(), 1);

        /* 生命周期 */
        WarehouseUniqueLifeCycleDo lifeCycleDo = warehouseUniqueLifeCycleQueryRepository
                .queryLatestByUniqueCode(detailRequest.getUniqueCode());
        if (null != lifeCycleDo) {
            WarehouseUniqueLifeCycleDo updateLifeCycleDo = new WarehouseUniqueLifeCycleDo();
            updateLifeCycleDo.setId(lifeCycleDo.getId());
            updateLifeCycleDo.setOutboundShippingTime(new Date());
            int rows = warehouseUniqueLifeCycleCommandRepository.updateByPrimaryKeySelective(updateLifeCycleDo);
            if (rows <= NumberUtils.INTEGER_ZERO) {
                throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
            }
        } else {
            log.warn("操作仓发货没有查询到仓库唯一码生命周期：{}", detailRequest.getUniqueCode());
        }
        log.info("操作仓发货消息uniqueCodes[{}], 入库单消费", detailRequest.getUniqueCode());
        List<String> uniqueCodes = Collections.singletonList(detailRequest.getUniqueCode());

        //已确认消费者未使用OutboundDto的orderCode
        outboundMessageProducer.outboundMessage(new OutboundDto(uniqueCodes, deliveryHeaderDo.getDeliveryOrderCode(), false,
                OperationUserContextHolder.getTenantCode()));

        //替换外部请求的交易单号为内部单号
        String type = deliveryHeaderDo.getType();
        //保存出库单承运商信息
        saveDeliveryLogistics(deliveryHeaderDo.getDeliveryOrderCode(),receiveRequest,type);

        //发布出库完成事件消息,现货的仓库需要从pink取，出库单上不准确。
        ShipEventParam shipEventParam = ShipEventParam.builder().deliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode())
                .uniqueCodes(uniqueCodes)
                .warehouseCode(detailRequest.getWarehouseCode())
                .notExistShipTaskFlag(Boolean.TRUE).build();
        deliveryShipEventProducer.publishShipEvent(shipEventParam);

        /*美妆效期,发货通知ofc要加上生产日期、过期日期、业务类型*/
        if (Boolean.TRUE.equals(receiveRequest.getNeedHandover()) && !isSameWarehouseConsolidation(deliveryDetailDo)) {
            /*需要交接的要调用ofc生成交接单信息,并且单据状态改成集货出库*/
            handoverDeliveryService.processHandoverDelivery(deliveryHeaderDo,deliveryDetailDos,deliveryDetailDo.getTradeOrderNo(),inventoryDo);
        }
    }

    private Boolean isSameWarehouseConsolidation(DeliveryDetailDo detailDo){
        return Optional.ofNullable(handoverApplyService.selectByReferenceNo(detailDo.getTradeOrderNo()))
                .filter(applyHeaderDo -> HandoverTypeEnum.XHJJGJJ.getCode().equals(applyHeaderDo.getOrderType()))
                .map(applyHeaderDo -> applyHeaderDo.getSourceWarehouseCode().equals(applyHeaderDo.getWarehouseCode()))
                .orElse(Boolean.FALSE);
    }

    @Override
    public void saveDeliveryLogistics(TaskReceiveRequest receiveRequest,String orderType) {
        log.info("开始保存承运商信息，receiveRequest -> {}, orderType ->{}",JSON.toJSONString(receiveRequest),orderType);
        List<BaseTaskDetailRequest> taskDetails = receiveRequest.getTaskDetails();
        if (CollectionUtils.isEmpty(taskDetails)) {
            return;
        }
        String deliveryOrderCode = receiveRequest.getOrderCode();
        if (WmsOutBoundTypeEnum.HBFHCK.getType().equals(orderType)){
            updateByTradeOrderNo(taskDetails, deliveryOrderCode);
        }else {
            updateByDeliveryOrderCode(taskDetails, deliveryOrderCode);
        }
    }

    @Override
    public void saveDeliveryLogistics(String deliveryOrderNo,TaskReceiveRequest receiveRequest,String orderType) {
        log.info("开始保存承运商信息deliveryOrderNo:{}，receiveRequest -> {}, orderType ->{}",deliveryOrderNo,JSON.toJSONString(receiveRequest),orderType);
        List<BaseTaskDetailRequest> taskDetails = receiveRequest.getTaskDetails();
        if (CollectionUtils.isEmpty(taskDetails)) {
            return;
        }
        updateByDeliveryOrderCode(taskDetails, deliveryOrderNo);
    }

    private void updateByDeliveryOrderCode(List<BaseTaskDetailRequest> taskDetails, String deliveryOrderCode) {
        //合并发以外的单据所有明细运单号都是相同的，取第一条即可。
        BaseTaskDetailRequest baseTaskDetailRequest = taskDetails.get(0);
        DeliveryLogisticDo deliveryLogisticDo = deliveryLogisticRepository.queryFirstOneByDeliveryOrderCode(deliveryOrderCode);
        if (Objects.isNull(deliveryLogisticDo)) {
            deliveryLogisticCommandRepository.saveDeliveryLogistics(
                    Lists.newArrayList(buildLogisticInsertDo(deliveryOrderCode, baseTaskDetailRequest)));
        } else {
            updateLogisticInfo(baseTaskDetailRequest, deliveryLogisticDo);
        }

    }

    private void updateByTradeOrderNo(List<BaseTaskDetailRequest> taskDetails, String deliveryOrderCode) {
        //合并发的单据在出库单落库的时候会根据明细记两条，运单号有可能不同，所以要根据tradeOrderNo分开处理。
        List<DeliveryLogisticDo> insertDos = new ArrayList<>();
        taskDetails.forEach(detailRequest -> {
            DeliveryLogisticDo logisticDo = deliveryLogisticRepository.queryByTradeOrderNo(detailRequest.getTradeOrderNo(),deliveryOrderCode);
            if (Objects.isNull(logisticDo)) {
                insertDos.add(buildLogisticInsertDo(deliveryOrderCode, detailRequest));
            } else {
                updateLogisticInfo(detailRequest, logisticDo);
            }
        });
        if (CollectionUtils.isNotEmpty(insertDos)){
            deliveryLogisticCommandRepository.saveDeliveryLogistics(insertDos);
        }
    }

    private void updateLogisticInfo( BaseTaskDetailRequest detailRequest, DeliveryLogisticDo logisticDo) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        logisticDo.setExpressCode(detailRequest.getExpressCode());
        logisticDo.setCarrierCode(detailRequest.getLogisticsCode());
        logisticDo.setCarrierName(detailRequest.getLogisticsName());
        logisticDo.setCarrierProductCode(detailRequest.getCarrierProductCode());
        logisticDo.setCarrierProductName(detailRequest.getCarrierProductName());
        logisticDo.setLogisticsFlag(detailRequest.getLogisticsFlag());
        logisticDo.setCityName(detailRequest.getCityName());
        logisticDo.setUpdatedTime(new Date());
        logisticDo.setUpdatedUserId(userContext.getUserId());
        logisticDo.setUpdatedRealName(userContext.getRealName());
        logisticDo.setUpdatedUserName(userContext.getUserName());
        deliveryLogisticCommandRepository.updateByCondition(logisticDo);
    }

    private DeliveryLogisticDo buildLogisticInsertDo(String deliveryOrderCode, BaseTaskDetailRequest detailRequest) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        DeliveryLogisticDo logisticDo = new DeliveryLogisticDo();
        logisticDo.setDeliveryOrderCode(deliveryOrderCode);
        logisticDo.setTradeOrderNo(StringUtils.isEmpty(detailRequest.getTradeOrderNo()) ? deliveryOrderCode : detailRequest.getTradeOrderNo());
        logisticDo.setExpressCode(detailRequest.getExpressCode());
        logisticDo.setCarrierCode(detailRequest.getLogisticsCode());
        logisticDo.setCarrierName(detailRequest.getLogisticsName());
        logisticDo.setCarrierProductCode(detailRequest.getCarrierProductCode());
        logisticDo.setCarrierProductName(detailRequest.getCarrierProductName());
        logisticDo.setLogisticsFlag(detailRequest.getLogisticsFlag());
        logisticDo.setCityName(detailRequest.getCityName());
        logisticDo.setCreatedUserId(userContext.getUserId());
        logisticDo.setCreatedUserName(userContext.getUserName());
        logisticDo.setCreatedRealName(userContext.getRealName());
        logisticDo.setUpdatedUserId(userContext.getUserId());
        logisticDo.setUpdatedUserName(userContext.getUserName());
        logisticDo.setUpdatedRealName(userContext.getRealName());
        logisticDo.setWarehouseCode(detailRequest.getWarehouseCode());
        logisticDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        return logisticDo;
    }


    /**
     * 创建任务并且执行发货
     * 支持一单多发场景
     *
     * @param receiveRequest
     * @param containerInstances
     * @return
     */
    private NotifyOfcDeductSciRequest shipSupportMultipleSpilt(TaskReceiveRequest receiveRequest) {
        DeliveryHeaderDo deliveryHeader = deliveryHeaderRepository.queryDeliveryInfo(receiveRequest.getOrderCode());
        if (Objects.isNull(deliveryHeader)) {
            log.error("合并单{}发货失败,出库单不存在", receiveRequest.getOrderCode());
            throw new WmsException(WmsExceptionCode.DELIVERY_EMPTY_ERROR);
        }
        /*获取发货请求里面的所有交易单据*/
        List<String> tradeOrderCodeList = receiveRequest.getTaskDetails().stream().map(BaseTaskDetailRequest::getTradeOrderNo).collect(Collectors.toList());
        /*获取单据明细*/
        List<DeliveryDetailDo> deliveryDetailList = deliveryDetailRepository.queryDeliveryDetailsByTradeOrderCode(deliveryHeader.getWarehouseCode(), tradeOrderCodeList);
        /*获取实际要发货的发货任务明细*/
        List<ShipTaskDetailDo> actualShipTaskDetails = getActualShipTaskDetail(deliveryHeader, deliveryDetailList);
        if (CollectionUtils.isEmpty(actualShipTaskDetails)) {
            log.error("发货请求{}实际要发货的任务明细为空", JsonUtils.serialize(receiveRequest));
            throw new WmsException(WmsExceptionCode.SHIP_TASK_NOT_EXIST);
        }
        List<String> invNoList = actualShipTaskDetails.stream().map(ShipTaskDetailDo::getInventoryNo).collect(Collectors.toList());
        Map<String, InventoryDo> inventoryMap = inventoryRepository.queryInventoryDoMap(OperationUserContextHolder.getTenantCode(), invNoList, deliveryHeader.getWarehouseCode());
        //判断是否SN模式，构建参数
        List<String> referenceDetailNos = actualShipTaskDetails.stream().map(ShipTaskDetailDo::getReferenceDetailNo).collect(Collectors.toList());
        Map<String, List<String>> snListMap = deliverySNService.getSNListByTaskDetailNo(referenceDetailNos, actualShipTaskDetails.get(0).getWarehouseCode());

        /*执行发货*/
        for (ShipTaskDetailDo taskDetail : actualShipTaskDetails) {
            try {
                combineDeliveryShipService.shipByTaskDetail(taskDetail.getWarehouseCode(), deliveryHeader.getDeliveryOrderCode(), taskDetail.getDetailNo(), taskDetail.getQty(), snListMap);
                /*处理出箱*/
                takeOutFromContainerIfNecessary(taskDetail);
                /*发送发货完成的消息*/
                sendOutShipMessage(taskDetail);
            } catch (Exception e) {
                log.error("任务明细{}发货报错", taskDetail.getDetailNo());
                throw e;
            }
        }
        //异步批量扣库存
        batchSubInventory(actualShipTaskDetails,snListMap);
        combineDeliveryShipOrderService.updateDeliveryHeaderIfNecessary(deliveryHeader.getDeliveryOrderCode());
        /*构建返回值*/
        return this.buildNotifyOfcDeductSciRequest(actualShipTaskDetails, deliveryDetailList, inventoryMap);
    }

    /**
     * 合并发货异步批量扣库存
     * @param actualShipTaskDetails
     * @param snListMap
     */
    private void batchSubInventory(List<ShipTaskDetailDo> actualShipTaskDetails,
                                   Map<String, List<String>> snListMap){
        List<InventoryBasePojo> inventoryBasePojoList = new ArrayList<>();
        List<String> detailNoList = Lists.newArrayList();
        String taskNo = "";
        for (ShipTaskDetailDo taskDetail : actualShipTaskDetails) {
            InventoryBasePojo inventoryBasePojo = new InventoryBasePojo();
            inventoryBasePojo.setTenantCode(taskDetail.getTenantCode());
            inventoryBasePojo.setReferenceNo(taskDetail.getReferenceNo());
            inventoryBasePojo.setReferenceType(taskDetail.getReferenceType());
            inventoryBasePojo.setOperateQty(taskDetail.getQty());
            inventoryBasePojo.setReferenceDetailNo(taskDetail.getReferenceDetailNo());
            detailNoList.add(taskDetail.getDetailNo());
            taskNo = taskDetail.getTaskNo();
            inventoryBasePojo.setTaskDetailNo(taskDetail.getDetailNo());
            inventoryBasePojo.setTaskNo(taskDetail.getTaskNo());
            inventoryBasePojo.setRelativeInventoryNo(taskDetail.getInventoryNo());
            if (!snsSwitch) {
                inventoryBasePojo.setSns(snListMap.get(taskDetail.getReferenceDetailNo()));
                if (CollectionUtils.isNotEmpty(inventoryBasePojo.getSns())) {
                    inventoryBasePojo.setSnTypeCode(SnTypeEnum.BAOSHUI_SN.getCode());
                }
            }
            inventoryBasePojoList.add(inventoryBasePojo);
        }
        Collections.sort(detailNoList);
        String requestId = taskNo + md5DigestAsHex(JSON.toJSONString(detailNoList).getBytes());
        shipCommonHandler.batchShip(inventoryBasePojoList, requestId,snListMap);
    }



    private boolean checkSnProcess(List<ShipTaskDetailDo> taskDetailEntityList){
        Set<Integer> bizSet = taskDetailEntityList.stream().map(n -> n.getBizType().intValue()).collect(Collectors.toSet());
        return deliverySNService.enableSnProcess(bizSet, taskDetailEntityList.get(0).getWarehouseCode());
    }
    /**
     * 获取时间发货的任务明细
     *
     * @param deliveryHeader
     * @param deliveryDetailList
     * @param containerInstances
     * @return
     */
    private List<ShipTaskDetailDo> getActualShipTaskDetail(DeliveryHeaderDo deliveryHeader, List<DeliveryDetailDo> deliveryDetailList) {
        /*获取当前单据所有的发货任务*/
        List<ShipTaskDetailDo> shipTaskDetails = combineDeliveryShipTaskService.getOrCreateShipTask(deliveryHeader);
        List<String> detailNoList = deliveryDetailList.stream().map(DeliveryDetailDo::getDetailNo).collect(Collectors.toList());
        /*只发实际要发的明细*/
        List<ShipTaskDetailDo> actualShipTaskDetails = shipTaskDetails.stream().filter(shipTaskDetail -> detailNoList.contains(shipTaskDetail.getReferenceDetailNo())).collect(Collectors.toList());
        /*获取的发货任务明细只要有一个是已经完成的,那就说明重复发货,或者重复消费了*/
        boolean exist = actualShipTaskDetails.stream().anyMatch(taskDetail -> finishList.contains(taskDetail.getStatus().intValue()));
        if (exist) {
            throw new WmsException(WmsExceptionCode.SHIP_TASK_REPEAT);
        }
        return actualShipTaskDetails;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoPackingShipWithWholeOrder(String deliveryOrderCode) {
        /*打单*/
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        if (deliveryHeaderDo == null) {
            throw new WmsOperationException("出库单不存在");
        }
        if (snConfig.isSN(deliveryHeaderDo.getWarehouseCode(), deliveryHeaderDo.getBizType())) {
            throw new WmsOperationException("保税企业不支持批量出库");
        }
        /*保税的才允许批量完成*/
        if (!WmsBizTypeEnum.BAO_SHUI_BIZ_TYPE.contains(deliveryHeaderDo.getBizType())){
            throw new WmsOperationException("只允许保税业务整单出库");
        }
        /*只有拣货完成才能批量出库*/
        if (!WmsOutBoundStatusEnum.pickComplete.contains(deliveryHeaderDo.getStatus())){
            throw new WmsOperationException("当前单据状态不允许批量出库");
        }
        /*保税业务需要校验存在取回方式*/
        if (WmsBizTypeEnum.BAO_SHUI_BIZ_TYPE.contains(deliveryHeaderDo.getBizType())){
            DeliveryExtraInfoDo deliveryExtraInfoDo = deliveryExtraInfoRepository.queryByDeliveryOrderCode(deliveryHeaderDo.getDeliveryOrderCode());
            if (Objects.isNull(deliveryExtraInfoDo) || org.apache.commons.lang.StringUtils.isBlank(deliveryExtraInfoDo.getRetrievalWay())) {
                throw new WmsOperationException("当前单据没有取回方式，暂时不允许出库");
            }
        }
        OperationUserContextHolder.get().setWarehouseCode(deliveryHeaderDo.getWarehouseCode());
        /*创建包裹*/
        WmsPackDo pack = wmsPackService.createPack(deliveryOrderCode);
        /*创建明细*/
        List<PickTaskDetailDo> pickTaskDetailDos = pickTaskDetailQueryRepository.queryByReferenceNo(deliveryOrderCode);
        List<PickTaskDetailDo> notCancelDetails = pickTaskDetailDos.stream().filter(pickTaskDetailDo -> !CANCEL.getStatus().equals(pickTaskDetailDo.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notCancelDetails)){
            throw new WmsOperationException("当前出库单没有未取消的拣货任务明细");
        }
        /*构建包裹明细*/
        List<WmsPackDetailDo> wmsPackDetailDos = buildWmsPackDetails(pack,notCancelDetails);
        /*批量保存*/
        saveWmsPackDetails(wmsPackDetailDos);
        //封箱
        SealBoxParam sealBoxParam = new SealBoxParam();
        sealBoxParam.setPackNo(pack.getPackNo());
        wmsPackService.sealBox(sealBoxParam);
        LogisticMakeParam param = new LogisticMakeParam();
        param.setDeliveryHeaderDo(deliveryHeaderDo);
        LogisticsInfo logisticsInfo = selfTakeLogisticsService.makeOrder(param);
        /*整箱完成*/
        wmsPackService.completeWholeOrder(deliveryOrderCode);
        /*发货*/
        wmsPackShipService.updatePackShip(pack.getPackNo());
        expressShipService.tryDoExpressShip(deliveryOrderCode,logisticsInfo.getExpressCode(), false);
    }

  /**
   * 小仓库现货订单、操作仓发货查询出库单
   *
   * @param deliveryHeaderDo
   * @param tradeOrderNo
   * @return DeliveryHeaderDo 出库单
   */
  @Override
  public DeliveryHeaderDo replaceSmallWarehouseXHDeliveryHeader(DeliveryHeaderDo deliveryHeaderDo, String tradeOrderNo) {
        //小仓库的bizType = 0
        if(Objects.isNull(deliveryHeaderDo)||Objects.equals(WmsBizTypeEnum.common.getBizType(),deliveryHeaderDo.getBizType())){
            //根据交易单号发货如果未查询到单据则，则根据交易单号查询明细反差出出库单信息
            //现货receiveRequest.getOrderCode()就是交易单号
            deliveryHeaderDo = deliveryHeaderRepository.queryXHDeliveryInfoWithDeliveryDetail(tradeOrderNo);

        }
        return deliveryHeaderDo;
    }

    /**
     * 保存包裹明细
     * @param wmsPackDetailDos
     */
    private void saveWmsPackDetails(List<WmsPackDetailDo> wmsPackDetailDos) {
        List<List<WmsPackDetailDo>> partition = Lists.partition(wmsPackDetailDos, MAX_BATCH_SAVE_SIZE);
        partition.stream().forEach(list-> wmsPackDetailRepository.batchInsert(list));
    }

    /**
     * 批量构建包裹明细
     *
     *
     * @param pack
     * @param notCancelDetails
     * @return
     */
    private List<WmsPackDetailDo> buildWmsPackDetails(WmsPackDo pack, List<PickTaskDetailDo> notCancelDetails) {
        List<WmsPackDetailDo> packDetailDos = new ArrayList<>();
        Date date = new Date();
        Set<String> skuIdSet = notCancelDetails.stream().map(PickTaskDetailDo::getSkuId).collect(Collectors.toSet());
        Map<String, SkuCommonRspDomain> skuCommonRspDomainMap = iCommodityQueryV2Service.querySkuCommonMapBySkuIds(pack.getTenantCode(), skuIdSet);
        Map<String, List<PickTaskDetailDo>> groups = notCancelDetails.stream().collect(Collectors.groupingBy(this::getGroupKey));
        groups.forEach((detailNo,list)->{
            int packQty = list.stream().mapToInt(PickTaskDetailDo::getOperationQty).sum();
            PickTaskDetailDo pickTaskDetailDo = list.get(0);
            WmsPackDetailDo wmsPackDetailDo = new WmsPackDetailDo();
            SkuCommonRspDomain skuCommonRspDomain = skuCommonRspDomainMap.get(pickTaskDetailDo.getSkuId());
            wmsPackDetailDo.setBarcode(skuCommonRspDomain.getBarcode());
            wmsPackDetailDo.setBizType(pickTaskDetailDo.getBizType());
            wmsPackDetailDo.setPackNo(pack.getPackNo());
            wmsPackDetailDo.setPackDetailNo(IdGenUtil.generateBusinessNo(SequenceTypeEnum.PACK_DETAIL_NO.getSequenceType()));
            wmsPackDetailDo.setBoxTime(date);
            wmsPackDetailDo.setCategoryId(skuCommonRspDomain.getCategoryIdLevel3());
            if (StringUtils.isNotBlank(skuCommonRspDomain.getScpAliasFirstCategoryName())) {
                wmsPackDetailDo.setCategoryName(skuCommonRspDomain.getScpAliasFirstCategoryName());
            } else {
                wmsPackDetailDo.setCategoryName(skuCommonRspDomain.getCategoryNameLevel1());
            }
            wmsPackDetailDo.setDeliveryOrderCode(pickTaskDetailDo.getReferenceNo());
            wmsPackDetailDo.setDeliveryOrderType(pickTaskDetailDo.getReferenceType());
            wmsPackDetailDo.setDeliveryDetailCode(pickTaskDetailDo.getReferenceDetailNo());
            wmsPackDetailDo.setUniqueCode(pickTaskDetailDo.getUniqueCode());
            wmsPackDetailDo.setSkuId(pickTaskDetailDo.getSkuId());
            wmsPackDetailDo.setOwnerCode(pickTaskDetailDo.getOwnerCode());
            wmsPackDetailDo.setQty(packQty);
            wmsPackDetailDo.setWarehouseCode(pickTaskDetailDo.getWarehouseCode());
            wmsPackDetailDo.setTenantCode(pickTaskDetailDo.getTenantCode());
            wmsPackDetailDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
            wmsPackDetailDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
            wmsPackDetailDo.setCreatedRealName(OperationUserContextHolder.get().getRealName());
            wmsPackDetailDo.setCreatedTime(date);
            wmsPackDetailDo.setSpecs(skuCommonRspDomain.getPropertyText());
            wmsPackDetailDo.setGoodsTitle(skuCommonRspDomain.getSpuName());
            wmsPackDetailDo.setGoodsArticleNumber(skuCommonRspDomain.getArtNoMain());
            wmsPackDetailDo.setGoodsPic(skuCommonRspDomain.getSkuLogoUrl());
            packDetailDos.add(wmsPackDetailDo);
        });
        return packDetailDos;
    }

    /**
     * 获取分组数据
     *
     * @param pickTaskDetailDo
     * @return
     */
    private String getGroupKey(PickTaskDetailDo pickTaskDetailDo){
        if (StringUtils.isNotBlank(pickTaskDetailDo.getUniqueCode())){
            /*如果有唯一码把唯一码当成分组条件*/
            return pickTaskDetailDo.getUniqueCode();
        }
        /*否则以商品和货主作为分组条件*/
        return pickTaskDetailDo.getSkuId()+"_"+pickTaskDetailDo.getOwnerCode();
    }
}
