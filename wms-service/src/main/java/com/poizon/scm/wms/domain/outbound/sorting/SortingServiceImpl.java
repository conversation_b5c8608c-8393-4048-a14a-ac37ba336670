package com.poizon.scm.wms.domain.outbound.sorting;

import com.beust.jcommander.internal.Nullable;
import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.common.TaskRepository;
import com.poizon.scm.wms.adapter.common.model.TaskDo;
import com.poizon.scm.wms.adapter.enums.BizLockEnum;
import com.poizon.scm.wms.adapter.lock.DistributeLockFacade;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.BatchContainerBindRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryDetailRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.adapter.outbound.launch.model.LaunchBatchDo;
import com.poizon.scm.wms.adapter.outbound.launch.repository.LaunchBatchRepository;
import com.poizon.scm.wms.adapter.outbound.outbound.model.BatchContainerBindDo;
import com.poizon.scm.wms.adapter.sorting.model.SortingTaskDetailDo;
import com.poizon.scm.wms.adapter.sorting.model.SortingTaskHeaderDo;
import com.poizon.scm.wms.adapter.sorting.model.UpdateSortingTaskDetailDo;
import com.poizon.scm.wms.adapter.sorting.repository.SortingTaskDetailRepository;
import com.poizon.scm.wms.adapter.sorting.repository.SortingTaskHeaderRepository;
import com.poizon.scm.wms.api.enums.LaunchBatchStatusEnum;
import com.poizon.scm.wms.api.enums.NotifyPinkOperationTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.notify.notifyLms.processor.SortingNotifyLmsProcessor;
import com.poizon.scm.wms.domain.outbound.container.lpn.DeliveryContainerQueryService;
import com.poizon.scm.wms.domain.outbound.container.lpn.LaunchBatchUseContainerService;
import com.poizon.scm.wms.domain.outbound.container.lpn.SortOccupyContainerService;
import com.poizon.scm.wms.domain.outbound.container.lpn.SortProcessContainerWrapper;
import com.poizon.scm.wms.domain.outbound.container.lpn.resp.BasContainerResult;
import com.poizon.scm.wms.domain.outbound.container.lpn.resp.ContainerInstanceResult;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.outbound.report.DeliveryOrderDetailOperateEvent;
import com.poizon.scm.wms.domain.outbound.sorting.dto.BatchBoundContainerDTO;
import com.poizon.scm.wms.domain.outbound.sorting.processor.SortingUpdateBatchProcessor;
import com.poizon.scm.wms.domain.outbound.sorting.processor.SortingUpdateHeaderProcessor;
import com.poizon.scm.wms.domain.outbound.sorting.processor.SortingUpdatePickTaskProcessor;
import com.poizon.scm.wms.domain.outbound.worklog.WmsOutboundWorkLogService;
import com.poizon.scm.wms.domain.outbound.worklog.enums.WmsLogActionEnum;
import com.poizon.scm.wms.domain.outbound.worklog.enums.WorkLogTypeEnum;
import com.poizon.scm.wms.domain.outbound.worklog.param.OperationLogParam;
import com.poizon.scm.wms.domain.sorting.param.SortConfirmParam;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.pojo.third.notify.request.base.WmsOperateLogRequest;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.third.executor.NotifyPinkOperationLogExecutor;
import com.poizon.scm.wms.util.enums.LaunchTypeEnum;
import com.poizon.scm.wms.util.enums.OutboundEventEnum;
import com.poizon.scm.wms.util.enums.TaskStatusEnum;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Deacription
 * <AUTHOR>
 * @Date 2021/1/6 6:01 下午
 **/
@Slf4j
@Service
public class SortingServiceImpl implements SortingService {

    @Autowired
    private SortingTaskDetailRepository sortingTaskDetailRepository;
    @Autowired
    private DistributeLockFacade distributeLockFacade;
    @Autowired
    private SortingUpdateHeaderProcessor sortingUpdateHeaderProcessor;
    @Autowired
    private SortingUpdateBatchProcessor sortingUpdateBatchProcessor;
    @Autowired
    private SortingTaskHeaderRepository sortingTaskHeaderRepository;
    @Autowired
    private LaunchBatchRepository launchBatchRepository;
    @Autowired
    private SortingUpdatePickTaskProcessor sortingUpdatePickTaskProcessor;
    @Autowired
    private TaskRepository taskRepository;
    @Autowired
    private DeliveryDetailRepository deliveryDetailRepository;
    @Autowired
    private DeliveryContainerQueryService deliveryContainerQueryService;
    @Autowired
    private NotifyPinkOperationLogExecutor notifyPinkOperationLogExecutor;
    @Resource
    private BatchContainerBindRepository batchContainerBindRepository;
    @Resource
    private DeliveryHeaderRepository deliveryHeaderRepository;
    @Autowired
    private SortingNotifyLmsProcessor sortingNotifyLmsProcessor;
    @Autowired
    private LaunchBatchUseContainerService launchBatchUseContainerService;
    @Autowired
    private SortOccupyContainerService sortOccupyContainerService;
    @Autowired
    private SortProcessContainerWrapper sortProcessContainerWrapper;
    @Resource
    private OutboundMessageProducer outboundMessageProducer;
    @Resource
    private WmsOutboundWorkLogService wmsOutboundWorkLogService;
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SortingTaskHeaderDo confirmBarcode(SortConfirmParam sortConfirmParam) {
        boolean lockFlag = false;
        String taskNo = null;
        try {
            lockFlag = distributeLockFacade.tryLockForBiz(BizLockEnum.SORTING_DETAIL_LOCK, sortConfirmParam.getTaskDetailNo());
            if (!lockFlag) {
                throw new WmsOperationException("正在分拣中,无需重复操作");
            }
            LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(sortConfirmParam.getBatchNo(), OperationUserContextHolder.get().getWarehouseCode());
            if (Objects.isNull(launchBatchDo)) {
                throw new WmsOperationException("当前批次不存在");
            }
            /*如果批次需要容器拣货需要校验全部绑定完成*/
            verifyLaunchBatchBoundAllIfNeed(launchBatchDo);
            SortingTaskDetailDo taskDetailDo = sortingTaskDetailRepository.selectByTaskDetailDo(sortConfirmParam.getTaskDetailNo());
            if (Objects.isNull(taskDetailDo)) {
                throw new WmsOperationException("未查询到分拣任务明细");
            }
            if (taskDetailDo.getOperateQty().compareTo(taskDetailDo.getPlanQty()) >= 0) {
                throw new WmsOperationException("当前商品已分拣完成,无需重复分拣");
            }
            if ((taskDetailDo.getOperateQty() + sortConfirmParam.getQty()) > taskDetailDo.getPlanQty()) {
                throw new WmsOperationException("分拣操作数大于计划数");
            }
            sortProcessContainerWrapper.processContainerIfHas(taskDetailDo, sortConfirmParam);
            OperationUserContext userContext = OperationUserContextHolder.get();
            UpdateSortingTaskDetailDo updateSortingTaskDetailDo = new UpdateSortingTaskDetailDo();
            updateSortingTaskDetailDo.setTaskDetailNo(sortConfirmParam.getTaskDetailNo());
            updateSortingTaskDetailDo.setOperateQty(taskDetailDo.getOperateQty());
            updateSortingTaskDetailDo.setCurrentOperateQty(sortConfirmParam.getQty());
            updateSortingTaskDetailDo.setUpdatedRealName(userContext.getRealName());
            updateSortingTaskDetailDo.setUpdatedUserId(userContext.getUserId());
            updateSortingTaskDetailDo.setUpdatedUserName(userContext.getUserName());
            // 设置自动化设备编号
            if (StringUtils.isNotBlank(sortConfirmParam.getDeviceCode())) {
                updateSortingTaskDetailDo.setDeviceCode(sortConfirmParam.getDeviceCode());
            }
            if (StringUtils.isNotBlank(sortConfirmParam.getBoxCode())) {
                updateSortingTaskDetailDo.setBoxCode(sortConfirmParam.getBoxCode());
            }
            int cnt = sortingTaskDetailRepository.updateOperateQty(updateSortingTaskDetailDo);
            if (cnt <= 0) {
                throw new WmsOperationException("当前商品已分拣完成,无需重复分拣");
            }
            taskNo = taskDetailDo.getTaskNo();
            //1.通知分拣任务头，如果整个出库单分拣完成，会发领域消息，或者创建发货任务，走后续流程
            sortingUpdateHeaderProcessor.execute(taskDetailDo.getTaskNo());
            //2.通知批次 & pink
            sortingUpdateBatchProcessor.execute(sortConfirmParam.getBatchNo());
            //3.更新拣货任务状态
            sortingUpdatePickTaskProcessor.execute(taskDetailDo.getPickTaskNo());
            /*通知pink操作日志*/
            notifyPinkOperationLog(taskDetailDo);
            //通知lms计时计件
            notifyLms(taskDetailDo,sortConfirmParam);
            //二分大宽表操作埋点
            sendShipmentOrderOperateDetailEvent(taskDetailDo,userContext);
            //日志中心
            OperationLogParam operationLogParam = OperationLogParam.builder()
                    .orderNo(Lists.newArrayList(sortConfirmParam.getTaskDetailNo()))
                    //.orderNo(Lists.newArrayList(taskDetailDo.getDeliveryOrderDetailNo()))
                    .workLogTypeEnum(WorkLogTypeEnum.SORT_TASK_DETAIL_NO)
                    .wmsLogActionEnum(WmsLogActionEnum.WMS_SORT_SCAN_CODE)
                    .qty(sortConfirmParam.getQty())
                    .build();
            wmsOutboundWorkLogService.operateLog(operationLogParam);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
        } finally {
            if (lockFlag) {
                distributeLockFacade.releaseLockForBiz(BizLockEnum.SORTING_DETAIL_LOCK, sortConfirmParam.getTaskDetailNo());
            }
        }
        return sortingTaskHeaderRepository.selectByTaskNo(taskNo);
    }

    /**
     * 校验容器全部绑定完成
     *
     * @param launchBatchDo
     */
    private void verifyLaunchBatchBoundAllIfNeed(LaunchBatchDo launchBatchDo) {
        boolean sortUseContainer = launchBatchUseContainerService.isSortUseContainer(launchBatchDo.getContainerFlag());
        if (!sortUseContainer) {
            return;
        }
        List<BatchContainerBindDo> batchContainerBindDos = batchContainerBindRepository.queryByBatchNo(launchBatchDo.getBatchNo(), launchBatchDo.getWarehouseCode());
        List<SortingTaskHeaderDo> sortingTaskHeaderDos = sortingTaskHeaderRepository.selectByBatchNo(launchBatchDo.getBatchNo());
        if (batchContainerBindDos.size() != sortingTaskHeaderDos.size()) {
            throw new WmsOperationException("当前批次没有全部绑定容器，不允许进行分拣");
        }
    }

    /**
     * 发送出库单操作明细事件
     *
     * @param taskDetailDo     任务细节做
     * @param userContext      用户上下文
     */
    public void sendShipmentOrderOperateDetailEvent(SortingTaskDetailDo taskDetailDo,OperationUserContext userContext){
        DeliveryOrderDetailOperateEvent deliveryOrderDetailOperateEvent = new DeliveryOrderDetailOperateEvent();
        deliveryOrderDetailOperateEvent.setShipmentOrderNo(taskDetailDo.getCombineNo());
        deliveryOrderDetailOperateEvent.setProcessCode(OutboundEventEnum.SORTED_EVENT.getProcessCode());
        deliveryOrderDetailOperateEvent.setShipmentDetailNo(taskDetailDo.getDeliveryOrderDetailNo());
        deliveryOrderDetailOperateEvent.setOperateUserId(userContext.getUserId());
        deliveryOrderDetailOperateEvent.setOperateUserName(userContext.getRealName());
        deliveryOrderDetailOperateEvent.setOperateWarehouseCode(userContext.getWarehouseCode());
        deliveryOrderDetailOperateEvent.setOperateTime(new Date());
        deliveryOrderDetailOperateEvent.setTaskNo(taskDetailDo.getTaskNo());
        deliveryOrderDetailOperateEvent.setTenantId(taskDetailDo.getTenantId());
        outboundMessageProducer.sendShipmentOrderOperateDetailEvent(deliveryOrderDetailOperateEvent,OutboundEventEnum.SORTED_EVENT.name());
    }

    /**
     * 通知LMS计时计件
     */
    private void notifyLms(SortingTaskDetailDo taskDetailDo, SortConfirmParam sortConfirmParam) {
        SortingTaskDetailDo notifyLmsParam = new SortingTaskDetailDo();
        BeanUtils.copyProperties(taskDetailDo, notifyLmsParam);
        notifyLmsParam.setOperateQty(sortConfirmParam.getQty());
        notifyLmsParam.setUpdatedTime(new Date());
        sortingNotifyLmsProcessor.notify(notifyLmsParam);
    }

    /**
     * 通知pink操作日志
     *
     * @param taskDetailDo
     */
    private void notifyPinkOperationLog(SortingTaskDetailDo taskDetailDo) {
        SendMessage<WmsOperateLogRequest> sendMessage = buildOperateMessage(taskDetailDo);
        if (Objects.nonNull(sendMessage)) {
            notifyPinkOperationLogExecutor.executeNew(sendMessage);
        }
    }

    /**
     * 构建二次分拣通知pink的操作日志
     *
     * @param taskDetailDo
     * @return
     */
    private SendMessage<WmsOperateLogRequest> buildOperateMessage(SortingTaskDetailDo taskDetailDo) {
        /*获取出库单明细编号*/
        String deliveryOrderDetailNo = taskDetailDo.getDeliveryOrderDetailNo();
        String deliveryOrderCode = taskDetailDo.getCombineNo();
        DeliveryDetailDo deliveryDetailDo = deliveryDetailRepository.queryDeliveryDetailByOrderCodeAndDetailNo(deliveryOrderCode, deliveryOrderDetailNo);
        if (Objects.isNull(deliveryDetailDo)) {
            log.error("构建二次分拣操作日志时查询出库单明细不存在,deliveryOrderCode:{},deliveryOrderDetailNo:{}", deliveryOrderCode, deliveryOrderDetailNo);
            throw new WmsException(WmsExceptionCode.DELIVERY_DETAIL_EMPTY);
        }
        WmsOperateLogRequest request = buildRequest(taskDetailDo, deliveryDetailDo);
        SendMessage<WmsOperateLogRequest> message = new SendMessage<>();
        message.setMessageContent(request);
        message.setTopic(RocketMqConstants.PinkOperationLogCallBackTopic.TOPIC_NAME);
        message.setTag(RocketMqConstants.PinkOperationLogCallBackTopic.TAG);
        message.setMessageKey(StringUtils.isNotBlank(request.getUniqueCode()) ? request.getUniqueCode() : request.getDeliveryOrderCode());
        return message;
    }

    private WmsOperateLogRequest buildRequest(SortingTaskDetailDo taskDetailDo, DeliveryDetailDo deliveryDetailDo) {
        WmsOperateLogRequest request = new WmsOperateLogRequest();
        request.setDeliveryOrderCode(deliveryDetailDo.getTradeOrderNo());
        OperationUserContext context = OperationUserContextHolder.get();
        request.setOperatorId(context.getUserId());
        request.setOperateRealName(context.getRealName());
        request.setOperateTime(new Date());
        request.setOperateType(NotifyPinkOperationTypeEnum.SORTING_PICK.getPinkType());
        request.setRepositoryCode(taskDetailDo.getWarehouseCode());
        request.setUniqueCode(taskDetailDo.getUniqueCode());
        return request;
    }

    /**
     * 通过扫描的条码获取批次，该方法废弃，使用改方法的地方可以替换为<code>LaunchBatchService#scanQuery</code>
     *
     * @param referenceNo (可能是批次可能是拣货单号)
     * @return
     * @see com.poizon.scm.wms.domain.outbound.launchbacth.LaunchBatchFindService#findLaunchBatch (String)
     */
    @Override
    @Deprecated
    public LaunchBatchDo getValidBatch(String referenceNo) {
        /*1、应该大部分时间都是查的批次,所以批次靠前*/
        LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(referenceNo, OperationUserContextHolder.get().getWarehouseCode());
        /*2、其次当作容器查询*/
        if (launchBatchDo == null) {
            launchBatchDo = getByReferenceAsContainerCode(referenceNo);
        }
        /*3、然后当作唯一码查询*/
        if (launchBatchDo == null) {
            launchBatchDo = getByReferenceAsUniqueCode(referenceNo);
        }
        if (launchBatchDo == null) {
            //获取拣货单
            launchBatchDo = getLaunchBatchDoByPickTaskNo(referenceNo);
        }
        if (launchBatchDo == null) {
            throw new WmsOperationException("未找到批次:" + referenceNo);
        }
        if (!(LaunchBatchStatusEnum.SORTING.getCode().equals(launchBatchDo.getBatchStatus().byteValue()) ||
                LaunchBatchStatusEnum.COLLECTING_GOODS_COMPLETED.getCode().equals(launchBatchDo.getBatchStatus().byteValue()))) {
            throw new WmsOperationException("批次" + referenceNo + "当前状态:"
                    + LaunchBatchStatusEnum.getMessageByStatus(launchBatchDo.getBatchStatus().byteValue()) + "不可分拣");
        }
        return launchBatchDo;
    }

    private LaunchBatchDo getByReferenceAsUniqueCode(String uniqueCode) {
        /*当成唯一码查询*/
        SortingTaskDetailDo sortingTaskDetailDo = sortingTaskDetailRepository.selectSortingTaskDetailByUniqueCodeAndStatus(uniqueCode, OperationUserContextHolder.get().getWarehouseCode(), TaskStatusEnum.unCompleteList);
        if (Objects.isNull(sortingTaskDetailDo)) {
            return null;
        }
        String batchNo = sortingTaskDetailDo.getBatchNo();
        LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(batchNo, OperationUserContextHolder.get().getWarehouseCode());
        /*这里都已经通过批次去查了，再查不到批次信息，就直接报错，没必要再往下查了*/
        if (launchBatchDo == null) {
            throw new WmsOperationException("通过唯一码未找到批次:" + uniqueCode);
        }
        return launchBatchDo;
    }

    private LaunchBatchDo getByReferenceAsContainerCode(String containerCode) {
        ContainerInstanceResult containerInstanceResult;
        try {
            containerInstanceResult = deliveryContainerQueryService.queryByContainerCode(OperationUserContextHolder.getTenantCode(), OperationUserContextHolder.get().getWarehouseCode(), containerCode);
        } catch (Exception e) {
            log.warn("没有查询到出容器");
            return null;
        }
        if (Objects.isNull(containerInstanceResult)) {
            return null;
        }
        List<SortingTaskDetailDo> list = sortingTaskDetailRepository.selectDetailListByContainer(containerInstanceResult.getContainerInstanceNo());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String batchNo = list.get(0).getBatchNo();
        LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(batchNo, OperationUserContextHolder.get().getWarehouseCode());
        /*这里都已经通过批次去查了，再查不到批次信息，就直接报错，没必要再往下查了*/
        if (launchBatchDo == null) {
            throw new WmsOperationException("通过容器未找到批次:" + containerCode);
        }
        return launchBatchDo;
    }

    /**
     * 根据拣货单号获取关联批次
     *
     * @param referenceNo
     * @return
     */
    private LaunchBatchDo getLaunchBatchDoByPickTaskNo(String referenceNo) {
        TaskDo taskDo = taskRepository.findByNo(referenceNo, TaskTypeEnum.PICK.getTaskType(),
                Collections.singletonList(TaskStatusEnum.COLLECTING_GOODS_COMPLETED.getStatus()));
        if (taskDo == null) {
            return null;
        }
        LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(taskDo.getReferenceNo(), taskDo.getWarehouseCode());
        if (launchBatchDo == null) {
            return null;
        }
        if (!LaunchTypeEnum.combineTypeList.contains(launchBatchDo.getBatchType())) {
            throw new WmsOperationException("拣货单" + referenceNo + "不是合并订单拣货批次类型,无需二次分拣");
        }
        return launchBatchDo;
    }

    @Override
    @Async
    public void updatePrintCountByBatchNo(String batchNo) {
        sortingTaskHeaderRepository.updatePrintCountByBatchNo(batchNo);
    }

    @Override
    @Async
    public void updatePrintCountByCombineNo(String combineNo) {
        sortingTaskHeaderRepository.updatePrintCountByCombineNo(combineNo);
    }

    /**
     * @param batchNo
     * @param containerCode
     * @see this#bindContainerWithOrder(String, String, String, boolean)
     */
    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindContainer(String batchNo, String containerCode) {
        boolean lockFlag = false;
        try {
            /**
             * 1、校验批次状态，集货完成的批次可进行容器绑定
             * 2、查询批次下的分拣任务，是否存在未绑定容器的分拣任务，存在则进行下一步
             * 3、校验容器的有效性，进行容器占用
             * 4、容器和分拣任务绑定，插入箱明细
             */
            LaunchBatchDo launchBatchDo = launchBatchRepository.findByNo(batchNo);
            if (launchBatchDo == null || LaunchBatchStatusEnum.COLLECTING_GOODS_COMPLETED.getStatus() != launchBatchDo.getBatchStatus()) {
                throw new WmsOperationException("批次不存在或批次状态非集货完成");
            }
            lockFlag = distributeLockFacade.tryLockForBiz(BizLockEnum.SORTING_BATCH_LOCK, batchNo);

            List<SortingTaskHeaderDo> sortingTaskHeaderDoList = sortingTaskHeaderRepository.selectByBatchNo(batchNo);
            if (CollectionUtils.isEmpty(sortingTaskHeaderDoList)) {
                throw new WmsOperationException("批次对应的分拣任务未找到");
            }
            List<BatchContainerBindDo> batchContainerBindDoList = batchContainerBindRepository.queryByBatchNo(batchNo, OperationUserContextHolder.get().getWarehouseCode());
            List<String> boundOrderList = batchContainerBindDoList.stream().map(BatchContainerBindDo::getReferenceNo).collect(Collectors.toList());
            //过滤已绑定容器的分拣任务
            sortingTaskHeaderDoList.removeIf(item -> boundOrderList.contains(item.getCombineNo()));
            if (CollectionUtils.isEmpty(sortingTaskHeaderDoList)) {
                throw new WmsOperationException("批次下所有订单都已绑定容器");
            }
            //取未绑定容器的第一个分拣任务，进行容器绑定操作
            SortingTaskHeaderDo sortingTaskHeaderDo = sortingTaskHeaderDoList.get(0);
            //进行容器占用
            occupyContainerForSorting(sortingTaskHeaderDo, containerCode);
            //保存批次和容器的关联关系
            BatchContainerBindDo batchContainerBindDo = buildBatchContainerBindDo(sortingTaskHeaderDo, containerCode);
            batchContainerBindRepository.insert(batchContainerBindDo);

            //日志中心
            OperationLogParam operationLogParam = OperationLogParam.builder()
                    .orderNo(Lists.newArrayList(batchContainerBindDo.getReferenceNo()))
                    .workLogTypeEnum(WorkLogTypeEnum.OUTBOUND_NO)
                    .build();
            wmsOutboundWorkLogService.operateLog(operationLogParam);


        } catch (WmsOperationException we) {
            log.warn("执行二次分拣绑定容器失败:{}", we);
            throw new WmsOperationException("执行二次分拣绑定容器失败" + we.getMessage());
        } catch (Exception e) {
            log.warn("执行二次分拣绑定容器失败:{}", e);
            throw new WmsOperationException("执行二次分拣绑定容器失败");
        } finally {
            if (lockFlag) {
                distributeLockFacade.releaseLockForBiz(BizLockEnum.SORTING_BATCH_LOCK, batchNo);
            }
        }

    }

    /**
     * 绑定批次和订单的关系
     *
     * @param batchNo
     * @param referenceNo
     * @param containerCode
     * @param replace       是否是换绑
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindContainerWithOrder(String batchNo, String referenceNo, String containerCode, boolean replace) {
        boolean lock = false;
        try {
            //这里有可能有并发，执行分拣的时候是对分拣任务明细加锁，绑箱的时候是对出库单加锁，两个锁不一样，可能会导致入箱的时候被换箱了
            //因为当前是一个人分拣一个批次，所以不会出现这种并发，但是理论上并发的可能性是存在的
            lock = distributeLockFacade.tryLockForBiz(BizLockEnum.DELIVERY_ORDER_LOCK, referenceNo);
            if (!lock) {
                throw new WmsOperationException("绑定订单过快，请稍后重试");
            }
            verifyContainerCode(containerCode);
            verifyLaunchBatch(batchNo);
            BatchContainerBindDo batchContainerBindDo = batchContainerBindRepository.queryByReferenceNo(batchNo, referenceNo);
            if (replace) {
                if (Objects.isNull(batchContainerBindDo)) {
                    /*换绑必须有绑定关系*/
                    throw new WmsOperationException("当前订单没有绑定关关系，不允许换绑");
                } else {
                    /*更新绑定关系*/
                    updateBoundRelation(batchContainerBindDo, containerCode);
                    /*已经存在了绑定关系，说明是换箱绑定*/
                    doReplaceBind(referenceNo, containerCode, batchContainerBindDo.getContainerCode());
                }
            } else {
                if (Objects.nonNull(batchContainerBindDo)) {
                    /*不是换绑不能有绑定关系*/
                    throw new WmsOperationException("当前订单已经绑定了容器，不允许重复绑定");
                } else {
                    /*保存绑定关系*/
                    saveBoundRelation(batchNo, referenceNo, containerCode);
                    /*直接绑定*/
                    doBind(referenceNo, containerCode);
                }
            }
            //日志中心
            OperationLogParam operationLogParam = OperationLogParam.builder()
                    .orderNo(Lists.newArrayList(referenceNo))
                    .workLogTypeEnum(WorkLogTypeEnum.OUTBOUND_NO)
                    .containerCode(containerCode)
                    .build();
            wmsOutboundWorkLogService.operateLog(operationLogParam);
        } catch (InterruptedException e) {
            log.warn("执行二次分拣绑定容器失败", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            /*将异常转成特定编码*/
            log.warn("执行二次分拣绑定容器失败:{}", e);
            throw new WmsOperationException(WmsExceptionCode.DIALOG.getCode(), e.getMessage());
        } finally {
            if (lock) {
                distributeLockFacade.releaseLockForBiz(BizLockEnum.DELIVERY_ORDER_LOCK, referenceNo);
            }
        }
    }

    /**
     * 校验容器
     *
     * @param containerCode
     */
    private void verifyContainerCode(String containerCode) {
        BasContainerResult result = null;
        try {
            result = deliveryContainerQueryService.queryBasContainer(OperationUserContextHolder.getTenantCode(), OperationUserContextHolder.get().getWarehouseCode(), containerCode);
        } catch (Exception e) {
            log.warn("请求容器服务异常", e);
            throw new WmsOperationException(WmsExceptionCode.DIALOG.getCode(), "请求容器服务异常 \n请稍后重试");
        }
        if (Objects.isNull(result)) {
            throw new WmsOperationException(WmsExceptionCode.DIALOG.getCode(), "容器号不存在 \n请扫描正确的容器号");
        }
        ContainerInstanceResult containerInstanceResult = null;
        try {
            containerInstanceResult = deliveryContainerQueryService.queryByContainerCode(OperationUserContextHolder.getTenantCode(), OperationUserContextHolder.get().getWarehouseCode(), containerCode);
        } catch (Exception e) {
            log.warn("请求容器服务异常", e);
            throw new WmsOperationException(WmsExceptionCode.DIALOG.getCode(), "请求容器服务异常 \n请稍后重试");
        }
        /*如果实例不为空，说明已经有占用了*/
        if (Objects.nonNull(containerInstanceResult)) {
            throw new WmsOperationException(WmsExceptionCode.DIALOG.getCode(), "容器号已被占用 \n请扫描正确的容器号");
        }
    }

    /**
     * 更新绑定关系
     *
     * @param batchContainerBindDo
     * @param containerCode
     */
    private void updateBoundRelation(BatchContainerBindDo batchContainerBindDo, String containerCode) {
        BatchContainerBindDo updating = new BatchContainerBindDo();
        updating.setId(batchContainerBindDo.getId());
        updating.setContainerCode(containerCode);
        updating.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        updating.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        updating.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        updating.setUpdatedTime(new Date());
        batchContainerBindRepository.updateSelective(updating);
    }

    /**
     * 保存绑定关系
     *
     * @param batchNo
     * @param referenceNo
     * @param containerCode
     */
    private void saveBoundRelation(String batchNo, String referenceNo, String containerCode) {
        LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(batchNo, OperationUserContextHolder.get().getWarehouseCode(), OperationUserContextHolder.getTenantCode());
        BatchContainerBindDo boundRelation = createOrderBindRelation(launchBatchDo, referenceNo, containerCode);
        batchContainerBindRepository.insert(boundRelation);
    }

    /**
     * 构建绑定关系
     *
     * @param launchBatchDo
     * @param referenceNo
     * @param containerCode
     * @return
     */
    private BatchContainerBindDo createOrderBindRelation(LaunchBatchDo launchBatchDo, String referenceNo, String containerCode) {
        BatchContainerBindDo batchContainerBindDo = new BatchContainerBindDo();
        batchContainerBindDo.setBatchNo(launchBatchDo.getBatchNo());
        batchContainerBindDo.setLaunchNo(launchBatchDo.getLaunchNo());
        batchContainerBindDo.setReferenceNo(referenceNo);
        batchContainerBindDo.setWarehouseCode(launchBatchDo.getWarehouseCode());
        batchContainerBindDo.setTenantCode(launchBatchDo.getTenantCode());
        batchContainerBindDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        batchContainerBindDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        batchContainerBindDo.setContainerCode(containerCode);
        return batchContainerBindDo;
    }

    /**
     * 绑定
     *
     * @param referenceNo
     * @param containerCode
     */
    private void doBind(String referenceNo, String containerCode) {
        sortOccupyContainerService.occupyContainer(referenceNo, containerCode);
    }

    /**
     * 做换绑
     *
     * @param referenceNo
     * @param containerCode
     * @param oldContainerCode
     */
    private void doReplaceBind(String referenceNo, String containerCode, String oldContainerCode) {
        if (StringUtils.isEmpty(oldContainerCode)) {
            throw new WmsOperationException("老的容器绑定关系容器号为空");
        }
        if (StringUtils.equals(containerCode, oldContainerCode)) {
            throw new WmsOperationException("换箱的容器和占用的容器相同");
        }
        sortOccupyContainerService.replaceOccupy(referenceNo, containerCode, oldContainerCode);
    }

    /**
     * 校验批次必须还没开始分拣
     *
     * @param batchNo
     */
    private void verifyLaunchBatch(String batchNo) {
        LaunchBatchDo launchBatchDo = launchBatchRepository.selectByBatchNo(batchNo, OperationUserContextHolder.get().getWarehouseCode(), OperationUserContextHolder.getTenantCode());
        if (Objects.isNull(launchBatchDo)){
            throw new WmsOperationException("批次不存在");
        }
        List<SortingTaskHeaderDo> sortingTaskHeaderDos = sortingTaskHeaderRepository.selectByBatchNo(batchNo);
        List<SortingTaskHeaderDo> executingTasks = sortingTaskHeaderDos.stream().filter(task -> !TaskStatusEnum.INIT.getStatus().equals(task.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(executingTasks)) {
            throw new WmsOperationException(WmsExceptionCode.DIALOG.getCode(), "换箱占用失败 \n当前批次已经开始分拣，不支持换箱");
        }
    }


    private void occupyContainerForSorting(SortingTaskHeaderDo sortingTaskHeaderDo, @Nullable String containerCode) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(sortingTaskHeaderDo.getCombineNo());
        if (deliveryHeaderDo == null) {
            throw new WmsOperationException("出库单头未找到");
        }
        sortOccupyContainerService.occupyContainer(deliveryHeaderDo.getDeliveryOrderCode(), containerCode);
    }


    private BatchContainerBindDo buildBatchContainerBindDo(SortingTaskHeaderDo sortingTaskHeaderDo, String containerCode) {
        BatchContainerBindDo batchContainerBindDo = new BatchContainerBindDo();
        batchContainerBindDo.setBatchNo(sortingTaskHeaderDo.getBatchNo());
        batchContainerBindDo.setLaunchNo(sortingTaskHeaderDo.getLaunchNo());
        batchContainerBindDo.setReferenceNo(sortingTaskHeaderDo.getCombineNo());
        batchContainerBindDo.setWarehouseCode(sortingTaskHeaderDo.getWarehouseCode());
        batchContainerBindDo.setTenantCode(sortingTaskHeaderDo.getTenantId());
        batchContainerBindDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        batchContainerBindDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        batchContainerBindDo.setContainerCode(containerCode);

        return batchContainerBindDo;
    }

    @Override
    public BatchBoundContainerDTO queryBatchBoundContainer(String referenceNo) {
        BatchBoundContainerDTO response = new BatchBoundContainerDTO();
        /**
         * 1、根据单号查询对应的批次号
         * 2、根据批次号查询分拣任务明细，获取批次订单数量
         * 3、根据批次号查询容器绑定关系
         *
         */
        LaunchBatchDo launchBatchDo = this.getValidBatch(referenceNo);
        //boolean useRealContainer = pickUseContainerHelper.isPickUseRealContainer(launchBatchDo.getBatchNo());
        boolean sortUseContainer = launchBatchUseContainerService.isSortUseContainer(launchBatchDo.getContainerFlag());
        if (!sortUseContainer) {
            log.info("当前仓库[{}]暂不支持使用容器进行二次分拣", OperationUserContextHolder.get().getWarehouseCode());
            response.setNeedBindContainer(false);
            response.setBatchNo(launchBatchDo.getBatchNo());
            return response;
        }
        List<SortingTaskHeaderDo> sortingTaskHeaderDoList = sortingTaskHeaderRepository.selectByBatchNo(launchBatchDo.getBatchNo());
        if (CollectionUtils.isEmpty(sortingTaskHeaderDoList)) {
            throw new WmsOperationException("分拣任务未找到");
        }
        Map<String, String> sortingOrderMap = sortingTaskHeaderDoList.stream().collect(
                Collectors.toMap(SortingTaskHeaderDo::getCombineNo, SortingTaskHeaderDo::getSortingCode));

        response.setBatchNo(launchBatchDo.getBatchNo());
        response.setOrderSize(sortingOrderMap.keySet().size());
        //查询批次&容器绑定关系表
        List<BatchContainerBindDo> batchContainerBindDoList = batchContainerBindRepository.queryByBatchNo(
                launchBatchDo.getBatchNo(), OperationUserContextHolder.get().getWarehouseCode());
        if (CollectionUtils.isEmpty(batchContainerBindDoList)) {
            response.setNeedBindContainer(true);
            response.setRelations(new ArrayList<>());
            return response;
        }
        List<BatchBoundContainerDTO.BoundRelation> bindingRelations = new ArrayList<>();
        batchContainerBindDoList.forEach(item -> {
            BatchBoundContainerDTO.BoundRelation bindingRelation = new BatchBoundContainerDTO.BoundRelation();
            bindingRelation.setReferenceNo(item.getReferenceNo());
            bindingRelation.setContainerCode(item.getContainerCode());
            bindingRelation.setSortingCode(sortingOrderMap.get(item.getReferenceNo()));

            bindingRelations.add(bindingRelation);
        });
        response.setRelations(bindingRelations);
        //批次下的订单都已绑定容器时，值为false
        boolean needBindContainer = batchContainerBindDoList.size() < sortingOrderMap.keySet().size();
        response.setNeedBindContainer(needBindContainer);

        return response;
    }

    @Override
    public Map<String, String> getSortingContainerMap(String batchNo) {
        Map<String, String> resultMap = new HashMap<>();
        //查询批次&容器绑定关系表，admin调用，不带仓库编码
        List<BatchContainerBindDo> batchContainerBindDoList = batchContainerBindRepository.queryByBatchNo(
                batchNo, null);
        if (CollectionUtils.isEmpty(batchContainerBindDoList)) {
            return resultMap;
        }
        return batchContainerBindDoList.stream().collect(Collectors.toMap(BatchContainerBindDo::getReferenceNo, BatchContainerBindDo::getContainerCode));
    }
}
