package com.poizon.scm.wms.third.receive.biz.out;

import com.poizon.scm.wms.adapter.apply.model.HandoverApplyHeaderDo;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.cis.CisInventoryOperation;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.domain.apply.HandoverApplyService;
import com.poizon.scm.wms.pojo.third.notify.request.out.NotifyOfcDeductSciRequest;
import com.poizon.scm.wms.third.receive.biz.out.executor.HandoverToOfcExecutor;
import com.poizon.scm.wms.third.receive.biz.out.executor.SciInventoryAddIntransitExecutor;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @data 2021/5/25
 */
@Service
public class HandoverDeliveryService {

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private HandoverToOfcExecutor handoverToOfcExecutor;

    @Autowired
    private HandoverApplyService handoverApplyService;

    @Autowired
    private SciInventoryAddIntransitExecutor addIntransitExecutor;

    @Resource
    private CisInventoryOperation cisInventoryOperation;

    @Value("${ck.hbfhjj.add.intransit.inventory.switch:true}")
    public boolean handoverAddSciInvFlag;

    @Value("${ck.xhjjgjj.add.intransit.inventory.switch:true}")
    public boolean xhjjgjjHandoverAddSciInvFlag;

    /**
     * 现货加价购交接处理
     * @param deliveryHeaderDo
     * @param deliveryDetailDos
     * @param tradeOrderCode
     * @param inventoryDo
     */
    @Transactional(rollbackFor = Exception.class)
    public void processHandoverDelivery(DeliveryHeaderDo deliveryHeaderDo,
                                        List<DeliveryDetailDo> deliveryDetailDos,
                                        String tradeOrderCode,
                                        InventoryDo inventoryDo){
        /*查询交接信息*/
        HandoverApplyHeaderDo handoverApplyHeaderDo = handoverApplyService.selectByReferenceNo(tradeOrderCode);
        if (Objects.isNull(handoverApplyHeaderDo)) {
            throw new WmsException(WmsExceptionCode.HANDOVER_INFO_NOT_EXIST);
        }
        /*通知ofc*/
        handoverToOfcExecutor.xhjjgHandover(handoverApplyHeaderDo,inventoryDo);
        //现货加价购交接增加sci在途库存开关, 默认开启, 上线后关闭
        if (xhjjgjjHandoverAddSciInvFlag) {
            /*增加在途库存V2*/
            addIntransitExecutor.xhjjgAddScInTransitInventory(deliveryHeaderDo,
                    deliveryDetailDos, inventoryDo, handoverApplyHeaderDo.getWarehouseCode());
        }
    }

    /**
     * 合并发货交接处理
     * @param deliveryCode
     * @param requestMessage
     */
    @Transactional(rollbackFor = Exception.class)
    public void processHandoverDelivery(String deliveryCode, NotifyOfcDeductSciRequest requestMessage){
        /*
        * 更新单据状态为集货出库
        * 需要集货的单据最终状态为50，且没有后续流程了，所以需要把交接时间更新为集货完成时间
        *
        * */
        deliveryHeaderRepository.updateStatusAndTimeByDeliveryCode(deliveryCode, WmsOutBoundStatusEnum.COLLECTION_OUT.getStatus(),new Date());
        /*查询交接信息*/
        HandoverApplyHeaderDo handoverApplyHeaderDo = handoverApplyService.selectByReferenceNo(deliveryCode);
        if (Objects.isNull(handoverApplyHeaderDo)) {
            throw new WmsException(WmsExceptionCode.HANDOVER_INFO_NOT_EXIST);
        }
        /*通知ofc*/
        handoverToOfcExecutor.handover(handoverApplyHeaderDo,requestMessage);
        /*扣减sci库存*/
        cisInventoryOperation.sendSubtractSciInventoryMsg(cisInventoryOperation.buildCisSubRequest(requestMessage));
        //合并发货交接增加sci在途库存开关, 默认开启, 上线后关闭
        if (handoverAddSciInvFlag) {
            addIntransitExecutor.addScInTransitInventoryV2(handoverApplyHeaderDo.getWarehouseCode(),requestMessage);
        }
    }
}
