package com.poizon.scm.wms.service.delivery.timeout;

import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyRuleQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyRuleDo;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.alarm.AlarmRuleExecutionTimeUpdateProcessor;
import com.poizon.scm.wms.util.enums.AlarmStrategyTypeEnum;
import com.poizon.scm.wms.util.enums.IsActiveEnum;
import com.poizon.scm.wms.util.util.DateUtils;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import com.shizhuang.duapp.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.poizon.scm.wms.common.utils.DateUtils.FORMAT_TIME;

/**
 * 出库预警service
 * <AUTHOR>
 * @date 2022/12/28
 */
@Slf4j
@Component
public class AlarmOutDeliveryService {

    @Autowired
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Autowired
    private AlarmStrategyRuleQueryRepository alarmStrategyRuleQueryRepository;

    @Autowired
    private TimeoutDeliveryService timeoutDeliveryService;
    @Autowired
    private AlarmRuleExecutionTimeUpdateProcessor alarmRuleExecutionTimeUpdateProcessor;
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private Executor asyncServiceExecutor;

    /**
     * 根据预警规则查询处理超时订单
     */
    public void searchAndProcessByAlarmRule() {
        log.info("开始根据预警规则查询处理超时订单");
        //查询启用的出库预警策略
        List<AlarmStrategyDo> alarmStrategyDos = alarmStrategyQueryRepository.queryStrategyByStatusAndType(AlarmStrategyTypeEnum.DELIVERY_TIMEOUT.getCode(),
                IsActiveEnum.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(alarmStrategyDos)) {
            log.warn("出库超时预警没有配置有效策略");
            return;
        }

        //查询所有启用的规则
        List<String> strategyNos = alarmStrategyDos.stream().map(AlarmStrategyDo::getStrategyNo).collect(Collectors.toList());
        List<AlarmStrategyRuleDo> alarmStrategyRuleDos = alarmStrategyRuleQueryRepository.queryEnableRuleByStrategyNos(strategyNos);
        if (CollectionUtils.isEmpty(alarmStrategyRuleDos)) {
            log.warn("出库超时预警没有配置有效规则");
            return;
        }

        //按策略（仓）分组
        Map<String, List<AlarmStrategyRuleDo>> rulesGroupByStrategyNo = alarmStrategyRuleDos.stream().collect(
                Collectors.groupingBy(AlarmStrategyRuleDo::getStrategyNo));

        //遍历每个策略下的规则
        rulesGroupByStrategyNo.forEach((key,value) -> {

            //设置上下文
            setContext(value.get(0));

            boolean lockFlag = false;
            try {
                lockFlag = distributeLockUtil.tryLockForBizNoWaitTime(LockEnum.ALARM_OUT_DELIVERY_LOCK, key);
                if (!lockFlag) {
                    throw new WmsOperationException("出库预警执行失败, 当前策略正在执行");
                }

                //按下次执行时间排序，近的先执行
                List<AlarmStrategyRuleDo> sortedAlarmStrategyRuleDos = value.stream().sorted(Comparator.comparing(
                        AlarmStrategyRuleDo::getNextExecutionTime)).collect(Collectors.toList());

                //遍历规则
                for (AlarmStrategyRuleDo ruleDo : sortedAlarmStrategyRuleDos) {
                    //获取规则的下次执行时间
                    Date nextExecutionTime = ruleDo.getNextExecutionTime();
                    Date currentDate = new Date();
                    //与当前时间比较, 如果小于等于当前时间则执行改规则
                    if (nextExecutionTime != null && currentDate.compareTo(nextExecutionTime) < 0) {
                        log.info("出库超时告警规则[{}]未到执行时间, 当前时间:{}, 下次执行时间:{}", ruleDo.getRuleNo(),
                                DateUtils.formatDate(currentDate, FORMAT_TIME),
                                DateUtils.formatDate(ruleDo.getNextExecutionTime(), FORMAT_TIME));
                        continue;
                    }

                    //更新下次执行时间
                    alarmRuleExecutionTimeUpdateProcessor.updateRuleNextExecuteTimeByRuleNo(ruleDo, nextExecutionTime);
                    asyncServiceExecutor.execute(() ->{
                            //执行本次任务,  AlarmRuleContent 和 DeliveryTimeoutRequest.queryDetails 字段对应
                         String queryJson = ruleDo.getRuleContent();
                    timeoutDeliveryService.searchAndProcessTimeoutDeliveryOrderV2(queryJson,
                            ruleDo.getWarehouseCode(),ruleDo.getTenantCode()) ;
                    });

                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("出库预警执行失败", e);
            } catch (WmsOperationException e) {
                log.warn("出库预警执行失败, 当前策略正在执行", e);
            } catch (Exception e) {
                log.error("出库预警执行失败", e);
            } finally {
                if (lockFlag) {
                    distributeLockUtil.releaseLockForBiz(LockEnum.ALARM_OUT_DELIVERY_LOCK, key);
                }
                OperationUserContextHolder.clear();
            }

        });

    }

    private void setContext(AlarmStrategyRuleDo ruleDo) {
        OperationUserContext userContext = OperationUserContext.builder()
                .tenantCode(ruleDo.getTenantCode())
                .userId(0L)
                .realName("SYSTEM")
                .userName("SYSTEM")
                .warehouseCode(ruleDo.getWarehouseCode())
                .build();
        OperationUserContextHolder.set(userContext);
        TenantContext.setContextId(ruleDo.getTenantCode());
    }
}

