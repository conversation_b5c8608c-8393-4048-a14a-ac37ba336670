package com.poizon.scm.wms.transfer.data.delivery;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.common.TaskDetailRepository;
import com.poizon.scm.wms.adapter.common.model.TaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackDetailRepository;
import com.poizon.scm.wms.adapter.transfer.model.TransferDataParam;
import com.poizon.scm.wms.adapter.transfer.repository.DeliveryTransferRepository;
import com.poizon.scm.wms.adapter.transfer.repository.TaskTransferRepository;
import com.poizon.scm.wms.adapter.transfer.repository.TransferDeliveryRepository;
import com.poizon.scm.wms.transfer.data.param.TransferStrategyParam;
import com.poizon.scm.wms.transfer.data.task.TaskTransferService;
import com.poizon.scm.wms.util.enums.TaskTypeEnum;
import com.poizon.scm.wms.util.util.DateUtils;
import com.shizhuang.duapp.tenant.TenantContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DeliveryDataTransferService {

    @Autowired
    private TaskDetailRepository taskDetailRepository;

    @Autowired
    private DeliveryTransferRepository deliveryTransferRepository;

    @Autowired
    private TaskTransferRepository taskTransferRepository;

    @Autowired
    private TransferDeliveryRepository transferDeliveryRepository;

    @Autowired
    private TaskTransferService taskTransferService;

    @Autowired
    private WmsPackDetailRepository wmsPackDetailRepository;
    /**
     * 默认最大大小
     */
    private static final Integer DEFAULT_MAX_BATCH_SIZE = 1000;
    /**
     * 设置两个半月前数据
     *
     * 0217 设置2个月前数据
     */
    private static final Integer DEFAULT_SAVE_DAY = 60;

    /**
     * 设置一次job最多删除10w行
     */
    private static final Integer DEFAULT_MAX_DEL_NUM = 100000;


    @Value("${delivery.transfer.sleepTime:100}")
    @Setter
    private long SLEEP_TIME_MS;

    /**
     * 需要删除原始订单号信息的仓库
     */
    @Value("${delivery.transfer.back_to_original.warehouseCodes:}")
    @Setter
    private List<String> warehouseCodes;

    /**
     * 每次查询id分页的大小
     */
    @Value("${delivery.transfer.back_to_original.idLimit:200}")
    @Setter
    private int idLimit;

    /**
     * 执行迁移数据
     *
     * @param strategyDTO
     */
    public void executeTransfer(TransferStrategyParam strategyDTO) {
        log.info("..............................begin execute delivery data transfer ..............................");
        try {
            batchDelOldData();
            /*获取本次需要清除的入库单数据*/
            List<DeliveryHeaderDo> deliveryHeaderDoList = queryDeliveryForTransfer(strategyDTO);
            if (CollectionUtils.isEmpty(deliveryHeaderDoList)) {
                log.info("本次需要清除的出库单数据为空,无需任何操作");
                return;
            }
            log.info("本次准备清除出库单数量, size -> [{}]", deliveryHeaderDoList.size());
            // 批量归档
            Map<String, List<DeliveryHeaderDo>> deliveryHeaderMap = deliveryHeaderDoList.stream().collect(Collectors.groupingBy(item -> item.getTenantCode()));
            deliveryHeaderMap.forEach((tenantCode, value) -> {
                List<List<DeliveryHeaderDo>> splitDeliveryHeaderList = Lists.partition(value, strategyDTO.getBatchDelBillSize());
                for (List<DeliveryHeaderDo> items : splitDeliveryHeaderList) {
                    // 批量删除
                    batchDelDeliveryHeader(strategyDTO, tenantCode, items);
                    try {
                        Thread.sleep(SLEEP_TIME_MS);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("del 删除出库数据线程中断：{}", JSON.toJSONString(items), e);
                    }
                }
            });
        } catch (Exception ex) {
            log.error("出库单数据迁移失败", ex);
        }
        log.info("..............................end execute delivery data transfer ..............................");
    }

    /**
     *
     * 批量删除以前未归档的数
     * 每次job单表最大删一万条，批量删除
     */
    private void batchDelOldData(){
        /**
         *
         * 	一次清理500条，20次=1w条，一天144w。
         */
        boolean isDelProcessTime =true;
        for (int i =0;i<20;i++) {
            if(isDelProcessTime){
                //每次删除500出库单关联表信息(500出库单不一定能对应500行关联信息)，直到删除影响行数为0为止。
              int count = deliveryTransferRepository.batchOldDeliveryExtraInfo();
                isDelProcessTime = count != 0;
            }else{
                log.info("重要的事情说三遍，DeliveryExtraInfo历史数据已全部删除完毕,可以删除此相关代码");
            }
            try {
                Thread.sleep(SLEEP_TIME_MS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("del 删除出库数据线程中断：{}", e);
            }
        }

    }
    /**
     * 获取需要迁移的出库单
     *
     * @return
     */
    private List<DeliveryHeaderDo> queryDeliveryForTransfer(TransferStrategyParam strategyDTO) {
        List<DeliveryHeaderDo> deliveryHeaderDos = new ArrayList<>();

        /*2.执行不同的策略*/
        TransferDataParam transferDataParam = new TransferDataParam();
        /*设置默认的条件*/
        transferDataParam.setMaxBatchSize(DEFAULT_MAX_BATCH_SIZE);
        // 默认保留时间 100 天
        Date defaultSaveDate = DateUtils.beforeDate(new Date(), DEFAULT_SAVE_DAY);
        transferDataParam.setTransferSaveTime(defaultSaveDate);
        /*2.执行不同的策略*/
        if (null != strategyDTO) {
            transferDataParam.setMaxDelNum(strategyDTO.getMaxDelNum());
            if (CollectionUtils.isNotEmpty(strategyDTO.getReferenceNos())) {
                // 1. 优先使用单号进行迁移
                transferDataParam.setReferenceNos(strategyDTO.getReferenceNos());
            } else if (null != strategyDTO.getTransferStartTime() && null != strategyDTO.getTransferEndTime()) {
                // 2. 使用指定时间段进行迁移
                transferDataParam.setTransferStartTime(strategyDTO.getTransferStartTime());
                transferDataParam.setTransferEndTime(strategyDTO.getTransferEndTime());
            } else {
                // 使用默认策略进行迁移
            }
            if (null != strategyDTO.getMaxBatchSize() && DEFAULT_MAX_BATCH_SIZE > strategyDTO.getMaxBatchSize()) {
                transferDataParam.setMaxBatchSize(strategyDTO.getMaxBatchSize());
            }
            // 加上拦截,强制保留75天
            if (null != strategyDTO.getSaveDays() && strategyDTO.getSaveDays() > DEFAULT_SAVE_DAY) {
                Date saveDate = DateUtils.beforeDate(new Date(), strategyDTO.getSaveDays());
                transferDataParam.setTransferSaveTime(saveDate);
            }
            /**
             * 设置超过10w，则改为默认值，保护可能因为配置有误导致的db负载过高
             */
            if (transferDataParam.getMaxDelNum() > DEFAULT_MAX_DEL_NUM) {
                transferDataParam.setMaxDelNum(0);
            }
        }

        /*查询出库单*/
        log.info("查询单据参数, transferDataParam -> [{}]", JSON.toJSONString(transferDataParam));
        int count = 0;
        for (; ; ) {
            List<DeliveryHeaderDo> list = this.transferDeliveryRepository.queryDeliveryForTransfer(transferDataParam);
            count += list.size();
            deliveryHeaderDos.addAll(list);
            if (list.size() != transferDataParam.getMaxBatchSize() || count >= transferDataParam.getMaxDelNum()) {
                break;
            }
            transferDataParam.setId(list.get(list.size() - 1).getId());
        }


        return deliveryHeaderDos;
    }

    /**
     * 批量进行归档
     *
     * @param strategyParam
     * @param tenantCode
     * @param items
     */
    private void batchDelDeliveryHeader(TransferStrategyParam strategyParam, String tenantCode, List<DeliveryHeaderDo> items) {
        List<String> deliveryOrderCodes = items.stream().map(DeliveryHeaderDo::getDeliveryOrderCode).collect(Collectors.toList());
        try {
            /*0. 设置上下文租户*/
            TenantContext.setContextId(tenantCode);
            log.info("准备清除 出库单&关联的任务 数据, deliveryOrderCodes -> {}", JSON.toJSONString(deliveryOrderCodes));
            /*1. 清除出库单关联的任务数据*/
            batchClearDeliveryRelationTaskData(deliveryOrderCodes, strategyParam.getBatchDelSize());
            /*2. 清除单据属数据*/
            batchClearDeliveryData(deliveryOrderCodes, strategyParam.getBatchDelSize());
            log.info("完成清除 出库单&关联的任务 数据, deliveryOrderCodes -> {}", JSON.toJSONString(deliveryOrderCodes));
        } catch (Exception ex) {
            log.error("清除出库单的数据失败, items -> {}", JSON.toJSONString(deliveryOrderCodes));
            log.info("清除出库单的数据失败", ex);
        } finally {
            TenantContext.clear();
        }
    }

    /**
     * 批量删除出库单关联数据
     *
     * @param deliveryOrderCodes
     * @param batchDelSize
     */
    private void batchClearDeliveryRelationTaskData(List<String> deliveryOrderCodes, Integer batchDelSize) {
        /*1. 清除发货任务*/
        taskTransferService.clearTaskData(TaskTypeEnum.SHIP, deliveryOrderCodes, batchDelSize);

        /*2. 清除拣货任务*/
        batchClearPickTask(deliveryOrderCodes, batchDelSize);
    }

    /**
     * 批量删除拣货任务
     *
     * @param deliveryOrderCodes
     * @param batchDelSize
     */
    private void batchClearPickTask(List<String> deliveryOrderCodes, Integer batchDelSize) {
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.PICK;
        List<TaskDetailDo> pickTaskDetailDoList = taskTransferRepository.queryDetailByReferences(deliveryOrderCodes, taskTypeEnum.getTaskType());
        if (CollectionUtils.isEmpty(pickTaskDetailDoList)) {
            log.info("出库单对应的拣货任务为空, deliveryOrderCodes -> {}", JSON.toJSONString(deliveryOrderCodes));
            return;
        }
        Set<String> taskHeaderNos = pickTaskDetailDoList.stream().map(entity -> entity.getTaskNo()).collect(Collectors.toSet());
        List<List<TaskDetailDo>> splitDetailDoList = Lists.partition(pickTaskDetailDoList, batchDelSize);
        splitDetailDoList.forEach(items -> {
            List<String> taskDetailNos = items.stream().map(entity -> entity.getDetailNo()).collect(Collectors.toList());
            /*1. 批量删除拣货任务结果*/
            while (true) {
                int cResultCount = this.taskTransferRepository.batchDelResultByTaskDetailNo(taskDetailNos, taskTypeEnum.getTaskType(), batchDelSize);
                log.info("清除taskDetailNos -> {}, taskType -> [{}] 对应的任务结果数据, cResultCount -> [{}]",
                        JSON.toJSONString(taskDetailNos), taskTypeEnum.getTaskName(), cResultCount);
                if (0 == cResultCount || cResultCount < batchDelSize) {
                    break;
                }
            }
            /*2. 删除拣货任务明细*/
            log.info("清除taskDetailNos -> {}, taskType -> [{}] 对应的任务结果数据", JSON.toJSONString(taskDetailNos), taskTypeEnum.getTaskName());
            taskTransferRepository.batchDelDetailByTaskDetailNo(taskDetailNos, TaskTypeEnum.PICK.getTaskType());
        });

        /*3. 删除拣货任务头*/
        //批量删除完成的拣货任务
        List<List<String>> taskHeaderLists = Lists.partition(new ArrayList<>(taskHeaderNos), batchDelSize);
        taskHeaderLists.forEach(list->{
            //查询任务头
            List<String> finishTaskNos = taskTransferRepository.selectFinishStatusByTaskNos(list);
            log.info("清除拣货任务数据, taskNo -> [{}]",JSON.toJSONString(finishTaskNos));
            if(CollectionUtils.isNotEmpty(finishTaskNos)) {
               int count = taskTransferRepository.deletePickExtByTaskNos(finishTaskNos);
                log.info("清除拣货任务扩展数据数量, count -> [{}]",count);
               count = taskTransferRepository.deleteFinishStatusHeaderByTaskNo(finishTaskNos);
                log.info("清除拣货任务头数据数量, count -> [{}]",count);
            }
        });
    }

    /**
     * 批量删除出库单数据
     *
     * @param deliveryOrderCodes
     * @param maxDelSize
     */
    private void batchClearDeliveryData(List<String> deliveryOrderCodes, Integer maxDelSize) {
        /*1. 清除单据allocate数据*/
        while (true) {
            int rAllocateCount = deliveryTransferRepository.batchDelDeliveryAllocate(deliveryOrderCodes, maxDelSize);
            log.info("清除Delivery Allocate 表的数据, deliveryOrderCodes -> {}, rAllocateCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rAllocateCount);
            if (rAllocateCount == 0 || rAllocateCount < maxDelSize) {
                break;
            }
        }
        /*2. 清除单据Tag数据*/
        while (true) {
            int rTagCount = deliveryTransferRepository.batchDelDeliveryTag(deliveryOrderCodes, maxDelSize);
            log.info("清除Delivery Tag 表的数据, deliveryOrderCodes -> {}, rTagCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rTagCount);
            if (rTagCount == 0 || rTagCount < maxDelSize) {
                break;
            }
        }

        /*3. 清除单据user数据*/
        while (true) {
            int rUserCount = deliveryTransferRepository.batchDelDeliveryUser(deliveryOrderCodes, maxDelSize);
            log.info("清除Delivery User 表的数据, deliveryOrderCodes -> {}, rUserCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rUserCount);
            if (rUserCount == 0 || rUserCount < maxDelSize) {
                break;
            }
        }

        /*4. 清除单据状态数据*/
        while (true) {
            int rStatusCount = deliveryTransferRepository.batchDelDeliveryStatus(deliveryOrderCodes, maxDelSize);
            log.info("清除Delivery status 表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rStatusCount);
            if (rStatusCount == 0 || rStatusCount < maxDelSize) {
                break;
            }
        }
        /*5. 清除单据关联订单表数据*/
        while (true) {
            int rCount = deliveryTransferRepository.batchDeliveryRelatedOrders(deliveryOrderCodes, maxDelSize);
            log.info("清除DeliveryRelatedOrders 表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rCount);
            if (rCount == 0 || rCount < maxDelSize) {
                break;
            }
        }
        /*6. 清除单据物流公司信息数据*/
        while (true) {
            int rCount = deliveryTransferRepository.batchDeliveryLogistic(deliveryOrderCodes, maxDelSize);
            log.info("清除batchDeliveryLogistic 表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rCount);
            if (rCount == 0 || rCount < maxDelSize) {
                break;
            }
        }
        /** 清除物流下单信息表数据 */
        while (true) {
            int rCount = deliveryTransferRepository.batchDelWmsLogisticsBill(deliveryOrderCodes, maxDelSize);
            log.info("清除wms_logistics_bill 表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rCount);
            if (rCount == 0 || rCount < maxDelSize) {
                break;
            }
        }
        /*7. 清除单据出库及时率数据*/
//        while (true) {
//            int rCount = deliveryTransferRepository.batchDeliveryTimelyRateNew(deliveryOrderCodes, maxDelSize);
//            log.info("清除batchDeliveryTimelyRateNew表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
//                    JSON.toJSONString(deliveryOrderCodes), rCount);
//            if (rCount == 0 || rCount < maxDelSize) {
//                break;
//            }
//        }
        /* 清除单据发货异常数据*/
        while (true) {
            int rCount = deliveryTransferRepository.batchShipAbnormalReport(deliveryOrderCodes, maxDelSize);
            log.info("清除batchShipAbnormalReport表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rCount);
            if (rCount == 0 || rCount < maxDelSize) {
                break;
            }
        }
        /* 清除单据出库delivery_process_time数据*/
//        while (true) {
//            int rCount = deliveryTransferRepository.batchDeliveryProcessTime(deliveryOrderCodes, maxDelSize);
//            log.info("清除batchDeliveryProcessTime表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
//                    JSON.toJSONString(deliveryOrderCodes), rCount);
//            if (rCount == 0 || rCount < maxDelSize) {
//                break;
//            }
//        }
        /*7. 清除单据明细数据*/
        while (true) {
            int rDetailCount = deliveryTransferRepository.batchDelDeliveryDetail(deliveryOrderCodes, maxDelSize);
            log.info("清除Delivery Detail 表的数据, deliveryOrderCodes -> {}, rDetailCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rDetailCount);
            if (rDetailCount == 0 || rDetailCount < maxDelSize) {
                break;
            }
        }
        List<String> packNos = wmsPackDetailRepository.queryPackNosByDeliveryOrderCodesIncludeDeleted(deliveryOrderCodes);
        if(CollectionUtils.isNotEmpty(packNos)){
            /*8. 清除包裹头数据*/
            while (true) {
                int rDetailCount =  deliveryTransferRepository.batchDelWmsPack(packNos,maxDelSize);
                log.info("清除wms_pack 表的数据, deliveryOrderCodes -> {}, rDetailCount -> [{}]",
                        JSON.toJSONString(deliveryOrderCodes), rDetailCount);
                if (rDetailCount == 0 || rDetailCount < maxDelSize) {
                    break;
                }
            }
            /*9. 清除包裹明细数据*/
            while (true) {
                int rDetailCount =   deliveryTransferRepository.batchDelWmsPackDetail(packNos,maxDelSize);
                log.info("清除wms_pack 表的数据, deliveryOrderCodes -> {}, rDetailCount -> [{}]",
                        JSON.toJSONString(deliveryOrderCodes), rDetailCount);
                if (rDetailCount == 0 || rDetailCount < maxDelSize) {
                    break;
                }
            }

            /*9. 清除包裹明细SN关联表数据*/
            while (true) {
                int rDetailCount =   deliveryTransferRepository.batchDelWmsPackDetailSnRelation(packNos,maxDelSize);
                log.info("清除wms_pack_detail_sn_relation 表的数据, deliveryOrderCodes -> {}, rDetailCount -> [{}]",
                        JSON.toJSONString(deliveryOrderCodes), rDetailCount);
                if (rDetailCount == 0 || rDetailCount < maxDelSize) {
                    break;
                }
            }

        }
        /* 清除单据出库delivery_extra_info数据*/
        while (true) {
            int rCount = deliveryTransferRepository.batchDelDeliveryExtraInfo(deliveryOrderCodes, maxDelSize);
            log.info("清除batchDelDeliveryExtraInfo表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rCount);
            if (rCount == 0 || rCount < maxDelSize) {
                break;
            }
        }
        /* 清除delivery_each_flow_record表的数据*/
        while (true) {
            int rCount = deliveryTransferRepository.batchDelDeliveryEachFlowRecord(deliveryOrderCodes, maxDelSize);
            log.info("清除delivery_each_flow_record表的数据, deliveryOrderCodes -> {}, rStatusCount -> [{}]",
                    JSON.toJSONString(deliveryOrderCodes), rCount);
            if (rCount == 0 || rCount < maxDelSize) {
                break;
            }
        }
        /*8. 清除单据头数据*/
        log.info("清除Delivery Header 表的数据, deliveryOrderCodes -> {}", JSON.toJSONString(deliveryOrderCodes));
        deliveryTransferRepository.batchDelDeliveryHeader(deliveryOrderCodes);
    }
}
