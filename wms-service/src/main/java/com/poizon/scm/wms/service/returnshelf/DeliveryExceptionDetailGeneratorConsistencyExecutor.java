package com.poizon.scm.wms.service.returnshelf;

import com.dewu.executor.annotation.EventualConsistency;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * date 2021-01-20
 */
@Slf4j
@Component
public class DeliveryExceptionDetailGeneratorConsistencyExecutor {

    @Autowired
    private DeliveryExceptionDetailGenerator deliveryExceptionDetailGenerator;

    @EventualConsistency(label = "DeliveryExceptionDetail", referenceNo = "#deliveryOrderCode")
    public void execute(String deliveryOrderCode) {
        log.info("deliveryOrderCode={},start execute", deliveryOrderCode);
        deliveryExceptionDetailGenerator.generateDeliveryExceptionDetail(deliveryOrderCode, null);
    }
}
