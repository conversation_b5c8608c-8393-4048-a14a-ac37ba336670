package com.poizon.scm.wms.service.delivery.operate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.poizon.scm.wms.adapter.exception.model.rpc.ExceptionInfoSimpleDto;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionInfoRpcRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryHeaderDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryHeaderRepository;
import com.poizon.scm.wms.api.dto.qimen.api.request.WmsDeliveryCreateRequest;
import com.poizon.scm.wms.api.enums.WmsQualityLevelEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.constant.WmsCommonConstant;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.dao.entitys.InvInventoryEntity;
import com.poizon.scm.wms.domain.flow.worker.bill.ImmediateShipFlowWorker;
import com.poizon.scm.wms.domain.inventory.query.InventoryQuery;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBill;
import com.poizon.scm.wms.domain.outbound.outbound.entity.WmsDeliveryBillDetail;
import com.poizon.scm.wms.domain.outbound.producer.OutboundMessageProducer;
import com.poizon.scm.wms.domain.outbound.producer.entity.OutboundDto;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipDetailPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipHeaderPojo;
import com.poizon.scm.wms.pojo.delivery.DeliveryShipPojo;
import com.poizon.scm.wms.pojo.inventory.InventoryBasePojo;
import com.poizon.scm.wms.pojo.inventory.InventoryDimensionPojo;
import com.poizon.scm.wms.pojo.task.operation.create.BaseCreateTaskDetailPojo;
import com.poizon.scm.wms.pojo.task.operation.create.BaseCreateTaskPojo;
import com.poizon.scm.wms.service.config.WmsDQConfig;
import com.poizon.scm.wms.service.delivery.operate.DeliveryOrderModifyService;
import com.poizon.scm.wms.service.inventory.operate.InventoryOperateService;
import com.poizon.scm.wms.util.enums.InventoryTypeEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundStatusEnum;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import com.poizon.scm.wms.util.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 特殊出库
 * 承接 个人寄存退货 或者 95分出库,不需要进行库存分配,直接从虚拟库位上减去库存
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/18 6:42 下午
 */
@Slf4j
@Component
public class SpecialDeliveryService {

    @Autowired
    private InventoryQuery inventoryQuery;

    @Autowired
    private InventoryOperateService inventoryOperateService;

    @Autowired
    private DeliveryOrderModifyService deliveryOrderModifyService;

    @Autowired
    private ImmediateShipFlowWorker immediateShipFlowWorker;

    @Autowired
    private WmsDQConfig wmsDQConfig;

    @Autowired
    private DeliveryHeaderRepository deliveryHeaderRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Resource
    private OutboundMessageProducer outboundMessageProducer;

    @Resource
    private WmsExceptionInfoRpcRepository wmsExceptionInfoRpcRepository;


    /**
     * 校验直接出库信息参数
     *
     * @param delivery
     * @param virtualReturnDelivery
     */
    private void checkSpecialDirectDelivery(WmsDeliveryBill delivery, boolean virtualReturnDelivery) {
        // 校验直接出库信息参数
        delivery.getWmsDeliveryBillDetails().stream().forEach(entity -> {
            if (StringUtils.isEmpty(entity.getOrderLineNo())) {
                log.error("单据明细编号为空, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY_DETAIL_NO_IS_NULL);
            }
            if (StringUtils.isEmpty(entity.getUniqueCode())) {
                log.error("出库单唯一码不存在, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.UNIQUE_CODE_NULL);
            }
            if (null == entity.getPlanQty()) {
                log.error("出库数量为空, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.QTY_IS_NULL);
            }
            if (StringUtils.isEmpty(entity.getQualityLevel())) {
                log.error("出库单质量等为空, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.QUALITY_LEVEL_NULL);
            }
            //越库出标记  不需要下面的记录
            if (Boolean.TRUE.equals(entity.getNeedCrossDockingFlag())) {
                return;
            }
            //  强制判断次品
            if (!virtualReturnDelivery && !wmsDQConfig.getUnPassQualityLevel().equals(entity.getQualityLevel())) {
                log.error("出库单质量等级错误, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.DELIVERY_QUALITY_LEVEL_ERROR);
            }
        });
    }


    /**
     * 校验直接出库信息参数
     *
     * @param wmsDeliveryInfo
     */
    private void checkSpecialDirectDelivery(WmsDeliveryCreateRequest wmsDeliveryInfo) {
        // 校验直接出库信息参数
        wmsDeliveryInfo.getOrderLines().stream().forEach(entity -> {
            if (StringUtils.isEmpty(entity.getOrderLineNo())) {
                log.error("单据明细编号为空, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.PARAMS_IS_EMPTY_DETAIL_NO_IS_NULL);
            }
            if (StringUtils.isEmpty(entity.getUniqueCode())) {
                log.error("出库单唯一码不存在, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.UNIQUE_CODE_NULL);
            }
            if (null == entity.getPlanQty()) {
                log.error("出库数量为空, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.QTY_IS_NULL);
            }
            if (StringUtils.isEmpty(entity.getQualityLevel())) {
                log.error("出库单质量等为空, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.QUALITY_LEVEL_NULL);
            }
            //  强制判断次品
            if (!wmsDQConfig.getUnPassQualityLevel().equals(entity.getQualityLevel())) {
                log.error("出库单质量等级错误, entity -> [{}]", JSON.toJSONString(entity));
                throw new WmsException(WmsExceptionCode.DELIVERY_QUALITY_LEVEL_ERROR);
            }
        });
    }

    /**
     * 执行直接发货处理
     *
     * @param wmsDeliveryInfo
     */
    @Deprecated
    public void execute(WmsDeliveryCreateRequest wmsDeliveryInfo) {
        log.info("单据直接出库,wmsDeliveryInfo -> [{}]", JSON.toJSONString(wmsDeliveryInfo));

        // 进行数据校验
        checkSpecialDirectDelivery(wmsDeliveryInfo);

        // 唯一码订单Map
        Map<String, WmsDeliveryCreateRequest.OrderLine> uniqueCodeOrderMap = wmsDeliveryInfo.getOrderLines()
                .stream().collect(Collectors.toMap(WmsDeliveryCreateRequest.OrderLine::getUniqueCode, Function.identity()));

        // 查询库存信息
        List<InvInventoryEntity> invInventoryList = this.inventoryQuery.queryInventoryByUniqueCodes(wmsDeliveryInfo.getDeliveryOrder().getWarehouseCode(),OperationUserContextHolder.getTenantCode(), uniqueCodeOrderMap.keySet());
        if (CollectionUtils.isEmpty(invInventoryList) || invInventoryList.size() != uniqueCodeOrderMap.keySet().size()) {
            // 抛出库存查询异常
            log.error("唯一码查询商品错误,uniqueCodes -> {}", JSON.toJSONString(uniqueCodeOrderMap.keySet()));
            throw new WmsException(WmsExceptionCode.DELIVERY_CREATE_ERROR);
        }
        // 按照唯一码进行分组
        Map<String, InvInventoryEntity> invInventoryEntityMap = invInventoryList.stream().collect(Collectors.toMap(InvInventoryEntity::getUniqueCode, Function.identity()));

        // 遍历进行处理
        for (Map.Entry<String, WmsDeliveryCreateRequest.OrderLine> entry : uniqueCodeOrderMap.entrySet()) {
            String uniqueCode = entry.getKey();
            WmsDeliveryCreateRequest.OrderLine orderDetailInfo = entry.getValue();

            // 校验库存信息
            InvInventoryEntity invInventoryEntity = invInventoryEntityMap.get(uniqueCode);
            checkUniqueCodeInv(uniqueCode, orderDetailInfo, invInventoryEntity);

            // 执行库存进行发货
            doExecuteInventoryShip(wmsDeliveryInfo, orderDetailInfo, invInventoryEntity);
        }

        // 修改单据为出库状态
//        modifyDeliveryToOuted(wmsDeliveryInfo.getDeliveryOrder().getDeliveryOrderCode());
        notifyDeliveryOrderOuted(wmsDeliveryInfo);
    }

    /**
     * 执行库存进行发货
     *
     * @param wmsDeliveryInfo
     * @param orderDetailInfo
     * @param invInventoryEntity
     */
    @Deprecated
    private void doExecuteInventoryShip(WmsDeliveryCreateRequest wmsDeliveryInfo, WmsDeliveryCreateRequest.OrderLine orderDetailInfo, InvInventoryEntity invInventoryEntity) {
        // 构建发货操作
        InventoryBasePojo shipInventoryBasePojo = new InventoryBasePojo();
        // 骚操作,设置任务为空
        shipInventoryBasePojo.setTaskType(WmsCommonConstant.EMPTY);
        shipInventoryBasePojo.setTaskNo(WmsCommonConstant.EMPTY);
        shipInventoryBasePojo.setTaskDetailNo(WmsCommonConstant.EMPTY);
        // 设置发货维度
        shipInventoryBasePojo.setTenantCode(OperationUserContextHolder.getTenantCode());
        shipInventoryBasePojo.setOperateQty(orderDetailInfo.getPlanQty());
        shipInventoryBasePojo.setReferenceType(wmsDeliveryInfo.getDeliveryOrder().getOrderType());
        shipInventoryBasePojo.setReferenceNo(wmsDeliveryInfo.getDeliveryOrder().getDeliveryOrderCode());
        shipInventoryBasePojo.setRelativeInventoryNo(invInventoryEntity.getInventoryNo());
        shipInventoryBasePojo.setReferenceNo(invInventoryEntity.getRelatedOrderCode());

        // 同步对库存进行发货处理
        log.info("库存进行发货处理,shipInventoryBasePojo -> [{}]", JSON.toJSONString(shipInventoryBasePojo));
        inventoryOperateService.ship(shipInventoryBasePojo);
    }

    /**
     * 通知单据为出库完成
     *
     * @param wmsDeliveryInfo
     */
    @Deprecated
    private void notifyDeliveryOrderOuted(WmsDeliveryCreateRequest wmsDeliveryInfo) {

        /*出库单头*/
        WmsDeliveryCreateRequest.DeliveryOrder deliveryOrder = wmsDeliveryInfo.getDeliveryOrder();
        DeliveryShipHeaderPojo shipHeaderPojo = new DeliveryShipHeaderPojo();
        shipHeaderPojo.setDeliveryOrderCode(deliveryOrder.getDeliveryOrderCode());
        shipHeaderPojo.setTotalActualQty(wmsDeliveryInfo.getOrderLines().size());
        shipHeaderPojo.setDeliveryOrderType(deliveryOrder.getOrderType());
        shipHeaderPojo.setStatus(WmsOutBoundStatusEnum.OUTED.getStatus());

        /*出库单明细*/
        List<DeliveryShipDetailPojo> shipDetailPojoList = wmsDeliveryInfo.getOrderLines().stream().map(entity -> {
            DeliveryShipDetailPojo shipDetailPojo = new DeliveryShipDetailPojo();
            shipDetailPojo.setDetailNo(entity.getOrderLineNo());
            // 唯一码场景,出库数量都是 1
            shipDetailPojo.setActualQty(NumberUtils.INTEGER_ONE);
            return shipDetailPojo;
        }).collect(Collectors.toList());

        /*出库单对象*/
        DeliveryShipPojo deliveryShipPojo = new DeliveryShipPojo();
        deliveryShipPojo.setDeliveryModifyHeader(shipHeaderPojo);
        deliveryShipPojo.setDeliveryShipDetailPojoList(shipDetailPojoList);

        log.info("通知单据进行出库,deliveryShipPojo  -> [{}]", JSON.toJSONString(deliveryShipPojo));
        deliveryOrderModifyService.handleDeliveryOrder(deliveryShipPojo);
    }


    /**
     * 创建发货任务
     * 1.现货转95 这个要发发货的消息TAG_OUTBOUND_FINISH
     * 2.个人寄存退货没有交接到寄存仓  这个要发发货的消息TAG_OUTBOUND_FINISH
     * 3.个人寄存退货交接到寄存仓 这个不要发
     * 4.个人寄存转95分未交接到寄存仓 这个需要发
     * 5.个人寄存转95分交接到寄存仓 不要发
     *
     * @param delivery
     * @param virtualReturnDelivery 虚拟出库
     */
    public void createShipTask(WmsDeliveryBill delivery, boolean virtualReturnDelivery) {
        log.info("单据直接出库,wmsDeliveryInfo -> [{}],virtualReturnDelivery:{}", JSON.toJSONString(delivery),virtualReturnDelivery);

        // 进行数据校验
        checkSpecialDirectDelivery(delivery,virtualReturnDelivery);

        // 唯一码订单Map
        Map<String, WmsDeliveryBillDetail> uniqueCodeOrderMap = delivery.getWmsDeliveryBillDetails()
                .stream().collect(Collectors.toMap(WmsDeliveryBillDetail::getUniqueCode, Function.identity()));

        //现货或个人寄存从现货仓退货, 仓库编码都不可用, 只能直接用唯一码查询库存
        List<InventoryDo> inventoryDos = this.inventoryRepository.findByUniqueCodes(Sets.newHashSet(
                uniqueCodeOrderMap.keySet()), OperationUserContextHolder.getTenantCode());
        List<InvInventoryEntity> invInventoryList = BeanUtil.copyByList(inventoryDos, InvInventoryEntity.class);

        if (CollectionUtils.isEmpty(invInventoryList) || invInventoryList.size() != uniqueCodeOrderMap.keySet().size()) {
            log.info("现货转95分出库，个人寄存退货出库，个人寄存转95分出库，没找到库存信息");
            /* 现货转95分出库，个人寄存退货出库，个人寄存转95分出库 wms可能没有库存，只需要更新出库单已出库，单据终结掉，不需要扣库存 */
            int rows = deliveryHeaderRepository.updateOutedStatus(delivery.getDeliveryOrderCode(),
                    delivery.getWmsDeliveryBillDetails().get(NumberUtils.INTEGER_ZERO).getPlanQty());
            if (rows <= NumberUtils.INTEGER_ZERO) {
                throw new WmsException(WmsExceptionCode.UPDATE_DATA_FAIL);
            }
        } else {
            // 按照唯一码进行分组
            Map<String, InvInventoryEntity> invInventoryEntityMap = invInventoryList.stream().collect(Collectors.toMap(InvInventoryEntity::getUniqueCode, Function.identity()));

            List<BaseCreateTaskDetailPojo> taskDetailList = new ArrayList<>();
            Set<String> uniqueCodeSet = new HashSet<>();
            String warehouseCode = null;
            // 遍历进行处理
            for (Map.Entry<String, WmsDeliveryBillDetail> entry : uniqueCodeOrderMap.entrySet()) {
                String uniqueCode = entry.getKey();
                WmsDeliveryBillDetail orderDetailInfo = entry.getValue();

                // 校验库存信息
                InvInventoryEntity invInventoryEntity = invInventoryEntityMap.get(uniqueCode);
                // 校验唯一码库存, 等真询问类型的库存不做校验
                if (!InventoryTypeEnum.OPERATION_WAREHOUSE_TYPE.contains(invInventoryEntity.getInventoryType())) {
                    checkUniqueCodeInv(uniqueCode, orderDetailInfo, invInventoryEntity,virtualReturnDelivery);
                }

                warehouseCode = invInventoryEntity.getWarehouseCode();

                if (uniqueCodeSet.contains(uniqueCode)) {
                    throw new WmsException("商品编码:" + uniqueCode + "重复");
                }
                uniqueCodeSet.add(uniqueCode);

                // 构建明细
                BaseCreateTaskDetailPojo taskDetailPojo = buildCreateTaskDetailPojo(delivery, orderDetailInfo, invInventoryEntity);

                taskDetailList.add(taskDetailPojo);
            }
            BaseCreateTaskPojo baseCreateTaskPojo = new BaseCreateTaskPojo();
            baseCreateTaskPojo.setReferenceNo(delivery.getDeliveryOrderCode());
            baseCreateTaskPojo.setReferenceType(delivery.getOrderType());
            baseCreateTaskPojo.setWarehouseCode(warehouseCode);
            log.info("立即出库参数 , baseCreateTaskPojo -> [{}]", JSON.toJSONString(baseCreateTaskPojo));
            immediateShipFlowWorker.process(baseCreateTaskPojo, taskDetailList);
        }
        if (virtualReturnDelivery){
            /*虚拟出库模拟发个消息给交易*/
            log.info("虚拟出库单{}模拟给交易发个拣货消息",delivery.getDeliveryOrderCode());
            outboundMessageProducer.sendVirtualPickMessageToOfc(delivery.getDeliveryOrderCode());
        }
        log.info("现货转95分出库，个人寄存退货出库，个人寄存转95分出库uniqueCodes[{}]", uniqueCodeOrderMap.keySet());
        outboundMessageProducer.outboundMessage(new OutboundDto(new ArrayList<>(uniqueCodeOrderMap.keySet()),
                delivery.getDeliveryOrderCode(), WmsOutBoundTypeEnum.JWCK.getType().equals(delivery.getOrderType()),
                OperationUserContextHolder.getTenantCode()));
    }


    private BaseCreateTaskDetailPojo buildCreateTaskDetailPojo(WmsDeliveryBill wmsDeliveryBillInfo, WmsDeliveryBillDetail orderDetailInfo, InvInventoryEntity invInventoryEntity) {
        BaseCreateTaskDetailPojo taskDetailPojo = new BaseCreateTaskDetailPojo();
        taskDetailPojo.setUniqueCode(invInventoryEntity.getUniqueCode());
        taskDetailPojo.setSkuId(invInventoryEntity.getSkuId());
        taskDetailPojo.setQty(Optional.ofNullable(orderDetailInfo.getPlanQty()).orElse(1));


        taskDetailPojo.setBizType(wmsDeliveryBillInfo.getBizType());
        taskDetailPojo.setOwnerCode(invInventoryEntity.getOwnerCode());
        taskDetailPojo.setLocationCode(invInventoryEntity.getLocationCode());

        taskDetailPojo.setInventoryNo(invInventoryEntity.getInventoryNo());
        taskDetailPojo.setReferenceDetailNo(orderDetailInfo.getOrderLineNo());
        taskDetailPojo.setReferenceNo(wmsDeliveryBillInfo.getDeliveryOrderCode());
        taskDetailPojo.setWarehouseCode(invInventoryEntity.getWarehouseCode());

        taskDetailPojo.setMfgTime(invInventoryEntity.getMfgTime());
        taskDetailPojo.setExpTime(invInventoryEntity.getExpTime());
        taskDetailPojo.setOriginalOrderCode(invInventoryEntity.getOriginalOrderCode());
        return taskDetailPojo;
    }

    /**
     * 校验唯一码库存是否满足
     *
     * @param uniqueCode
     * @param orderDetailInfo
     * @param invInventoryEntity
     */
    private void checkUniqueCodeInv(String uniqueCode, WmsDeliveryCreateRequest.OrderLine orderDetailInfo, InvInventoryEntity invInventoryEntity) {
        if (null == invInventoryEntity) {
            log.error("唯一码对应的库存不存在, uniqueCode -> [{}]", uniqueCode);
            throw new WmsException(WmsExceptionCode.DELIVERY_CREATE_ERROR);
        }
        if (!orderDetailInfo.getQualityLevel().equals(invInventoryEntity.getQualityLevel())) {
            // 校验商品质量等级
            log.error("出库单中的质量等级和库存中不一致, orderDetailInfo -> [{}] , invInventoryEntity -> [{}]", JSON.toJSONString(orderDetailInfo), JSON.toJSON(invInventoryEntity));
            throw new WmsException(WmsExceptionCode.DELIVERY_QUALITY_LEVEL_ERROR);
        }
    }


    /**
     * 校验唯一码库存是否满足
     *
     * @param uniqueCode
     * @param orderDetailInfo
     * @param invInventoryEntity
     * @param virtualReturnDelivery
     */
    private void checkUniqueCodeInv(String uniqueCode, WmsDeliveryBillDetail orderDetailInfo, InvInventoryEntity invInventoryEntity, boolean virtualReturnDelivery) {
        if (null == invInventoryEntity) {
            log.error("唯一码对应的库存不存在, uniqueCode -> [{}]", uniqueCode);
            throw new WmsException(WmsExceptionCode.DELIVERY_CREATE_ERROR);
        }
        // 校验商品是否在虚拟库位上
//        if (!invInventoryEntity.getLocationCode().equals(wmsDQConfig.getUpperLocationCode())) {
//            log.error("商品不是在正确的库位中, invInventoryEntity -> [{}]", JSON.toJSON(invInventoryEntity));
//            throw new WmsException(WmsExceptionCode.DELIVERY_CREATE_ERROR);
//        }
        if (!virtualReturnDelivery && !orderDetailInfo.getQualityLevel().equals(invInventoryEntity.getQualityLevel())) {
            // 校验商品质量等级
            log.error("出库单中的质量等级和库存中不一致, orderDetailInfo -> [{}] , invInventoryEntity -> [{}]", JSON.toJSONString(orderDetailInfo), JSON.toJSON(invInventoryEntity));
            throw new WmsException(WmsExceptionCode.DELIVERY_QUALITY_LEVEL_ERROR);
        }
        if (virtualReturnDelivery){
            // TODO 校验库存的质量等级是202
        }
    }

    /**
     * 虚拟出库
     * 1、创建发货任务并发货
     * 2、扣减wms&sci库存
     * 3、给ofc发一个出库消息
     * @param deliveryBill
     */
    @Transactional(rollbackFor = Exception.class)
    public void virtualDeliveryShip(WmsDeliveryBill deliveryBill) {
        //创建发货任务
        BaseCreateTaskPojo baseCreateTaskPojo = new BaseCreateTaskPojo();
        baseCreateTaskPojo.setReferenceNo(deliveryBill.getDeliveryOrderCode());
        baseCreateTaskPojo.setReferenceType(deliveryBill.getOrderType());
        baseCreateTaskPojo.setWarehouseCode(deliveryBill.getWarehouseCode());
        // 构建明细
        List<BaseCreateTaskDetailPojo> taskDetailList = new ArrayList<>();
        //这里list支持单个场景
        deliveryBill.getWmsDeliveryBillDetails().forEach(orderDetailInfo -> {
            //根据唯一码或库存维度查询库存行
            InvInventoryEntity invInventoryEntity = queryInventory(deliveryBill,orderDetailInfo);
            if (invInventoryEntity == null) {
                log.info("根据唯一码或库存维度未查找到库存");
                throw new WmsOperationException("商品库存未找到");
            }
            BaseCreateTaskDetailPojo taskDetailPojo = buildCreateTaskDetailPojo(deliveryBill, orderDetailInfo, invInventoryEntity);
            taskDetailList.add(taskDetailPojo);
        });

        log.info("立即出库参数 , baseCreateTaskPojo -> [{}],taskDetailList:[{}]", JSON.toJSONString(baseCreateTaskPojo),JSON.toJSONString(taskDetailList));
        immediateShipFlowWorker.process(baseCreateTaskPojo, taskDetailList);
    }

    private InvInventoryEntity queryInventory(WmsDeliveryBill deliveryBill,WmsDeliveryBillDetail orderDetailInfo) {
        String warehouseCode = deliveryBill.getWarehouseCode();
        if (StringUtils.isNotBlank(orderDetailInfo.getUniqueCode())) {
            return inventoryQuery.findInventoryByUniqueCode(warehouseCode,OperationUserContextHolder.getTenantCode(),orderDetailInfo.getUniqueCode());
        }
        InventoryDimensionPojo inventoryDimensionPojo = new InventoryDimensionPojo();
        inventoryDimensionPojo.setWarehouseCode(warehouseCode);
        inventoryDimensionPojo.setSkuId(orderDetailInfo.getSkuId());
        inventoryDimensionPojo.setOwnerCode(orderDetailInfo.getOwnerCode());
        inventoryDimensionPojo.setQualityLevel(orderDetailInfo.getQualityLevel());
        inventoryDimensionPojo.setBizType(orderDetailInfo.getBizType());

        //免赔付虚拟出库场景库存查询需要到库位维度
        if (WmsOutBoundTypeEnum.MPF_XNCK.getType().equals(deliveryBill.getOrderType())) {
            inventoryDimensionPojo.setLocationCode(getLocationCode(deliveryBill));
        }

        List<InvInventoryEntity> inventoryEntityList = inventoryQuery.findInventoryByInventoryDimension(inventoryDimensionPojo,null);
        if (CollectionUtils.isNotEmpty(inventoryEntityList)) {
            return inventoryEntityList.get(0);
        }
        return null;
    }

    private String getLocationCode(WmsDeliveryBill deliveryBill) {
        ExceptionInfoSimpleDto exceptionInfoSimpleDto = wmsExceptionInfoRpcRepository.querySimpleExceptionInfo(deliveryBill.getDeliveryOrderCode());
        List<ExceptionInfoSimpleDto.ExceptionInventoryInfo.Inventory> inventories = Optional.ofNullable(exceptionInfoSimpleDto)
                .map(ExceptionInfoSimpleDto::getExceptionInventoryInfo)
                .map(ExceptionInfoSimpleDto.ExceptionInventoryInfo::getInventories)
                .orElse(null);

        if (CollectionUtils.isEmpty(inventories)) {
            return null;
        }
        return inventories.stream()
                .map(ExceptionInfoSimpleDto.ExceptionInventoryInfo.Inventory::getInventoryDimension)
                .filter(inventoryDimension -> WmsQualityLevelEnum.CP.getCode().equals(inventoryDimension.getQualityLevel()))
                .map(ExceptionInfoSimpleDto.ExceptionInventoryInfo.Inventory.InventoryDimensionParam::getLocationCode)
                .findAny()
                .orElse(null);
    }

    public void updateHandoverTime(String deliveryOrderCode) {
        DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderRepository.queryDeliveryInfo(deliveryOrderCode);
        deliveryHeaderDo.setHandoverTime(new Date());
        deliveryHeaderRepository.updateById(deliveryHeaderDo);
    }


}
