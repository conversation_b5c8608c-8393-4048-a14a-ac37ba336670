package com.poizon.scm.wms.common.auth;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.poizon.scm.wms.common.constants.BaseOperationUserContext;
import com.poizon.scm.wms.util.enums.LanguageEnum;
import com.poizon.scm.wms.util.enums.TenantEnum;
import com.shizhuang.duapp.tenant.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Deacription User TheadLocal
 * <AUTHOR>
 * @Date 2020/4/21 8:41 下午
 **/
@Slf4j
public class OperationUserContextHolder {

    private final static TransmittableThreadLocal<OperationUserContext> operationUserContext = new TransmittableThreadLocal<>();

    public static OperationUserContext get() {
        return operationUserContext.get();
    }

    public static void set(OperationUserContext operationContext) {
        operationUserContext.set(operationContext);
    }

    public static void clear() {
        operationUserContext.remove();
    }

    /**
     * 默认租户编码
     * 目前默认的住户编码为 dewu = 1
     */
    public static final String DEFAULT_TENANT_CODE = "1";

    public static String getTenantCode() {
        String tenantId = TenantContext.getTenantId();
        if(StringUtils.isBlank(tenantId)){
            log.warn("tenant-check: 当前TenantContext上下问没有租户id。");
        }
        return StringUtils.isNotEmpty(tenantId) ? tenantId : DEFAULT_TENANT_CODE;
    }

    public static String getScmTenantCode() {
        String tenantId = TenantContext.getTenantId();
        if(StringUtils.isBlank(tenantId)){
            log.warn("tenant-check: 当前TenantContext上下问没有租户id。");
        }
        return StringUtils.isNotEmpty(tenantId) ? tenantId : DEFAULT_TENANT_CODE;
    }

    //todo: 租户集合, 取自lms
    public static List<String> getTenantCodeList() {
        return new ArrayList<>();
    }


    /**
     * 是否是云仓wms
     *
     * @return boolean
     */
    public static boolean isCloudWms() {
        String tenantId = TenantContext.getTenantId();
        return TenantEnum.CLOUD_WMS.getCode().equals(tenantId);
    }

    /**
     * 获取上下文中的语言
     * @return
     */
    public static String getLanguage() {
        OperationUserContext userContext = OperationUserContextHolder.get();
        if(userContext == null){
            log.warn("language-check: 当前用户上下文为空！");
            return LanguageEnum.DEFAULT.getCode();
        }
        return StringUtils.isNotEmpty(userContext.getLanguage()) ? userContext.getLanguage() : LanguageEnum.DEFAULT.getCode();
    }

    /**
     * 根据入参tenantId构建租户
     * @param tenantId
     * @param userId
     * @param realName
     * @param userName
     * @param warehouseCode
     * @param warehouseName
     * @param signId 签到id
     */
    public static void buildOperationUserContext(String tenantId,
                                                           Long userId,
                                                           String realName,
                                                           String userName,
                                                           String warehouseCode,
                                                           String warehouseName,
                                                           Long signId) {
        OperationUserContext context = OperationUserContext.builder()
                .tenantCode(tenantId)
                .userId(userId)
                .userName(userName)
                .realName(realName)
                .warehouseCode(warehouseCode)
                .warehouseName(warehouseName)
                .signId(signId)
                .build();
        OperationUserContextHolder.set(context);
    }

    /**
     * 根据入参tenantId构建租户
     * @param tenantId
     * @param userId
     * @param realName
     * @param userName
     * @param warehouseCode
     * @param warehouseName
     */
    public static void buildOperationUserContextWithTenant(String tenantId,
                                                           Long userId,
                                                           String realName,
                                                           String userName,
                                                           String warehouseCode, String warehouseName) {
        OperationUserContext context = OperationUserContext.builder()
                .tenantCode(tenantId)
                .userId(userId)
                .userName(userName)
                .realName(realName)
                .warehouseCode(warehouseCode)
                .warehouseName(warehouseName)
                .build();
        OperationUserContextHolder.set(context);
    }

    public static void buildOperationUserContext(Long userId,
                                                 String realName,
                                                 String userName,
                                                 String warehouseCode) {
        buildOperationUserContext(userId,realName,userName,warehouseCode,null);

    }
    /**
     * 构建用户上下文
//     * @param tenantCode
     * @param userId
     * @param realName
     * @param userName
     * @param warehouseCode
     */
    public static void buildOperationUserContext(Long userId,
                                                 String realName,
                                                 String userName,
                                                 String warehouseCode,String warehouseName) {
        String tenantCode = checkTenant(warehouseCode);
        OperationUserContext context = OperationUserContext.builder()
                .tenantCode(tenantCode)
                .userId(userId)
                .userName(userName)
                .realName(realName)
                .warehouseCode(warehouseCode)
                .warehouseName(warehouseName)
                .build();
        OperationUserContextHolder.set(context);
    }

    public static void buildDefaultUserContextWithTenantCode(String warehouseCode,
                                                 String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            tenantCode = getTenantCode();
        }
        OperationUserContext context = OperationUserContext.builder()
                .tenantCode(tenantCode)
                .userId(BaseOperationUserContext.userId)
                .userName(BaseOperationUserContext.userName)
                .realName(BaseOperationUserContext.realName)
                .warehouseCode(warehouseCode)
                .build();
        TenantContext.setContextId(tenantCode);
        OperationUserContextHolder.set(context);
        //根据传参设置租户
        TenantContext.getContext().setTenantId(tenantCode);
    }

    /**
     * 构建用户上下文
     * @param operationUserContext
     */
    public static void buildUserContext(OperationUserContext operationUserContext,String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            tenantCode = getTenantCode();
        }
        TenantContext.setContextId(tenantCode);
        OperationUserContextHolder.set(operationUserContext);
    }

    /**
     * 租户信息替换一下
     * @param tenantCode
     */
    public static void buildTenantCode(String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            return;
        }

        OperationUserContext context = OperationUserContextHolder.get();
        if (null != context) {
            context.setTenantCode(tenantCode);
            OperationUserContextHolder.set(context);
        }

        //根据传参设置租户
        TenantContext.setContextId(tenantCode);
    }

    /**
     * 构建默认用户上下文
     * 目前使用场景 feign接口/mq消息
     * @param warehouseCode
     */
    public static void buildDefaultUserContext(String warehouseCode){
        buildDefaultUserContext(warehouseCode,null);
    }
    /**
     * 构建默认用户上下文
     * 目前使用场景 feign接口/mq消息
     * @param warehouseCode
     */
    public static void buildDefaultUserContext(String warehouseCode,String warehouseName){
        buildOperationUserContext(
                BaseOperationUserContext.userId,
                BaseOperationUserContext.userName,
                BaseOperationUserContext.realName,
                warehouseCode,warehouseName);
    }
    /**
     * 目前feign接口有入参无warehouseCode的情况
     * 此方法专门针对这种情况构建默认上下文
     * 使用时需要根据具体场景判断
     */
    public static void buildNoWarehouseDefaultContext(){
        OperationUserContextHolder.buildDefaultUserContext(null);
    }

    /**
     * 检查租户是否存在
     * @param warehouseCode
     * @return
     */
    private static String checkTenant(String warehouseCode) {
        String tenantId = TenantContext.getTenantId();
        /*//开发自测用
        if("SH66".equals(warehouseCode)){
            tenantId ="3";
            log.info("95仓库{}租户写死",warehouseCode);
        }*/
        if (StringUtils.isEmpty(tenantId)) {
            log.error("未获取到租户上下文，请排查问题。 warehouseCode :{}", warehouseCode);
            tenantId = DEFAULT_TENANT_CODE;
        }
        //log.info("95仓库租户写死：{}",tenantId);
        return tenantId;
    }

    /**
     * 此方法仅本地postman测试使用!!!
     * 正式开发构建默认上下文不要用这个方法!!!
     * 请使用上面 buildDefaultUserContext 方法!!!
     */
    @Deprecated
    public static void buildAdminUser(Long userId, String warehouseCode, List<OperationUserContext.UserWarehouse> warehouses){
        OperationUserContext userContext = OperationUserContext.builder()
                .userId(userId)
                .realName("超级管理员")
                .userName("admin")
                .tenantCode("2")
                .warehouseCode(warehouseCode)
                .warehouses(warehouses)
                .build();
        OperationUserContextHolder.set(userContext);
        TenantContext.setContextId("1");
    }

}
