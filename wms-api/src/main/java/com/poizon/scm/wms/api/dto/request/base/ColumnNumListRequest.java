package com.poizon.scm.wms.api.dto.request.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 层列表查询
 * @Deacription
 * <AUTHOR>
 * @Date 2020/4/24 3:06 下午
 **/
@Data
@ApiModel
public class ColumnNumListRequest {

    @NotBlank(message = "仓库编码不能为空")
    @ApiModelProperty(value="仓库编码",required = true)
    private String warehouseCode;

    @NotBlank(message = "库区编码不能为空")
    @ApiModelProperty(value="库区编码",required = true)
    private String areaCode;

    @ApiModelProperty(value="港道")
    @NotBlank(message = "通道不能为空")
    private String passNum;

}
