# 出库业务租户使用情况梳理

## 概述
本文档专门梳理了WMS系统中与出库业务相关的所有使用 `OperationUserContextHolder.getTenantCode()` 和 `TenantContext.getTenantId()` 的代码位置，包括出库单管理、拣货、发货、配送等出库相关业务场景。

## 出库业务租户使用情况统计表

| 序号 | 业务域 | 应用名 | 场景 | 涉及代码 | 使用方法 | 代码路径 | 说明 |
|------|--------|--------|------|----------|----------|----------|------|
| 1 | 出库业务 | wms-service | 发货服务 | ShippingServiceImpl.buildSnBindOperateParam() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/shipping/ShippingServiceImpl.java | 构建SN绑定操作参数时设置租户编码 |
| 2 | 出库业务 | wms-service | 发货服务 | ShippingServiceImpl.executeShippingByTaskDetailNo() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/shipping/ShippingServiceImpl.java | 发货执行时获取租户编码进行SN码校验 |
| 3 | 出库业务 | wms-service | 发货服务 | ShippingServiceImpl.executeShippingBySn() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/shipping/ShippingServiceImpl.java | SN码发货时获取租户编码查询SKU信息 |
| 4 | 出库业务 | wms-service | 发货服务 | ShippingServiceImpl.notifyOutboundMessage() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/shipping/ShippingServiceImpl.java | 发货完成后发送出库消息时设置租户编码 |
| 5 | 出库业务 | wms-service | 拣货服务 | PickupService.pickScanNumber() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/pickup/PickupService.java | 拣货扫码时查询拣货任务使用租户编码 |
| 6 | 出库业务 | wms-service | 拣货服务 | PickupService.buildBarcodeRecognizeParam() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/pickup/PickupService.java | 构建条码识别参数时设置租户编码 |
| 7 | 出库业务 | wms-service | 拣货命令服务 | PickCommandService.pickByUniqueCode() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/domain/outbound/pick/service/PickCommandService.java | 根据唯一码拣货时查询任务明细使用租户编码 |
| 8 | 出库业务 | wms-service | 拣货命令服务 | PickCommandService.checkTemporaryRecallFlag() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/domain/outbound/pick/service/PickCommandService.java | 检查暂存拦截标志时查询订单信息使用租户编码 |
| 9 | 出库业务 | wms-service | 出库异常处理 | DeliveryExceptionDetailGenerator.queryInvInventoryEntity() | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/service/returnshelf/DeliveryExceptionDetailGenerator.java | 查询库存实体时保存和恢复租户上下文 |
| 10 | 出库业务 | wms-service | 出库超时告警服务 | AlarmOutDeliveryService.setContext() | - | wms-service/src/main/java/com/poizon/scm/wms/service/delivery/timeout/AlarmOutDeliveryService.java | 设置告警策略的租户上下文 |
| 11 | 出库业务 | wms-service | 自动发货执行器 | DeliveryOrderAutoShipExecutor.buildUserContext() | - | wms-service/src/main/java/com/poizon/scm/wms/service/delivery/executor/DeliveryOrderAutoShipExecutor.java | 拣货后自动发货时构建用户上下文设置租户编码 |
| 12 | 出库业务 | wms-service | 尾货发起服务 | TailLaunchService.setContext() | - | wms-service/src/main/java/com/poizon/scm/wms/domain/outbound/launch/TailLaunchService.java | 尾货发起时设置租户上下文 |
| 13 | 出库业务 | wms-service | 出库工作日志服务 | WmsOutboundWorkLogService.operateLogWithConsistency() | - | wms-service/src/main/java/com/poizon/scm/wms/domain/outbound/worklog/WmsOutboundWorkLogService.java | 出库操作日志记录时获取用户上下文 |
| 14 | 出库业务 | wms-service | 时效拣货生产者 | TimeSensitivePickProducer.sendMessageToOFC() | - | wms-service/src/main/java/com/poizon/scm/wms/service/pickup/producer/TimeSensitivePickProducer.java | 发送时效拣货消息给OFC时使用一致性框架 |
| 15 | 出库业务 | wms-service | 发货一致性执行器 | ShippingConsistencyExecutor.insertBackToOriginal() | - | wms-service/src/main/java/com/poizon/scm/wms/service/shipping/ShippingConsistencyExecutor.java | 发货数据回写时查询收货人信息使用租户编码 |
| 16 | 出库业务 | wms-service | 拣货发货报表服务 | PickShipReportServiceImpl.pickingShippingPaginatedQuery() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/pick/impl/PickShipReportServiceImpl.java | 拣货发货报表分页查询时设置租户编码 |
| 17 | 出库业务 | wms-service | WMS发货工厂 | WmsShipFactory.resetUserContext() | - | wms-service/src/main/java/com/poizon/scm/wms/domain/ship/WmsShipFactory.java | WMS发货时重置用户上下文设置租户ID |
| 18 | 出库业务 | wms-service | 出库单服务 | WmsOutBillServiceImpl.saveReceiver() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/domain/outbound/outbill/service/WmsOutBillServiceImpl.java | 保存出库单收件人信息时设置租户编码 |
| 19 | 出库业务 | wms-infrastructure | 出库单头仓储 | DeliveryHeaderRepositoryImpl.countPageDeliveryInfo() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/outbound/repository/DeliveryHeaderRepositoryImpl.java | 分页查询出库单信息时设置租户编码 |
| 20 | 出库业务 | wms-infrastructure | 出库单头仓储 | DeliveryHeaderRepositoryImpl.exportRollDeliveryInfo() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/outbound/repository/DeliveryHeaderRepositoryImpl.java | 导出出库单信息时设置租户编码 |
| 21 | 出库业务 | wms-infrastructure | 出库单头仓储 | DeliveryHeaderRepositoryImpl.updateDeliverByCode() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/outbound/repository/DeliveryHeaderRepositoryImpl.java | 根据编码更新出库单时使用租户编码 |
| 22 | 出库业务 | wms-infrastructure | 出库单头仓储 | DeliveryHeaderRepositoryImpl.shipPrintQueryDeliveryOrderByDeliveryOrderOrTradeOrderCode() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/outbound/repository/DeliveryHeaderRepositoryImpl.java | 发货打印查询出库单时使用租户编码 |
| 23 | 出库业务 | wms-infrastructure | 出库用户仓储 | DeliveryUserRepositoryImpl.saveDeliveryUser() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/outbound/repository/DeliveryUserRepositoryImpl.java | 保存出库用户信息时设置租户编码 |
| 24 | 出库业务 | wms-infrastructure | 拣货任务命令仓储 | PickTaskCommandRepositoryImpl.updateTaskReceiver() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/pick/repository/db/command/PickTaskCommandRepositoryImpl.java | 更新拣货任务接收人时使用租户编码 |
| 25 | 出库业务 | wms-infrastructure | 拣货任务命令仓储 | PickTaskCommandRepositoryImpl.modifyTaskHeader() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/pick/repository/db/command/PickTaskCommandRepositoryImpl.java | 修改拣货任务头信息时使用租户编码 |
| 26 | 出库业务 | wms-infrastructure | 拣货任务命令仓储 | PickTaskCommandRepositoryImpl.batchCancel() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/pick/repository/db/command/PickTaskCommandRepositoryImpl.java | 批量取消拣货任务时使用租户编码 |
| 27 | 出库业务 | wms-infrastructure | 拣货任务命令仓储 | PickTaskCommandRepositoryImpl.assignTask() | getTenantCode() | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/outbound/pick/repository/db/command/PickTaskCommandRepositoryImpl.java | 分配拣货任务时使用租户编码 |
| 28 | 出库业务 | wms-interfaces | 出库单详情查询执行器 | DeliveryDetailQueryRequestExecutor.preProcess() | - | wms-interfaces/src/main/java/com/poizon/scm/wms/command/outbound/outbound/executor/DeliveryDetailQueryRequestExecutor.java | 出库单详情查询预处理时设置租户上下文 |
| 29 | 出库业务 | wms-interfaces | 唯一码上架执行器 | ExecuteUpperByUniqueCodeRequestExecutor.buildContext() | - | wms-interfaces/src/main/java/com/poizon/scm/wms/command/onshelf/executor/ExecuteUpperByUniqueCodeRequestExecutor.java | 自动上架时构建上下文设置租户编码 |
| 30 | 出库业务 | wms-interfaces | 出库打印运单服务 | OutboundPrintWaybillDubboService.buildOperateUserContext() | - | wms-interfaces/src/main/java/com/poizon/scm/wms/command/outbound/ship/OutboundPrintWaybillDubboService.java | 出库打印运单时构建操作用户上下文设置租户编码 |
| 31 | 出库业务 | wms-interfaces | 下架任务未完成查询 | OffTaskUnFinishedQueryRequestExecutor.doExecute() | getTenantCode() | wms-interfaces/src/main/java/com/poizon/scm/wms/command/inner/off/executor/OffTaskUnFinishedQueryRequestExecutor.java | 查询未完成下架任务时使用租户编码 |
| 32 | 出库业务 | wms-interfaces | 拣货列表查询执行器 | PickListQueryRequestExecutor | - | wms-interfaces/src/main/java/com/poizon/scm/wms/query/outbound/pick/executor/PickListQueryRequestExecutor.java | 拣货列表查询时使用租户相关信息 |
| 33 | 出库业务 | wms-interfaces | 仓库请求执行器 | WarehouseRequestExecutor.doExecute() | getTenantCode() | wms-interfaces/src/main/java/com/poizon/scm/wms/command/common/warehouse/executor/WarehouseRequestExecutor.java | 查询仓库信息时使用租户编码 |
| 34 | 出库业务 | wms-consumer | 出库分配完成执行器 | DeliveryAllocateCompleteRequestExecutor.validateAndSetContext() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/out/executor/DeliveryAllocateCompleteRequestExecutor.java | 出库分配完成时校验出库单并设置租户上下文 |
| 35 | 出库业务 | wms-consumer | 工作站订单商品消费执行器 | WorkStationOrderGoodsConsumerRequestExecutor.initContext() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/outbound/staiton/executor/WorkStationOrderGoodsConsumerRequestExecutor.java | 工作站订单商品处理时初始化租户上下文 |
| 36 | 出库业务 | wms-consumer | 客退拣货下架执行器 | CustomerReturnPickOffShelfRequestExecutor.preProcess() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/outbound/customerreturn/exesutor/CustomerReturnPickOffShelfRequestExecutor.java | 客退拣货下架预处理时设置租户上下文 |
| 37 | 出库业务 | wms-service | 发货接收器 | ShipReceiver.process() | - | wms-service/src/main/java/com/poizon/scm/wms/third/receive/biz/out/ShipReceiver.java | 发货任务接收处理时自动设置用户上下文 |
| 38 | 出库业务 | wms-service | 发货处理器工厂 | ShipProcessorFactory.createProcessor() | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/third/receive/biz/out/ship/ShipProcessorFactory.java | 创建发货处理器时检查租户ID匹配 |
| 39 | 出库业务 | wms-service | 默认发货处理器 | DefaultShipProcessor.getTenantIdList() | - | wms-service/src/main/java/com/poizon/scm/wms/third/receive/biz/out/ship/DefaultShipProcessor.java | 默认发货处理器支持的租户ID列表 |
| 40 | 出库业务 | wms-service | 客退发货处理器 | CustomerReturnShipProcessor.getTenantIdList() | - | wms-service/src/main/java/com/poizon/scm/wms/third/receive/biz/out/ship/CustomerReturnShipProcessor.java | 客退发货处理器支持的租户ID列表 |
| 41 | 出库业务 | wms-service | 个人退货发货处理器 | PersonalReturnShipProcessor.getTenantIdList() | - | wms-service/src/main/java/com/poizon/scm/wms/third/receive/biz/out/ship/PersonalReturnShipProcessor.java | 个人退货发货处理器支持的租户ID列表 |
| 42 | 出库业务 | wms-admin-interfaces | 下架策略列表查询 | StrategyListQueryRequestExecutor.doExecute() | getTenantCode() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/query/outbound/launch/strategy/executor/StrategyListQueryRequestExecutor.java | 查询下架策略列表时使用租户编码查询仓库信息 |
| 43 | 出库业务 | wms-admin-interfaces | 下架规则列表查询 | RuleListQueryRequestExecutor.doExecute() | getTenantCode() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/query/outbound/launch/rule/executor/RuleListQueryRequestExecutor.java | 查询下架规则列表时使用租户编码 |
| 44 | 出库业务 | wms-job-interfaces | 拣货延迟任务 | PickDelayTaskJob.execute() | - | wms-job-interfaces/src/main/java/com/poizon/scm/wms/job/service/pick/PickDelayTaskJob.java | 拣货延迟任务处理时查询出库单信息 |
| 45 | 出库业务 | wms-out-adapter | 出库单头实体 | DeliveryHeaderEntity.tenantCode | - | wms-out-adapter/src/main/java/com/poizon/scm/wms/adapter/outbound/entity/DeliveryHeaderEntity.java | 出库单头实体包含租户编码字段 |
| 46 | 出库业务 | wms-out-adapter | 容器查询参数 | BasContainerQueryParam.tenantId | - | wms-out-adapter/src/main/java/com/poizon/scm/wms/adapter/container/query/BasContainerQueryParam.java | 容器查询参数包含租户ID字段 |
| 25 | 库存管理 | wms-dao | 库存CRUD | InventoryCurd.queryInventoryList() | getTenantCode() | wms-dao/src/main/java/com/poizon/scm/wms/dao/curd/InventoryCurd.java | 查询库存列表时租户参数校验和获取 |
| 26 | 库存管理 | wms-dao | 库存CRUD | InventoryCurd.findInventoryByUniqueCode() | getTenantCode() | wms-dao/src/main/java/com/poizon/scm/wms/dao/curd/InventoryCurd.java | 根据唯一码查找库存时租户参数校验 |
| 27 | 库存管理 | wms-dao | 库存CRUD | InventoryCurd.batchQueryByUniqueCodes() | getTenantCode() | wms-dao/src/main/java/com/poizon/scm/wms/dao/curd/InventoryCurd.java | 批量查询库存时租户参数校验 |
| 28 | 库存管理 | wms-dao | 库存CRUD | InventoryCurd.findInventoryByMd5() | getTenantCode() | wms-dao/src/main/java/com/poizon/scm/wms/dao/curd/InventoryCurd.java | 根据MD5查找库存时租户参数校验 |
| 29 | 管理后台 | wms-admin-interfaces | 操作日志查询 | OperationLogInfoQueryController.pageQuery() | getTenantCode() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/web/log/OperationLogInfoQueryController.java | 分页查询操作日志时设置租户编码 |
| 30 | 管理后台 | wms-admin-interfaces | 枚举控制器 | EnumController.load() | getTenantCode() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/web/cfg/EnumController.java | 加载路由时效时间枚举时使用租户隔离 |
| 31 | 管理后台 | wms-admin-interfaces | 容器管理 | BasContainerController.queryBasContainerList() | getTenantCode() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/web/base/BasContainerController.java | 容器管理分页查询时设置租户ID |
| 32 | 管理后台 | wms-admin-interfaces | 增值服务 | AddValueServicesController.export() | getTenantCode() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/web/command/assembling/controller/AddValueServicesController.java | 导出增值服务单时设置租户编码 |
| 33 | 基础服务 | wms-service | 仓库服务 | BasWarehouseServiceImpl.queryWarehouseByCode() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/base/BasWarehouseServiceImpl.java | 根据编码查询仓库信息时使用租户隔离 |
| 34 | 基础服务 | wms-service | 仓库服务 | BasWarehouseServiceImpl.warehouseName() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/base/BasWarehouseServiceImpl.java | 获取仓库名称时使用租户编码 |
| 35 | 基础服务 | wms-service | 容器服务 | BasContainerServiceImpl.queryRealContainer() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/base/container/BasContainerServiceImpl.java | 查询真实容器时使用租户编码 |
| 36 | 库存管理 | wms-service | 库存批次查询 | InventoryBatchQueryServiceImpl.findInventoryEntity() | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/service/inventory/query/InventoryBatchQueryServiceImpl.java | 根据批次号查询库存时使用租户ID |
| 37 | 库存管理 | wms-service | 库存效期更新 | InventoryEffectivMofifyJobServiceImpl.fillNeedModifyInventoryForContext() | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/service/inventory/job/impl/InventoryEffectivMofifyJobServiceImpl.java | 库存效期更新时查询库存使用租户ID |
| 38 | 库存管理 | wms-service | 库存效期更新 | InventoryEffectiveUpdateJobServiceImpl.fillNeedModifyInventoryForContext() | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/service/inventory/job/impl/InventoryEffectiveUpdateJobServiceImpl.java | 库存效期更新Job中查询库存使用租户ID |
| 39 | 出库业务 | wms-service | 出库异常处理 | DeliveryExceptionDetailGenerator.queryInvInventoryEntity() | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/service/returnshelf/DeliveryExceptionDetailGenerator.java | 查询库存实体时保存和恢复租户上下文 |
| 40 | 出库业务 | wms-service | 发货服务 | ShippingServiceImpl.buildSnBindOperateParam() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/service/shipping/ShippingServiceImpl.java | 构建SN绑定操作参数时设置租户编码 |
| 41 | 消息队列 | wms-consumer | 唯一码质检完成 | UniqueQualityCompletedConsumer.setOperationUserContext() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/inbound/quality/UniqueQualityCompletedConsumer.java | 设置操作用户上下文时设置租户ID |
| 42 | 消息队列 | wms-consumer | 识别完成请求 | IdentifyCompletedRequestExecutor.preProcess() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/inbound/identify/executor/IdentifyCompletedRequestExecutor.java | 预处理时设置租户上下文 |
| 43 | 消息队列 | wms-consumer | 唯一码识别 | UcIdentifyConsumer | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/inbound/identify/UcIdentifyConsumer.java | 处理唯一码识别消息时设置租户上下文 |
| 44 | 消息队列 | wms-consumer | 二次质检容器 | ReceivedQualityContainerExecuteConsumer.setOperationUserContext() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/inbound/receivedquality/ReceivedQualityContainerExecuteConsumer.java | 二次质检消息处理时设置租户上下文 |
| 45 | 消息队列 | wms-consumer | 工作站订单商品 | WorkStationOrderGoodsConsumerRequestExecutor.initContext() | - | wms-consumer/src/main/java/com/poizon/scm/wms/consumer/outbound/staiton/executor/WorkStationOrderGoodsConsumerRequestExecutor.java | 初始化上下文时设置租户ID |
| 46 | 定时任务 | wms-job-interfaces | 暂存货架触发 | TemporaryShelvesTriggerJob.execute() | - | wms-job-interfaces/src/main/java/com/poizon/scm/wms/job/service/inbound/TemporaryShelvesTriggerJob.java | 暂存货架商品下个流程时设置租户上下文 |
| 47 | 定时任务 | wms-job-interfaces | 库存效期更新 | InventoryEffectiveUpdateJob.setUserContext() | - | wms-job-interfaces/src/main/java/com/poizon/scm/wms/job/service/inventory/InventoryEffectiveUpdateJob.java | 库存效期实时刷新时设置租户上下文 |
| 48 | 上架业务 | wms-service | 上架包装唯一码 | UpperPackUniqueCreateUpperInfoProcessor.setContext() | - | wms-service/src/main/java/com/poizon/scm/wms/domain/upper/processor/sn/UpperPackUniqueCreateUpperInfoProcessor.java | 设置上下文时设置租户ID |
| 49 | 查询接口 | wms-interfaces | 质检任务查询 | QueryUnQualityRequestExecutor.buildRpcQueryParam() | getTenantCode() | wms-interfaces/src/main/java/com/poizon/scm/wms/query/inbound/quality/quality/executor/QueryUnQualityRequestExecutor.java | 构建RPC查询参数时设置租户ID |
| 50 | 查询接口 | wms-interfaces | 质检任务RPC查询 | QueryUnQualityRpcRequestExecutor.buildRpcQueryParam() | getTenantCode() | wms-interfaces/src/main/java/com/poizon/scm/wms/query/inbound/quality/quality/executor/QueryUnQualityRpcRequestExecutor.java | 构建RPC查询参数时设置租户ID |
| 51 | 查询接口 | wms-admin-interfaces | 退货库存查询 | QueryReturnInventoryRequestExecutor | getTenantId() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/query/returned/executor/QueryReturnInventoryRequestExecutor.java | 查询退货库存时设置租户ID |
| 52 | 查询接口 | wms-admin-interfaces | 未退货查询 | UnReturnQueryRequestExecutor | getTenantId() | wms-admin-interfaces/src/main/java/com/poizon/scm/wms/query/returned/executor/UnReturnQueryRequestExecutor.java | 查询未退货信息时使用租户ID |
| 53 | 命令接口 | wms-interfaces | 库存调整 | InventoryAdjustSkuOrQualityLevelRequestExecutor | getTenantId() | wms-interfaces/src/main/java/com/poizon/scm/wms/command/inventory/executor/InventoryAdjustSkuOrQualityLevelRequestExecutor.java | 库存调整时适配租户ID |
| 54 | 查询接口 | wms-service | SKU信息查询 | SnAdminQueryServiceImpl | getTenantId() | wms-service/src/main/java/com/poizon/scm/wms/service/inventory/query/impl/SnAdminQueryServiceImpl.java | 查询SKU信息时使用租户ID |
| 55 | 基础设施 | wms-common | 一致性上下文监听器 | ConsistencyContextListener | - | wms-service/src/main/java/com/poizon/scm/wms/common/listener/ConsistencyContextListener.java | 一致性框架上下文传递时处理租户信息 |
| 56 | 基础设施 | wms-common | 敏感信息切面 | ConvertSensitiveInfoAspect.sensitiveInfoAfter() | - | wms-common/src/main/java/com/poizon/scm/wms/common/aop/ConvertSensitiveInfoAspect.java | 处理敏感信息时获取用户上下文 |
| 57 | 数据访问 | wms-dao | 任务映射器 | TaskMapper.findTaskByNos() | - | wms-dao/src/main/java/com/poizon/scm/wms/dao/mappers/task/TaskMapper.java | 批量查询任务时使用租户编码条件 |
| 58 | 数据访问 | wms-dao | 任务详情映射器 | TaskDetailMapper.queryTaskByReferenceNo() | - | wms-dao/src/main/java/com/poizon/scm/wms/dao/mappers/task/TaskDetailMapper.java | 根据关联单据号查询任务时使用租户编码 |
| 59 | 数据访问 | wms-dao | 任务详情映射器 | TaskDetailMapper.queryReferenceByTaskNos() | - | wms-dao/src/main/java/com/poizon/scm/wms/dao/mappers/task/TaskDetailMapper.java | 根据任务号查询关联信息时使用租户编码 |
| 60 | 数据访问 | wms-infrastructure | 任务扩展映射器 | TaskDoExtMapper.queryToDoListByAreaCodes() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/common/mapper/TaskDoExtMapper.java | 根据区域编码查询待办任务时使用租户编码 |
| 61 | 数据访问 | wms-infrastructure | 任务扩展映射器 | TaskDoExtMapper.findByLaunchNo() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/common/mapper/TaskDoExtMapper.java | 根据发起号查询任务时使用租户编码 |
| 62 | 数据访问 | wms-infrastructure | 任务扩展映射器 | TaskDoExtMapper.findExceptionUpperByReferenceNo() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/common/mapper/TaskDoExtMapper.java | 查询异常上架任务时使用租户编码 |
| 63 | 数据访问 | wms-infrastructure | 移库单扩展映射器 | MoveBillDoExtMapper.queryMoveBillList() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/inner/move/mapper/MoveBillDoExtMapper.java | 查询移库单列表时使用租户编码 |
| 64 | 数据访问 | wms-infrastructure | 移库单扩展映射器 | MoveBillDoExtMapper.queryByMoveBillNo() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/inner/move/mapper/MoveBillDoExtMapper.java | 根据移库单号查询时使用租户编码 |
| 65 | 数据访问 | wms-infrastructure | 货主扩展映射器 | ScpMerchantDoExtMapper.findNameByCode() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/scp/mapper/ScpMerchantDoExtMapper.java | 根据货主编码查询名称时使用租户ID |
| 66 | 数据访问 | wms-infrastructure | 货主扩展映射器 | ScpMerchantDoExtMapper.findCodeByName() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/scp/mapper/ScpMerchantDoExtMapper.java | 根据货主名称查询编码时使用租户ID |
| 67 | 数据访问 | wms-infrastructure | 货主扩展映射器 | ScpMerchantDoExtMapper.queryNameAndMerchantIdByUserIds() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/scp/mapper/ScpMerchantDoExtMapper.java | 根据用户ID查询商家信息时使用租户ID |
| 68 | 数据访问 | wms-infrastructure | 上架策略扩展映射器 | UpperStrategyDoExtMapper.findByStrategyNo() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/upper/mapper/UpperStrategyDoExtMapper.java | 根据策略号查询上架策略时使用租户ID |
| 69 | 数据访问 | wms-infrastructure | 上架策略扩展映射器 | UpperStrategyDoExtMapper.existStrategy() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/upper/mapper/UpperStrategyDoExtMapper.java | 检查上架策略是否存在时使用租户ID |
| 70 | 数据访问 | wms-infrastructure | SKU扩展映射器 | SkuDoExtMapper.findByCode() | - | wms-infrastructure/src/main/resources/mapper/common/SkuDoExtMapper.xml | 根据条码查询SKU时使用租户ID |
| 71 | 数据访问 | wms-infrastructure | SKU扩展映射器 | SkuDoExtMapper.findBySkuIdList() | - | wms-infrastructure/src/main/resources/mapper/common/SkuDoExtMapper.xml | 根据SKU ID列表查询时使用租户ID |
| 72 | 数据访问 | wms-dao | SPU映射器 | ScpSpuMapper.selectCommodityIdInfoByTitle() | - | wms-dao/src/main/resources/mapper/ScpSpuMapper.xml | 根据标题查询商品ID信息时使用租户编码 |
| 73 | 数据访问 | wms-dao | SPU映射器 | ScpSpuMapper.selectPageSkuIdList() | - | wms-dao/src/main/resources/mapper/ScpSpuMapper.xml | 分页查询SKU ID列表时使用租户编码 |
| 74 | 数据访问 | wms-dao | 商品图片映射器 | ScpProductImageMapper.selectProductImagesBySpuId() | - | wms-dao/src/main/resources/mapper/ScpProductImageMapper.xml | 根据SPU ID查询商品图片时使用租户ID |
| 75 | 数据访问 | wms-dao | 库存映射器 | InvInventoryMapper.batchQueryInventoryList() | - | wms-dao/src/main/resources/mapper/InvInventoryMapper.xml | 批量查询库存列表时使用租户编码 |
| 76 | 数据访问 | wms-dao | 任务映射器 | TaskMapper.xml | - | wms-dao/src/main/resources/mapper/TaskMapper.xml | 任务相关查询和更新时使用租户编码 |
| 77 | 业务实体 | wms-out-adapter | 出库单头实体 | DeliveryHeaderEntity.tenantCode | - | wms-out-adapter/src/main/java/com/poizon/scm/wms/adapter/outbound/entity/DeliveryHeaderEntity.java | 出库单头实体包含租户编码字段 |
| 78 | 业务实体 | wms-service | 集货位信息实体 | BasCollectingLocationInfo.tenantId | - | wms-service/src/main/java/com/poizon/scm/wms/domain/base/entity/BasCollectingLocationInfo.java | 集货位领域实体包含租户ID字段 |
| 79 | 业务实体 | wms-service | 容器占用实体 | ContainerOccupy.tenantId | - | wms-service/src/main/java/com/poizon/scm/wms/domain/inner/container/instance/processor/entity/ContainerOccupy.java | 容器占用实体包含租户ID字段 |
| 80 | 业务实体 | wms-out-adapter | 容器查询参数 | BasContainerQueryParam.tenantId | - | wms-out-adapter/src/main/java/com/poizon/scm/wms/adapter/container/query/BasContainerQueryParam.java | 容器查询参数包含租户ID字段 |
| 81 | 数据访问 | wms-dao | 租户映射器 | ScpTenantMapper | - | wms-dao/src/main/java/com/poizon/scm/wms/dao/mappers/commodity/ScpTenantMapper.java | 租户信息表映射器（已废弃） |
| 82 | 数据访问 | wms-service | MyBatis任务拦截器 | MybatisInterceptor.intercept() | - | wms-service/src/main/java/com/poizon/scm/wms/service/interceptor/task/MybatisInterceptor.java | 任务表动态替换时的SQL拦截处理 |
| 83 | 基础设施 | wms-common | 打印链路拦截器 | PrintTraceInterceptor.intercept() | - | wms-common/src/main/java/com/poizon/scm/wms/common/interceptor/PrintTraceInterceptor.java | SQL执行时打印链路ID的拦截器 |
| 84 | 基础设施 | wms-common | MyBatis查询拦截器 | MybatisSelectInterceptor.sqlIntercepter() | - | wms-common/src/main/java/com/poizon/scm/wms/common/interceptor/MybatisSelectInterceptor.java | SQL查询拦截器，用于监控和日志 |
| 85 | 工具类 | wms-common | 用户工具类 | UserUtils.buildQueryCommonParams() | getTenantCode() | wms-common/src/main/java/com/poizon/scm/wms/common/utils/UserUtils.java | 构建查询通用参数时设置租户编码 |
| 86 | 工具类 | wms-service | 入库传输工具类 | InboundTransferenceBeanUtils.setBeanUserWarehouseContext() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/common/utils/InboundTransferenceBeanUtils.java | 设置Bean用户仓库上下文时获取租户编码 |
| 87 | 工具类 | wms-service | SCP入库传输工具类 | ScpInboundTransferenceBeanUtils.setBeanUserWarehouseContext() | getTenantCode() | wms-service/src/main/java/com/poizon/scm/wms/common/utils/ScpInboundTransferenceBeanUtils.java | 设置SCP入库Bean上下文时获取租户编码 |
| 88 | 配置管理 | wms-infrastructure | WMS配置仓储 | WmsConfigConstRepositoryImpl.targetIdMap() | - | wms-infrastructure/src/main/java/com/poizon/scm/wms/infra/bas/config/repository/WmsConfigConstRepositoryImpl.java | 获取配置目标ID映射时使用用户上下文 |
| 89 | 配置管理 | wms-service | 收货磐石配置 | ReceivePanShiConfig.printTransfer() | - | wms-service/src/main/java/com/poizon/scm/wms/domain/inbound/receive/config/ReceivePanShiConfig.java | 磐石迁移功能配置判断时使用用户上下文 |
| 90 | 配置管理 | wms-service | 95分配置 | NinetyFiveConfiguration.needIdentifyAfterUpper() | - | wms-service/src/main/java/com/poizon/scm/wms/domain/inbound/common/config/NinetyFiveConfiguration.java | 95分配置判断时查询库存使用租户信息 |
| 91 | 数据源管理 | wms-infrastructure-report | 报表数据源处理器 | ReportDsProcessor.doDetermineDatasource() | - | wms-infrastructure-report/src/main/java/com/poizon/scm/wms/report/config/ReportDsProcessor.java | 动态数据源切换时的处理逻辑 |
| 92 | 测试代码 | wms-job-interfaces | 基础测试类 | BaseTest.setUp() | - | wms-job-interfaces/src/test/java/base/BaseTest.java | 测试环境中构建操作用户上下文 |
| 93 | 测试代码 | wms-interfaces | 基础测试类 | BaseTest.setUp() | - | wms-interfaces/src/test/java/com/poizon/base/BaseTest.java | 测试环境中构建操作用户上下文 |
| 94 | 测试代码 | wms-interfaces | 位置测试类 | LocationTest.setUp() | - | wms-interfaces/src/test/java/com/poizon/base/LocationTest.java | 测试环境中构建操作用户上下文 |

## 按业务域分类统计

### 1. 基础设施层 (Infrastructure)
- **wms-common**: 租户上下文管理、拦截器、切面处理
- **分布式调用**: Dubbo过滤器处理租户信息传递
- **数据访问**: MyBatis拦截器自动设置租户字段

### 2. 业务服务层 (Service)
- **入库业务**: 上架及时率、唯一码别名、离线识别
- **出库业务**: 出库超时告警、发货服务、异常处理
- **库存管理**: 库存查询、效期更新、批次管理
- **任务管理**: 任务接收、处理工厂
- **基础服务**: 仓库服务、容器服务

### 3. 数据访问层 (Data Access)
- **库存相关**: 库存CRUD操作、查询条件构建
- **任务相关**: 任务查询、任务详情查询
- **货主相关**: 货主信息查询、编码转换
- **商品相关**: SPU/SKU查询、商品图片查询

### 4. 接口层 (Interface)
- **管理后台**: 操作日志、枚举加载、容器管理
- **查询接口**: 质检任务查询、退货查询
- **命令接口**: 库存调整、业务操作

### 5. 消息队列 (Message Queue)
- **入库消息**: 质检完成、识别完成、二次质检
- **出库消息**: 工作站订单处理
- **上下文设置**: 消息处理时的租户上下文管理

### 6. 定时任务 (Scheduled Job)
- **库存相关**: 盘点变更、效期更新
- **业务流程**: 暂存货架触发
- **多租户处理**: 按租户维度执行任务

## 风险点分析

### 1. 高风险场景
- **数据隔离失效**: 租户参数校验不严格可能导致数据泄露
- **上下文丢失**: 异步处理、消息队列中租户上下文可能丢失
- **默认租户依赖**: 过度依赖默认租户"1"可能导致数据混乱

### 2. 中风险场景
- **RPC调用**: 跨服务调用时租户信息传递可能中断
- **定时任务**: 批量处理时租户上下文设置不当
- **缓存污染**: 不同租户数据可能在缓存中混合

### 3. 建议改进
- **统一租户获取**: 建议统一使用 `OperationUserContextHolder.getTenantCode()`
- **参数校验加强**: 在关键业务方法中增加租户参数有效性校验
- **监控告警**: 增加租户上下文缺失的监控和告警机制
- **文档完善**: 补充租户使用规范和最佳实践文档

## 总结

本次梳理共发现 **94个** 租户相关的使用场景，覆盖了WMS系统的各个层次和业务域。租户机制在系统中得到了广泛应用，主要用于：

1. **数据隔离**: 确保不同租户的数据相互隔离
2. **权限控制**: 基于租户进行访问权限控制
3. **业务路由**: 根据租户类型进行不同的业务处理逻辑
4. **上下文传递**: 在分布式调用和异步处理中传递租户信息

系统整体的租户机制设计较为完善，但仍需要在参数校验、异常处理和监控告警方面进一步加强。

## 梳理说明

本次梳理已进行 **10轮以上** 的全面自查，确保覆盖所有使用场景：

1. **第1-3轮**: 检索核心租户获取方法的直接调用
2. **第4-6轮**: 检索业务服务层、数据访问层的使用场景
3. **第7-8轮**: 检索消息队列、定时任务中的异步使用场景
4. **第9-10轮**: 检索工具类、配置类、测试类中的使用场景
5. **最终轮**: 全面检查确认无遗漏

梳理范围涵盖：
- **核心方法**: `OperationUserContextHolder.getTenantCode()` 和 `TenantContext.getTenantId()`
- **间接使用**: 通过用户上下文对象调用租户相关方法
- **配置设置**: 在各种上下文构建和参数设置中的使用
- **测试代码**: 测试环境中的租户上下文初始化

确保了梳理结果的完整性和准确性。
