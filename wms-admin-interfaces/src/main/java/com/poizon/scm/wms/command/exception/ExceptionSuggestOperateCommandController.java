package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateAddRequest;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateDisableRequest;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateEnableRequest;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateModifyRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description 异常建议操作表维护
 */
@RestController
@RequestMapping("/admin/exce/sug/operate")
@Api(tags = "H5-库内异常-异常建议操作")
public class ExceptionSuggestOperateCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 新增
     */
    @ApiOperation("新增异常建议操作")
    @PostMapping("/v1/add")
    public Result<Void> addSuggestOperate(@RequestBody ExceptionSuggestOperateAddRequest request) {
        return bus.handle(request);
    }

    /**
     * 修改
     */
    @ApiOperation("修改异常建议操作")
    @PostMapping("/v1/modify")
    public Result<Void> modifySuggestOperate(@RequestBody ExceptionSuggestOperateModifyRequest request) {
        return bus.handle(request);
    }

    /**
     * 停用
     */
    @ApiOperation("停用异常建议操作")
    @PostMapping("/v1/disable")
    public Result<Void> disableSuggestOperate(@RequestBody ExceptionSuggestOperateDisableRequest request) {
        return bus.handle(request);
    }

    /**
     * 启用
     */
    @ApiOperation("启用异常建议操作")
    @PostMapping("/v1/enable")
    public Result<Void> enableSuggestOperate(@RequestBody ExceptionSuggestOperateEnableRequest request) {
        return bus.handle(request);
    }

}
