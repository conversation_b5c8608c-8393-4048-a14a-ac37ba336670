package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ExceptionAuditParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionInfoRpcRepository;
import com.poizon.scm.wms.api.dto.request.exception.info.ExceptionAuditRequest;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.common.StringUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/6
 * @desc
 */
@Component
public class ExceptionAuditRequestExecutor extends AbstractCommandExecutor<ExceptionAuditRequest, Void> {

    @Autowired
    private WmsExceptionInfoRpcRepository wmsExceptionInfoRpcRepository;

    @Value("${wms.exception.audit.min.compensateAmount:0}") // 单位为分
    private Long minCompensateAmount;

    @Value("${wms.exception.audit.max.compensateAmount:2600000}") // 单位为分
    private Long maxCompensateAmount;

    @Override
    protected Void doExecute(ExceptionAuditRequest request) {

        ExceptionAuditParam param = BeanUtil.copy(request, ExceptionAuditParam.class);
        if (StringUtils.isNotBlank(request.getBondedCompensateAmount())) {
            try {
                param.setCompensateAmount(new BigDecimal(request.getBondedCompensateAmount()).multiply(new BigDecimal("100")).longValue());
                if (param.getCompensateAmount() < minCompensateAmount || param.getCompensateAmount() > maxCompensateAmount) {
                    throw new WmsOperationException("赔付金额输入有误");
                }
            } catch (NumberFormatException e) {
                throw new WmsOperationException("赔付金额格式错误", e);
            }
        }
        wmsExceptionInfoRpcRepository.audit(param);
        return null;
    }
}
