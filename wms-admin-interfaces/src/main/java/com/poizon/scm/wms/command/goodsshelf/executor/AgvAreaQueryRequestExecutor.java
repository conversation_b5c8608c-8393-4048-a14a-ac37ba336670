package com.poizon.scm.wms.command.goodsshelf.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaAdminApi;
import com.poizon.scm.wms.bas.api.command.area.response.BasAgvAreaResponse;
import com.poizon.scm.wms.command.goodsshelf.request.AgvAreaQueryRequest;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AgvAreaQueryRequestExecutor extends AbstractCommandExecutor<AgvAreaQueryRequest, List<BasAgvAreaResponse>> {
    @DubboReference(check = false)
    private BaseAreaAdminApi baseAreaAdminApi;
    @Override
    protected List<BasAgvAreaResponse> doExecute(AgvAreaQueryRequest request) {
        if(ObjectUtils.isEmpty(request.getWarehouseCode())){
            return new ArrayList<>();
        }
        Result<List<BasAgvAreaResponse>> apiResult = baseAreaAdminApi.selectAgvAreaList(request.getWarehouseCode());
        if(!Result.isSuccess(apiResult)){
            return new ArrayList<>();
        }
        return apiResult.getData();
    }
}
