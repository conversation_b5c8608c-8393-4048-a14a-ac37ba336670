package com.poizon.scm.wms.command.goodsshelf.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GoodsShelfSideUnbindRequest extends Request {
    /**
     * 库位层
     */
    private Integer goodsShelfLevel;
    /**
     * 库位列
     */
    private Integer goodsShelfColumn;
    /**
     *库位编码
     */
    private String locationCode;
    /**
     *仓库编码
     */
    private String warehouseCode;
    /**
     *库区编码
     */
    private String areaCode;
    /**
     *货架编码
     */
    private String goodsShelfCode;
    /**
     *货架面
     */
    private String goodsShelfSide;

}
