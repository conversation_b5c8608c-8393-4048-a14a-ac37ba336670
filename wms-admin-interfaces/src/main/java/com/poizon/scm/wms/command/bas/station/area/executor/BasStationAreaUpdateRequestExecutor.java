package com.poizon.scm.wms.command.bas.station.area.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationAreaApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaRequest;
import com.poizon.scm.wms.command.bas.station.area.request.BasStationAreaUpdateRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * date  23/04/2024 周二
 */
@Slf4j
@Component
public class BasStationAreaUpdateRequestExecutor extends AbstractCommandExecutor<BasStationAreaUpdateRequest, Boolean> {

    @DubboReference(interfaceClass = BasStationAreaApi.class, check = false)
    private BasStationAreaApi basStationAreaApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected Boolean doExecute(BasStationAreaUpdateRequest request) {
        BasStationAreaRequest basStationAreaRequest = BeanCopyUtils.copyProperties(request, BasStationAreaRequest.class);
        basStationAreaRequest.setTenantId(OperationUserContextHolder.getTenantCode());

        Result<Boolean> updateResult = LogUtils.logRemoteInvoke(log, basStationAreaRequest, basStationAreaApi::update);

        return Optional.ofNullable(updateResult).map(Result::getData)
                .orElse(Boolean.FALSE);
    }

}
