package com.poizon.scm.wms.command.bas.station.station.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * date  28/04/2024 周日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationResponse implements Serializable {
    private static final long serialVersionUID = -8616036016258886535L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 工位分区编码
     */
    private String stationAreaCode;

    /**
     * 工位组编码
     */
    private String stationGroupCode;

    /**
     * 工位编码
     */
    private String stationCode;

    /**
     * 工位名称
     */
    private String stationName;

    /**
     * 工位横坐标
     */
    private Integer row;

    /**
     * 工位纵坐标
     */
    private Integer column;

    /**
     * 逻辑删除
     */
    private Integer deleted;

    /**
     * 租户编码
     */
    private String tenantId;

    /**
     * 作业属性
     *
     * @see com.poizon.scm.wms .api.enums.StationPropertyEnum
     */
    private String stationProperty;

    /**
     * 作业属性 描述
     *
     * @see com.poizon.scm.wms.api.enums.StationPropertyEnum
     */
    private String stationPropertyDesc;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 工位类型(站点类型)
     *
     * @see com.poizon.scm.wms.bas.api.enums.StationTypeEnum
     * 上架、下架
     */
    private String stationType;

    /**
     * 工位/工作站的面（A、B面）
     * 普通工位该字段为空，AGV工位会有某一面的概念
     */
    private String stationSide;

}
