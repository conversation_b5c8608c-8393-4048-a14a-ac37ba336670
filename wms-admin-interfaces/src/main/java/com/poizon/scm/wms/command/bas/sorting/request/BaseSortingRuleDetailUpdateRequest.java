package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BaseSortingRuleDetailUpdateRequest {

    @ApiModelProperty("id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("格口")
    private String chuteCode;

    @ApiModelProperty("工作台")
    private String stationCode;

    @ApiModelProperty("优先级")
    private Integer priority;

    @ApiModelProperty("满箱提示")
    private String opGuide;

    @ApiModelProperty("状态")
    private Integer status;

    private Integer deleted;

    private List<RuleDetailItemRequest> itemApiRequestList;
}
