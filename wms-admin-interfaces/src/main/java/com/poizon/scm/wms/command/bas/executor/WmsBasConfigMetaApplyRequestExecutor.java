package com.poizon.scm.wms.command.bas.executor;

import com.google.common.collect.Maps;
import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigMetaApplyParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigConstRepository;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigMetaAdminRepository;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigMetaApplyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.config.WcsConfig;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class WmsBasConfigMetaApplyRequestExecutor extends AbstractCommandExecutor<WmsBasConfigMetaApplyRequest, Void> {

    @Autowired
    private WmsConfigMetaAdminRepository wmsConfigMetaAdminRepository;

    @Autowired
    private WmsConfigConstRepository wmsConfigConstRepository;

    @Autowired
    private WcsConfig wcsConfig;

    @Override
    protected void verify(WmsBasConfigMetaApplyRequest request) {
        super.verify(request);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(request.getTargetIdList()), "配置作用域对象列表不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getTargetType()), "配置作用域类型不能为空！");
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(request.getConfigItemIdList()), "配置元数据ID列表不能为空！");

        //元数据先只有开发能操作
        Preconditions.checkBiz(wcsConfig.allowOperateBaseInfoUserId(OperationUserContextHolder.get().getUserId()), "无操作权限！");
    }

    @Override
    protected Void doExecute(WmsBasConfigMetaApplyRequest request) {

        Map<String, String> targetNameMap = Maps.newHashMapWithExpectedSize(request.getTargetIdList().size());
        Map<String, String> map = wmsConfigConstRepository.targetIdMap(request.getTargetType());
        request.getTargetIdList().forEach(e -> targetNameMap.put(e, map.getOrDefault(e, "")));

        wmsConfigMetaAdminRepository.applyConfigMeta(WmsConfigMetaApplyParam.builder()
                .targetIdList(request.getTargetIdList())
                .targetNameMap(targetNameMap)
                .targetType(request.getTargetType())
                .configItemIdList(request.getConfigItemIdList())
                .updatedUserId(OperationUserContextHolder.get().getUserId())
                .updatedRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
