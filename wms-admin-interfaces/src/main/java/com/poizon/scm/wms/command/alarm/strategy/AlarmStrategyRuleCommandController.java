package com.poizon.scm.wms.command.alarm.strategy;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleCreateRequest;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleDeleteRequest;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleEditRequest;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleUpdateStatusRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Api(tags = {"PC-预警策略-command"})
@RequestMapping("/admin/alarm/strategy/rule")
@RestController
public class AlarmStrategyRuleCommandController {
    @Resource
    private CommandBus commandBus;

    @ApiOperation("创建策略规则")
    @PostMapping("/create")
    public Result<Void> create(@RequestBody @Valid AlarmStrategyRuleCreateRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("编辑策略规则")
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody @Valid AlarmStrategyRuleEditRequest request) {
        return commandBus.handle(request);
    }


    @ApiOperation("规则启用/禁用")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@RequestBody @Valid AlarmStrategyRuleUpdateStatusRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("删除规则")
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody @Valid AlarmStrategyRuleDeleteRequest request) {
        return commandBus.handle(request);
    }
}
