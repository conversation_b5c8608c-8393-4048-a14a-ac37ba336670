package com.poizon.scm.wms.command.centerreport.delivery;

import cn.hutool.core.date.DateUtil;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.report.DynamicPagingObject;
import com.poizon.scm.wms.api.dto.response.report.ReportCenterResponse;
import com.poizon.scm.wms.command.centerreport.delivery.request.DeliveryTimelinessRateExportRequest;
import com.poizon.scm.wms.command.centerreport.delivery.request.DeliveryTimelinessRateRequest;
import com.poizon.scm.wms.command.centerreport.delivery.response.DeliveryTimelinessRateResponse;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.utils.Preconditions;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: dwq
 * @Date: 2021/1/20 10:25 上午
 */
@RestController
@RequestMapping("admin/deliveryReport")
public class DeliveryReportController {

    @Autowired
    private CommandBus commandBus;

    @ApiOperation("截单出库24小时及时率报表")
    @PostMapping(value = "/list", produces = "application/json")
    @ResponseBody
    public Result<DynamicPagingObject<DeliveryTimelinessRateResponse>> delivery24Rate(@RequestBody Map param) {
        DeliveryTimelinessRateRequest request = new DeliveryTimelinessRateRequest();
        List<String> dates= (List<String>) MapUtils.getObject(param,"dateRange_2",null);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(dates), WmsExceptionCode.PARAMS_IS_EMPTY);
        request.setStartDate(DateUtil.parseDate(dates.get(0)));
        request.setEndDate(DateUtil.parseDate(dates.get(1)));
        List<String> warehouseCodes = (List<String>)MapUtils.getObject(param,"selectMultiWarehouseCode_1");
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(warehouseCodes), WmsExceptionCode.PARAMS_IS_EMPTY);
        //List<String> list = Arrays.asList(warehouseCodes.split(","));
        request.setWarehouseCodes(warehouseCodes);
        return commandBus.handle(request);
    }
    @ApiOperation(value = "截单出库24小时及时率报表导出")
    @PostMapping(path = "/export")
    @ResponseBody
    public Result<ReportCenterResponse> exportDetailList(@RequestBody Map param) {
        DeliveryTimelinessRateExportRequest request = new DeliveryTimelinessRateExportRequest();
        List<String> dates= (List<String>) MapUtils.getObject(param,"dateRange_2",null);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(dates), WmsExceptionCode.PARAMS_IS_EMPTY);
        request.setStartDate(DateUtil.parseDate(dates.get(0)));
        request.setEndDate(DateUtil.parseDate(dates.get(1)));
        List<String> warehouseCodes = (List<String>)MapUtils.getObject(param,"selectMultiWarehouseCode_1");
        request.setWarehouseCodes(warehouseCodes);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(warehouseCodes), WmsExceptionCode.PARAMS_IS_EMPTY);
        return commandBus.handle(request);
    }
}
