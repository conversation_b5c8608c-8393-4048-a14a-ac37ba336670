package com.poizon.scm.wms.command.goodsshelf.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.adapter.commodity.model.rsp.SkuBasicRspDo;
import com.poizon.scm.wms.adapter.commodity.repository.ScpSkuRepository;
import com.poizon.scm.wms.bas.api.command.goodsshelf.GoodsShelfAdminApi;
import com.poizon.scm.wms.bas.api.command.goodsshelf.request.GoodsShelfDetailApiRequest;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.GoodsShelfDetailResponse;
import com.poizon.scm.wms.command.goodsshelf.request.GoodsShelfDetailRequest;
import com.poizon.scm.wms.command.goodsshelf.response.GoodsShelfCommodityInfoResponse;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.service.commodity.mdmv3.ScpSkuV3Service;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoodsShelfDetailRequestExecutor extends AbstractCommandExecutor<GoodsShelfDetailRequest, GoodsShelfCommodityInfoResponse> {
    @DubboReference(check = false)
    GoodsShelfAdminApi goodsShelfAdminApi;
    @Autowired
    ICommodityQueryV2Service iCommodityQueryV2Service;

    @Autowired
    ScpSkuRepository scpSkuRepository;
    @Override
    protected GoodsShelfCommodityInfoResponse doExecute(GoodsShelfDetailRequest request) {
        if(ObjectUtils.isEmpty(request.getId())){
            throw new WmsOperationException("参数为空");
        }
        GoodsShelfDetailApiRequest apiRequest = new GoodsShelfDetailApiRequest();
        BeanUtils.copyProperties(request,apiRequest);
        apiRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        Result<GoodsShelfDetailResponse> apiResult = goodsShelfAdminApi.goodsShelfDetail(apiRequest);
        if(!Result.isSuccess(apiResult)){
            throw new WmsOperationException(apiResult.getMsg());
        }
        GoodsShelfCommodityInfoResponse response = new GoodsShelfCommodityInfoResponse();
        GoodsShelfDetailResponse goodsShelfDetail = apiResult.getData();
        if(!ObjectUtils.isEmpty(goodsShelfDetail)){
            BeanUtils.copyProperties(apiResult.getData(),response);
        }
        if(!ObjectUtils.isEmpty(goodsShelfDetail)&&!CollectionUtils.isEmpty(goodsShelfDetail.getList())){
            Set<String> skuSet = goodsShelfDetail.getList()
                    .stream()
                    .filter((goodsShelfItem) -> !StringUtils.isEmpty(goodsShelfItem.getSkuId()) && !StringUtils.isEmpty(goodsShelfItem.getUniqueCode()))
                    .map(GoodsShelfDetailResponse.GoodsShelfInfo::getSkuId)
                    .collect(Collectors.toSet());
            List<GoodsShelfCommodityInfoResponse.GoodsShelfCommodityDetail> commodityDetailList = goodsShelfDetail.getList().stream().map((goodsShelfItem) -> {
                GoodsShelfCommodityInfoResponse.GoodsShelfCommodityDetail goodsShelfCommodityDetail = new GoodsShelfCommodityInfoResponse.GoodsShelfCommodityDetail();
                BeanUtils.copyProperties(goodsShelfItem, goodsShelfCommodityDetail);
                return goodsShelfCommodityDetail;
            }).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(skuSet)){
                List<SkuBasicRspDo> skuBasicRspList = scpSkuRepository.selectSkuInfoBySkuIds(OperationUserContextHolder.getTenantCode(), skuSet);
                if(!CollectionUtils.isEmpty(skuBasicRspList)){
                    Map<String, SkuBasicRspDo> skuRspMap = skuBasicRspList.stream().collect(Collectors.toMap(SkuBasicRspDo::getSkuId, Function.identity(), (existingValue, newValue) -> existingValue));
                    commodityDetailList.forEach((commodityDetail)->{
                        if(!StringUtils.isEmpty(commodityDetail.getSkuId())&&!StringUtils.isEmpty(commodityDetail.getUniqueCode())){
                            SkuBasicRspDo skuBasicRspDo = skuRspMap.get(commodityDetail.getSkuId());
                            if(!ObjectUtils.isEmpty(skuBasicRspDo)){
                                BeanUtils.copyProperties(skuBasicRspDo,commodityDetail);
                            }
                        }
                    });
                }
            }
            response.setList(commodityDetailList);
        }
        String warehouseName = OperationUserContextHolder.get().getWarehouses()
                .stream()
                .filter((warehouse) -> warehouse.getWarehouseCode().equals(response.getWarehouseCode()))
                .map(OperationUserContext.UserWarehouse::getWarehouseName)
                .findFirst().orElse(null);
        //将仓库编码转换为仓库名
        response.setWarehouseName(warehouseName);
        return response;
    }
}
