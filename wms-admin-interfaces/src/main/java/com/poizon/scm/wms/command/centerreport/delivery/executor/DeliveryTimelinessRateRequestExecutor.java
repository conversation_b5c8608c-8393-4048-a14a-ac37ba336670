package com.poizon.scm.wms.command.centerreport.delivery.executor;

import com.poizon.scm.wms.api.dto.request.report.DynamicPagingObject;
import com.poizon.scm.wms.api.dto.request.report.ReportTableTitle;
import com.poizon.scm.wms.command.centerreport.delivery.request.DeliveryTimelinessRateRequest;
import com.poizon.scm.wms.command.centerreport.delivery.response.DeliveryTimelinessRateResponse;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.BeanUtil;
import com.poizon.scm.wms.domain.delivery.DeliveryReportService;
import com.poizon.scm.wms.domain.delivery.params.TimelinessRate24Params;
import com.poizon.scm.wms.domain.delivery.response.DeliveryTimeliness24RateReport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 截单24点及时率报表
 * @Author: dwq
 * @Date: 2021/1/20 9:48 下午
 */
@Service
public class DeliveryTimelinessRateRequestExecutor extends AbstractCommandExecutor<DeliveryTimelinessRateRequest, DynamicPagingObject<DeliveryTimelinessRateResponse>> {
    @Autowired
    private DeliveryReportService deliveryReportService;
    @Override
    protected DynamicPagingObject<DeliveryTimelinessRateResponse> doExecute(DeliveryTimelinessRateRequest request) {
        TimelinessRate24Params params = new TimelinessRate24Params();
        params.setStartDate(request.getStartDate());
        params.setEndDate(request.getEndDate());
        params.setWarehouseCodes(request.getWarehouseCodes());
        List<DeliveryTimeliness24RateReport> list = deliveryReportService.deliveryTimelinessRate24List(params);
        DynamicPagingObject<DeliveryTimelinessRateResponse> pagingObject = new DynamicPagingObject<>();
        pagingObject.setTableTitle(buildTableTitle());
        pagingObject.setTotal(list.size());
        pagingObject.setPageSize(10000);
        pagingObject.setPageNum(1);
        pagingObject.setContents(BeanUtil.copyListProperties(list,DeliveryTimelinessRateResponse.class));
        return pagingObject;
    }
    private List<ReportTableTitle> buildTableTitle() {
        List<ReportTableTitle> list = new ArrayList<>(4);

        ReportTableTitle warehouseTitle = new ReportTableTitle();
        warehouseTitle.setLabel("仓库");
        warehouseTitle.setProp(DeliveryTimeliness24RateReport.COL_WAREHOUSENAME);
        list.add(warehouseTitle);
        ReportTableTitle dateTitle = new ReportTableTitle();
        dateTitle.setLabel("日期");
        dateTitle.setProp(DeliveryTimeliness24RateReport.COL_DATE);
        list.add(dateTitle);
        ReportTableTitle totalTitle = new ReportTableTitle();
        totalTitle.setLabel("总单数");
        totalTitle.setProp(DeliveryTimeliness24RateReport.COL_TOTAL);
        list.add(totalTitle);
        ReportTableTitle num24Title = new ReportTableTitle();
        num24Title.setLabel("24点出库单数");
        num24Title.setProp(DeliveryTimeliness24RateReport.COL_NUM_24);
        list.add(num24Title);
        ReportTableTitle rateTitle = new ReportTableTitle();
        rateTitle.setLabel("24点出库及时率");
        rateTitle.setProp(DeliveryTimeliness24RateReport.COL_RATE);
        list.add(rateTitle);
        return list;
    }
}
