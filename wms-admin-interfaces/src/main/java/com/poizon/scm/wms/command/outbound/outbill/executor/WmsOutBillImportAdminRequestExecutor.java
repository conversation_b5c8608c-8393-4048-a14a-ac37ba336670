package com.poizon.scm.wms.command.outbound.outbill.executor;

import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.command.outbound.outbill.request.WmsOutBillImportAdminRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.outbound.outbill.listener.WmsOutBillDataListListener;
import com.poizon.scm.wms.domain.outbound.outbill.param.WmsOutBillImportData;
import com.poizon.scm.wms.domain.outbound.outbill.service.IWmsOutBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;


/**
 * 导入出库单据(手动生成)执行器
 *
 * <AUTHOR>
 * @date 2022/01/06 2:33 下午
 */
@Slf4j
@Service
public class WmsOutBillImportAdminRequestExecutor extends AbstractCommandExecutor<WmsOutBillImportAdminRequest, Void> {

    @Resource
    private IWmsOutBillService wmsOutBillService;
    @Resource
    private ThreadPoolTaskExecutor adminThreadPoolExecutor;

    @Override
    protected Void doExecute(WmsOutBillImportAdminRequest request) {
        log.info("入参:{}", JSON.toJSONString(request));
        InputStream inputStream = HttpUtil.createGet(request.getFileUrl()).execute().bodyStream();
        EasyExcelFactory.read(inputStream, WmsOutBillImportData.class,
                new WmsOutBillDataListListener(wmsOutBillService,
                        adminThreadPoolExecutor,
                        request.getFileUrl(),
                        request.getType(),
                        request.getWarehouseCode()))
                .sheet()
                .doRead();
        return null;
    }
}
