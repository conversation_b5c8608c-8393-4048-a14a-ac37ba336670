package com.poizon.scm.wms.command.alarm.strategy.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AlarmStrategyEditRequest extends Request {

    @ApiModelProperty(value = "策略编码")
    @NotBlank(message = "策略编码不能为空")
    private String strategyCode;

    @ApiModelProperty(value = "策略名称")
    @NotBlank(message = "策略名称不能为空")
    private String strategyName;

    @ApiModelProperty(value = "策略描述")
    @NotBlank(message = "策略描述不能为空")
    private String strategyDesc;


}
