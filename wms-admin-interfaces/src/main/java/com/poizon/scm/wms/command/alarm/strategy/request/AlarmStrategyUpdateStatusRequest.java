package com.poizon.scm.wms.command.alarm.strategy.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class AlarmStrategyUpdateStatusRequest extends Request {
    @ApiModelProperty(value = "策略编号")
    @NotBlank(message = "策略编号不能为空")
    private String strategyNo;
    @ApiModelProperty(value = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status;
}