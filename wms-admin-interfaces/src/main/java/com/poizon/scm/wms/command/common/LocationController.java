package com.poizon.scm.wms.command.common;

import com.poizon.scm.wms.api.command.common.request.LocationInitRequest;
import com.poizon.scm.wms.api.command.common.request.PickSeqRequest;
import com.poizon.scm.wms.common.WmsResult;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/admin/location/")
@Api(tags = "库位信息")
public class LocationController {

    @Autowired
    private CommandBus commandBus;

    @PostMapping(value = "/resetPickSeq")
    public WmsResult<String> resetPickSeq(@RequestBody @Valid PickSeqRequest pickSeqRequest){
        return WmsResult.success(commandBus.handle(pickSeqRequest));
    }

    /**
     * 初始化虚拟库位
     * @param locationInitRequest
     * @return
     */
    @PostMapping(value = "/initVirtualLocationCodes")
    public WmsResult<String> initVirtualLocationCodes(@RequestBody @Valid LocationInitRequest locationInitRequest){
        return WmsResult.success(commandBus.handle(locationInitRequest));
    }
}
