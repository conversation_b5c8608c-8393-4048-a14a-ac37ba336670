package com.poizon.scm.wms.command.common.executor;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.command.common.request.PickSeqRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.base.BasLocationService;
import com.poizon.scm.wms.service.base.param.PickSeqParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class PickSeqRequestExecutor extends AbstractCommandExecutor<PickSeqRequest, String> {

    @Autowired
    private BasLocationService basLocationService;

    @Override
    protected String doExecute(PickSeqRequest pickSeqRequest) {
        log.info("接受刷仓库的拣货库位请求, pickSeqRequest -> [{}]", JSON.toJSONString(pickSeqRequest));
        PickSeqParam pickSeqParam = new PickSeqParam();
        pickSeqParam.setTenantCode(pickSeqRequest.getTenantCode());
        pickSeqParam.setWarehouseCode(pickSeqRequest.getWarehouseCode());
        pickSeqParam.setAreaCode(pickSeqRequest.getAreaCode());
        pickSeqParam.setForcePass(pickSeqRequest.getForcePass());
        if(CollectionUtils.isNotEmpty(pickSeqRequest.getItems())){
            List<PickSeqParam.Item> items = new ArrayList<>(pickSeqRequest.getItems().size());
            for (PickSeqRequest.Item item : pickSeqRequest.getItems()) {
                PickSeqParam.Item paramItem = new PickSeqParam.Item();
                paramItem.setPassCode(item.getPassCode());
                paramItem.setSortRule(item.getSortRule());
                items.add(paramItem);
            }
            pickSeqParam.setItems(items);
        }

        /*批量更新库位的拣货顺序*/
        basLocationService.batchModifyPickSeq(pickSeqParam);
        return Result.DEFAULT_SUCCESS_MESSAGE;
    }
}
