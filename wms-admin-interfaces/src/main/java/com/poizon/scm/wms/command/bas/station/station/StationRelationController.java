package com.poizon.scm.wms.command.bas.station.station;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.station.station.request.BindStationRelationRequest;
import com.poizon.scm.wms.command.bas.station.station.request.UnbindStationRelationRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Author: 平川
 * @createTime: 2024年07月04日 15:47:45
 * @version: 1.0
 * @Description: 工位关系controller
 */

@RestController
@RequestMapping("/admin/station/relation")
public class StationRelationController {

    @Resource
    private CommandBus commandBus;

    /**
     * 人员登陆/绑定工位（工作站）
     *
     * @param request request
     */
    @PostMapping("/bind")
    public Result<Void> bind(@RequestBody @Valid BindStationRelationRequest request) {
        return commandBus.handle(request);
    }

    /**
     * 人员解绑工位（工作站）
     *
     * @param request request
     */
    @PostMapping("/unbind")
    public Result<Void> unbind(@RequestBody @Valid UnbindStationRelationRequest request) {
        return commandBus.handle(request);
    }

}
