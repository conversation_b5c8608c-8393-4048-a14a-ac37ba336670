package com.poizon.scm.wms.command.bas.station.station.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationApi;
import com.poizon.scm.wms.command.bas.station.station.request.BasStationQueryRequest;
import com.poizon.scm.wms.command.bas.station.station.response.BasStationResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  28/04/2024 周日
 */
@Slf4j
@Component
public class BasStationQueryRequestExecutor extends AbstractCommandExecutor<BasStationQueryRequest, List<BasStationResponse>> {

    @DubboReference(check = false)
    private BasStationApi basStationApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected List<BasStationResponse> doExecute(BasStationQueryRequest request) {
        com.poizon.scm.wms.bas.api.command.station.request.BasStationRequest basStationRequest = BeanCopyUtils.copyProperties(request, com.poizon.scm.wms.bas.api.command.station.request.BasStationRequest.class);

        basStationRequest.setTenantId(OperationUserContextHolder.getTenantCode());

        Result<List<com.poizon.scm.wms.bas.api.command.station.response.BasStationResponse>>
                selectResult = LogUtils.logRemoteInvoke(log, basStationRequest, basStationApi::select);

        return Optional.ofNullable(selectResult).map(Result::getData)
                .map(basStationResponses ->
                        basStationResponses.stream()
                                .map(basStationResponse -> BeanCopyUtils.copyProperties(basStationResponse, BasStationResponse.class))
                                .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

}
