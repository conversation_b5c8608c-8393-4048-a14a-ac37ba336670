package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.info.task.ExceptionTaskConfirmParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionTaskAdminRepository;
import com.poizon.scm.wms.api.dto.request.exception.task.ExceptionTaskConfirmRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionTaskConfirmRequestExecutor extends AbstractCommandExecutor<ExceptionTaskConfirmRequest, Void> {

    @Autowired
    private WmsExceptionTaskAdminRepository wmsExceptionTaskAdminRepository;

    @Override
    protected Void doExecute(ExceptionTaskConfirmRequest request) {
        ExceptionTaskConfirmParam param = new ExceptionTaskConfirmParam();
        param.setId(request.getId());
        param.setExceptionNo(request.getExceptionNo());
        param.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        param.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        param.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionTaskAdminRepository.confirm(param);
        return null;
    }

}
