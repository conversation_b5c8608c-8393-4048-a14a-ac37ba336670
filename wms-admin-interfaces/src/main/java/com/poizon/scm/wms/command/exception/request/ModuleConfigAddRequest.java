package com.poizon.scm.wms.command.exception.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class ModuleConfigAddRequest extends Request {

    @ApiModelProperty(value = "模块编码", required = true)
    @NotEmpty(message = "模块编码不能为空")
    private String moduleCode;

    @ApiModelProperty(value = "模块名称", required = true)
    @NotEmpty(message = "模块名称不能为空")
    private String moduleName;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("模块类型(1:仓储,0:生产)")
    private Integer type;
    /**
     * 所属责任方
     */
    @ApiModelProperty("所属责任方")
    private String duty;

}
