package com.poizon.scm.wms.command.bas.station.station.request;

import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: 平川
 * @createTime: 2024年07月04日 15:59:23
 * @version: 1.0
 * @Description: 绑定工位关系request
 */
@Data
public class BindStationRelationRequest extends Request {

    @NotBlank(message = "仓库编码不能为空")
    private String warehouseCode;

    @NotBlank(message = "工位/工作站不能为空")
    private String stationCode;

    /**
     * 工位面
     */
    private String stationSide;

}
