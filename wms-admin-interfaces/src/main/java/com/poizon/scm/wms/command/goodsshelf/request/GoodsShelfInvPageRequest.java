package com.poizon.scm.wms.command.goodsshelf.request;


import com.poizon.scm.wms.api.query.common.request.BaseSlidingRequest;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestPage;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsShelfInvPageRequest extends BaseSlidingRequest {
    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 货架编码
     */
    private String goodsShelfCode;

    /**
     * 库位编码
     */
    private String locationCode;

    /**
     * 唯一码（商品编码）
     */
    private String uniqueCode;

    /**
     *货架是否有货
     */
    private Boolean containsInv;

    /**
     *货架库存小于等于
     */
    private Integer invBelow;
}
