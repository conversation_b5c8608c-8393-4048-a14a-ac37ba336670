package com.poizon.scm.wms.command.alarm.rule.executor;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyRuleQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyRuleDo;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleDetailQueryRequest;
import com.poizon.scm.wms.command.alarm.rule.response.AlarmStrategyRuleDetailQueryResponse;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmRuleContent;
import com.poizon.scm.wms.domain.alarm.AlarmRuleTimeContent;
import com.poizon.scm.wms.util.json.JsonUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AlarmStrategyRuleDetailQueryRequestExecutor extends AbstractCommandExecutor<AlarmStrategyRuleDetailQueryRequest, AlarmStrategyRuleDetailQueryResponse> {

    @Resource
    private AlarmStrategyRuleQueryRepository alarmStrategyRuleQueryRepository;

    @Override
    protected AlarmStrategyRuleDetailQueryResponse doExecute(AlarmStrategyRuleDetailQueryRequest request) {
        AlarmStrategyRuleDo strategyRuleDo = alarmStrategyRuleQueryRepository.queryRuleByRuleNo(request.getRuleNo());
        if (strategyRuleDo == null) {
            throw new WmsOperationException("规则不存在");
        }
        return buildRuleDetailQueryResponse(strategyRuleDo);
    }

    private AlarmStrategyRuleDetailQueryResponse buildRuleDetailQueryResponse(AlarmStrategyRuleDo strategyRuleDo) {
        AlarmStrategyRuleDetailQueryResponse response = new AlarmStrategyRuleDetailQueryResponse();
        response.setRuleNo(strategyRuleDo.getRuleNo());
        response.setRuleName(strategyRuleDo.getRuleName());
        response.setRuleDesc(strategyRuleDo.getRuleDesc());
        response.setStatus(strategyRuleDo.getStatus());
        response.setRuleContent(JsonUtils.deserialize(strategyRuleDo.getRuleContent(), new TypeReference<List<AlarmRuleContent>>() {
        }));
        AlarmRuleTimeContent alarmRuleTimeContent = JSON.parseObject(strategyRuleDo.getScheduleTimes(), AlarmRuleTimeContent.class);
        response.setDuration(alarmRuleTimeContent.getFrequency());
        response.setTimePoints(alarmRuleTimeContent.getTimePoint());
        response.setScheduleType(strategyRuleDo.getScheduleType());
        return response;
    }
}
