package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ModuleConfigAddParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionConfigAdminRepository;
import com.poizon.scm.wms.command.exception.request.ModuleConfigAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ModuleConfigAddRequestExecutor extends AbstractCommandExecutor<ModuleConfigAddRequest, Void> {

    @Autowired
    private WmsExceptionConfigAdminRepository wmsExceptionConfigAdminRepository;

    @Override
    protected Void doExecute(ModuleConfigAddRequest request) {
        /* 检查名称不能超过30个字符 */
        Preconditions.checkBiz(request.getModuleName().length() <= 30, "模块名称长度不能超过30个字符！");
        Preconditions.checkBiz(request.getModuleCode().length() <= 30, "模块编码长度不能超过30个字符！");

        ModuleConfigAddParam param = new ModuleConfigAddParam();
        param.setTenantId(OperationUserContextHolder.getScmTenantCode());
        param.setModuleCode(request.getModuleCode());
        param.setModuleName(request.getModuleName());
        param.setSort(request.getSort());
        param.setType(request.getType());
        param.setDuty(request.getDuty());
        param.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        param.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        param.setCreatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionConfigAdminRepository.addModule(param);
        return null;
    }

}
