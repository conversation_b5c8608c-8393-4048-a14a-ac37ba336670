package com.poizon.scm.wms.command.alarm.rule.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Data
public class AlarmStrategyRuleListQueryResponse {
    /**
     * 策略编号
     */
    @ApiModelProperty("策略编号")
    private String strategyNo;

    /**
     * 规则编码
     */
    @ApiModelProperty("规则编码")
    private String ruleNo;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String ruleName;

    /**
     * 规则描述
     */
    @ApiModelProperty("规则描述")
    private String ruleDesc;


    /**
     * 规则状态(0-已停用,1-生效中)
     */
    @ApiModelProperty("规则状态(0-已停用,1-生效中)")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createdTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("修改时间")
    private String updatedTime;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private String nextExecutionTime;


    /**
     * 修改人真实姓名
     */
    @ApiModelProperty("修改人真实姓名")
    private String updatedRealName;

    /**
     * 创建人真实姓名
     */
    @ApiModelProperty("创建人真实姓名")
    private String createdRealName;
}
