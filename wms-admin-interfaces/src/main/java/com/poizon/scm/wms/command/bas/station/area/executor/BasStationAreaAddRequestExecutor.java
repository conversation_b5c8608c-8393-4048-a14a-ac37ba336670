package com.poizon.scm.wms.command.bas.station.area.executor;

import cn.hutool.core.collection.CollUtil;
import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationAreaApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaBatchAddRequest;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaRequest;
import com.poizon.scm.wms.bas.api.command.station.response.BaseStationAreaResponse;
import com.poizon.scm.wms.command.bas.station.area.request.BasStationAreaAddRequest;
import com.poizon.scm.wms.command.bas.station.area.response.BasStationAreaSelectResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * date 22/04/2024 1:57
 */
@Slf4j
@Component
public class BasStationAreaAddRequestExecutor extends AbstractCommandExecutor<BasStationAreaAddRequest, BasStationAreaSelectResponse> {

    @DubboReference(interfaceClass = BasStationAreaApi.class, check = false)
    private BasStationAreaApi basStationAreaApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected BasStationAreaSelectResponse doExecute(BasStationAreaAddRequest request) {
        List<BasStationAreaRequest> requestList = Collections.singletonList(BeanCopyUtils.copyProperties(request, BasStationAreaRequest.class));
        for (BasStationAreaRequest basStationAreaRequest : requestList) {
            basStationAreaRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        }

        BasStationAreaBatchAddRequest batchAddRequest = new BasStationAreaBatchAddRequest();
        batchAddRequest.setRequestList(requestList);

        Result<List<BaseStationAreaResponse>> result = LogUtils.logRemoteInvoke(log, batchAddRequest, basStationAreaApi::add);

        return Optional.ofNullable(result).map(Result::getData)
                .map(baseStationAreaResponses ->
                        {
                            if (CollUtil.isEmpty(baseStationAreaResponses)) {
                                return null;
                            } else {
                                return baseStationAreaResponses
                                        .stream()
                                        .map(baseStationAreaResponse -> BeanCopyUtils.copyProperties(baseStationAreaResponse, BasStationAreaSelectResponse.class))
                                        .findFirst()
                                        .orElse(null);

                            }
                        }
                )
                .orElse(null);
    }

}
