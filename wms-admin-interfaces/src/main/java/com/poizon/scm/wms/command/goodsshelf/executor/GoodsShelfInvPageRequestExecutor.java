package com.poizon.scm.wms.command.goodsshelf.executor;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaAdminApi;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaApi;
import com.poizon.scm.wms.bas.api.command.area.response.BasAgvAreaResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.GoodsShelfAdminApi;
import com.poizon.scm.wms.bas.api.command.goodsshelf.request.GoodsShelfInvPageApiRequest;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.GoodsShelfInvPageResponse;
import com.poizon.scm.wms.bas.api.common.response.BaseSlidingResponse;
import com.poizon.scm.wms.command.goodsshelf.request.GoodsShelfInvPageRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.util.NumberUtils;
import com.poizon.scm.wms.util.util.PagingObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoodsShelfInvPageRequestExecutor extends AbstractCommandExecutor<GoodsShelfInvPageRequest, BaseSlidingResponse<GoodsShelfInvPageResponse>> {
    @DubboReference(timeout = 5000, check = false)
    GoodsShelfAdminApi goodsShelfAdminApi;

    @DubboReference(check = false)
    private BaseAreaAdminApi baseAreaAdminApi;
    @Override
    protected BaseSlidingResponse<GoodsShelfInvPageResponse> doExecute(GoodsShelfInvPageRequest request) {
        if(!checkParam(request)){
            return BaseSlidingResponse.emptyResponse();
        }
        Result<List<BasAgvAreaResponse>> areaListResult = baseAreaAdminApi.selectAgvAreaList(request.getWarehouseCode());
        if(!Result.isSuccess(areaListResult)|| org.apache.commons.collections.CollectionUtils.isEmpty(areaListResult.getData())){
            return BaseSlidingResponse.emptyResponse();
        }
        List<String> areaCodes = areaListResult.getData().stream().map(BasAgvAreaResponse::getAreaCode).collect(Collectors.toList());
        GoodsShelfInvPageApiRequest apiRequest = new GoodsShelfInvPageApiRequest();
        apiRequest.setAreaCodes(areaCodes);
        BeanUtils.copyProperties(request,apiRequest);
        apiRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        Result<BaseSlidingResponse<GoodsShelfInvPageResponse>> aoiResult = goodsShelfAdminApi.queryGoodsShelfInvPage(apiRequest);
        if(!Result.isSuccess(aoiResult)||ObjectUtils.isEmpty(aoiResult.getData())|| CollectionUtils.isEmpty(aoiResult.getData().getContents())){
            return BaseSlidingResponse.emptyResponse();
        }
        String warehouseName = OperationUserContextHolder.get().getWarehouses()
                .stream()
                .filter((warehouse) -> warehouse.getWarehouseCode().equals(request.getWarehouseCode()))
                .map(OperationUserContext.UserWarehouse::getWarehouseName)
                .findFirst().orElse(null);
        //将仓库编码转换为仓库名
        aoiResult.getData().getContents().forEach((item)-> item.setWarehouseName(warehouseName));
        return aoiResult.getData();
    }
    private boolean checkParam(GoodsShelfInvPageRequest request){
        if(StringUtils.isEmpty(request.getWarehouseCode())){
            return Boolean.FALSE;
        }
        if(ObjectUtils.isEmpty(request.getContainsInv())){
            return Boolean.FALSE;
        }
        if(!ObjectUtils.isEmpty(request.getInvBelow())&&request.getInvBelow()<= NumberUtils.INTEGER_ZERO){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
