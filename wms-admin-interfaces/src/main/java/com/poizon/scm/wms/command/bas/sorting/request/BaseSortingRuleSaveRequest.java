package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BaseSortingRuleSaveRequest {

    @ApiModelProperty("仓库")
    @NotBlank(message = "仓库不能为空")
    private String warehouseCode;

    @ApiModelProperty("场景")
    @NotBlank(message = "场景不能为空")
    private String sceneCode;

    @ApiModelProperty("名字")
    @NotBlank(message = "名字不能为空")
    private String name;

    @ApiModelProperty("描述")
    @NotBlank(message = "描述不能为空")
    private String description;

    private Integer status;
}
