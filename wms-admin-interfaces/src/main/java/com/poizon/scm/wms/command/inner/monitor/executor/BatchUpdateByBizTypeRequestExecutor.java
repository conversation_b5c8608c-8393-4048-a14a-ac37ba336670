package com.poizon.scm.wms.command.inner.monitor.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.adapter.inner.monitor.dto.InventoryMonitorConfigOperateDTO;
import com.poizon.scm.wms.adapter.inner.monitor.repository.InventoryMonitorCommandRepository;
import com.poizon.scm.wms.command.inner.monitor.request.BatchUpdateByBizTypeRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.BeanUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *
 **/
@Component
public class BatchUpdateByBizTypeRequestExecutor extends AbstractCommandExecutor<BatchUpdateByBizTypeRequest, Object> {

    @Resource
    private InventoryMonitorCommandRepository inventoryMonitorCommandRepository;

    @Override
    protected Result<Boolean> doExecute(BatchUpdateByBizTypeRequest request) {

        InventoryMonitorConfigOperateDTO inventoryMonitorConfigOperateDTO = new InventoryMonitorConfigOperateDTO();
        BeanUtil.copyProperties(request, inventoryMonitorConfigOperateDTO);
        inventoryMonitorConfigOperateDTO.setMonitorType(2);
        return inventoryMonitorCommandRepository.batchUpdateConfig(inventoryMonitorConfigOperateDTO);
    }


}
