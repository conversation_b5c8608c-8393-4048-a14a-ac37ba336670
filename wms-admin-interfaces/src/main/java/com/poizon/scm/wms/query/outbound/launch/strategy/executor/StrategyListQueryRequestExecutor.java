package com.poizon.scm.wms.query.outbound.launch.strategy.executor;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.OffShelvesStrategyQueryParam;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyQueryRepository;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.query.outbound.launch.strategy.request.StrategyListQueryRequest;
import com.poizon.scm.wms.query.outbound.launch.strategy.response.StrategyListQueryResponse;
import com.poizon.scm.wms.util.enums.BizStrategyTypeEnum;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/29
 */
@Component
public class StrategyListQueryRequestExecutor extends AbstractCommandExecutor<StrategyListQueryRequest, PagingObject<StrategyListQueryResponse>> {

    @Resource
    private BizOffShelvesStrategyQueryRepository strategyQueryRepository;

    @Autowired
    private ScpWarehouseRepository scpWarehouseRepository;

    @Override
    protected PagingObject<StrategyListQueryResponse> doExecute(StrategyListQueryRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        OffShelvesStrategyQueryParam queryParam = BeanUtil.copy(request, OffShelvesStrategyQueryParam.class);
        if(CollectionUtils.isEmpty(queryParam.getWarehouseCodesList())){
            queryParam.setWarehouseCodesList(OperationUserContextHolder.get().getAuthWarehouseCodeList());
        }
        List<BizOffShelvesStrategyDo> strategyDos = strategyQueryRepository.queryList(queryParam);
        PageInfo<BizOffShelvesStrategyDo> pageInfo = new PageInfo<>(strategyDos);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PagingObject<>();
        }
        PagingObject<StrategyListQueryResponse> result = new PagingObject<>();
        result.setTotal(pageInfo.getTotal());
        List<StrategyListQueryResponse> responses = new ArrayList<>();
        for (BizOffShelvesStrategyDo bizOffShelvesStrategyDo : pageInfo.getList()) {
            StrategyListQueryResponse response = BeanUtil.copy(bizOffShelvesStrategyDo, StrategyListQueryResponse.class);
            response.setCreatedTime(DateUtils.formatTimeForAdmin(bizOffShelvesStrategyDo.getCreatedTime()));
            response.setUpdatedTime(DateUtils.formatTimeForAdmin(bizOffShelvesStrategyDo.getUpdatedTime()));
            responses.add(response);
        }
        Set<String> codes = responses.stream().map(StrategyListQueryResponse::getWarehouseCode).collect(Collectors.toSet());
        List<ScpWarehouseDo> scpWarehouseDos = scpWarehouseRepository.queryByCodesAndTenant(codes, OperationUserContextHolder.getScmTenantCode());
        Map<String, String> warehouseMap = scpWarehouseDos.stream().collect(Collectors.toMap(ScpWarehouseDo::getWarehouseCode, ScpWarehouseDo::getWarehouseName));
        for (StrategyListQueryResponse response : responses) {
            response.setWarehouseName(warehouseMap.get(response.getWarehouseCode()));
            response.setType(BizStrategyTypeEnum.getDescByType(response.getType()));
        }
        result.setContents(responses);
        return result;
    }



}
