package com.poizon.scm.wms.query.outbound.pick.executor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.model.BasAreaDo;
import com.poizon.scm.wms.adapter.common.model.BasLocationDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.LaunchDo;
import com.poizon.scm.wms.adapter.outbound.launch.repository.LaunchRepository;
import com.poizon.scm.wms.adapter.outbound.offshelves.model.PickingOrderDo;
import com.poizon.scm.wms.adapter.outbound.offshelves.repository.query.PickingOrderQueryRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDetailDo;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskDo;
import com.poizon.scm.wms.adapter.task.model.BasWorkZoneDo;
import com.poizon.scm.wms.adapter.task.repository.BasWorkZoneRepository;
import com.poizon.scm.wms.adapter.uniquecode.rpc.UniqueCodeAliasRepository;
import com.poizon.scm.wms.api.dto.request.pick.PickPageQueryRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.domain.inner.container.instance.enums.DirectionEnum;
import com.poizon.scm.wms.domain.outbound.pick.service.PickQueryService;
import com.poizon.scm.wms.domain.print.PrintStatisticalService;
import com.poizon.scm.wms.query.outbound.pick.request.PickPageQueryAdminRequest;
import com.poizon.scm.wms.query.outbound.pick.response.PickAreaInfo;
import com.poizon.scm.wms.query.outbound.pick.response.PickTaskAdminResponse;
import com.poizon.scm.wms.service.base.BasAreaService;
import com.poizon.scm.wms.service.base.BasLocationService;
import com.poizon.scm.wms.service.base.BasWarehouseService;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/16 11:53 上午
 **/
@Slf4j
@Component
public class PickPageQueryAdminRequestExecutor extends AbstractCommandExecutor<PickPageQueryAdminRequest, PagingObject<PickTaskAdminResponse>> {

    @Resource
    private PickQueryService pickQueryService;
    @Resource
    private PrintStatisticalService printStatisticalService;
    @Resource
    private LaunchRepository launchRepository;
    @Resource
    private PickingOrderQueryRepository pickingOrderQueryRepository;
    @Resource
    private BasWarehouseService basWarehouseService;
    @Resource
    private UniqueCodeAliasRepository uniqueCodeAliasRepository;

    @Autowired
    private BasAreaService basAreaService;

    @Autowired
    private BasLocationService basLocationService;
    @Autowired
    private BasWorkZoneRepository basWorkZoneRepository;
    @Override
    protected void verify(PickPageQueryAdminRequest request) {
        super.verify(request);
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getWorkZoneCodeList())){
            if(request.getWorkZoneCodeList().size()>5){
                throw new WmsOperationException(WmsExceptionCode.PARAMS_ERROR_WORK_ZONE_SIZE_ERROR);
            }
        }
    }

    @Override
    protected PagingObject<PickTaskAdminResponse> doExecute(PickPageQueryAdminRequest request) {
        PagingObject<PickTaskAdminResponse> resultPage =  new PagingObject<>();
        PickPageQueryRequest pageQueryRequest = BeanUtil.copy(request, PickPageQueryRequest.class);
        if (StringUtils.isNotBlank(pageQueryRequest.getUniqueCode())) {
            String uniqueCode = uniqueCodeAliasRepository.queryAlias(pageQueryRequest.getUniqueCode(), OperationUserContextHolder.getTenantCode());
            pageQueryRequest.setUniqueCode(StringUtils.isBlank(uniqueCode) ? pageQueryRequest.getUniqueCode() : uniqueCode);
        }
        PagingObject<PickTaskDo> pagingObject = pickQueryService.queryPickPageList(pageQueryRequest);
        resultPage.setPageNum(request.getPageNum());
        resultPage.setPageSize(request.getPageSize());
        resultPage.setPages(pagingObject.getPages());
        resultPage.setTotal(pagingObject.getTotal());
        List<PickTaskDo> taskEntityList = pagingObject.getContents();
        if (CollectionUtils.isEmpty(taskEntityList)) {
            resultPage.setContents(new ArrayList<>());
            return resultPage;
        }
        Set<String> taskNoSet = Sets.newHashSet();
        Set<String> launchNoSet = Sets.newHashSet();
        Set<String> warehouseCodeSet = Sets.newHashSet();
        taskEntityList.forEach(pickTaskDo -> {
            taskNoSet.add(pickTaskDo.getTaskNo());
            launchNoSet.add(pickTaskDo.getLaunchNo());
            warehouseCodeSet.add(pickTaskDo.getWarehouseCode());
        });

        //查询仓库名称
        Map<String, String> warehouseMap = basWarehouseService.findWarehouseName(warehouseCodeSet);
        //查询拣货明细
        List<PickTaskDetailDo> taskDetailEntityList = pickQueryService.queryPickDetailByTaskNos(new ArrayList<>(taskNoSet));
        Map<String, Integer> taskOperationCountMap = getTaskOperationCountMap(taskDetailEntityList);
        Map<String, Integer> taskInsertCountMap = getTaskInsertCountMap(taskDetailEntityList);
        //查询流向
        Map<String, List<String>> taskDirectionMap = getTaskDirectionMap(taskDetailEntityList);

        Map<String, PickAreaInfo> workZoneNamesAndAreaNames = getWorkZoneNamesAndAreaNames(taskDetailEntityList);

        //查询统计信息
        Map<String, Long> printStatisticalMap = Maps.newHashMap();
        printStatisticalMap.putAll(printStatisticalService.queryMapByReferences(Lists.newArrayList(taskNoSet), TaskTypeEnum.PICK.getTaskType()));
        //查询波次信息
        List<LaunchDo> launchDos = launchRepository.selectByLaunchNos(new ArrayList<>(launchNoSet));
        //判空
        Map<String, LaunchDo> launchMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(launchDos)){
            launchMap = launchDos.stream().collect(Collectors.toMap(LaunchDo::getLaunchNo,Function.identity()));
        }
        List<PickTaskAdminResponse> resultList = new ArrayList<>();
        for (PickTaskDo item : taskEntityList) {
            resultList.add(buildPickTaskAdminResponse(item,taskOperationCountMap,printStatisticalMap,
                    launchMap,taskDirectionMap,warehouseMap,workZoneNamesAndAreaNames, taskInsertCountMap));
        }
        resultPage.setContents(resultList);

        return resultPage;
    }

    private Map<String, Integer> getTaskInsertCountMap(List<PickTaskDetailDo> taskDetailEntityList) {
        HashMap<String, Integer> taskInsertCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(taskDetailEntityList)){
            return taskInsertCountMap;
        }

        return taskDetailEntityList.stream().collect(Collectors.groupingBy(PickTaskDetailDo::getTaskNo, Collectors.summingInt(PickTaskDetailDo::getAutoAddFlag)));


    }

    private Map<String, PickAreaInfo> getWorkZoneNamesAndAreaNames(List<PickTaskDetailDo> pickTaskDetailDoList){
        Map<String, List<PickTaskDetailDo>> collect = pickTaskDetailDoList.stream().
                collect(Collectors.groupingBy(PickTaskDetailDo::getTaskNo));
        Map<String,PickAreaInfo> pickAreaInfoMap = new HashMap<>();
        Map<String,Map<String, BasAreaDo>> warehouseAreaNameMap = new HashMap<>();
        collect.forEach((key,value)->{
            Set<String> locationCodes = new HashSet<>();
            Set<String> workZoneCodes = new HashSet<>();
            String warehouseCode = value.get(0).getWarehouseCode();
            for(PickTaskDetailDo detailDo:value){
                if(locationCodes.size()<6) {
                    locationCodes.add(detailDo.getLocationCode());
                    workZoneCodes.add(detailDo.getWorkZoneCode());
                }else{
                    break;
                }
            }
            if(!warehouseAreaNameMap.containsKey(warehouseCode)){
                Map<String, BasAreaDo> areaMap = basAreaService.areaMap(warehouseCode);
                warehouseAreaNameMap.put(warehouseCode,areaMap);
            }
            List<BasLocationDo> locationInfoList = basLocationService.areaDetailByCodes(warehouseCode, locationCodes);
            List<BasWorkZoneDo> workZoneInfoList = basWorkZoneRepository.selectByWorkZoneCodes(warehouseCode, workZoneCodes);
            Map<String, BasAreaDo> areaMap =warehouseAreaNameMap.get(warehouseCode);
            Set<String> areaNames = new HashSet<>();
            locationInfoList.forEach(e->{
                if(StringUtils.isNotBlank(e.getAreaCode()) && areaMap.get(e.getAreaCode()) !=null){
                    areaNames.add(areaMap.get(e.getAreaCode()).getAreaName());
                }
            });
            Set<String> workZoneNames = workZoneInfoList.stream().map(BasWorkZoneDo::getWorkZoneName).collect(Collectors.toSet());
            PickAreaInfo areaInfo = new PickAreaInfo(key,areaNames,workZoneNames);
            pickAreaInfoMap.put(key,areaInfo);
        });
        return pickAreaInfoMap;
    }

    private Map<String,Integer> getTaskOperationCountMap(List<PickTaskDetailDo> taskDetailEntityList) {
        Map<String, Integer> taskOperationCountMap = Maps.newHashMap();
       if (CollectionUtils.isEmpty(taskDetailEntityList)) {
            return taskOperationCountMap;
        }
        return taskDetailEntityList.stream().collect(Collectors.groupingBy(PickTaskDetailDo::getTaskNo,
                Collectors.summingInt(PickTaskDetailDo::getOperationQty)));
    }

    private Map<String,List<String>> getTaskDirectionMap(List<PickTaskDetailDo> taskDetailEntityList) {
        Map<String, List<String>> taskDirectionMap = new HashMap<>();
        List<String> pickingOrders = taskDetailEntityList.stream().filter(detail -> PickReferenceType.pickingOrderType.contains(detail.getReferenceType())).
                map(PickTaskDetailDo::getReferenceNo).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pickingOrders)) {
            return taskDirectionMap;
        }
        List<PickingOrderDo> pickingOrderDos = pickingOrderQueryRepository.queryByPickingOrderNo(pickingOrders);
        Map<String,String> pickingOrderMap = pickingOrderDos.stream().collect(Collectors.toMap(PickingOrderDo::getPickingOrderNo,PickingOrderDo::getFlowDirection));
        Map<String, List<PickTaskDetailDo>> collect = taskDetailEntityList.stream().filter(detail -> pickingOrders.contains(detail.getReferenceNo())).
                collect(Collectors.groupingBy(PickTaskDetailDo::getTaskNo));
        collect.forEach((k,v)->{
            List<String> flows = new ArrayList<>();
            for (PickTaskDetailDo pickTaskDetailDo : v) {
                String directionCode = pickingOrderMap.get(pickTaskDetailDo.getReferenceNo());
                flows.add(DirectionEnum.getDescByType(directionCode));
            }
            //一个拣货任务的流向去重复
            List<String> distinctFlows = flows.stream().distinct().collect(Collectors.toList());
            taskDirectionMap.put(k,distinctFlows);
        });
        return taskDirectionMap;
    }


    private PickTaskAdminResponse buildPickTaskAdminResponse(PickTaskDo entity,
                                                             Map<String, Integer> taskOperationCountMap,
                                                             Map<String, Long> printStatisticalMap,
                                                             Map<String, LaunchDo> launchMap,
                                                             Map<String, List<String>> taskDirectionMap,
                                                             Map<String, String> warehouseMap,
                                                             Map<String, PickAreaInfo> workZoneNamesAndAreaNames,
                                                             Map<String, Integer> taskInsertCountMap) {
        PickTaskAdminResponse taskAdmin = new PickTaskAdminResponse();
        taskAdmin.setPickTaskNo(entity.getTaskNo());
        taskAdmin.setTaskStatus(entity.getStatus());
        taskAdmin.setLaunchNo("DefaultNull".equals(entity.getLaunchNo()) ? "" : entity.getLaunchNo());
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.getTaskStatus(entity.getStatus());
        if (taskStatusEnum != null) {
            taskAdmin.setTaskStatusName(taskStatusEnum.getStatusName());
        }
        taskAdmin.setPlanQty(entity.getTotalQty());
        // 设置任务操作数量
        if (TaskStatusEnum.COMPLETE.getStatus().equals(entity.getStatus())) {
            taskAdmin.setOperationQty(entity.getTotalQty());
            taskAdmin.setTaskCompleteTime(DateUtils.formatTimeForAdmin(entity.getUpdatedTime()));
        } else {
            Integer operationQty = taskOperationCountMap.get(entity.getTaskNo());
            taskAdmin.setOperationQty(null != operationQty ? operationQty : 0);
        }
        LaunchDo launchDo = launchMap.get(entity.getLaunchNo());
        if (launchDo != null) {
            taskAdmin.setRemark(launchDo.getLaunchComment());
            taskAdmin.setLaunchCreateType(LaunchAutomaticFlagEnum.getDescByType(launchDo.getAutomaticFlag()));
            taskAdmin.setAutomaticFlag(launchDo.getAutomaticFlag());
            taskAdmin.setLaunchMode(LaunchModeEnum.getDescByType(launchDo.getLaunchMode()));
            if (launchDo.getAddOrderFlag().equals(1)) {
                taskAdmin.setLaunchMode("BP插单");
            }
        }

        taskAdmin.setTaskCreateTime(DateUtils.formatTimeForAdmin(entity.getCreatedTime()));
        taskAdmin.setPrintCounts(printStatisticalMap.getOrDefault(entity.getTaskNo(), 0L));
        taskAdmin.setTaskReceiver(entity.getReceiverName());
        taskAdmin.setTaskReceiveTime(DateUtils.formatTimeForAdmin(entity.getReceiveTime()));
        //从任务上取类型
        taskAdmin.setReferenceType(LaunchTypeEnum.getDescByType(entity.getReferenceType()));
        if(taskDirectionMap !=null && taskDirectionMap.size() > NumberUtils.INTEGER_ZERO){
            taskAdmin.setFlowDirections(taskDirectionMap.get(entity.getTaskNo()));
        }
        taskAdmin.setWarehouseCode(entity.getWarehouseCode());
        taskAdmin.setWareHouseName(Optional.ofNullable(warehouseMap.get(entity.getWarehouseCode())).orElse(StringUtils.EMPTY));
        taskAdmin.setOrderTags(DeliveryTagV2Enum.getDescByBitVal(entity.getOrderTags()));


        //未完成且领取人不为空
        if (TaskStatusEnum.unCompleteSet.contains(entity.getStatus())&&entity.getReceiverId().intValue()!= 0) {
            //捡货未完成
            //当前时间减去领取时间
            Date receiveTime = entity.getReceiveTime();
            String temp = com.poizon.scm.wms.util.util.DateUtils.obtainFormatStrFromTwoDate(receiveTime, new Date());
            taskAdmin.setPickNotFinish(temp);
        }else if(TaskStatusEnum.finishList.contains(entity.getStatus())&&entity.getReceiverId().intValue()!= 0){
            //捡货已完成
            //完成时间减去领取时间
            Date receiveTime = entity.getReceiveTime();
            Date finishTime = entity.getUpdatedTime();
            String temp = com.poizon.scm.wms.util.util.DateUtils.obtainFormatStrFromTwoDate(receiveTime, finishTime);
            taskAdmin.setPickFinish(temp);
        }
        taskAdmin.setReferenceNo(entity.getReferenceNo());

        taskAdmin.setAssignTime(DateUtils.formatTimeForAdmin(entity.getAssignTime()));
        taskAdmin.setAssignUserName(entity.getAssignUserName());

        PickAreaInfo info = workZoneNamesAndAreaNames.get(taskAdmin.getPickTaskNo());
        if (info != null){
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(info.getAreaNames())){
                if(info.getAreaNames().size()<6) {
                    taskAdmin.setAreaNames(String.join(",", info.getAreaNames()));
                }else {
                    StringBuffer sb = new StringBuffer();
                    info.getAreaNames().forEach(e-> sb.append(",").append(e));
                    sb.append("....");
                    taskAdmin.setAreaNames(sb.substring(1));
                }
            }
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(info.getWorkZoneNames())){
                if(info.getWorkZoneNames().size()<6) {
                    taskAdmin.setWorkZoneNames(String.join(",", info.getWorkZoneNames()));
                }else {
                    StringBuffer sb = new StringBuffer();
                    info.getWorkZoneNames().forEach(e-> sb.append(",").append(e));
                    sb.append("....");
                    taskAdmin.setWorkZoneNames(sb.substring(1));
                }
            }
        }

        // 插单数量
        Integer insertCount = taskInsertCountMap.getOrDefault(entity.getTaskNo(), 0);
        if(insertCount > 0){
            taskAdmin.setInsertQty(insertCount);
        }
        return taskAdmin;


    }
}
