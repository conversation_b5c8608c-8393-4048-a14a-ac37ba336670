package com.poizon.scm.wms.command.bas.station.area.request;

import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date 22/04/2024 11:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationAreaRequest extends Request implements Serializable {
    private static final long serialVersionUID = -1309093535990821225L;

    @NotNull(message = "id不能为空", groups = {ValidateGroups.Delete.class, ValidateGroups.Update.class})
    private Long id;

    @ApiModelProperty("仓库编码")
    @NotBlank(message = "仓库编码不能为空", groups = {ValidateGroups.Add.class})
    private String warehouseCode;

    @ApiModelProperty("分区编码")
    private String stationAreaCode;

    @ApiModelProperty("分区名称")
    @NotBlank(message = "分区名称不能为空", groups = ValidateGroups.Add.class)
    @Length(max = 100, message = "分区名称长度不能超过100", groups = ValidateGroups.Add.class)
    private String stationAreaName;

    @NotBlank(message = "分区类型不能为空", groups = ValidateGroups.Add.class)
    private String areaType;

}
