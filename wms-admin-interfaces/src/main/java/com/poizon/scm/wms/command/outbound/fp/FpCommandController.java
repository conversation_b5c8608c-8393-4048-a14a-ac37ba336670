package com.poizon.scm.wms.command.outbound.fp;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.consumer.outbound.inventoryAllocated.AlgLaunchInventoryAllocatedConsumer;
import com.poizon.scm.wms.domain.outbound.report.OutboundOrderEvent;
import com.poizon.scm.wms.outbound.api.common.request.Request;
import com.poizon.scm.wms.outbound.api.fp.FpCommandApi;
import com.poizon.scm.wms.outbound.api.fp.request.app.FpBatchUserOutParam;
import com.poizon.scm.wms.outbound.api.fp.request.app.FpUserOutParam;
import com.poizon.scm.wms.outbound.api.fp.request.command.*;
import com.poizon.scm.wms.outbound.api.fp.request.instance.FpRuleChangeRequest;
import com.poizon.scm.wms.outbound.api.fp.request.instance.FpShardingUpdateShardingStatusRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@RestController
@Api(tags = {"后台-fp订单规则相关"})
@RequestMapping("/admin/fp/")
public class FpCommandController {

    @DubboReference(interfaceClass = FpCommandApi.class, timeout = 5000, check = false)
    private FpCommandApi fpCommandApi;



    /**
     * fp区域订单规则新增
     * @param request
     * @return
     */
    @ApiOperation("fp区域订单规则新增")
    @PostMapping(value = "/v1/orderRule/add", produces = "application/json")
    public Result<Boolean> add(
            @RequestBody @Valid FpOrderRuleAddRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.add(request);
    }


    /**
     * 批量fp区域人员修改 （批量）
     * @param request
     * @return
     */
    @ApiOperation("批量fp区域人员修改")
    @PostMapping(value = "/v1/orderRule/batchAreaUserUpdate", produces = "application/json")
    public Result<Boolean> batchAreaUserUpdate(
            @RequestBody @Valid BatchFpAreaUserUpdateRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.batchAreaUserUpdate(request);
    }


    /**
     * 批量fp区域订单规则修改 （批量）
     * @param request
     * @return
     */
    @ApiOperation("批量fp区域订单规则修改")
    @PostMapping(value = "/v1/orderRule/batchUpdate", produces = "application/json")
    public Result<Boolean> batchAreaUserUpdate(
            @RequestBody @Valid BatchFpOrderRuleUpdateRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        return fpCommandApi.batchUpdate(request);
    }



    private void fillUserInfo(Request request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
    }


    @ApiOperation("fp区域订单规则修改")
    @PostMapping(value = "/v1/orderRule/update", produces = "application/json")
    public Result<Boolean> update(
            @RequestBody @Valid FpOrderRuleUpdateRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.update(request);
    }


    @ApiOperation("fp区域删除")
    @PostMapping(value = "/v1/orderRule/del", produces = "application/json")
    public Result<Boolean> orderRuleDel(
            @RequestBody @Valid FpOrderRuleDelRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.del(request);
    }

    @ApiOperation("fp区域规则修改")
    @PostMapping(value = "/v1/orderRule/updateFpInstanceRule", produces = "application/json")
    public Result<Boolean> updateFpInstanceRule(  @RequestBody @Valid FpRuleChangeRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        return fpCommandApi.updateFpInstanceRule(request);
    }


    @ApiOperation("fp区域人员新增")
    @PostMapping(value = "/v1/areaUser/add", produces = "application/json")
    public Result<Boolean> areaUserAdd(
            @RequestBody @Valid FpAreaUserAddRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.areaUserAdd(request);
    }

    @ApiOperation("fp区域人员修改")
    @PostMapping(value = "/v1/areaUser/update", produces = "application/json")
    public Result<Boolean> areaUserUpdate(
            @RequestBody @Valid FpAreaUserUpdateRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.areaUserUpdate(request);
    }


    @ApiOperation("删除区域人员配置")
    @PostMapping(value = "/v1/areaUser/del", produces = "application/json")
    public Result<Boolean> areaUserDel(
            @RequestBody @Valid FpAreaUserDelRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.areaUserDel(request);
    }


    @ApiOperation("批量置顶")
    @PostMapping(value = "/v1/pinned/top", produces = "application/json")
    public Result<String> batchPinnedTop(
            @RequestBody @Valid FpBatchPinnedTopRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.batchPinnedTop(request);
    }


    @ApiOperation("批量移除")
    @PostMapping(value = "/v1/batch/remove", produces = "application/json")
    public Result<Boolean> batchRemove(
            @RequestBody @Valid FpBatchRemoveRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.batchRemove(request);
    }


    /**
     * 新增fp模式配置
     *
     * @param request
     * @return
     */
    @ApiOperation("新增fp模式配置")
    @PostMapping(value = "/v1/addFpModeConfig", produces = "application/json")
    public Result<Boolean> addFpModeConfig(
            @RequestBody @Valid FpAreaModeAddRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.addFpModeConfig(request);
    }

    /**
     * 修改fp模式配置
     *
     * @param request
     * @return
     */
    @ApiOperation("修改fp模式配置")
    @PostMapping(value = "/v1/updateFpModeConfig", produces = "application/json")
    public Result<Boolean> updateFpModeConfig(
            @RequestBody @Valid FpAreaModeUpdateRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.updateFpModeConfig(request);
    }

    /**
     * 删除 fp模式配置
     *
     * @param request
     * @return
     */
    @ApiOperation("删除 fp模式配置")
    @PostMapping(value = "/v1/delFpModeConfig", produces = "application/json")
    public Result<Boolean> delFpModeConfig(
            @RequestBody @Valid FpAreaModeDelRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.delFpModeConfig(request);
    }

    /**
     * 批量人员退出s1
     *
     * @param request
     * @return
     */
    @ApiOperation("批量人员退出")
    @PostMapping(value = "/v1/batchUserOut", produces = "application/json")
    public Result<Boolean> batchUserOut(
            @RequestBody @Valid FpBatchUserOutParam request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        request.setUserId(userContext.getUserId());
        request.setUserName(userContext.getUserName());
        request.setRealName(userContext.getRealName());
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpCommandApi.batchUserOut(request);
    }


    /**
     * 批量人员退出s1
     *
     * @param request
     * @return
     */
    @ApiOperation("指派人员")
    @PostMapping(value = "/v1/assignFpShardingUser", produces = "application/json")
    public Result<Boolean> assignFpShardingUser(
            @RequestBody @Valid FpAssignFpShardingUserRequest request) {
        return fpCommandApi.assignFpShardingUser(request);
    }


    /**
     * 禁用队列1
     * @param request
     * @return
     */
    @ApiOperation("禁用队列")
    @PostMapping(value = "/v1/updateShardingStatus", produces = "application/json")
    public Result<Boolean> updateShardingStatus(
            @RequestBody @Valid FpShardingUpdateShardingStatusRequest request) {
        return fpCommandApi.updateShardingStatus(request);
    }

}
