package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BaseSortingEntityUpdateRequest {

    @ApiModelProperty("场景")
    @NotBlank(message = "场景不能为空")
    private String sceneCode;

    @ApiModelProperty("编码")
    @NotBlank(message = "编码不能为空")
    private String code;

    private String name;

    @ApiModelProperty("1 启用 2禁用")
    private Integer status;


    private Integer deleted;
}
