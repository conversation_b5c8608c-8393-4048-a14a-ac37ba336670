package com.poizon.scm.wms.command.antifake.response;
import lombok.Data;

import java.util.List;

/**
 * 防伪信息
 *
 * <AUTHOR>
 * @date 2023/05/18
 */
@Data
public class AntiFakeResponse {
    /**
     * 订单号
     */
    private String tradeOrderNo;
    /**
     * itemId
     */
    private Long itemId;
    /**
     * 唯一码
     */
    private String uniqueCode;
    /**
     * 最少绑扣数量  -1表示不固定数量
     */
    private Integer minCount;
    /**
     * 防伪扣绑定数量上限
     */
    private Integer maxCount;

    /**
     * 防伪完成标记  true 防伪完成  false 未完成
     */
    private Boolean antifakeDoneFlag;
    /**
     * 是否无需绑证 true 不需要绑定证书
     */
    private Boolean skipCertFlag;
    /**
     * 证书URL
     */
    private String cert;
    /**
     * 扣URL列表
     */
    private List<String> clasps;
    /**
     * 是否加购主单
     */
    private Boolean masterOrder;
    /**
     * 是否加购单
     */
    private Boolean slaveOrder;
    /**
     * 展示是否需要防伪贴
     */
    private boolean showBrandStickerFlag;
    /**
     * ai质检结果
     */
    private PinkAiQualityResponse aiQualityResponse;
    /**
     * 备注
     */
    private List<PinkOpRemarkDetailResponse> remarkDetailList;

    /**
     * 防伪复检复用证扣信息
     */
    private PinkAntifakeResponse antifakeVo;
}
