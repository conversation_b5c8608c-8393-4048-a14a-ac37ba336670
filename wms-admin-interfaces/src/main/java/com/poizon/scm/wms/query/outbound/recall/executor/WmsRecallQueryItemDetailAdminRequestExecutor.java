package com.poizon.scm.wms.query.outbound.recall.executor;

import com.poizon.scm.wms.adapter.third.outbound.recall.WmsRecallRpcRepository;
import com.poizon.scm.wms.adapter.third.outbound.recall.params.RecallQueryBillDetailParam;
import com.poizon.scm.wms.adapter.third.outbound.recall.result.WmsRecallDetailResult;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.query.outbound.recall.request.WmsRecallQueryItemDetailAdminRequest;
import com.poizon.scm.wms.query.outbound.recall.response.WmsRecallQueryItemDetailAdminResponse;
import com.poizon.scm.wms.util.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author： LMF
 * @date： 2023/12/4 5:48 PM
 * @description： 后台-召回详情查询执行器
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Component
public class WmsRecallQueryItemDetailAdminRequestExecutor  extends AbstractCommandExecutor<WmsRecallQueryItemDetailAdminRequest, List<WmsRecallQueryItemDetailAdminResponse>> {

    @Resource
    private WmsRecallRpcRepository wmsRecallRpcRepository;

    @Override
    protected List<WmsRecallQueryItemDetailAdminResponse> doExecute(WmsRecallQueryItemDetailAdminRequest request) {
        RecallQueryBillDetailParam param = new RecallQueryBillDetailParam();
        param.setRecallBillCode(request.getRecallBillCode());
        param.setTenantId(OperationUserContextHolder.getScmTenantCode());
        List<WmsRecallDetailResult> wmsRecallDetailResults = wmsRecallRpcRepository.queryRecallDetail(param);
        if (CollectionUtils.isEmpty(wmsRecallDetailResults)){
            return new ArrayList<>();
        }
        return BeanUtil.deepCopyByList(wmsRecallDetailResults,WmsRecallQueryItemDetailAdminResponse.class);
    }
}
