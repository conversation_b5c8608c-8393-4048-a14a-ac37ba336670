package com.poizon.scm.wms.command.common;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.command.common.CommonApi;
import com.poizon.scm.wms.api.command.common.request.OutBoundTypeQueryRequest;
import com.poizon.scm.wms.api.command.common.response.OutBoundTypeQueryResponse;
import com.poizon.scm.wms.common.WmsResult;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.util.enums.WmsBizTypeEnum;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 公共的接口
 * @Date 2020/11/12 2:21 下午
 */
@RestController
@RequestMapping("/admin/common/")
@Api(tags = "公共的接口")
public class CommonController implements CommonApi {

    @Autowired
    private CommandBus commandBus;

    @Override
    @PostMapping(value = "queryOutBoundTypeList", produces = "application/json")
    public Result queryOutBoundTypeList(@RequestBody OutBoundTypeQueryRequest outBoundTypeQueryRequest) {
        return commandBus.handle(outBoundTypeQueryRequest);
    }

    @Override
    @PostMapping(value = "queryBsTypeList", produces = "application/json")
    public Result queryBsTypeList() {

        OutBoundTypeQueryResponse outBoundTypeQueryResponse = new OutBoundTypeQueryResponse();

        List<OutBoundTypeQueryResponse.SingleOutBoundType> outBoundTypeList = new ArrayList<>();
        //保税品牌
        OutBoundTypeQueryResponse.SingleOutBoundType bsOutBoundType = new OutBoundTypeQueryResponse.SingleOutBoundType();
        bsOutBoundType.setBizType(WmsBizTypeEnum.BAO_SHUI.getBizType());
        bsOutBoundType.setBizTypeName(WmsBizTypeEnum.BAO_SHUI.getName());
        //国内品牌
        OutBoundTypeQueryResponse.SingleOutBoundType innerOutBoundType = new OutBoundTypeQueryResponse.SingleOutBoundType();
        innerOutBoundType.setBizType(WmsBizTypeEnum.PIN_PAI.getBizType());
        innerOutBoundType.setBizTypeName(WmsBizTypeEnum.PIN_PAI.getName());

        //活动入仓
        OutBoundTypeQueryResponse.SingleOutBoundType hDOutBoundType = new OutBoundTypeQueryResponse.SingleOutBoundType();
        hDOutBoundType.setBizType(WmsBizTypeEnum.HUO_DONG.getBizType());
        hDOutBoundType.setBizTypeName(WmsBizTypeEnum.HUO_DONG.getName());

        outBoundTypeList.add(bsOutBoundType);
        outBoundTypeList.add(innerOutBoundType);
        outBoundTypeList.add(hDOutBoundType);
        outBoundTypeQueryResponse.setOutBoundTypeList(outBoundTypeList);

        WmsResult result = new WmsResult();
        result.setData(outBoundTypeQueryResponse);
        return result;
    }
}
