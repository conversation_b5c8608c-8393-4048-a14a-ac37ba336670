package com.poizon.scm.wms.command.alarm.rule.response;

import com.poizon.scm.wms.domain.alarm.AlarmRuleContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AlarmStrategyRuleDetailQueryResponse {

    /**
     * 规则编码
     */
    @ApiModelProperty("规则编码")
    private String ruleNo;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String ruleName;

    /**
     * 规则描述
     */
    @ApiModelProperty("规则描述")
    private String ruleDesc;

    /**
     * 规则状态(0-已停用,1-生效中)
     */
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("规则维度内容")
    private List<AlarmRuleContent> RuleContent;

    /**
     * 调度类型 0-间隔,1-时间点
     */
    @ApiModelProperty("调度类型 0-指定频率,1-指定时间点")
    private Integer scheduleType;


    @ApiModelProperty("指定频率时间间隔（分钟）")
    private Integer duration;


    @ApiModelProperty("指定时间点（12:00）")
    private List<String> timePoints;

}
