package com.poizon.scm.wms.command.config.request;

import com.poizon.scm.wms.util.framework.Preconditions;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestException;
import com.poizon.scm.wms.util.framework.RequestExceptionCode;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: tywei
 * @create: 2021-06-03 4:52 下午
 **/
@Data
public class ModifyConfigRequest extends Request {
    private String configCode;
    private String configValue1;
    private String configValue2;
    private String configValue3;
    private String configValue4;
    private String configValue5;
    private String warehouseCode;
    private String bizKey;
    private String configDetailDesc;

    @Override
    public void verify() throws RequestException {
        Preconditions.checkBiz(StringUtils.isNotBlank(configCode), RequestExceptionCode.PARAMS_ERROR, "配置编码不为空");
    }
}
