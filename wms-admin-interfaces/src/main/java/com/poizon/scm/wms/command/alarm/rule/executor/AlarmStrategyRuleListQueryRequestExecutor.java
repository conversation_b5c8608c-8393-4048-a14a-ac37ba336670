package com.poizon.scm.wms.command.alarm.rule.executor;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyRuleQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.param.AlarmStrategyRuleListQueryParam;
import com.poizon.scm.wms.adapter.alarm.model.param.result.AlarmStrategyRuleListQueryResult;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleListQueryRequest;
import com.poizon.scm.wms.command.alarm.rule.response.AlarmStrategyRuleListQueryResponse;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Component
public class AlarmStrategyRuleListQueryRequestExecutor extends AbstractCommandExecutor<AlarmStrategyRuleListQueryRequest, PagingObject<AlarmStrategyRuleListQueryResponse>> {

    @Resource
    private AlarmStrategyRuleQueryRepository alarmStrategyRuleQueryRepository;

    @Override
    protected PagingObject<AlarmStrategyRuleListQueryResponse> doExecute(AlarmStrategyRuleListQueryRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<AlarmStrategyRuleListQueryResult> queryResults = alarmStrategyRuleQueryRepository.queryList(buildRuleListQueryParam(request));
        PageInfo<AlarmStrategyRuleListQueryResult> pageInfo = new PageInfo<>(queryResults);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PagingObject<>();
        }
        List<AlarmStrategyRuleListQueryResponse> responses = new ArrayList<>();
        for (AlarmStrategyRuleListQueryResult queryResult : queryResults) {
            AlarmStrategyRuleListQueryResponse response = BeanUtil.copy(queryResult, AlarmStrategyRuleListQueryResponse.class);
            response.setCreatedTime(DateUtils.formatTimeForAdmin(queryResult.getCreatedTime()));
            response.setUpdatedTime(DateUtils.formatTimeForAdmin(queryResult.getUpdatedTime()));
            response.setNextExecutionTime(DateUtils.formatTimeForAdmin(queryResult.getNextExecutionTime()));
            responses.add(response);
        }
        PagingObject<AlarmStrategyRuleListQueryResponse> result = new PagingObject<>();
        result.setTotal(pageInfo.getTotal());
        result.setContents(responses);
        return result;
    }

    private AlarmStrategyRuleListQueryParam buildRuleListQueryParam(AlarmStrategyRuleListQueryRequest request) {
        AlarmStrategyRuleListQueryParam queryParam = new AlarmStrategyRuleListQueryParam();
        queryParam.setStrategyNo(request.getStrategyNo());
        queryParam.setRuleName(request.getRuleName());
        queryParam.setStatus(request.getStatus());
        return queryParam;
    }
}
