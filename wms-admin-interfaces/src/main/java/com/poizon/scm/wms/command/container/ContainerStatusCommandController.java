package com.poizon.scm.wms.command.container;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.command.container.request.ContainerReleaseAdminRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 容器状态查询页面
 * <AUTHOR>
 * @date 2022/03/10
 */
@RequestMapping("/admin/container/status")
@RestController
@Slf4j
@Api(tags = "后台-容器状态查询")
public class ContainerStatusCommandController {

    @Resource
    private CommandBus commandBus;

    @ApiOperation(value = "容器释放")
    @PostMapping(value = "/v1/release")
    public Result<Void> releaseContainer(@RequestBody @Valid ContainerReleaseAdminRequest request) {
        return commandBus.handle(request);
    }
}
