package com.poizon.scm.wms.command.bas.station.map.request;

import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationMapRequest extends Request implements Serializable {
    private static final long serialVersionUID = -7812463892487968049L;

    /**
     * 自增ID
     */
    @NotNull(message = "id不能为空", groups = ValidateGroups.Delete.class)
    private Long id;

    /**
     * 仓库编码
     */
    @NotEmpty(message = "仓库编码不能为空", groups = ValidateGroups.Add.class)
    private String warehouseCode;

    /**
     * 工位分区编码
     */
    @NotEmpty(message = "工位分区编码不能为空", groups = {ValidateGroups.Add.class, ValidateGroups.Delete.class})
    private String stationAreaCode;

    /**
     * 工位组编码
     */
    private String stationGroupCode;

    /**
     * 工位横坐标
     */
    @Min(0)
    @Max(4)
    @NotNull(message = "工位横坐标不能为空", groups = ValidateGroups.Add.class)
    private Integer row;

    /**
     * 工位纵坐标
     */
    @Min(0)
    @Max(20)
    @NotNull(message = "工位纵坐标不能为空", groups = ValidateGroups.Add.class)
    private Integer column;

}
