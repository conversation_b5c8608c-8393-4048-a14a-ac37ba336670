package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.direction.ExceptionSuggestDirectionModifyParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionSuggestDirectionRepository;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionModifyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionSuggestDirectionModifyRequestExecutor extends AbstractCommandExecutor<ExceptionSuggestDirectionModifyRequest, Void> {

    @Autowired
    private WmsExceptionSuggestDirectionRepository wmsExceptionSuggestDirectionRepository;

    @Override
    protected void verify(ExceptionSuggestDirectionModifyRequest request) {
        /* 检查名称长度不能超 */
        if (StringUtils.isNotBlank(request.getDirectionName())) {
            Preconditions.checkBiz(request.getDirectionName().length() <= 150, "建议处理方向文案长度不能超过150个字符！");
        }
    }

    @Override
    protected Void doExecute(ExceptionSuggestDirectionModifyRequest request) {
        ExceptionSuggestDirectionModifyParam modifyParam = new ExceptionSuggestDirectionModifyParam();
        modifyParam.setId(request.getId());
        modifyParam.setDirectionCode(StringUtils.isNotBlank(request.getDirectionCode()) ? request.getDirectionCode() : null);
        modifyParam.setDirectionName(request.getDirectionName());
        modifyParam.setSort(request.getSort());
        modifyParam.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        modifyParam.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        modifyParam.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionSuggestDirectionRepository.modify(modifyParam);
        return null;
    }

}
