package com.poizon.scm.wms.web.log;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.util.enums.TenantEnum;
import com.poizon.scm.wms.web.log.request.OperationLogPageQueryRequest;
import com.poizon.scp.wms.log.api.dto.OperationLogInfoDTO;
import com.poizon.scp.wms.log.api.param.PageQueryLogParam;
import com.poizon.scp.wms.log.api.service.OperationLogQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * 统一日志中心-埋点管理
 */
@RestController
@RequestMapping("/admin/log/")
@Api(tags = "后台-统一日志中心-日志查询")
public class OperationLogInfoQueryController {

    @DubboReference(interfaceClass = OperationLogQueryService.class, timeout = 5000, check = false)
    private OperationLogQueryService trackingOperationLogService;

    @PostMapping(value = "/pageQuery", produces = "application/json")
    @ApiOperation("分页查询")
    public Result<PagingObject<OperationLogInfoDTO>> pageQuery(@RequestBody OperationLogPageQueryRequest operationLogPageQueryRequest) {
        PageQueryLogParam pageQueryLogParam = new PageQueryLogParam();
        BeanUtils.copyProperties(operationLogPageQueryRequest, pageQueryLogParam);
        pageQueryLogParam.setTenantCode(OperationUserContextHolder.getTenantCode());
        if (pageQueryLogParam.getTenantCode().equals(TenantEnum.CLOUD_WMS.getCode())) {
            List<String> warehouseCodeList = operationLogPageQueryRequest.getWarehouseCodeList();
            if (warehouseCodeList == null || warehouseCodeList.isEmpty()) {
                throw new WmsOperationException("请选择仓库");
            }
            List<String> authWarehouseCodeList = OperationUserContextHolder.get().getAuthWarehouseCodeList();
            for (String warehouseCode : warehouseCodeList) {
                if (!authWarehouseCodeList.contains(warehouseCode)) {
                    throw new WmsOperationException("只能查询有权限的仓库数据");
                }
            }
        }
        Result<PagingObject<OperationLogInfoDTO>> pageQueryResult = trackingOperationLogService.pageQuery(pageQueryLogParam);
        return pageQueryResult;
    }
}
