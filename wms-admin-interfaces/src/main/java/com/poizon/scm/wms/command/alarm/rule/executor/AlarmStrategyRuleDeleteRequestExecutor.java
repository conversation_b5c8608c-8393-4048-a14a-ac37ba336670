package com.poizon.scm.wms.command.alarm.rule.executor;

import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleDeleteRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyRuleCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AlarmStrategyRuleDeleteRequestExecutor extends AbstractCommandExecutor<AlarmStrategyRuleDeleteRequest, Void> {

    @Resource
    private AlarmStrategyRuleCommandService alarmStrategyRuleCommandService;

    @Override
    protected Void doExecute(AlarmStrategyRuleDeleteRequest request) {
        alarmStrategyRuleCommandService.deleteRule(request.getRuleNo());
        return null;
    }



}
