package com.poizon.scm.wms.command.common.response;
import lombok.Data;

import java.util.List;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @date 2023/05/18
 */
@Data
public class GoodsInfoResponse {
    /**
     * skuId
     */
    private String skuId;

    /**
     * spuId
     */
    private String spuId;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 商品编码(69码)
     */
    private String barcode;
    /**
     * 商品图片
     */
    private String picture;
    /**
     * 商品货号
     */
    private String articleNumber;
    /**
     * 商品规格
     */
    private String specs;
    /**
     * 国码
     */
    private String standCode;
    /**
     * 附加码
     */
    private String extraCode;
    /**
     * 一级类目Id
     */
    private String level1CategoryId;
    /**
     * 一级类目名称
     */
    private String level1CategoryName;
    /**
     * 三级类目Id
     */
    private String level3CategoryId;
    /**
     * 三级类目名称
     */
    private String level3CategoryName;
    /**
     * 价格等级名称
     */
    private String priceLevelName;
    /**
     * 配件列表
     */
    private List<Accessories> accessoriesList;
    /**
     * 商品轮播图
     */
    private List<String> productSliderImages;

    /**
     * 属性集合
     * */
    private String propertyText;

    //配件详情内部类
    @Data
    public static class Accessories {
        //配件图
        private String pic;
        //配件名
        private String label;
    }
}
