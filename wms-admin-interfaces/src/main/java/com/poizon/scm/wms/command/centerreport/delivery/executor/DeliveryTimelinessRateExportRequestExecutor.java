package com.poizon.scm.wms.command.centerreport.delivery.executor;

import cn.hutool.json.JSONUtil;
import com.poizon.scm.wms.api.dto.request.reportDownload.ReportCreateRequest;
import com.poizon.scm.wms.api.dto.response.report.ReportCenterResponse;
import com.poizon.scm.wms.command.centerreport.delivery.request.DeliveryTimelinessRateExportRequest;
import com.poizon.scm.wms.common.IExcelExportService;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.domain.delivery.DeliveryReportService;
import com.poizon.scm.wms.domain.delivery.params.TimelinessRate24Params;
import com.poizon.scm.wms.service.reportDownload.ReportDownloadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 截单24点及时率报表
 * @Author: dwq
 * @Date: 2021/1/20 9:48 下午
 */
@Service
public class DeliveryTimelinessRateExportRequestExecutor extends AbstractCommandExecutor<DeliveryTimelinessRateExportRequest, ReportCenterResponse> {
    @Autowired
    private DeliveryReportService deliveryReportService;
    @Autowired
    private IExcelExportService excelExportService;
    @Autowired
    private ReportDownloadService reportDownloadService;

    private static String[] REPORT_HEADER={"仓库","日期","出库单总数","24点出库单数","出库及时率"};
    @Override
    protected ReportCenterResponse doExecute(DeliveryTimelinessRateExportRequest request) {
        TimelinessRate24Params params = new TimelinessRate24Params();
        params.setStartDate(request.getStartDate());
        params.setEndDate(request.getEndDate());
        params.setWarehouseCodes(request.getWarehouseCodes());

        String s = JSONUtil.toJsonStr(params);
        ReportCreateRequest reportCreateRequest = reportDownloadService.buildReportDownloadRequest("截单24点出库及时率", "wms", s);
        ReportCenterResponse response = excelExportService.triggerAsyncDownload((() -> deliveryReportService.deliveryTimelinessRate24Export(params)),
                "截单24点出库及时率" + DateUtils.dateToString(new Date(), DateUtils.FROMAT_DATE)+ OperationUserContextHolder.get().getUserId(),
                "截单24点出库及时率", REPORT_HEADER, reportCreateRequest);
        return response;
    }
}
