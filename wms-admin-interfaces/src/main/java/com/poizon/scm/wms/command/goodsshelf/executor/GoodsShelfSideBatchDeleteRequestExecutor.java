package com.poizon.scm.wms.command.goodsshelf.executor;

import com.alibaba.fastjson.JSONObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.goodsshelf.GoodsShelfAdminApi;
import com.poizon.scm.wms.bas.api.command.goodsshelf.request.GoodsShelfBatchDeleteApiRequest;
import com.poizon.scm.wms.bas.api.command.location.BasLocationAdminApi;
import com.poizon.scm.wms.bas.api.command.location.request.LocationGoodsShelfQueryRequest;
import com.poizon.scm.wms.bas.api.command.location.request.LocationQueryRequest;
import com.poizon.scm.wms.bas.api.command.location.response.LocationByGoodsShelfResponse;
import com.poizon.scm.wms.bas.api.enums.GoodsShelfSideEnum;
import com.poizon.scm.wms.command.goodsshelf.request.GoodsShelfSideBatchDeleteRequest;
import com.poizon.scm.wms.command.goodsshelf.request.GoodsShelfSideDeleteRequest;
import com.poizon.scm.wms.common.constants.GoodsShelfSideConstant;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.util.NumberUtils;
import com.shizhuang.athena.api.query.InventoryQueryApi;
import com.shizhuang.athena.api.request.query.LocationInvQueryRequest;
import com.shizhuang.athena.api.response.query.InventoryQtyListResponse;
import com.shizhuang.athena.api.response.query.LocationInvQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoodsShelfSideBatchDeleteRequestExecutor extends AbstractCommandExecutor<GoodsShelfSideBatchDeleteRequest,Void> {
    @DubboReference(check = false)
    private GoodsShelfAdminApi goodsShelfAdminApi;

    @DubboReference(check = false, version = "1.0", interfaceClass = InventoryQueryApi.class, timeout = 4000)
    private InventoryQueryApi inventoryQueryApi;

    @DubboReference(interfaceClass = BasLocationAdminApi.class, timeout = 5000, check = false, version="1.0")
    private BasLocationAdminApi basLocationAdminApi;

    @Override
    protected Void doExecute(GoodsShelfSideBatchDeleteRequest request) {
        if(CollectionUtils.isEmpty(request.getList()) ||request.getList().size()> GoodsShelfSideConstant.MAX_DELETE_SIZE){
            throw new WmsOperationException(String.format("批量删除的货架面超过了%d个或不存在",GoodsShelfSideConstant.MAX_DELETE_SIZE));
        }
        String warehouseCode = checkParam(request);
        log.info("批量删除以下库位朝向:{}", JSONObject.toJSONString(request.getList()));
        //转换请求list
        List<LocationGoodsShelfQueryRequest.LocationGoodsShelfItem> list = request.getList().stream().map((item) -> {
            LocationGoodsShelfQueryRequest.LocationGoodsShelfItem locationGoodsShelfItem = new LocationGoodsShelfQueryRequest.LocationGoodsShelfItem();
            BeanUtils.copyProperties(item, locationGoodsShelfItem);
            return locationGoodsShelfItem;
        }).collect(Collectors.toList());
        LocationGoodsShelfQueryRequest locationGoodsShelfQueryRequest = new LocationGoodsShelfQueryRequest();
        locationGoodsShelfQueryRequest.setList(list);
        //查询朝向集合对应的所有库位
        Result<List<LocationByGoodsShelfResponse>> locationCodeListResult = basLocationAdminApi.locationListByGoodsShelf(locationGoodsShelfQueryRequest);
        if(!Result.isSuccess(locationCodeListResult)){
            throw new WmsOperationException(locationCodeListResult.getMsg());
        }
        List<LocationByGoodsShelfResponse> locationRespList = locationCodeListResult.getData();
        //要删除的货架朝向集合没有绑定库位
        if(CollectionUtils.isEmpty(locationRespList)){
            locationRespList = new ArrayList<>();
        }
        //locationCodeList:货架朝向集合包含的已绑定的所有库位编码
        List<String> locationCodeList = locationRespList.stream().map(LocationByGoodsShelfResponse::getLocationCode).collect(Collectors.toList());
        //hasQtyLocationCodes:库存大于0的库位编码
        Set<String> hasQtyLocationCodes = new HashSet<>();
        //需要判断库位上是否存在库存（分批判断）
        if(!CollectionUtils.isEmpty(locationCodeList)){
            int size = locationCodeList.size();
            int index = NumberUtils.INTEGER_ZERO;
            while(index<size){
                int endIndex = Math.min(index+GoodsShelfSideConstant.BATCH_QUERY_SIZE,size);
                List<String> subLocationCodeList = locationCodeList.subList(index, endIndex);
                //调用库存接口查询,返回的是包含库存和库位编码的对象列表
                LocationInvQueryRequest locationInvQueryRequest = new LocationInvQueryRequest();
                locationInvQueryRequest.setWarehouseCode(warehouseCode);
                locationInvQueryRequest.setLocationCodeList(subLocationCodeList);
                com.shizhuang.avatar.common.model.Result<LocationInvQueryResponse> queryResult = inventoryQueryApi.queryInvByLocation(locationInvQueryRequest);
                if(!com.shizhuang.avatar.common.model.Result.isSuccess(queryResult)){
                    throw new WmsOperationException(queryResult.getMsg());
                }
                if(ObjectUtils.isEmpty(queryResult.getData())||CollectionUtils.isEmpty(queryResult.getData().getLocationInvInfoList())){
                    index = endIndex;
                    continue;
                }
                List<LocationInvQueryResponse.LocationInvInfo> locationInvInfoList = queryResult.getData().getLocationInvInfoList();
                //遍历列表，如果库存大于0，将其加入到removeLocationCodes这个set中
                    locationInvInfoList.forEach((invInfo)->{
                        if(invInfo.getQty()>NumberUtils.INTEGER_ZERO){
                            hasQtyLocationCodes.add(invInfo.getLocationCode());
                        }
                    });
                index = endIndex;
            }
        }
        //库存大于0的库位的标识符集合
        Set<String> hasQtyLocationIdentifier = new HashSet<>();
        locationRespList.forEach((locationItem)->{
            if(hasQtyLocationCodes.contains(locationItem.getLocationCode())){
                hasQtyLocationIdentifier.add(locationItem.getWarehouseCode()+"_"+locationItem.getAreaCode()+"_"+locationItem.getGoodsShelfCode()+"_"+locationItem.getGoodsShelfSide());
            }
        });
        List<GoodsShelfSideDeleteRequest> goodsShelfList = request.getList();
        //删除库存大于0的库位对应的货架朝向
        goodsShelfList.removeIf((goodsShelfItem)-> hasQtyLocationIdentifier.contains(goodsShelfItem.getWarehouseCode()+"_"+goodsShelfItem.getAreaCode()+"_"+goodsShelfItem.getGoodsShelfCode()+"_"+goodsShelfItem.getGoodsShelfSide()));
        //库存为0或null的货架朝向的标识符集合
        Set<String> zeroQtyGoodsShelfIdentifier = new HashSet<>();
        goodsShelfList.forEach((goodsShelfItem)-> zeroQtyGoodsShelfIdentifier.add(goodsShelfItem.getWarehouseCode()+"_"+goodsShelfItem.getAreaCode()+"_"+goodsShelfItem.getGoodsShelfCode()+"_"+goodsShelfItem.getGoodsShelfSide()));
        locationRespList.removeIf((locationItem)-> !zeroQtyGoodsShelfIdentifier.contains(locationItem.getWarehouseCode()+"_"+locationItem.getAreaCode()+"_"+locationItem.getGoodsShelfCode()+"_"+locationItem.getGoodsShelfSide()));
        //6.转化为Id集合
        List<Long> goodsShelfIdList = goodsShelfList.stream().map(GoodsShelfSideDeleteRequest::getId).collect(Collectors.toList());
        List<Long> locationIdList = locationRespList.stream().map(LocationByGoodsShelfResponse::getId).collect(Collectors.toList());
        //批量删除的货架朝向上都存在库存
        if(ObjectUtils.isEmpty(goodsShelfIdList)){
            return null;
        }
        log.info("实际能删除的货架朝向:{}", JSONObject.toJSONString(goodsShelfList));
        log.info("实际能删除的库位:{}", JSONObject.toJSONString(locationRespList));
        GoodsShelfBatchDeleteApiRequest batchDeleteApiRequest = new GoodsShelfBatchDeleteApiRequest();
        batchDeleteApiRequest.setGoodsShelfIds(goodsShelfIdList);
        batchDeleteApiRequest.setLocationIds(locationIdList);
        Result<Boolean> batchDeleteApiResult = goodsShelfAdminApi.batchDeleteGoodsShelf(batchDeleteApiRequest);
        if(!Result.isSuccess(batchDeleteApiResult)){
            throw new WmsOperationException(batchDeleteApiResult.getMsg());
        }
        return null;
    }
    public String checkParam(GoodsShelfSideBatchDeleteRequest request){
        List<GoodsShelfSideDeleteRequest> list = request.getList();
        Set<String> warehouseSet = new HashSet<>();
        list.forEach((goodsShelfDeleteItem)->{
            if(ObjectUtils.isEmpty(goodsShelfDeleteItem.getId())){
                throw new WmsOperationException("存在货架面id为空");
            }
            if(StringUtils.isBlank(goodsShelfDeleteItem.getWarehouseCode())){
                throw new WmsOperationException("存在货架面仓库为空");
            }
            if(StringUtils.isBlank(goodsShelfDeleteItem.getAreaCode())){
                throw new WmsOperationException("存在货架面库区为空");
            }
            if(StringUtils.isBlank(goodsShelfDeleteItem.getGoodsShelfCode())){
                throw new WmsOperationException("存在货架面编码为空");
            }
            if(StringUtils.isBlank(goodsShelfDeleteItem.getGoodsShelfSide())|| !GoodsShelfSideEnum.containsSide(goodsShelfDeleteItem.getGoodsShelfSide())){
                throw new WmsOperationException("存在货架面朝向为空或不存在");
            }
            warehouseSet.add(goodsShelfDeleteItem.getWarehouseCode());
        });
        if(warehouseSet.size()!=NumberUtils.INTEGER_ONE){
            throw new WmsOperationException("批量删除的货架面不在同一仓库");
        }
        return warehouseSet.iterator().next();
    }
}
