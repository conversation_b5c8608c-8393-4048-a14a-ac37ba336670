package com.poizon.scm.wms.command.inner.onshelf.executor;

import com.poizon.scm.wms.command.inner.onshelf.request.OnShelfAdminReleaseRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.upper.UpperCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/8/24 8:53 下午
 * @description
 */
@Component
public class OnShelfAdminReleaseRequestExecutor
        extends AbstractCommandExecutor<OnShelfAdminReleaseRequest, Void> {

    @Resource
    UpperCommandService upperCommandService;

    @Override
    protected Void doExecute(OnShelfAdminReleaseRequest request) {
        // 释放上架任务
        upperCommandService.releaseUpperTask(request.getTaskNo());
        return null;
    }
}
