package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配置元数据应用请求")
public class WmsBasConfigMetaApplyRequest extends Request {

    /**
     * 作用域对象列表
     */
    @ApiModelProperty(value = "作用域对象列表", required = true)
    private List<String> targetIdList;

    /**
     * 配置作用域类型
     */
    @ApiModelProperty(value = "配置作用域类型", required = true)
    private String targetType;

    /**
     * 配置元数据ID列表
     */
    @ApiModelProperty(value = "配置元数据ID列表", required = true)
    private List<Long> configItemIdList;

}
