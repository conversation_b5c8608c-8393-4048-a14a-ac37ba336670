package com.poizon.scm.wms.web.base;

import com.dewu.scm.lms.api.report.Import.IReportImportApi;
import com.dewu.scm.lms.api.report.dto.request.ImportRequest;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.base.container.BasContainerQueryRequest;
import com.poizon.scm.wms.api.dto.request.base.container.ChangeBasContainerStatusRequest;
import com.poizon.scm.wms.api.dto.request.base.container.DeleteBasContainerRequest;
import com.poizon.scm.wms.api.dto.response.base.BasTypeResponse;
import com.poizon.scm.wms.api.dto.response.base.container.BasContainerQueryResponse;
import com.poizon.scm.wms.api.dto.response.report.ReportCenterResponse;
import com.poizon.scm.wms.common.WmsResult;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.translators.BasEnumTranslators;
import com.poizon.scm.wms.lpn.api.container.ContainerBasQueryApi;
import com.poizon.scm.wms.lpn.api.container.ContainerBasServiceApi;
import com.poizon.scm.wms.lpn.api.container.request.bas.ChangeBasContainerActiveRequest;
import com.poizon.scm.wms.lpn.api.container.response.bas.BasContainerQueryListResponse;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.web.base.executor.ContainerImportFileRequestExecutor;
import com.poizon.scm.wms.web.base.request.BasContainerImportRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @description:
 * @author:lantianxiang
 * @createTime:2020/12/23 2:09 下午
 */
@RequestMapping("admin/base/")
@RestController
@Slf4j
@Api(tags = {"后台-基础资料-容器管理"})
public class BasContainerController {

    @Value("${lpn.base.container.switch:true}")
    private Boolean lpnSwitch;

    @Autowired
    private CommandBus commandBus;

    @DubboReference(interfaceClass = ContainerBasQueryApi.class, timeout = 5000, check = false)
    private ContainerBasQueryApi containerBasQueryApi;

    @DubboReference(interfaceClass = ContainerBasServiceApi.class, timeout = 5000, check = false)
    private ContainerBasServiceApi containerBasServiceApi;

    @Resource
    private ContainerImportFileRequestExecutor containerImportFileRequestExecutor;

    @PostMapping("/v1/queryBasContainerList")
    @ApiOperation("容器管理分页查询")
    public Result<PagingObject<BasContainerQueryResponse>> queryBasContainerList(@RequestBody @Valid BasContainerQueryRequest request){
        if(lpnSwitch){
            com.poizon.scm.wms.lpn.api.container.request.bas.BasContainerQueryRequest queryRequest = BeanUtil.copy(request, com.poizon.scm.wms.lpn.api.container.request.bas.BasContainerQueryRequest.class);
            queryRequest.setTenantId(OperationUserContextHolder.getTenantCode());
            com.shizhuang.avatar.common.model.Result<PagingObject<BasContainerQueryListResponse>> result = containerBasQueryApi.queryBasContainerList(queryRequest);
            if(Objects.isNull(result)){
                log.error("调用wms-lpn接口异常 request:{}", JsonUtils.serialize(request));
                throw new WmsException("调用wms-lpn接口异常");
            }
            if (!com.shizhuang.avatar.common.model.Result.isSuccess(result)) {
                return Result.of(result.getCode(),result.getMsg());
            }
            return Result.ofSuccess(BeanUtil.deepCopy(result.getData(), PagingObject.class));
        }

        return commandBus.handle(request);
    }

    @PostMapping("/v1/importBasContainerList")
    @ApiOperation("容器基础数据导入")
    public Result<ReportCenterResponse> queryBasContainerList(@RequestBody @Valid BasContainerImportRequest request){
        return commandBus.handle(request);
    }

    @GetMapping("/v1/deleteBasContainer")
    @ApiOperation("容器删除")
    public Result<Void> deleteReplenish(@Valid DeleteBasContainerRequest request){
        if(lpnSwitch){
            com.poizon.scm.wms.lpn.api.container.request.bas.DeleteBasContainerRequest deleteRequest = new com.poizon.scm.wms.lpn.api.container.request.bas.DeleteBasContainerRequest();
            deleteRequest.setTenantId(OperationUserContextHolder.getTenantCode());
            deleteRequest.setOperationTime(new Date());
            OperationUserContext userContext = OperationUserContextHolder.get();
            deleteRequest.setOperationUserId(userContext.getUserId());
            deleteRequest.setOperationUserName(userContext.getUserName());
            deleteRequest.setTenantId(userContext.getTenantCode());
            deleteRequest.setContainerCode(request.getContainerCode());
            com.shizhuang.avatar.common.model.Result<Void> result = containerBasServiceApi.deleteReplenish(deleteRequest);
            if(Objects.isNull(result)){
                log.error("调用wms-lpn接口异常 request:{}", JsonUtils.serialize(request));
                throw new WmsException("调用wms-lpn接口异常");
            }
            if (!com.shizhuang.avatar.common.model.Result.isSuccess(result)) {
                return Result.of(result.getCode(),result.getMsg());
            }
            return Result.ofSuccess(null);
        }
        return commandBus.handle(request);
    }

    @GetMapping("/v1/changeBasContainerStatus")
    @ApiOperation("容器禁用/启用")
    public Result<Void> deleteReplenish(@Valid ChangeBasContainerStatusRequest request){
        if(lpnSwitch){
            ChangeBasContainerActiveRequest updateActiveRequest = new ChangeBasContainerActiveRequest();
            updateActiveRequest.setTenantId(OperationUserContextHolder.getTenantCode());
            updateActiveRequest.setOperationTime(new Date());
            OperationUserContext userContext = OperationUserContextHolder.get();
            updateActiveRequest.setOperationUserId(userContext.getUserId());
            updateActiveRequest.setOperationUserName(userContext.getUserName());
            updateActiveRequest.setTenantId(userContext.getTenantCode());
            updateActiveRequest.setIsActive(request.getIsActive());
            updateActiveRequest.setContainerCode(request.getContainerCode());
            com.shizhuang.avatar.common.model.Result<Void> result = containerBasServiceApi.updateContainerActive(updateActiveRequest);
            if(Objects.isNull(result)){
                log.error("调用wms-lpn接口异常 request:{}", JsonUtils.serialize(request));
                throw new WmsException("调用wms-lpn接口异常");
            }
            if (!com.shizhuang.avatar.common.model.Result.isSuccess(result)) {
                return Result.of(result.getCode(),result.getMsg());
            }
            return Result.ofSuccess(null);
        }

        return commandBus.handle(request);
    }

    @ApiOperation("容器类别")
    @GetMapping(path = "/containerTypes")
    @ResponseBody
    public Result<List<BasTypeResponse>> containerTypes() {
        List<BasTypeResponse> list = BasEnumTranslators.getContainerTypeResponse();
        return WmsResult.success(list);
    }

    @ApiOperation("容器使用类型")
    @GetMapping(path = "/containerUseTypes")
    @ResponseBody
    public Result<List<BasTypeResponse>> containerUseTypes() {
        List<BasTypeResponse> list = BasEnumTranslators.getContainerUseTypeResponse();
        return WmsResult.success(list);
    }

    @ApiOperation("容器状态")
    @GetMapping(path = "/containerStatus")
    @ResponseBody
    public Result<List<BasTypeResponse>> containerStatus() {
        List<BasTypeResponse> list = BasEnumTranslators.getContainerStatusResponse();
        return WmsResult.success(list);
    }

    @ApiOperation("容器是否启用")
    @GetMapping(path = "/containerActive")
    @ResponseBody
    public Result<List<BasTypeResponse>> containerActive() {
        List<BasTypeResponse> list = BasEnumTranslators.getContainerActiveResponse();
        return WmsResult.success(list);
    }

    @ApiOperation("容器是否启用")
    @PostMapping(path = "/lpnContainerSwitch")
    @ResponseBody
    public Result<Boolean> lpnContainerSwitch() {
        return WmsResult.success(lpnSwitch);
    }


    /**
     * 目前仅DB云仓使用，
     * 因为DB不支持lms公共导出，且没有导入中心，所以使用老的导入
     *
     * @since 556 db云仓容器导入
     * @param request
     * @return
     */
    @ApiOperation("容器导入")
    @PostMapping(path = "/container/importFile")
    @ResponseBody
    public Result<ReportCenterResponse> importFile(@RequestBody BasContainerImportRequest request) {
        try {
            log.info("容器导入参数:{}", JsonUtils.serialize(request));
            ReportCenterResponse reportCenterResponse = containerImportFileRequestExecutor.doExecute(request);
            return Result.ofSuccess(reportCenterResponse);
        } catch (WmsOperationException e){
            throw e;
        } catch (Exception e){
            log.error("导入出现未知异常 request:{}", JsonUtils.serialize(request), e);
            throw new WmsException("导入容器异常，请稍后再试");
        }
    }
}
