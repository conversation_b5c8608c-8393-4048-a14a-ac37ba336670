package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.operate.ExceptionSuggestOperateModifyParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionSuggestOperateRepository;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateModifyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionSuggestOperateModifyRequestExecutor extends AbstractCommandExecutor<ExceptionSuggestOperateModifyRequest, Void> {

    @Autowired
    private WmsExceptionSuggestOperateRepository wmsExceptionSuggestOperateRepository;

    @Override
    protected void verify(ExceptionSuggestOperateModifyRequest request) {
        /* 检查名称长度不能超 */
        if (StringUtils.isNotBlank(request.getOperateName())) {
            Preconditions.checkBiz(request.getOperateName().length() <= 50, "建议操作按钮文案长度不能超过50个字符！");
        }

        if (StringUtils.isNotBlank(request.getSysName())) {
            Preconditions.checkBiz(request.getSysName().length() <= 100, "对应系统模块名称长度不能超过100个字符！");
        }
    }

    @Override
    protected Void doExecute(ExceptionSuggestOperateModifyRequest request) {
        ExceptionSuggestOperateModifyParam modifyParam = new ExceptionSuggestOperateModifyParam();
        modifyParam.setId(request.getId());
        modifyParam.setOperateCode(StringUtils.isNotBlank(request.getOperateCode()) ? request.getOperateCode() : null);
        modifyParam.setOperateName(request.getOperateName());
        modifyParam.setSysCode(StringUtils.isNotBlank(request.getSysCode()) ? request.getSysCode() : null);
        modifyParam.setSysName(request.getSysName());
        modifyParam.setSort(request.getSort());
        modifyParam.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        modifyParam.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        modifyParam.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionSuggestOperateRepository.modify(modifyParam);
        return null;
    }

}
