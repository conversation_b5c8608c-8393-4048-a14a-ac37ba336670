package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigSaveParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigAdminRepository;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigConstRepository;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigSaveRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class WmsBasConfigSaveRequestExecutor extends AbstractCommandExecutor<WmsBasConfigSaveRequest, Void> {

    @Autowired
    private WmsConfigAdminRepository wmsConfigAdminRepository;

    @Autowired
    private WmsConfigConstRepository wmsConfigConstRepository;

    @Override
    protected void verify(WmsBasConfigSaveRequest request) {
        super.verify(request);

        Preconditions.checkBiz(StringUtils.isNotBlank(request.getTargetId()), "配置作用域对象不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getTargetType()), "配置作用域类型不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getType()), "配置类型不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getCategory()), "配置分类不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getCode()), "配置编码不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getName()), "配置名称不能为空！");

        if (StringUtils.isNotBlank(request.getValue())) {
            Preconditions.checkBiz(StringUtils.isNotBlank(request.getValueType()), "配置值类型不能为空！");
        }
    }

    @Override
    protected Void doExecute(WmsBasConfigSaveRequest request) {

        String targetName = request.getTargetName();
        if (StringUtils.isBlank(targetName)) {
            Map<String, String> map = wmsConfigConstRepository.targetIdMap(request.getTargetType());
            targetName = map.getOrDefault(request.getTargetId(), "");
        }

        wmsConfigAdminRepository.save(WmsConfigSaveParam.builder()
                .targetId(request.getTargetId().trim())
                .targetName(targetName)
                .targetType(request.getTargetType().trim())
                .configItemId(request.getConfigItemId())
                .type(request.getType().trim())
                .category(request.getCategory().trim())
                .code(request.getCode().trim())
                .name(request.getName().trim())
                .valueType(request.getValueType())
                .value(request.getValue())
                .level(request.getLevel())
                .priority(request.getPriority())
                .sort(request.getSort())
                .remark(request.getRemark())
                .createdUserId(OperationUserContextHolder.get().getUserId())
                .createdRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
