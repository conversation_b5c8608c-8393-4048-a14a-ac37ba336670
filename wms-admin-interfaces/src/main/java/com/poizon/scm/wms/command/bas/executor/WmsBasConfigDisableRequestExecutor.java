package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigModifyParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigAdminRepository;
import com.poizon.scm.wms.bas.api.enums.EnableStatusEnum;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigDisableRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WmsBasConfigDisableRequestExecutor extends AbstractCommandExecutor<WmsBasConfigDisableRequest, Void> {

    @Autowired
    private WmsConfigAdminRepository wmsConfigAdminRepository;

    @Override
    protected void verify(WmsBasConfigDisableRequest request) {
        super.verify(request);
        Preconditions.checkBiz(null != request.getId(), "配置ID不能为空！");
    }

    @Override
    protected Void doExecute(WmsBasConfigDisableRequest request) {

        wmsConfigAdminRepository.modify(WmsConfigModifyParam.builder()
                .id(request.getId())
                .mode(EnableStatusEnum.DISABLE.code())
                .updatedUserId(OperationUserContextHolder.get().getUserId())
                .updatedRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
