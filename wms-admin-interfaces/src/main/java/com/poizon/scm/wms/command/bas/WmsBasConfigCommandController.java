package com.poizon.scm.wms.command.bas;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.request.*;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 配置查询服务
 */
@RestController
@RequestMapping("/admin/bas/config")
@Api(tags = "H5-BAS配置-配置管理")
public class WmsBasConfigCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 新增配置
     */
    @ApiOperation("新增配置")
    @PostMapping("/add")
    public Result<Void> addConfig(@RequestBody WmsBasConfigSaveRequest request) {
        return bus.handle(request);
    }

    /**
     * 复制配置
     */
    @ApiOperation("复制配置")
    @PostMapping("/copy")
    public Result<Void> copyConfig(@RequestBody WmsBasConfigCopyRequest request) {
        return bus.handle(request);
    }

    /**
     * 删除配置
     */
    @ApiOperation("删除配置")
    @PostMapping("/remove")
    public Result<Void> removeConfig(@RequestBody WmsBasConfigRemoveRequest request) {
        return bus.handle(request);
    }

    /**
     * 修改配置
     */
    @ApiOperation("修改配置")
    @PostMapping("/modify")
    public Result<Void> modifyConfig(@RequestBody WmsBasConfigModifyRequest request) {
        return bus.handle(request);
    }

    /**
     * 停用配置
     */
    @ApiOperation("停用配置")
    @PostMapping("/disable")
    public Result<Void> disableConfig(@RequestBody WmsBasConfigDisableRequest request) {
        return bus.handle(request);
    }

    /**
     * 启用配置
     */
    @ApiOperation("启用配置")
    @PostMapping("/enable")
    public Result<Void> enableConfig(@RequestBody WmsBasConfigEnableRequest request) {
        return bus.handle(request);
    }

}
