package com.poizon.scm.wms.command.bas.station.map.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationMapApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationMapRequest;
import com.poizon.scm.wms.command.bas.station.map.request.BasStationMapDeleteRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@Slf4j
@Component
public class BasStationMapDeleteRequestExecutor extends AbstractCommandExecutor<BasStationMapDeleteRequest, Boolean> {

    @DubboReference(check = false)
    private BasStationMapApi basStationMapApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected Boolean doExecute(BasStationMapDeleteRequest request) {
        BasStationMapRequest basStationMapRequest = BeanCopyUtils.copyProperties(request, BasStationMapRequest.class);
        basStationMapRequest.setTenantId(OperationUserContextHolder.getTenantCode());

        Result<Boolean> result = LogUtils.logRemoteInvoke(log, basStationMapRequest, basStationMapApi::delete);

        return Optional.ofNullable(result).map(Result::getData)
                .orElse(Boolean.FALSE);
    }
}
