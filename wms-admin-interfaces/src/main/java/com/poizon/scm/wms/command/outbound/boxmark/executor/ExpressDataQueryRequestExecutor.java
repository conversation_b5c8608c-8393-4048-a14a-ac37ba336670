package com.poizon.scm.wms.command.outbound.boxmark.executor;

import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsLogisticsBillRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackRepository;
import com.poizon.scm.wms.command.outbound.boxmark.request.ExpressDataQueryRequest;
import com.poizon.scm.wms.command.outbound.boxmark.response.ExpressDataQueryResponse;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.outbound.returngoods.service.ExpressBillPrintService;
import com.poizon.scm.wms.util.common.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/14 10:49 AM
 * @description
 */
@Component
public class ExpressDataQueryRequestExecutor extends AbstractCommandExecutor<ExpressDataQueryRequest, ExpressDataQueryResponse> {

    @Resource
    private WmsPackRepository wmsPackRepository;

    @Resource
    private WmsLogisticsBillRepository wmsLogisticsBillRepository;

    @Resource
    private ExpressBillPrintService expressBillPrintService;

    @Override
    protected ExpressDataQueryResponse doExecute(ExpressDataQueryRequest request) {
        WmsPackDo wmsPackDo = wmsPackRepository.queryByPackNo(request.getPackNo());
        checkPackExistAndHasPlaceExpress(wmsPackDo);
        WmsLogisticsBillDo logisticsBillDo = wmsLogisticsBillRepository.queryWmsLogisticsBillBySubExpress(wmsPackDo.getSubExpressCode(), wmsPackDo.getWarehouseCode());
        String url = logisticsBillDo.getLogisticsBillUrl();
        if (StringUtils.isBlank(url)) {
            /*没有地址说明还没有请求DES获取地址信息*/
            url = expressBillPrintService.queryAndStoreExpressBillUrl(logisticsBillDo);
        }
        if (StringUtils.isBlank(url)){
            throw new WmsOperationException("获取面单地址失败，面单文件上传中，请稍后重试");
        }
        ExpressDataQueryResponse response = new ExpressDataQueryResponse();
        response.setUrl(url);
        return response;
    }

    private void checkPackExistAndHasPlaceExpress(WmsPackDo wmsPackDo) {
        if (Objects.isNull(wmsPackDo)) {
            throw new WmsOperationException("包裹不存在");
        }
        if (StringUtils.isBlank(wmsPackDo.getSubExpressCode())) {
            throw new WmsOperationException("当前包裹还未下单");
        }
    }

    @Override
    protected void verify(ExpressDataQueryRequest request) {
        super.verify(request);
        if (StringUtils.isBlank(request.getPackNo())){
            throw new WmsOperationException("包裹编号不能为空");
        }
    }
}
