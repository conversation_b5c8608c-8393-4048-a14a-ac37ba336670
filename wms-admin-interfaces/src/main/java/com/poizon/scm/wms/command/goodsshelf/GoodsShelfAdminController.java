package com.poizon.scm.wms.command.goodsshelf;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.query.common.response.BaseSlidingResponse;
import com.poizon.scm.wms.bas.api.command.area.response.BasAgvAreaResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.BindLocationInfoResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.GoodsShelfInvPageResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.GoodsShelfSideResponse;
import com.poizon.scm.wms.command.goodsshelf.request.*;
import com.poizon.scm.wms.command.goodsshelf.response.GoodsShelfCommodityInfoResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/admin/goodsShelf")
public class GoodsShelfAdminController {
    @Resource
    private CommandBus commandBus;
    @ApiOperation("货架朝向分页查询")
    @PostMapping("/query")
    public Result<PagingObject<GoodsShelfSideResponse>> querySide(@RequestBody GoodsShelfSideQueryRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("绑定货架朝向")
    @PostMapping("/bind")
    public Result<Boolean> bind(@RequestBody GoodsShelfSideBindRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("解绑货架朝向")
    @PostMapping("/unbind")
    public Result<Boolean> unbind(@RequestBody GoodsShelfSideUnbindRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("保存货架朝向")
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody GoodsShelfSideSaveRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("查询agv库区")
    @PostMapping("/queryAgvArea")
    public Result<List<BasAgvAreaResponse>> queryAgvArea(@RequestBody AgvAreaQueryRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("查询货架朝向已绑定库位")
    @PostMapping("/queryLocationList")
    public Result<BindLocationInfoResponse> bindList(@RequestBody BindLocationQueryRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("批量删除货架朝向")
    @PostMapping("/batchDelete")
    public Result<Void> batchDelete(@RequestBody GoodsShelfSideBatchDeleteRequest request){
        return commandBus.handle(request);
    }

    @ApiOperation("货架库存分页查询")
    @PostMapping("/inventoryPageQuery")
    public Result<BaseSlidingResponse<GoodsShelfInvPageResponse>> invPageQuery(@RequestBody GoodsShelfInvPageRequest request){
        return commandBus.handle(request);
    }

    @ApiOperation("货架库存详情")
    @PostMapping("/inventoryDetail")
    public Result<GoodsShelfCommodityInfoResponse> invDetail(@RequestBody GoodsShelfDetailRequest request){
        return commandBus.handle(request);
    }

}
