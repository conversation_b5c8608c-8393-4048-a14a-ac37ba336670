package com.poizon.scm.wms.command.bas.station.map.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationMapApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationMapBatchAddRequest;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationMapRequest;
import com.poizon.scm.wms.command.bas.station.map.request.BasStationMapAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@Slf4j
@Component
public class BasStationMapAddRequestExecutor extends AbstractCommandExecutor<BasStationMapAddRequest, Boolean> {

    @DubboReference(check = false)
    private BasStationMapApi basStationMapApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected Boolean doExecute(BasStationMapAddRequest request) {
        List<BasStationMapRequest> requestList = Collections.singletonList(BeanCopyUtils.copyProperties(request, BasStationMapRequest.class));
        for (BasStationMapRequest basStationMapRequest : requestList) {
            basStationMapRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        }
        BasStationMapBatchAddRequest batchAddRequest = new BasStationMapBatchAddRequest();
        batchAddRequest.setRequestList(requestList);

        Result<Boolean> addResult = LogUtils.logRemoteInvoke(log, batchAddRequest, basStationMapApi::add);

        return Optional.ofNullable(addResult).map(Result::getData)
                .orElse(Boolean.FALSE);
    }
}
