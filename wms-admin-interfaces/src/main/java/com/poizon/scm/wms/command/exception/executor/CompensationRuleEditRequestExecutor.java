package com.poizon.scm.wms.command.exception.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wehs.api.definition.exception.config.DbCompensationRuleAdminApi;
import com.poizon.scm.wehs.api.request.exception.config.DbCompensationRuleEditRequest;
import com.poizon.scm.wms.api.dto.request.exception.config.CompensationRuleEditRequest;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.util.BeanUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class CompensationRuleEditRequestExecutor extends AbstractCommandExecutor<CompensationRuleEditRequest,Void> {

    @DubboReference(check = false)
    private DbCompensationRuleAdminApi dbCompensationRuleAdminApi;

    @Override
    protected Void doExecute(CompensationRuleEditRequest request) {
        DbCompensationRuleEditRequest dbCompensationRuleEditRequest = BeanUtil.copy(request,DbCompensationRuleEditRequest.class);
        Result result =  dbCompensationRuleAdminApi.edit(dbCompensationRuleEditRequest);
        if(!Result.isSuccess(result)){
            throw  new WmsOperationException(result.getCode(),result.getMsg());
        }
        return null;
    }
}
