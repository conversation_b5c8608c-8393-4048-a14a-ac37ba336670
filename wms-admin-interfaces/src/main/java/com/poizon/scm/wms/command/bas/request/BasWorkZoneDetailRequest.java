package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: wuyusen
 * @Date: 2021/6/1
 * @Description:
 */
@ApiModel
@Data
public class BasWorkZoneDetailRequest extends Request {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id",required = false,notes="")
    private Long id;

    /**
     * 仓库code
     */
    @ApiModelProperty(value = "仓库编码",required = true,notes="查询仓库列表来源")
    @NotBlank(message = "仓库编码不能为空")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称",required = true,notes="")
    private String warehouseName;

    /**
     * 作业区编码
     */
    @ApiModelProperty(value = "作业区编码",required = true,notes="")
    @NotBlank(message = "作业区编码不能为空")
    private String workZoneCode;

    /**
     * 作业区名称
     */
    @ApiModelProperty(value = "作业区名称",required = true,notes="")
    @NotBlank(message = "作业区名称不能为空")
    private String workZoneName;

    /**
     * 是否有效:1是0否
     */
    @ApiModelProperty(value="启用/停用，1启用，0停用",required = true,example = "1")
    private Integer isActive;
}
