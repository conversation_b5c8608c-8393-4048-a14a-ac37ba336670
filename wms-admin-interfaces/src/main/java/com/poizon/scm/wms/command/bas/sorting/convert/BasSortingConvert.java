package com.poizon.scm.wms.command.bas.sorting.convert;

import com.poizon.scm.wms.bas.api.sorting.request.*;
import com.poizon.scm.wms.command.bas.sorting.request.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BasSortingConvert {
    BasSortingConvert INSTANCE = Mappers.getMapper(BasSortingConvert.class);

    BaseSortingSceneSaveApiRequest convert(BaseSortingSceneSaveRequest request);

    BaseSortingSceneUpdateApiRequest convert1(BaseSortingSceneUpdateRequest request);

    BaseSortingScenePageApiRequest convert2(BaseSortingScenePageRequest request);

    BaseSortingEntitySaveApiRequest conver3(BaseSortingEntitySaveRequest request);

    BaseSortingEntityUpdateApiRequest convert4(BaseSortingEntityUpdateRequest request);

    BaseSortingEntityPageApiRequest convert5(BaseSortingEntityPageRequest request);

    BaseSortingEntityCopyApiRequest conver6(BaseSortingEntityCopyRequest request);

    BaseSortingSceneListApiRequest convert7(BaseSortingSceneListRequest request);

    BaseSortingRuleSaveApiRequest convert8(BaseSortingRuleSaveRequest request);

    BaseSortingRuleUpdateApiRequest convert9(BaseSortingRuleUpdateRequest request);

    BaseSortingRulePageApiRequest convert10(BaseSortingRulePageRequest request);

    BaseSortingRuleDetailSaveApiRequest convert11(BaseSortingRuleDetailSaveRequest request);

    BaseSortingRuleDetailUpdateApiRequest convert12(BaseSortingRuleDetailUpdateRequest request);

    BaseSortingRuleDetailPageApiRequest convert13(BaseSortingRuleDetailPageRequest request);

    BaseSortingRuleDetailQueryApiRequest convert14(BaseSortingRuleDetailQueryRequest request);

    BaseSortingRuleUpdateApiRequest convert15(BaseSortingRuleEnableRequest request);

    BaseSortingEntityQueryApiRequest convert16(BaseSortingEntityListRequest request);

    BaseSortingEntitySyncApiRequest convert17(BaseSortingEntitySyncRequest request);
}
