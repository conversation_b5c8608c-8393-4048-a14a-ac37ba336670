package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigMetaSaveParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigMetaAdminRepository;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigMetaSaveRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.config.WcsConfig;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WmsBasConfigMetaSaveRequestExecutor extends AbstractCommandExecutor<WmsBasConfigMetaSaveRequest, Void> {

    @Autowired
    private WmsConfigMetaAdminRepository wmsConfigMetaAdminRepository;

    @Autowired
    private WcsConfig wcsConfig;

    @Override
    protected void verify(WmsBasConfigMetaSaveRequest request) {
        super.verify(request);

        Preconditions.checkBiz(StringUtils.isNotBlank(request.getType()), "配置类型不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getCategory()), "配置分类不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getCode()), "配置元数据编码不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getName()), "配置元数据名称不能为空！");

        if (StringUtils.isNotBlank(request.getValue())) {
            Preconditions.checkBiz(StringUtils.isNotBlank(request.getValueType()), "配置元数据的值类型不能为空！");
        }

        //元数据先只有开发能操作
        Preconditions.checkBiz(wcsConfig.allowOperateBaseInfoUserId(OperationUserContextHolder.get().getUserId()), "无操作权限！");
    }

    @Override
    protected Void doExecute(WmsBasConfigMetaSaveRequest request) {

        wmsConfigMetaAdminRepository.save(WmsConfigMetaSaveParam.builder()
                .type(request.getType().trim())
                .category(request.getCategory().trim())
                .code(request.getCode().trim())
                .name(request.getName().trim())
                .valueType(request.getValueType())
                .value(request.getValue())
                .level(request.getLevel())
                .priority(request.getPriority())
                .sort(request.getSort())
                .remark(request.getRemark())
                .createdUserId(OperationUserContextHolder.get().getUserId())
                .createdRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
