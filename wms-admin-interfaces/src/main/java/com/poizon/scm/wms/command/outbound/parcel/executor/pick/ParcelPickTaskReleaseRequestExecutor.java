package com.poizon.scm.wms.command.outbound.parcel.executor.pick;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.JsonUtils;
import com.poizon.scm.wms.command.outbound.parcel.request.pick.ParcelPickTaskReleaseRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.outbound.api.parcel.ParcelPickTaskApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description:
 * @author:chaoyuan
 * @createTime:2023/8/6 10:36
 */
@Slf4j
@Component
public class ParcelPickTaskReleaseRequestExecutor extends AbstractCommandExecutor<ParcelPickTaskReleaseRequest, String> {


    @DubboReference(interfaceClass = ParcelPickTaskApi.class, timeout = 5000, check = false)
    private ParcelPickTaskApi parcelPickTaskApi;


    @Override
    protected String doExecute(ParcelPickTaskReleaseRequest request) {

        com.poizon.scm.wms.outbound.api.parcel.request.pick.ParcelPickTaskReleaseRequest param = new com.poizon.scm.wms.outbound.api.parcel.request.pick.ParcelPickTaskReleaseRequest();

        param.setTaskNo(request.getTaskNo());
        param.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        param.setWarehouseCode(request.getWarehouseCode());
        OperationUserContext userContext = OperationUserContextHolder.get();
        param.setRealName(userContext.getRealName());
        param.setUserId(userContext.getUserId());
        param.setUserName(userContext.getUserName());

        log.info("开始执行方法 【 parcelPickTaskApi.release】 param{}", JsonUtils.serialize(param));
        Result<String> rpcResult = parcelPickTaskApi.release(param);
        log.info("执行方法完成 【 parcelPickTaskApi.release】 param{}", JsonUtils.serialize(rpcResult));

        if (!Result.isSuccess(rpcResult)) {
            //直接报错
            throw new WmsOperationException(rpcResult.getMsg());
        }
        if (Objects.isNull(rpcResult.getData())) {
            throw new WmsOperationException("执行拣货返回值为空");
        }
        return rpcResult.getData();
    }
}
