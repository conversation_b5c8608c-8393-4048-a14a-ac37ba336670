package com.poizon.scm.wms.command.config.executor;

import com.poizon.scm.wms.command.config.convert.ConvertConfig;
import com.poizon.scm.wms.command.config.request.AddConfigRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.pojo.sysconfig.AddConfigDetailParam;
import com.poizon.scm.wms.service.sysconfig.SysConfigV2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: tywei
 * @create: 2021-06-03 4:54 下午
 **/
@Component
public class AddConfigRequestExecutor extends AbstractCommandExecutor<AddConfigRequest, Void> {

    @Autowired
    private SysConfigV2Service sysConfigV2Service;

    @Override
    protected Void doExecute(AddConfigRequest request) {
        AddConfigDetailParam param = ConvertConfig.INSTANCE.toParam(request);
        sysConfigV2Service.add(param);
        return null;
    }
}
