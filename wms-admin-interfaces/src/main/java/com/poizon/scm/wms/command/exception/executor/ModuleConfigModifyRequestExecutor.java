package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ModuleConfigModifyParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionConfigAdminRepository;
import com.poizon.scm.wms.command.exception.request.ModuleConfigModifyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ModuleConfigModifyRequestExecutor extends AbstractCommandExecutor<ModuleConfigModifyRequest, Void> {

    @Autowired
    private WmsExceptionConfigAdminRepository wmsExceptionConfigAdminRepository;

    @Override
    protected Void doExecute(ModuleConfigModifyRequest request) {
        /* 检查名称不能超过30个字符 */
        Preconditions.checkBiz(request.getModuleName().length() <= 30, "模块名称长度不能超过30个字符！");

        ModuleConfigModifyParam param = new ModuleConfigModifyParam();
        param.setId(request.getId());
        param.setModuleName(request.getModuleName());
        param.setSort(request.getSort());
        param.setType(request.getType());
        param.setDuty(request.getDuty());
        param.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        param.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        param.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionConfigAdminRepository.modifyModule(param);
        return null;
    }

}
