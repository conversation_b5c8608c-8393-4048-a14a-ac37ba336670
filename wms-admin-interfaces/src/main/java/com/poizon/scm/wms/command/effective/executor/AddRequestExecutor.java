package com.poizon.scm.wms.command.effective.executor;

import com.poizon.scm.wms.adapter.commodity.model.ScpSpuDo;
import com.poizon.scm.wms.adapter.commodity.repository.ScpSpuRepository;
import com.poizon.scm.wms.adapter.effective.model.SpuEffectiveRecordDo;
import com.poizon.scm.wms.adapter.effective.repository.SpuEffectiveRecordRepository;
import com.poizon.scm.wms.api.command.effective.request.AddRequest;
import com.poizon.scm.wms.api.enums.SpuEffectiveStatusEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.commodity.response.SkuBasicRspDomain;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.effective.EffectiveService;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 新增效期
 *
 * @author: tywei
 * @create: 2021-03-11 8:22 下午
 **/
@Slf4j
@Component
public class AddRequestExecutor extends AbstractCommandExecutor<AddRequest, Void> {
    @Autowired
    private DistributeLockUtil distributeLockUtil;
    @Autowired
    private EffectiveService effectiveService;
    @Autowired
    private SpuEffectiveRecordRepository spuEffectiveRecordRepository;
    @Autowired
    private ScpSpuRepository scpSpuRepository;
    @Autowired
    private ICommodityQueryV2Service iCommodityQueryV2Service;

    @Override
    protected Void doExecute(AddRequest request) {
        SkuBasicRspDomain skuInfo = iCommodityQueryV2Service
                .querySkuInfoBySkuId(OperationUserContextHolder.getTenantCode(), request.getSkuId());
        if (Objects.isNull(skuInfo)) {
            //"商品信息不存在"
            log.error("商品信息不存在:{}",request.getSkuId());
            throw new WmsException(WmsExceptionCode.SPU_EFFECTIVE_ADD_ERROR_BY_SKU_NOT_FIND);
        }
        boolean flag = false;
        try {
            if (!distributeLockUtil.tryLockForBiz(LockEnum.SPU_EFFECTIVE_ADD_LOCK, skuInfo.getSpuId())) {
                throw new WmsOperationException(WmsExceptionCode.GET_LOCK_FAILED);
            }
            flag = true;
            execute0(skuInfo.getSpuId(), request);
        } catch (WmsException | WmsOperationException e) {
            throw e;
        } catch (InterruptedException e) {
            log.error("interrupt 新增效期失败", e);
            Thread.currentThread().interrupt();
            throw new WmsOperationException(WmsExceptionCode.UNKNOWN_ERROR, e);
        } catch (Exception e) {
            log.error("新增效期失败", e);
            throw new WmsOperationException(WmsExceptionCode.UNKNOWN_ERROR, e);
        } finally {
            if (flag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SPU_EFFECTIVE_ADD_LOCK, skuInfo.getSpuId());
            }
        }
        return null;
    }

    private void execute0(String spuId, AddRequest request) {
        /*if (!validateExpiryDate(spuId)) {
            throw new WmsException(WmsExceptionCode.SPU_EFFECTIVE_ADD_ERROR,
                    String.format("spuId:%s不受效期管控，无需新增", spuId));
        }*/
        SpuEffectiveRecordDo effectiveRecordDo = spuEffectiveRecordRepository
                .findBySkuId(request.getSkuId(), SpuEffectiveStatusEnum.INIT.getStatus());
        if (Objects.nonNull(effectiveRecordDo)) {
            throw new WmsOperationException(WmsExceptionCode.SPU_EFFECTIVE_ADD_ERROR,
                    "SKU未审核记录已存在，无需重复新增。skuId:" + request.getSkuId());
        }
        ScpSpuDo spuDo = scpSpuRepository.selectSpuBySpuId(OperationUserContextHolder.getTenantCode(), spuId);
        int life = NumberUtils.toInt(spuDo.getShelfLife());
        effectiveService.addEffectiveRecord(request.getSkuId(), spuId, request.getExpiryDate(), life);
    }
}
