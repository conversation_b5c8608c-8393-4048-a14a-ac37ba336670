package com.poizon.scm.wms.command.alarm.strategy.executor;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.adapter.alarm.model.param.AlarmStrategyQueryListParam;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyListQueryRequest;
import com.poizon.scm.wms.command.alarm.strategy.response.AlarmStrategyListQueryResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.enums.AlarmStrategyTypeEnum;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
@Component
public class AlarmStrategyListQueryRequestExecutor extends AbstractCommandExecutor<AlarmStrategyListQueryRequest, PagingObject<AlarmStrategyListQueryResponse>> {

    @Resource
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Autowired
    private ScpWarehouseRepository scpWarehouseRepository;

    @Override
    protected PagingObject<AlarmStrategyListQueryResponse> doExecute(AlarmStrategyListQueryRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        AlarmStrategyQueryListParam queryParam = BeanUtil.copy(request, AlarmStrategyQueryListParam.class);
        if(CollectionUtils.isEmpty(queryParam.getWarehouseCodesList())){
            queryParam.setWarehouseCodesList(OperationUserContextHolder.get().getAuthWarehouseCodeList());
        }
        List<AlarmStrategyDo> strategyDos = alarmStrategyQueryRepository.queryList(queryParam);
        PageInfo<AlarmStrategyDo> pageInfo = new PageInfo<>(strategyDos);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PagingObject<>();
        }
        PagingObject<AlarmStrategyListQueryResponse> result = new PagingObject<>();
        result.setTotal(pageInfo.getTotal());
        List<AlarmStrategyListQueryResponse> responses = new ArrayList<>();
        for (AlarmStrategyDo strategyDo : pageInfo.getList()) {
            AlarmStrategyListQueryResponse response = BeanUtil.copy(strategyDo, AlarmStrategyListQueryResponse.class);
            response.setCreatedTime(DateUtils.formatTimeForAdmin(strategyDo.getCreatedTime()));
            response.setUpdatedTime(DateUtils.formatTimeForAdmin(strategyDo.getUpdatedTime()));
            responses.add(response);
        }
        Set<String> codes = responses.stream().map(AlarmStrategyListQueryResponse::getWarehouseCode).collect(Collectors.toSet());
        List<ScpWarehouseDo> scpWarehouseDos = scpWarehouseRepository.queryByCodesAndTenant(codes, OperationUserContextHolder.getTenantCode());
        Map<String, String> warehouseMap = scpWarehouseDos.stream().collect(Collectors.toMap(ScpWarehouseDo::getWarehouseCode, ScpWarehouseDo::getWarehouseName));
        for (AlarmStrategyListQueryResponse response : responses) {
            response.setWarehouseName(warehouseMap.get(response.getWarehouseCode()));
            response.setType(AlarmStrategyTypeEnum.getDescByType(response.getType()));
        }
        result.setContents(responses);
        return result;
    }

}
