package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigRemoveParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigAdminRepository;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigRemoveRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WmsBasConfigRemoveRequestExecutor extends AbstractCommandExecutor<WmsBasConfigRemoveRequest, Void> {

    @Autowired
    private WmsConfigAdminRepository wmsConfigAdminRepository;

    @Override
    protected void verify(WmsBasConfigRemoveRequest request) {
        super.verify(request);
        Preconditions.checkBiz(null != request.getId(), "配置ID不能为空！");
    }

    @Override
    protected Void doExecute(WmsBasConfigRemoveRequest request) {

        wmsConfigAdminRepository.remove(WmsConfigRemoveParam.builder()
                .id(request.getId())
                .updatedUserId(OperationUserContextHolder.get().getUserId())
                .updatedRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
