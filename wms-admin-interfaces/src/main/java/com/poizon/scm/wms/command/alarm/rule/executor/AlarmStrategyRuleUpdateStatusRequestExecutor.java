package com.poizon.scm.wms.command.alarm.rule.executor;

import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyRuleQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyRuleDo;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleUpdateStatusRequest;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyRuleCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
@Component
public class AlarmStrategyRuleUpdateStatusRequestExecutor extends AbstractCommandExecutor<AlarmStrategyRuleUpdateStatusRequest, Void> {

    @Resource
    private AlarmStrategyRuleCommandService alarmStrategyRuleCommandService;
    @Resource
    private AlarmStrategyRuleQueryRepository alarmStrategyRuleQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyRuleUpdateStatusRequest request) {
        alarmStrategyRuleCommandService.updateRuleStatus(request.getRuleNo(),request.getStatus());
        return null;
    }

    @Override
    protected void verify(AlarmStrategyRuleUpdateStatusRequest request) {
        AlarmStrategyRuleDo strategyRuleDo = alarmStrategyRuleQueryRepository.queryRuleByRuleNo(request.getRuleNo());
        if (strategyRuleDo == null) {
            throw new WmsOperationException("预警规则不存在");
        }
    }
}
