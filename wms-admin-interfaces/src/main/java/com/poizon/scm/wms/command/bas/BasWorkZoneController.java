package com.poizon.scm.wms.command.bas;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.workZone.BasWorkZoneAdminApi;
import com.poizon.scm.wms.bas.api.command.workZone.request.BasWorkZoneDeleteRequest;
import com.poizon.scm.wms.bas.api.command.workZone.request.BasWorkZoneDetailRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;

/**
 * @Author: wuyusen
 * @Date: 2021/5/31
 * @Description:
 */
@RestController
@RequestMapping("admin/bas")
@Api(tags = {"后台-基础资料-拣货库区-command"})
public class BasWorkZoneController {

    @DubboReference(interfaceClass = BasWorkZoneAdminApi.class, timeout = 5000, check = false)
    private BasWorkZoneAdminApi basWorkZoneAdminApi;

    /**
     * 更改作业库区,包括编辑和新增
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/workZone/update", produces = "application/json", method = RequestMethod.POST)
    @ApiOperation(value = "作业库区修改接口", notes = "作业库区修改接口", tags = "后台-基础资料-拣货库区-command", httpMethod = "POST")
    public Result<Void> update(@RequestBody @Valid BasWorkZoneDetailRequest request) {
        return basWorkZoneAdminApi.update(request);
    }

    /**
     * 更改作业库区,包括编辑和新增
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/workZone/delete", produces = "application/json", method = RequestMethod.POST)
    @ApiOperation(value = "作业库区删除接口", notes = "作业库区删除接口", tags = "后台-基础资料-拣货库区-command", httpMethod = "POST")
    public Result<Void> delete(@RequestBody @Valid BasWorkZoneDeleteRequest request) {
        return basWorkZoneAdminApi.delete(request);
    }
}
