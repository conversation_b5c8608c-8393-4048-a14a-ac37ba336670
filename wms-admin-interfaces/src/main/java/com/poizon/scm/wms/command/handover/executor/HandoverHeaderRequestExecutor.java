package com.poizon.scm.wms.command.handover.executor;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.handover.model.DeliveryHandoverHeaderDo;
import com.poizon.scm.wms.adapter.handover.query.HandoverHeaderQueryParams;
import com.poizon.scm.wms.api.command.handover.request.HandoverHeaderRequest;
import com.poizon.scm.wms.api.command.handover.response.DeliveryHandoverHeaderResponse;
import com.poizon.scm.wms.api.enums.HandoverStatusEnums;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.service.base.BasWarehouseService;
import com.poizon.scm.wms.service.haandover.DeliveryHandoverHeaderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 交接单头列表信息查询
 * @Author: dwq
 * @Date: 2020/10/19 5:10 下午
 */
@Service
public class HandoverHeaderRequestExecutor extends AbstractCommandExecutor<HandoverHeaderRequest, PagingObject<DeliveryHandoverHeaderResponse>> {
    @Autowired
    private DeliveryHandoverHeaderService deliveryHandoverHeaderService;
    @Autowired
    private BasWarehouseService basWarehouseService;
    @Override
    protected PagingObject<DeliveryHandoverHeaderResponse> doExecute(HandoverHeaderRequest command) {
        HandoverHeaderQueryParams params=new HandoverHeaderQueryParams();
        BeanUtils.copyProperties(command,params);
        if(StringUtils.isNotBlank(command.getWarehouseCodes())){
            String[] warehouseCodes = command.getWarehouseCodes().split(",");
            params.setWarehouseCodeList(Arrays.asList(warehouseCodes));
        }else{
            List<OperationUserContext.UserWarehouse> warehouseCodes = OperationUserContextHolder.get().getWarehouses();
            if(CollectionUtils.isEmpty(warehouseCodes)){
                throw new WmsOperationException(WmsExceptionCode.AUTHENTICATION_WAREHOUSE_IS_NULL);
            }
            List<String> list= OperationUserContextHolder.get().getWarehouses().stream().map(OperationUserContext.UserWarehouse::getWarehouseCode).collect(Collectors.toList());
            params.setWarehouseCodeList(list);
        }
        params.setSkuIds(buildSkuIds(command.getSkuIds()));
        params.setSkip(params.getPageSize()*(params.getPageNum()-1));
        PagingObject<DeliveryHandoverHeaderDo> pagingObject = deliveryHandoverHeaderService.selectPage(params);
        PagingObject<DeliveryHandoverHeaderResponse> result=new PagingObject<>();
        result.setPageSize(pagingObject.getPageSize());
        result.setPageNum(pagingObject.getPageNum());
        result.setTotal(pagingObject.getTotal());
        result.setContents(new ArrayList<>());
        if(result.getTotal()>0){
            List<DeliveryHandoverHeaderResponse> list=new ArrayList<>();
            pagingObject.getContents().forEach(e->{
                DeliveryHandoverHeaderResponse response=new DeliveryHandoverHeaderResponse();
                BeanUtils.copyProperties(e,response);
                response.setCompletionTime(DateUtils.formatDate(e.getCompletionTime(),DateUtils.FORMAT_TIME));
                list.add(response);

            });
            Map<String,String> warehouseNames = basWarehouseService.findWarehouseName(list.stream().map(DeliveryHandoverHeaderResponse::getWarehouseCode).collect(Collectors.toSet()));
            list.forEach(e->{
                e.setWarehouseName(MapUtils.getString(warehouseNames,e.getWarehouseCode()));
                e.setStatusDesc(HandoverStatusEnums.getEnums(e.getStatus()).getName());
                e.setPrintStatusDesc(e.getPrintStatus()==0?"未打印":"已打印");
            });
            result.setContents(list);
        }

        return result;
    }

    /**
     * 构建skuIds list
     *
     * @param skuIds
     * @return
     */
    private List<String> buildSkuIds(String skuIds){
        if(StringUtils.isNotBlank(skuIds)){
            return Arrays.asList(skuIds.split(","));
        }
        return null;
    }
}
