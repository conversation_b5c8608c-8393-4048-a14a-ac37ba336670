package com.poizon.scm.wms.command.alarm.rule.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AlarmStrategyRuleUpdateStatusRequest extends Request {
    @NotBlank(message = "规则编码不能为空")
    @ApiModelProperty("规则编码")
    private String ruleNo;
    @NotNull(message = "状态不能为空")
    @ApiModelProperty("状态")
    private Integer status;
}
