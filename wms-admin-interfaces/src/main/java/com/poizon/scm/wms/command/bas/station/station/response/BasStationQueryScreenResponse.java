package com.poizon.scm.wms.command.bas.station.station.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date  29/04/2024 周一
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationQueryScreenResponse implements Serializable {
    private static final long serialVersionUID = 4359136182895040771L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 工位分区编码
     */
    private String stationAreaCode;

    /**
     * 工位组编码
     */
    private String stationGroupCode;

    /**
     * 工位组横坐标
     */
    private Integer row;

    /**
     * 工位组纵坐标
     */
    private Integer column;

    /**
     * 租户编码
     */
    private String tenantId;

    /**
     * 工位列表 工位组下的的工位 一般不超过两个
     */
    private List<StationDto> stationDtoList;


    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class StationDto implements Serializable {
        private static final long serialVersionUID = -9077437303903780596L;

        /**
         * 自增ID
         */
        private Long id;

        /**
         * 仓库编码
         */
        private String warehouseCode;

        /**
         * 工位分区编码
         */
        private String stationAreaCode;

        /**
         * 工位组编码
         */
        private String stationGroupCode;

        /**
         * 工位编码
         */
        private String stationCode;

        /**
         * 工位名称
         */
        private String stationName;

        /**
         * 工位横坐标
         */
        private Integer row;

        /**
         * 工位纵坐标
         */
        private Integer column;

        /**
         * 工位是否已绑定 true-已绑定
         */
        private Boolean bound;

        /**
         * 作业属性
         *
         * @see com.poizon.scm.wms .api.enums.StationPropertyEnum
         */
        private String stationProperty;

    }

}
