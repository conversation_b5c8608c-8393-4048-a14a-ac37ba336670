package com.poizon.scm.wms.command.bas.station.onetake.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationOneTakeApi;
import com.poizon.scm.wms.bas.api.command.station.response.BasStationOneTakeResponse;
import com.poizon.scm.wms.command.bas.station.onetake.request.BasStationOneTakeRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  25/04/2024 周四
 */
@Slf4j
@Component
public class BasStationOneTakeRequestExecutor extends AbstractCommandExecutor<BasStationOneTakeRequest, com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse> {

    @DubboReference(check = false)
    private BasStationOneTakeApi basStationOneTakeApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse doExecute(BasStationOneTakeRequest request) {
        OperationUserContext userContext = OperationUserContextHolder.get();
        com.poizon.scm.wms.bas.api.command.station.request.BasStationOneTakeRequest oneTakeRequest = new com.poizon.scm.wms.bas.api.command.station.request.BasStationOneTakeRequest();
        oneTakeRequest.setTenantId(userContext.getTenantCode());
        oneTakeRequest.setWarehouseCode(request.getWarehouseCode());
        oneTakeRequest.setStationAreaCode(request.getStationAreaCode());
        Result<BasStationOneTakeResponse> basStationOneTakeResponseResult = LogUtils.logRemoteInvoke(log, oneTakeRequest, basStationOneTakeApi::queryOneTake);

        return Optional.ofNullable(basStationOneTakeResponseResult)
                .map(Result::getData)
                .map(basOneTakeResponse -> {
                    com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse oneTakeResponse = new com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse();

                    oneTakeResponse.setToBeCirculatedCount(basOneTakeResponse.getToBeCirculatedCount());
                    oneTakeResponse.setToBeInspectedCount(basOneTakeResponse.getToBeInspectedCount());
                    oneTakeResponse.setToBeIdentifiedCount(basOneTakeResponse.getToBeIdentifiedCount());
                    oneTakeResponse.setToBePackedCount(basOneTakeResponse.getToBePackedCount());
                    oneTakeResponse.setStationAreaName(basOneTakeResponse.getStationAreaName());

                    List<BasStationOneTakeResponse.MapDto> list = basOneTakeResponse.getMapDtoList();
                    List<com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse.MapDto> mapDtoList = list.stream()
                            .map(dto -> {
                                com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse.MapDto mapDto = new com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse.MapDto();
                                mapDto.setWarehouseCode(dto.getWarehouseCode());
                                mapDto.setWarehouseName(dto.getWarehouseName());
                                mapDto.setStationAreaCode(dto.getStationAreaCode());
                                mapDto.setStationGroupCode(dto.getStationGroupCode());
                                mapDto.setRow(dto.getRow());
                                mapDto.setColumn(dto.getColumn());
                                List<com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse.StationDto> stationDtoList = dto.getStationDtoList()
                                        .stream()
                                        .map(stationDto -> BeanCopyUtils.copyProperties(stationDto, com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse.StationDto.class))
                                        .collect(Collectors.toList());
                                mapDto.setStationDtoList(stationDtoList);
                                return mapDto;
                            })
                            .collect(Collectors.toList());
                    oneTakeResponse.setMapDtoList(mapDtoList);

                    return oneTakeResponse;
                }).orElse(null);
    }
}
