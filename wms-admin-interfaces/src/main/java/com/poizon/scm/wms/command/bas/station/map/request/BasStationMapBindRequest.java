package com.poizon.scm.wms.command.bas.station.map.request;

import com.poizon.scm.wms.bas.api.enums.BasStationAreaTypeEnum;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationMapBindRequest extends Request implements Serializable {
    private static final long serialVersionUID = -2898040293636179259L;

    @NotEmpty(message = "仓库编码不能为空", groups = ValidateGroups.Update.class)
    private String warehouseCode;

    /**
     * 工位分区编码
     */
    @NotEmpty(message = "工位分区编码不能为空", groups = ValidateGroups.Update.class)
    private String stationAreaCode;

    /**
     * 工位组编码
     */
    @NotEmpty(message = "工位组编码不能为空", groups = ValidateGroups.Update.class)
    private String stationGroupCode;

    /**
     * 工位编码
     */
    @NotEmpty(message = "工位编码不能为空", groups = ValidateGroups.Update.class)
    private String stationCode;

    /**
     * bind 使用
     * 工位组 上方 else 下方
     */
    @NotNull(message = "up不能为空", groups = ValidateGroups.Update.class)
    private Boolean up;

    /**
     * 分区类型
     *
     * @see BasStationAreaTypeEnum
     */
    @NotEmpty(message = "分区类型不能为空", groups = ValidateGroups.Update.class)
    private String areaType;

}
