package com.poizon.scm.wms.command.bas.station.station.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * cq
 *
 * 工位查询request
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AdminStationQueryRequest extends Request implements Serializable {
    /**
     * 工位编码
     */
    @NotBlank(message = "工位编码不能为空")
    @ApiModelProperty(value="工位编码",required = true)
    private String stationCode;

    /**
     * 仓库code
     */
    @NotBlank(message = "仓库编码不能为空")
    @ApiModelProperty(value="仓库编码",required = true)
    private String warehouseCode;

}
