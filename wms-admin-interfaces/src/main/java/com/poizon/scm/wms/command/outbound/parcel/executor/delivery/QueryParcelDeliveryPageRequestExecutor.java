package com.poizon.scm.wms.command.outbound.parcel.executor.delivery;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.outbound.parcel.request.delivery.QueryParcelDeliveryPageRequest;
import com.poizon.scm.wms.command.outbound.parcel.response.delivery.QueryDeliveryPageResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.outbound.api.parcel.DeliveryParcelOrderApi;
import com.poizon.scm.wms.outbound.api.parcel.request.delivery.QueryDeliveryPageRequest;
import com.poizon.scm.wms.util.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * @description:
 * @author:chaoyuan
 * @createTime:2023/7/3 18:31
 */
@Component
@Slf4j
public class QueryParcelDeliveryPageRequestExecutor extends AbstractCommandExecutor<QueryParcelDeliveryPageRequest, PagingObject<QueryDeliveryPageResponse>> {

    @DubboReference(interfaceClass = DeliveryParcelOrderApi.class, timeout = 5000, check = false)
    private DeliveryParcelOrderApi deliveryParcelOrderApi;

    @Override
    protected PagingObject<QueryDeliveryPageResponse> doExecute(QueryParcelDeliveryPageRequest request) {
        return getResult(request);
    }

    private PagingObject<QueryDeliveryPageResponse> getResult(QueryParcelDeliveryPageRequest request) {

        QueryDeliveryPageRequest param = new QueryDeliveryPageRequest();
        BeanUtil.copy(request, param);
        param.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        Result<PagingObject<com.poizon.scm.wms.outbound.api.parcel.response.delivery.QueryDeliveryPageResponse>> pagingObjectResult = deliveryParcelOrderApi.queryPage(param);
        if (!Result.isSuccess(pagingObjectResult)) {
            //直接报错
            throw new WmsOperationException(pagingObjectResult.getMsg());
        }

        PagingObject<QueryDeliveryPageResponse> result = new PagingObject<>();

        if (Objects.isNull(pagingObjectResult.getData())) {
            return result;
        }

        BeanUtil.copy(pagingObjectResult.getData(), result);

        if (!CollectionUtils.isEmpty(pagingObjectResult.getData().getContents())) {
            result.setContents(BeanUtil.deepCopyByList(pagingObjectResult.getData().getContents(), QueryDeliveryPageResponse.class));
        }

        return result;

    }
}
