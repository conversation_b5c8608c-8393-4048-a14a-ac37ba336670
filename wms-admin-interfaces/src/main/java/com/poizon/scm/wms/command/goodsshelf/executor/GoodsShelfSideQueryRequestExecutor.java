package com.poizon.scm.wms.command.goodsshelf.executor;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.definition.BasWarehouseApi;
import com.poizon.scm.wms.api.dto.response.base.BasWarehouseResponse;
import com.poizon.scm.wms.api.enums.BasAreaPropertiesEnum;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaAdminApi;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaApi;
import com.poizon.scm.wms.bas.api.command.area.response.BasAgvAreaResponse;
import com.poizon.scm.wms.bas.api.command.area.response.BasAreaResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.GoodsShelfAdminApi;
import com.poizon.scm.wms.bas.api.command.goodsshelf.request.GoodsShelfSideQueryApiRequest;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.GoodsShelfSideResponse;
import com.poizon.scm.wms.command.goodsshelf.request.GoodsShelfSideQueryRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.outbound.delivery.entity.param.DeliveryCancelUpperFinishParam;
import com.poizon.scm.wms.util.util.PagingObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoodsShelfSideQueryRequestExecutor extends AbstractCommandExecutor<GoodsShelfSideQueryRequest, PagingObject<GoodsShelfSideResponse>> {
    @DubboReference(check = false)
    private GoodsShelfAdminApi goodsShelfAdminApi;
    @DubboReference(check = false)
    private BasWarehouseApi basWarehouseApi;
    @DubboReference(check = false)
    private BaseAreaAdminApi baseAreaAdminApi;
    @Override
    protected PagingObject<GoodsShelfSideResponse> doExecute(GoodsShelfSideQueryRequest request) {
        if(StringUtils.isBlank(request.getWarehouseCode())){
            return PagingObjectUtils.emptyPagingObject(request.getPageNum(),request.getPageSize());
        }
        //查询仓库下所有的agv库区
        Result<List<BasAgvAreaResponse>> areaListResult = baseAreaAdminApi.selectAgvAreaList(request.getWarehouseCode());
        if(!Result.isSuccess(areaListResult)||CollectionUtils.isEmpty(areaListResult.getData())){
            return PagingObjectUtils.emptyPagingObject(request.getPageNum(),request.getPageSize());
        }
        List<String> areaCodes = areaListResult.getData().stream().map(BasAgvAreaResponse::getAreaCode).collect(Collectors.toList());
        //判断请求参数中是否存在库区码
        if(!CollectionUtils.isEmpty(request.getAreaCodes())){
            //如果请求参数中库区码不为空，需要库区码判断是否AGV库区,过滤掉非AGV库区的库区码
            List<String> remainCodes = request.getAreaCodes()
                    .stream()
                    .filter(areaCodes::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(remainCodes)){
                return PagingObjectUtils.emptyPagingObject(request.getPageNum(),request.getPageSize());
            }
            request.setAreaCodes(remainCodes);
        }else{
            request.setAreaCodes(areaCodes);
        }
        //构造api接口请求参数
        GoodsShelfSideQueryApiRequest apiRequest = new GoodsShelfSideQueryApiRequest();
        BeanUtils.copyProperties(request,apiRequest);
        apiRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        //api调用
        Result<PagingObject<GoodsShelfSideResponse>> apiResult = goodsShelfAdminApi.selectGoodsShelves(apiRequest);
        if(!Result.isSuccess(apiResult)||ObjectUtils.isEmpty(apiResult.getData())||CollectionUtils.isEmpty(apiResult.getData().getContents())){
            return PagingObjectUtils.emptyPagingObject(request.getPageNum(),request.getPageSize());
        }
        String warehouseName = OperationUserContextHolder.get().getWarehouses()
                .stream()
                .filter((warehouse) -> warehouse.getWarehouseCode().equals(request.getWarehouseCode()))
                .map(OperationUserContext.UserWarehouse::getWarehouseName)
                .findFirst().orElse(null);
        //将仓库编码转换为仓库名
        apiResult.getData().getContents().forEach((item)-> item.setWarehouseName(warehouseName));
        return apiResult.getData();
    }


}
