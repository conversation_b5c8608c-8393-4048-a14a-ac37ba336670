package com.poizon.scm.wms.command.bas.sorting;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.pink.sdk.dto.pink.response.wms.SecSortingRulesResp;
import com.poizon.scm.pink.sdk.interfaces.WcsApi;
import com.poizon.scm.wcs.api.exceptions.WcsException;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.bas.api.enums.EnableStatusEnum;
import com.poizon.scm.wms.bas.api.sorting.api.BaseSortingRuleApi;
import com.poizon.scm.wms.bas.api.sorting.request.*;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingRuleDetailPageApiResponse;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingRuleDetailQueryApiResponse;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingRulePageApiResponse;
import com.poizon.scm.wms.command.bas.sorting.convert.BasSortingConvert;
import com.poizon.scm.wms.command.bas.sorting.request.*;
import com.poizon.scm.wms.command.bas.sorting.response.SortRuleValueEnumResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wuyusen
 * @Date: 2021/5/31
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("admin/bas")
public class BaseSortingRuleController {

    private BasSortingConvert basSortingConvert = BasSortingConvert.INSTANCE;

    @DubboReference(interfaceClass = BaseSortingRuleApi.class, timeout = 5000, check = false)
    private BaseSortingRuleApi baseSortingRuleApi;
    @DubboReference(interfaceClass = WcsApi.class, timeout = 500, check = false)
    private WcsApi wcsApi;

    @Resource
    private ScpWarehouseRepository scpWarehouseRepository;

    @PostMapping(value = "/sorting/rule/save", produces = "application/json")
    @ApiOperation(value = "新建规则")
    public Result<Void> save(@RequestBody @Valid BaseSortingRuleSaveRequest request) {
        BaseSortingRuleSaveApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert8(request);
        baseSortingSceneSaveApiRequest.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setWarehouseName(scpWarehouseRepository.queryNameByCodeAndTenant(OperationUserContextHolder.get().getTenantCode(),
                request.getWarehouseCode()));
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        baseSortingSceneSaveApiRequest.setStatus(EnableStatusEnum.DISABLE.code());
        return baseSortingRuleApi.saveBaseSortingRule(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/update", produces = "application/json")
    @ApiOperation(value = "修改和删除,等规则")
    public Result<Void> update(@RequestBody @Valid BaseSortingRuleUpdateRequest request) {
        BaseSortingRuleUpdateApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert9(request);
        baseSortingSceneSaveApiRequest.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingRuleApi.updateBaseSortingRule(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/enable", produces = "application/json")
    @ApiOperation(value = "启用规则 只给启用用 停用走更新")
    public Result<Void> enable(@RequestBody @Valid BaseSortingRuleEnableRequest request) {
        BaseSortingRuleUpdateApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert15(request);
        baseSortingSceneSaveApiRequest.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingRuleApi.enableBaseSortingRule(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/page", produces = "application/json")
    @ApiOperation(value = "规则分页查询")
    public Result<PagingObject<BaseSortingRulePageApiResponse>> page(@RequestBody @Valid BaseSortingRulePageRequest request) {
        BaseSortingRulePageApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert10(request);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        request.setStatus(EnableStatusEnum.ENABLE.code());
        return baseSortingRuleApi.pageBaseSortingRule(baseSortingSceneSaveApiRequest);
    }



    @PostMapping(value = "/sorting/rule/detail/save", produces = "application/json")
    @ApiOperation(value = "新建规则明细")
    public Result<Void> saveRuleDetail(@RequestBody @Valid BaseSortingRuleDetailSaveRequest request) {
        BaseSortingRuleDetailSaveApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert11(request);
        baseSortingSceneSaveApiRequest.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setWarehouseName(scpWarehouseRepository.queryNameByCodeAndTenant(OperationUserContextHolder.get().getTenantCode(),
                request.getWarehouseCode()));
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        baseSortingSceneSaveApiRequest.setPriorityUniFlag(true);
        return baseSortingRuleApi.saveBaseSortingRuleDetail(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/detail/update", produces = "application/json")
    @ApiOperation(value = "修改规则明细")
    public Result<Void> updateRuleDetail(@RequestBody @Valid BaseSortingRuleDetailUpdateRequest request) {
        BaseSortingRuleDetailUpdateApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert12(request);
        baseSortingSceneSaveApiRequest.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        baseSortingSceneSaveApiRequest.setPriorityUniFlag(true);
        return baseSortingRuleApi.updateBaseSortingRuleDetail(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/detail/simple/update", produces = "application/json")
    @ApiOperation(value = "删除,启用等规则明细")
    public Result<Void> updateRuleDetailSimple(@RequestBody @Valid BaseSortingRuleDetailUpdateRequest request) {
        BaseSortingRuleDetailUpdateApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert12(request);
        baseSortingSceneSaveApiRequest.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingRuleApi.updateBaseSortingRuleDetailSimple(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/detail/page", produces = "application/json")
    @ApiOperation(value = "规则明细分页查询")
    public Result<PagingObject<BaseSortingRuleDetailPageApiResponse>> pageRuleDetail(@RequestBody @Valid BaseSortingRuleDetailPageRequest request) {
        //兼容前段bug，会传""字符串
        if ("".equals(request.getStationCode())){
            request.setStationCode(null);
        }
        BaseSortingRuleDetailPageApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert13(request);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingRuleApi.pageBaseSortingRuleDetail(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/rule/detail/detail", produces = "application/json")
    @ApiOperation(value = "规则明细详情")
    public Result<BaseSortingRuleDetailQueryApiResponse> pageRuleDetailDetail(@RequestBody @Valid BaseSortingRuleDetailQueryRequest request) {
        BaseSortingRuleDetailQueryApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert14(request);
        return baseSortingRuleApi.queryBaseSortingRuleDetail(baseSortingSceneSaveApiRequest);
    }
    @PostMapping(value = "/sorting/queryStandardSortRuleValue", produces = "application/json")
    @ApiOperation(value = "一道二分规则下拉查询")
    public Result<List<SortRuleValueEnumResponse>> queryStandardSortRuleValue(@RequestBody @Valid SortRuleValueEnumRequest request) {
        String warehouseCode = request.getWarehouseCode();
        com.shizhuang.avatar.common.model.Result<List<SecSortingRulesResp>> result = wcsApi.getSecSortingRules(warehouseCode, 1);
        if (!com.shizhuang.avatar.common.model.Result.isSuccess(result)) {
            log.error("wcsApi.getSecSortingRules warehouseCode = {} , respResult = {}"
                    , warehouseCode , JSON.toJSONString(result));
            Result.ofError(result.getCode() == null ? -1 : result.getCode().intValue(), result.getMsg());
        }
        List<SecSortingRulesResp> data = result.getData();
        if (data == null) {
            return Result.ofSuccess(new ArrayList<>());
        }
        List<SortRuleValueEnumResponse> responseList = data.stream().map(this::convert).collect(Collectors.toList());
        return Result.ofSuccess(responseList);
    }
    private SortRuleValueEnumResponse convert(SecSortingRulesResp secSortingRulesResp) {
        SortRuleValueEnumResponse response = new SortRuleValueEnumResponse();
        response.setValue(secSortingRulesResp.getRuleCode());
        response.setValueDesc(secSortingRulesResp.getRuleCode());
        return response;
    }



}
