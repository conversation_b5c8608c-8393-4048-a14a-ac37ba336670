package com.poizon.scm.wms.command.outbound.launch.rule.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyRuleDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.RuleListQueryParam;
import com.poizon.scm.wms.adapter.outbound.launch.model.result.RuleListQueryResult;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyRuleQueryRepository;
import com.poizon.scm.wms.command.outbound.launch.rule.request.EditOutLaunchStrategyRuleRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.outbound.launch.StrategyRuleCommandService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class EditOutLaunchStrategyRuleRequestExecutor extends AbstractCommandExecutor<EditOutLaunchStrategyRuleRequest, Void> {

    @Resource
    private StrategyRuleCommandService strategyRuleCommandService;

    @Resource
    private BizOffShelvesStrategyRuleQueryRepository strategyRuleQueryRepository;

    @Autowired
    private CreateOutLaunchStrategyRuleRequestExecutor createStrategyRuleRequestExecutor;

    @Override
    protected Void doExecute(EditOutLaunchStrategyRuleRequest request) {
        createStrategyRuleRequestExecutor.validateAutoLaunchRule(request.getAutoLaunchRuleContent());
        strategyRuleCommandService.updateByRuleNoSelective(buildUpdateParam(request));
        return null;
    }

    private BizOffShelvesStrategyRuleDo buildUpdateParam(EditOutLaunchStrategyRuleRequest request) {
        BizOffShelvesStrategyRuleDo ruleDo = new BizOffShelvesStrategyRuleDo();
        ruleDo.setRuleNo(request.getRuleNo());
        ruleDo.setFrequency(request.getFrequency());
        ruleDo.setRuleName(request.getRuleName());
        ruleDo.setRuleDesc(request.getRuleDesc());
        ruleDo.setPriority(request.getPriority());
        ruleDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        ruleDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        ruleDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        ruleDo.setUpdatedTime(new Date());
        ruleDo.setRuleContent(JSON.toJSONString(request.getAutoLaunchRuleContent()));
        return ruleDo;
    }

    @Override
    protected void verify(EditOutLaunchStrategyRuleRequest request) {
        BizOffShelvesStrategyRuleDo ruleDo = strategyRuleQueryRepository.queryByRuleNo(request.getRuleNo());
        if (ruleDo == null) {
            throw new WmsOperationException("规则不存在");
        }
        List<RuleListQueryResult> list = strategyRuleQueryRepository.queryList(new RuleListQueryParam(ruleDo.getStrategyNo()), OperationUserContextHolder.getScmTenantCode());
        list = list.stream().filter(item -> !item.getRuleNo().equals(request.getRuleNo())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getPriority().equals(request.getPriority()))) {
            throw new WmsOperationException("该优先级已存在");
        }
    }
}
