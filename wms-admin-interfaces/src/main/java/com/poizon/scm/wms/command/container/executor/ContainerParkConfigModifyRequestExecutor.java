package com.poizon.scm.wms.command.container.executor;

import com.poizon.scm.wms.adapter.container.repository.lpn.LpnContainerParkConfigRepository;
import com.poizon.scm.wms.adapter.container.repository.lpn.param.LpnContainerParkConfigModifyParam;
import com.poizon.scm.wms.command.container.request.ContainerParkConfigModifyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.common.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/7 10:16 PM
 * @description
 */
@Component
public class ContainerParkConfigModifyRequestExecutor extends AbstractCommandExecutor<ContainerParkConfigModifyRequest, Void> {

    @Resource
    private LpnContainerParkConfigRepository lpnContainerParkConfigRepository;


    @Override
    protected void verify(ContainerParkConfigModifyRequest request) {
        if (StringUtils.isBlank(request.getWarehouseCode())) {
            throw new WmsOperationException("仓库不能为空");
        }
        if (StringUtils.isBlank(request.getLocationCode())) {
            throw new WmsOperationException("库位不能为空");
        }
        if (Objects.isNull(request.getIsReleaseContainer()) || Objects.isNull(request.getIsCreateHandover())) {
            throw new WmsOperationException("配置参数为空");
        }
    }

    @Override
    protected Void doExecute(ContainerParkConfigModifyRequest request) {
        lpnContainerParkConfigRepository.modifyLocationContainerParkConfig(buildModifyParam(request));
        return null;
    }

    private LpnContainerParkConfigModifyParam buildModifyParam(ContainerParkConfigModifyRequest request) {
        LpnContainerParkConfigModifyParam param = new LpnContainerParkConfigModifyParam();
        param.setTenantCode(OperationUserContextHolder.getTenantCode());
        param.setWarehouseCode(request.getWarehouseCode());
        param.setLocationCode(request.getLocationCode());
        param.setIsAcceptRelease(request.getIsReleaseContainer());
        param.setIsCreateHandover(request.getIsCreateHandover());
        param.setUserId(OperationUserContextHolder.get().getUserId());
        param.setUserName(OperationUserContextHolder.get().getUserName());
        param.setRealName(OperationUserContextHolder.get().getRealName());
        return param;
    }
}
