package com.poizon.scm.wms.command.bas;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.request.*;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 配置元数据查询服务
 */
@RestController
@RequestMapping("/admin/bas/config/meta")
@Api(tags = "H5-BAS配置-配置元数据管理")
public class WmsBasConfigMetaCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 新增配置元数据
     */
    @ApiOperation("新增配置元数据")
    @PostMapping("/add")
    public Result<Void> addConfigMeta(@RequestBody WmsBasConfigMetaSaveRequest request) {
        return bus.handle(request);
    }

    /**
     * 应用配置元数据
     */
    @ApiOperation("应用配置元数据")
    @PostMapping("/apply")
    public Result<Void> applyConfigMeta(@RequestBody WmsBasConfigMetaApplyRequest request) {
        return bus.handle(request);
    }

    /**
     * 删除配置元数据
     */
    @ApiOperation("删除配置元数据")
    @PostMapping("/remove")
    public Result<Void> removeConfigMeta(@RequestBody WmsBasConfigMetaRemoveRequest request) {
        return bus.handle(request);
    }

    /**
     * 修改配置元数据
     */
    @ApiOperation("修改配置元数据")
    @PostMapping("/modify")
    public Result<Void> modifyConfigMeta(@RequestBody WmsBasConfigMetaModifyRequest request) {
        return bus.handle(request);
    }

    /**
     * 停用配置元数据
     */
    @ApiOperation("停用配置元数据")
    @PostMapping("/disable")
    public Result<Void> disableConfigMeta(@RequestBody WmsBasConfigMetaDisableRequest request) {
        return bus.handle(request);
    }

    /**
     * 启用配置元数据
     */
    @ApiOperation("启用配置元数据")
    @PostMapping("/enable")
    public Result<Void> enableConfigMeta(@RequestBody WmsBasConfigMetaEnableRequest request) {
        return bus.handle(request);
    }

}
