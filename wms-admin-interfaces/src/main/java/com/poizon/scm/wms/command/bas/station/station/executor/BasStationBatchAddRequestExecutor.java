package com.poizon.scm.wms.command.bas.station.station.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationRequest;
import com.poizon.scm.wms.command.bas.station.station.request.BasStationBatchAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  30/04/2024 周二
 */
@Slf4j
@Component
public class BasStationBatchAddRequestExecutor extends AbstractCommandExecutor<BasStationBatchAddRequest, Boolean> {

    @DubboReference(check = false)
    private BasStationApi basStationApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected Boolean doExecute(BasStationBatchAddRequest request) {

        List<BasStationRequest> requestList = request.getRequestList()
                .stream()
                .map(basStationRequest -> BeanCopyUtils.copyProperties(basStationRequest, BasStationRequest.class))
                .peek(basStationRequest -> basStationRequest.setTenantId(OperationUserContextHolder.getTenantCode()))
                .collect(Collectors.toList());

        com.poizon.scm.wms.bas.api.command.station.request.BasStationBatchAddRequest
                batchAddRequest = new com.poizon.scm.wms.bas.api.command.station.request.BasStationBatchAddRequest();
        batchAddRequest.setRequestList(requestList);

        Result<Boolean> result = LogUtils.logRemoteInvoke(log, batchAddRequest, basStationApi::add);

        return Optional.ofNullable(result).map(Result::getData)
                .orElse(Boolean.FALSE);
    }
}
