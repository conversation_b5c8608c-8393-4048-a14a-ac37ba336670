package com.poizon.scm.wms.command.goodsshelf.request;

import com.poizon.scm.wms.util.framework.RequestPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GoodsShelfSideQueryRequest extends RequestPage {
    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("库区编码")
    private List<String> areaCodes;

    @ApiModelProperty("货架编码")
    private String goodsShelfCode;

    @ApiModelProperty("库位编码")
    private String locationCode;
}
