package com.poizon.scm.wms.query.outbound.fp;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.outbound.api.fp.FpQueryApi;
import com.poizon.scm.wms.outbound.api.fp.request.query.*;
import com.poizon.scm.wms.outbound.api.fp.response.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


@RestController
@Api(tags = {"后台-fp查询相关"})
@RequestMapping("/admin/fp/")
public class FpQueryController {


    @DubboReference(interfaceClass = FpQueryApi.class, timeout = 5000, check = false)
    private FpQueryApi fpQueryApi;


    @ApiOperation("fp区域列表")
    @PostMapping(value = "/v1/order/rule/list", produces = "application/json")
    public Result<PagingObject<FpOrderRuleResponse>> queryList(@RequestBody @Valid FpQueryAreaListRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryList(request);
    }


    @ApiOperation("fp区域人员绑定列表")
    @PostMapping(value = "/v1/rule/user/list", produces = "application/json")
    public Result<PagingObject<FpAreaUserResponse>> queryList(@RequestBody @Valid FpQueryAreaUserRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryList(request);
    }


    @ApiOperation("fp待拣选订单明细列表")
    @PostMapping(value = "/v1/wait/order/list", produces = "application/json")
    public Result<PagingObject<FpWaitPickListResponse>> queryWaitPickOrderList(@RequestBody @Valid FpQueryWaitPickOrderRequest request) {

        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        Result<PagingObject<FpWaitPickListResponse>> pagingObjectResult = fpQueryApi.queryWaitPickOrderList(request);
        return pagingObjectResult;
    }

    @ApiOperation("fp待拣选订单明细列表")
    @PostMapping(value = "/v1/wait/order/list/v2", produces = "application/json")
    public Result<PagingObject<FpWaitPickListResponse>> queryWaitPickOrderListV2(@RequestBody @Valid FpQueryWaitPickOrderRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        Result<PagingObject<FpWaitPickListResponse>> pagingObjectResult = fpQueryApi.queryWaitPickOrderList(request);
        return pagingObjectResult;
    }

    /**
     * 获取区域模式规则列表
     * @param request
     * @return
     */
    @ApiOperation("获取区域模式规则列表")
    @PostMapping(value = "/v1/fp/area/mode/list", produces = "application/json")
    public Result<PagingObject<FpAreaModeListResponse>> queryFpAreaModeList(@RequestBody @Valid FpAreaModeListRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryFpAreaModeList(request);
    }

    @ApiOperation("获取fp实例列表")
    @PostMapping(value = "/v1/fp/queryFpHistoryInfo/list", produces = "application/json")
    public Object queryFpHistoryInfo(@RequestBody FpHistoryInfoRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryFpHistoryInfo(request);
    }

    @ApiOperation("获取该仓库所有的活跃用户")
    @PostMapping(value = "/v1/fp/queryFpActiveUser", produces = "application/json")
    public  Result<List<FpActiveUserResponse>> queryFpActiveUserList(@RequestBody FpActiveUserRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryFpActiveUserList(request);
    }


    @ApiOperation("获取仓库所有配置用户")
    @PostMapping(value = "/v1/fp/queryFpUserItem", produces = "application/json")
    public  Result<List<FpUserItemResponse>> queryFpUserItem(@RequestBody FpActiveUserRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryFpUserItemResponse(request);
    }


    /**
     * 获取fp大盘配置1
     * @param request
     * @return
     */
    @ApiOperation("获取fp大盘配置")
    @PostMapping(value = "/v1/fp/queryFpDetailInfo", produces = "application/json")
    public Result<List<FpDetailInfoResponse>> queryFpDetailInfo(@RequestBody FpActiveUserRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryFpDetailInfo(request.getWarehouseCode(), request.getTenantCode());
    }



    /**
     * 获取fp大盘配置V2
     * @param request
     * @return
     */
    @ApiOperation("获取fp大盘配置V2")
    @PostMapping(value = "/v1/fp/queryFpDetailInfoV2", produces = "application/json")
    public Result<FpGlobalInfoResponse> queryFpDetailInfoV2(@RequestBody FpQueryFpDetailInfoRequest request) {
        request.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return fpQueryApi.queryFpDetailInfoV2(request);
    }

}
