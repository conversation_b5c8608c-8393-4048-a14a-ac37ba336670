package com.poizon.scm.wms.query.outbound.outbill.executor;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.outbound.delivery.model.DeliveryRelatedOrdersDo;
import com.poizon.scm.wms.adapter.outbound.delivery.repository.db.DeliveryRelatedOrdersRepository;
import com.poizon.scm.wms.adapter.outbound.outbill.model.WmsOutBillHeaderDo;
import com.poizon.scm.wms.adapter.outbound.outbill.model.WmsOutBillReceiverInfoDo;
import com.poizon.scm.wms.adapter.outbound.outbill.query.WmsOutBillHeaderQueryParam;
import com.poizon.scm.wms.adapter.outbound.outbill.repository.db.query.WmsOutBillHeaderQueryRepository;
import com.poizon.scm.wms.adapter.outbound.outbill.repository.db.query.WmsOutBillReceiverInfoQueryRepository;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsLogisticsBillDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsLogisticsBillRepository;
import com.poizon.scm.wms.adapter.scp.model.ScpWarehouseDo;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.api.enums.WmsOutBillStatusEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.query.outbound.outbill.request.WmsOutBillListQueryAdminRequest;
import com.poizon.scm.wms.query.outbound.outbill.response.WmsOutBillListQueryAdminResponse;
import com.poizon.scm.wms.util.enums.WmsOutBoundTypeEnum;
import com.poizon.scm.wms.util.util.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 查询出库单据列表(手动生成)执行器
 *
 * <AUTHOR>
 * @date 2022/01/06 2:33 下午
 */
@Service
public class WmsOutBillListQueryAdminRequestExecutor extends AbstractCommandExecutor<WmsOutBillListQueryAdminRequest, PagingObject<WmsOutBillListQueryAdminResponse>> {

    @Autowired
    private WmsOutBillHeaderQueryRepository wmsOutBillHeaderQueryRepository;

    @Autowired
    private ScpWarehouseRepository scpWarehouseRepository;

    @Autowired
    private WmsOutBillReceiverInfoQueryRepository billReceiverInfoQueryRepository;

    @Resource
    private DeliveryRelatedOrdersRepository deliveryRelatedOrdersRepository;

    @Resource
    private WmsLogisticsBillRepository wmsLogisticsBillRepository;

    @Override
    protected PagingObject<WmsOutBillListQueryAdminResponse> doExecute(WmsOutBillListQueryAdminRequest request) {
        try {
            PagingObject<WmsOutBillListQueryAdminResponse> pagingResponse = new PagingObject<>();
            WmsOutBillHeaderQueryParam queryParam = BeanUtil.copy(request, WmsOutBillHeaderQueryParam.class);
            Page<WmsOutBillHeaderDo> pagingObject = PageHelper.startPage(request.getPageNum(), request.getPageSize());
            List<WmsOutBillHeaderDo> billHeaderDos = wmsOutBillHeaderQueryRepository.queryByCondition(queryParam);
            pagingResponse.setPageNum(request.getPageNum());
            pagingResponse.setPageSize(request.getPageSize());
            pagingResponse.setPages(pagingObject.getPages());
            pagingResponse.setTotal(pagingObject.getTotal());
            List<WmsOutBillListQueryAdminResponse> responseList = convertFrom(billHeaderDos);
            pagingResponse.setContents(responseList);
            return pagingResponse;
        } finally {
            PageHelper.clearPage();
        }

    }

    /**
     * 对象转换
     *
     * @param contents
     * @return
     */
    private List<WmsOutBillListQueryAdminResponse> convertFrom(List<WmsOutBillHeaderDo> contents) {
        List<WmsOutBillListQueryAdminResponse> responseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(contents)) {
            return responseList;
        }
        /*获取仓库信息*/
        Map<String, String> warehouseCodeMapping = getWarehouseCodeMap(contents);

        Set<String> billNoSet = contents.stream().map(WmsOutBillHeaderDo::getOutBillNo).collect(Collectors.toSet());
        /*获取收件人信息*/
        Map<String, WmsOutBillReceiverInfoDo> receiverInfoMap = getReceiverInfoMap(billNoSet);
        Map<String, String> relatedOrderMap = new HashMap<>();
        Map<String, String> logisticMap = new HashMap<>();
        List<DeliveryRelatedOrdersDo> relatedOrdersDos = deliveryRelatedOrdersRepository.selectByRelatedOrderCodes(billNoSet);
        if (CollectionUtils.isNotEmpty(relatedOrdersDos)){
             relatedOrderMap = relatedOrdersDos.stream().collect(
                    Collectors.toMap(DeliveryRelatedOrdersDo::getRelatedOrderCode, DeliveryRelatedOrdersDo::getDeliveryOrderCode));
            List<WmsLogisticsBillDo> wmsLogisticsBillDos =
                    wmsLogisticsBillRepository.selectByDeliveryOrderCodeList(relatedOrderMap.values());
            if (CollectionUtils.isNotEmpty(wmsLogisticsBillDos)){
               logisticMap = wmsLogisticsBillDos.stream().collect(
                       Collectors.toMap(WmsLogisticsBillDo::getDeliveryOrderCode, WmsLogisticsBillDo::getExpressCode));
            }
        }
        for (WmsOutBillHeaderDo content : contents) {
            WmsOutBillListQueryAdminResponse response = buildBy(content,
                    warehouseCodeMapping.get(content.getWarehouseCode()),
                    receiverInfoMap.get(content.getOutBillNo()),
                    relatedOrderMap.get(content.getOutBillNo()),
                    logisticMap);
            responseList.add(response);
        }
        return responseList;
    }

    private Map<String, WmsOutBillReceiverInfoDo> getReceiverInfoMap(Set<String> billNoSet) {
        Map<String, WmsOutBillReceiverInfoDo> userInfoMap = new HashMap<>();
        List<WmsOutBillReceiverInfoDo> receiverInfoDos = billReceiverInfoQueryRepository.selectByBillNos(billNoSet, OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isNotEmpty(receiverInfoDos)) {
            userInfoMap = receiverInfoDos.stream().collect(Collectors.toMap(WmsOutBillReceiverInfoDo::getOutBillNo, Function.identity()));
        }
        return userInfoMap;
    }

    private Map<String, String> getWarehouseCodeMap(List<WmsOutBillHeaderDo> contents) {
        Set<String> warehouseCodeSet = contents.stream().map(WmsOutBillHeaderDo::getWarehouseCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(warehouseCodeSet)) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_CODE_EMPTY);
        }
        List<ScpWarehouseDo> scpWarehouseDos = scpWarehouseRepository.batchSelectByWarehouseCode(warehouseCodeSet, OperationUserContextHolder.getTenantCode());
        if (CollectionUtils.isEmpty(scpWarehouseDos)) {
            throw new WmsException(WmsExceptionCode.WAREHOUSE_NOT_EXIST);
        }
        /*仓库编码和名称的映射*/
        Map<String, String> warehouseCodeMapping = scpWarehouseDos.stream().collect(Collectors.toMap(ScpWarehouseDo::getWarehouseCode, ScpWarehouseDo::getWarehouseName));
        return warehouseCodeMapping;
    }

    /**
     * 构建相应对象
     *
     * @param content
     * @param warehouseName
     * @param receiverInfoDo
     * @param deliveryOrderCode
     * @return
     */
    private WmsOutBillListQueryAdminResponse buildBy(WmsOutBillHeaderDo content,
                                                     String warehouseName,
                                                     WmsOutBillReceiverInfoDo receiverInfoDo,
                                                     String deliveryOrderCode,
                                                     Map<String,String> logisticMap) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        WmsOutBillListQueryAdminResponse response = new WmsOutBillListQueryAdminResponse();
        response.setId(content.getId());
        response.setWarehouseCode(content.getWarehouseCode());
        response.setWarehouseName(warehouseName);
        response.setOutBillNo(content.getOutBillNo());
        response.setType(content.getType());
        response.setTypeName(WmsOutBoundTypeEnum.getDescByType(content.getType()));
        response.setStatus(content.getStatus());
        response.setStatusName(WmsOutBillStatusEnum.getDescriptionByStatus(content.getStatus()));
        response.setIsSendExpress(content.getIsSendExpress());
        response.setReceiverInfo(buildReceiverInfoFrom(receiverInfoDo));
        response.setCreatedRealName(content.getCreatedRealName());
        response.setCreatedTime(format.format(content.getCreatedTime()));
        response.setAuditRealName(content.getAuditRealName());
        response.setAuditTime(formatTime(format, content.getAuditTime()));
        response.setFinishTime(formatTime(format, content.getFinishTime()));
        response.setRemark(content.getRemark());
        response.setDeliveryOrderCode(deliveryOrderCode);
        response.setExpressCode(logisticMap.getOrDefault(deliveryOrderCode, StringUtils.EMPTY));
        return response;
    }

    /**
     * 转换时间
     *
     * @param format
     * @param time
     * @return
     */
    private String formatTime(SimpleDateFormat format, Date time) {
        if (Objects.isNull(time)) {
            return null;
        }
        return format.format(time);
    }

    /**
     * 构建收货人信息
     *
     * @param receiverInfoDo
     * @return
     */
    private WmsOutBillListQueryAdminResponse.ReceiverInfo buildReceiverInfoFrom(WmsOutBillReceiverInfoDo receiverInfoDo) {
        if (Objects.isNull(receiverInfoDo)) {
            return null;
        }
        WmsOutBillListQueryAdminResponse.ReceiverInfo receiverInfo = new WmsOutBillListQueryAdminResponse.ReceiverInfo();
        receiverInfo.setReceiverName(receiverInfoDo.getReceiverName());
        receiverInfo.setReceiverPhone(receiverInfoDo.getReceiverPhone());
        receiverInfo.setProvince(receiverInfoDo.getReceiverProvince());
        receiverInfo.setCity(receiverInfoDo.getReceiverCity());
        receiverInfo.setArea(receiverInfoDo.getReceiverArea());
        receiverInfo.setDetailAddress(receiverInfoDo.getReceiverAddress());
        return receiverInfo;
    }
}
