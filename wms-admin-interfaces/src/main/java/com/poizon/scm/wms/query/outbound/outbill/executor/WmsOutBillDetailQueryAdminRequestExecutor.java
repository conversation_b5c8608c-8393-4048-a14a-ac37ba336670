package com.poizon.scm.wms.query.outbound.outbill.executor;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.outbound.outbill.model.WmsOutBillDetailDo;
import com.poizon.scm.wms.adapter.outbound.outbill.repository.db.query.WmsOutBillDetailQueryRepository;
import com.poizon.scm.wms.api.dto.common.SelectStringValueOption;
import com.poizon.scm.wms.api.enums.DictTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.GetPropertyHelper;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.query.outbound.outbill.request.WmsOutBillDetailQueryAdminRequest;
import com.poizon.scm.wms.query.outbound.outbill.response.WmsOutBillDetailQueryAdminResponse;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.sys.SysDictV2Service;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 查询出库单据列表(手动生成)执行器
 * <AUTHOR>
 * @date 2022/01/06 2:33 下午
 */
@Service
public class WmsOutBillDetailQueryAdminRequestExecutor extends AbstractCommandExecutor<WmsOutBillDetailQueryAdminRequest, PagingObject<WmsOutBillDetailQueryAdminResponse>> {

    @Resource
    private WmsOutBillDetailQueryRepository wmsOutBillDetailQueryRepository;

    @Resource
    private ICommodityQueryV2Service commodityQueryV2Service;

    @Resource
    private SysDictV2Service sysDictService;

    @Override
    protected PagingObject<WmsOutBillDetailQueryAdminResponse> doExecute(WmsOutBillDetailQueryAdminRequest request) {
        PagingObject<WmsOutBillDetailQueryAdminResponse> pagingObject = new PagingObject<>();
        pagingObject.setPageSize(request.getPageSize());
        pagingObject.setPageNum(request.getPageNum());
        try {
            PageHelper.startPage(request.getPageNum(),request.getPageSize());
            List<WmsOutBillDetailDo> wmsOutBillDetailDos =
                    wmsOutBillDetailQueryRepository.queryListByOutBillNo(request.getOutBillNo());
            PageInfo<WmsOutBillDetailDo> pageInfo = new PageInfo(wmsOutBillDetailDos);
            if (pageInfo.getSize() > NumberUtils.INTEGER_ZERO
                    && CollectionUtils.isNotEmpty(pageInfo.getList())) {
                List<WmsOutBillDetailQueryAdminResponse> responseList = new ArrayList<>(pageInfo.getSize());
                // 商品信息
                Map<String, SkuCommonRspDomain> skuCommonRspDomainMap = getSkuCommonMap(pageInfo.getList());
                // 瑕疵等级
                Map<String, String> qualityLevelMap = getQualityLevelMap(pageInfo.getList().get(0).getWarehouseCode());

                pageInfo.getList().forEach(wmsOutBillDetailDo -> {
                    WmsOutBillDetailQueryAdminResponse response = new WmsOutBillDetailQueryAdminResponse();
                    response.setId(wmsOutBillDetailDo.getId());
                    response.setUniqueCode(wmsOutBillDetailDo.getUniqueCode());
                    response.setQty(wmsOutBillDetailDo.getQty());
                    response.setSkuId(wmsOutBillDetailDo.getSkuId());
                    response.setQualityLevel(wmsOutBillDetailDo.getQualityLevel());
                    response.setOwnerCode(wmsOutBillDetailDo.getOwnerCode());
                    response.setBizType(wmsOutBillDetailDo.getBizType());
                    response.setQualityLevelName(qualityLevelMap.get(wmsOutBillDetailDo.getQualityLevel()));
                    SkuCommonRspDomain skuCommonRspDomain;
                    if (skuCommonRspDomainMap != null
                            && (skuCommonRspDomain = skuCommonRspDomainMap.get(wmsOutBillDetailDo.getSkuId())) != null) {
                        response.setGoodsCategory(skuCommonRspDomain.getCategoryNameLevel3());
                        response.setGoodsArticleNumber(GetPropertyHelper.getString(skuCommonRspDomain::getArtNoMulti));
                        response.setGoodsName(GetPropertyHelper.getString(skuCommonRspDomain::getSpuName));
                        response.setProperty(skuCommonRspDomain.getPropertyText());
                    }
                    responseList.add(response);
                });
                pagingObject.setContents(responseList);
                pagingObject.setTotal(pageInfo.getTotal());
            }
        } finally {
            PageHelper.clearPage();
        }
        return pagingObject;
    }

    private Map<String, SkuCommonRspDomain> getSkuCommonMap(List<WmsOutBillDetailDo> detailDos) {
        Set<String> skuIds = detailDos.stream().map(WmsOutBillDetailDo::getSkuId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(skuIds)) {
            Map<String, SkuCommonRspDomain> skuCommonRspDomainMap = commodityQueryV2Service
                    .querySkuCommonMapBySkuIds(OperationUserContextHolder.getTenantCode(), skuIds);
            return skuCommonRspDomainMap;
        }
        return Maps.newHashMap();
    }

    private Map<String, String> getQualityLevelMap(String warehouseCode) {
        return sysDictService.listByStringValueNoException(
                warehouseCode, DictTypeEnum.QUALITY_LEVEL).stream().collect(Collectors.toMap(SelectStringValueOption::getValue, SelectStringValueOption::getText));
    }
}
