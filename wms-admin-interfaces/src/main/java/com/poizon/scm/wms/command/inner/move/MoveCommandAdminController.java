package com.poizon.scm.wms.command.inner.move;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.saas.wms.inner.cancel.MoveOrderCancelApi;
import com.poizon.scm.saas.wms.inner.cancel.request.MoveOderCancelRequest;
import com.poizon.scm.saas.wms.inner.create.MoveOrderCreateApi;
import com.poizon.scm.saas.wms.inner.create.request.MoveInventoryItemRequest;
import com.poizon.scm.saas.wms.inner.create.request.MoveOderCreateRequest;
import com.poizon.scm.saas.wms.inner.nearexpire.query.QualityControlOrderDetailQueryApi;
import com.poizon.scm.saas.wms.inner.nearexpire.query.dto.QualityControlOrderExportApiDTO;
import com.poizon.scm.saas.wms.inner.nearexpire.query.request.QualityControlOrderExportApiRequest;
import com.poizon.scm.saas.wms.inner.off.execute.OffExecuteApi;
import com.poizon.scm.saas.wms.inner.off.execute.request.OffFinishRequest;
import com.poizon.scm.saas.wms.inner.off.execute.response.OffExecuteResponse;
import com.poizon.scm.saas.wms.inner.off.query.OffTaskDetailQueryApi;
import com.poizon.scm.saas.wms.inner.off.query.OffTaskQueryApi;
import com.poizon.scm.saas.wms.inner.off.query.dto.OffTaskDTO;
import com.poizon.scm.saas.wms.inner.off.query.dto.OffTaskDetailDTO;
import com.poizon.scm.saas.wms.inner.off.query.request.OffTaskDetailQuery;
import com.poizon.scm.saas.wms.inner.off.query.request.OffTaskQuery;
import com.poizon.scm.saas.wms.inner.query.InnerIdentifyMoveQueryApi;
import com.poizon.scm.saas.wms.inner.query.MoveOrderAdminQueryApi;
import com.poizon.scm.saas.wms.inner.query.request.InnerIdentifyMovePageQueryRequest;
import com.poizon.scm.saas.wms.inner.query.request.MoveOderAdminQueryExportRequest;
import com.poizon.scm.saas.wms.inner.query.response.InnerIdentifyMoveItemDTO;
import com.poizon.scm.saas.wms.inner.query.response.MoveOrderAdminExportDTO;
import com.poizon.scm.wms.adapter.commodity.model.rsp.SpuBrandRspDo;
import com.poizon.scm.wms.adapter.commodity.repository.ScpBrandRepository;
import com.poizon.scm.wms.adapter.common.SkuRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryEntity;
import com.poizon.scm.wms.adapter.inventory.repository.InventoryRepository;
import com.poizon.scm.wms.api.dto.request.reportDownload.ReportCreateRequest;
import com.poizon.scm.wms.api.dto.response.report.ReportCenterResponse;
import com.poizon.scm.wms.command.inner.move.executor.MoveBillCreateRequestExecutor;
import com.poizon.scm.wms.command.inner.move.request.*;
import com.poizon.scm.wms.common.IExcelExportService;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.domain.inner.move.param.MoveBillCreateParam;
import com.poizon.scm.wms.domain.inner.move.param.MoveInventorySelectedDTO;
import com.poizon.scm.wms.pojo.common.RocketMqConstants;
import com.poizon.scm.wms.producer.SendMessageHandler;
import com.poizon.scm.wms.producer.message.SendMessage;
import com.poizon.scm.wms.query.inner.move.NearExpireOrderConverter;
import com.poizon.scm.wms.query.inner.move.request.MoveInventoryPageQueryRequest;
import com.poizon.scm.wms.service.commodity.service.ICommodityCostlyService;
import com.poizon.scm.wms.service.reportDownload.ReportDownloadService;
import com.poizon.scm.wms.util.common.ItemCode;
import com.poizon.scm.wms.util.enums.ItemCodeType;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/9/23 9:59 上午
 **/
@RestController
@RequestMapping(value = "/admin/move/command")
@Slf4j
public class MoveCommandAdminController {

    @Resource
    private CommandBus commandBus;

    @Resource
    private SkuRepository skuRepository;

    @DubboReference(interfaceClass = QualityControlOrderDetailQueryApi.class, timeout = 5000, check = false)
    private QualityControlOrderDetailQueryApi qualityControlOrderDetailQueryApi;

    private NearExpireOrderConverter nearExpireOrderConverter = NearExpireOrderConverter.INSTANCE;

    @Resource
    private ReportDownloadService reportDownloadService;

    @Resource
    private IExcelExportService excelExportService;

    @Resource
    private MoveOrderCreateApi moveOrderCreateApi;
    @Resource
    private MoveOrderCancelApi moveOrderCancelApi;
    @Resource
    private OffExecuteApi offExecuteApi;
    @Resource
    private OffTaskQueryApi offTaskQueryApi;
    @Resource
    private OffTaskDetailQueryApi offTaskDetailQueryApi;
    @Resource
    private ScpBrandRepository scpBrandRepository;
    @Resource
    private InnerIdentifyMoveQueryApi innerIdentifyMoveQueryApi;

    @Resource
    private InventoryRepository inventoryRepository;

    @Resource
    private ICommodityCostlyService commodityCostlyService;

    private final String[] REPORT_HEADER = {"仓库名称", "移库单号", "关联单号","单据状态", "创建人", "创建时间", "下架人", "领取任务时间",
            "下架完成时间", "计划移出数量", "已锁定数量", "实际移出数量", "商品编码", "国标码","附加码","货主ID","货主名称",
            "容器号","移出库位","商品计划移出数量","商品已锁定数量","商品实际移出数量","上架单号","上架人","上架数量","上架库位","上架完成时间","上架状态"};
    @Value("${qc.export.pageSize:200}")
    private Integer pageSize;

    @Value("${qc.export.days:30}")
    private Integer maxExportDays;

    /**
     * 同步创建最大数量
     */
    @Value("${saas.move.oder.sync.create.max.size:100}")
    private Integer syncMaxSize;

    /**
     * 异步创建最大数量和任务分页最大数量一样
     */
    @Value("${off.task.detail.page.size:1000}")
    private Integer asynMaxSize;

    @Resource
    SendMessageHandler sendMessageHandler;

    @Resource
    private MoveBillCreateRequestExecutor moveBillCreateRequestExecutor;

    @DubboReference(interfaceClass = MoveOrderAdminQueryApi.class, timeout = 5000, check = false)
    private MoveOrderAdminQueryApi moveOrderAdminQueryApi;

    /**
     * /admin/move/command/v1/create
     * @param request
     * @return
     */
    @ApiModelProperty(value = "创建移库单")
    @RequestMapping(value = "/v1/create",method = RequestMethod.POST)
    public Result<Boolean> createMoveBill(@Valid @RequestBody MoveBillCreateRequest request) {
        OperationUserContext context = OperationUserContextHolder.get();
        if (context == null) {
            throw new WmsOperationException("请重新登录");
        }
        check(request);
        // 没有勾选全选
        if (NumberUtils.INTEGER_ZERO.equals(request.getSelectAll())) {
            return createOrder(request, context);
        }
        MoveBillCreateParam moveBillCreateParam = moveBillCreateRequestExecutor.buildMoveBillCreateParam(request);
        List<MoveInventorySelectedDTO> selectedList = moveBillCreateParam.getSelectedList();

        // 需要筛选贵品
        List<MoveInventoryDTO> newSelectedList;
        if (moveBillCreateParam.getSelectCostly().equals(NumberUtils.INTEGER_ONE)) {
            // MoveInventorySelectedDTO -> MoveInventoryDTO
            newSelectedList = selectedList.stream().map(this::convert).collect(Collectors.toList());
            newSelectedList = filterCostly(newSelectedList, context.getTenantCode());
            if (CollectionUtils.isEmpty(newSelectedList)) {
                throw new WmsOperationException("此次移库新增操作选中的库存中没有贵品，请重新选择!");
            }
            // MoveInventoryDTO -> MoveInventorySelectedDTO
            selectedList = newSelectedList.stream().map(this::convertMoveInventorySelectedDTO).collect(Collectors.toList());
        }

        int invQty = 0;
        for (MoveInventorySelectedDTO moveInventorySelectedDTO : selectedList) {
            String inventoryNos = moveInventorySelectedDTO.getInventoryNos();
            invQty = invQty + inventoryNos.split(",").length;
        }
        if (invQty <= syncMaxSize) {
            request.setSelectAll(NumberUtils.INTEGER_ZERO);
            request.setSelectedList(selectedList.stream().map(this::convert).collect(Collectors.toList()));
            return createOrder(request, context);
        }
        if (invQty > asynMaxSize) {
            throw new WmsOperationException("选择数量太大, 请分多次创建");
        }
        return createOrderAsynchronous(request, selectedList, context);
    }


    private  Result<Boolean> createOrderAsynchronous(MoveBillCreateRequest request
            , List<MoveInventorySelectedDTO> selectedList
            , OperationUserContext context) {
        MoveOderCreateRequest createRequest = this.convert(request);
        createRequest.setTenantCode(context.getTenantCode());
        createRequest.setCreateUserId(context.getUserId());
        createRequest.setCreateUserName(context.getUserName());
        createRequest.setSelectedList(null);
        // 先创建头部，再发消息创建明细
        Result<String> result = moveOrderCreateApi.createOrderHead(createRequest);
        if (!Result.isSuccess(result)) {
            log.warn("createMoveBill_fail = {}", JSON.toJSON(result));
            throw new WmsOperationException(result.getMsg());
        }
        String moveOrderNo = result.getData();
        SendMessage <WmsMoveOrderGenerateRequest> message = new SendMessage<>();
        WmsMoveOrderGenerateRequest generateRequest = new WmsMoveOrderGenerateRequest();
        generateRequest.setMoveOrderNo(moveOrderNo);
        generateRequest.setTenantCode(context.getTenantCode());
        generateRequest.setWarehouseCode(request.getWarehouseCode());
        generateRequest.setSelectedList(selectedList.stream().map(this::convert).collect(Collectors.toList()));
        message.setTopic(RocketMqConstants.WMS_COMMON_TOPIC);
        message.setTag("MOVE_GENERATE_REQUEST_TAG");
        message.setMessageContent(generateRequest);
        message.setMessageKey("MOVE_ASYN_GENERATE_" + moveOrderNo);
        sendMessageHandler.process(message);

        return Result.ofSuccess(true);
    }

    private  Result<Boolean> createOrder(MoveBillCreateRequest request, OperationUserContext context) {
        MoveOderCreateRequest createRequest = this.convert(request);
        createRequest.setTenantCode(context.getTenantCode());
        createRequest.setCreateUserId(context.getUserId());
        createRequest.setCreateUserName(context.getUserName());

        List<MoveInventoryDTO> selectedList = request.getSelectedList();
        // 此时勾选了全选 需要筛选贵品
        if (request.getSelectCostly().equals(NumberUtils.INTEGER_ONE)) {
            selectedList = filterCostly(selectedList, context.getTenantCode());
            if (CollectionUtils.isEmpty(selectedList)) {
                throw new WmsOperationException("此次移库新增操作选中的库存中没有贵品，请重新选择!");
            }
        }
        //  MoveInventoryDTO ->  MoveInventoryItemRequest
        createRequest.setSelectedList(selectedList.stream().map(this::convert).collect(Collectors.toList()));
        Result<Void> result = moveOrderCreateApi.createOrder(createRequest);
        if (!Result.isSuccess(result)) {
            log.warn("createMoveBill_fail = {}", JSON.toJSON(result));
            throw new WmsOperationException(result.getMsg());
        }
        return Result.ofSuccess(true);
    }

    /**
     * 将selectedList的非贵品筛除
     * @param selectedList
     * @param tenantCode
     * @return
     */
    private List<MoveInventoryDTO> filterCostly(List<MoveInventoryDTO> selectedList, String tenantCode) {
        List<String> skuIdList = new ArrayList<>();
        // 构建inventoryNo与skuId的映射
        Map<String, String> invNoSkuIdMap = new HashMap<>();
        for (MoveInventoryDTO moveInventoryDTO : selectedList) {
            String[] splitInventoryNos = moveInventoryDTO.getInventoryNos().split(",");
            List<String> inventoryNosList = Arrays.asList(splitInventoryNos);
            // 去数据库中根据InventoryNo批量查询库存行
            List<InventoryEntity> inventoryEntities = inventoryRepository.queryByInventoryNos(inventoryNosList);
            for (InventoryEntity inventoryEntity : inventoryEntities) {
                invNoSkuIdMap.put(inventoryEntity.getInventoryNo(), inventoryEntity.getSkuId());
                skuIdList.add(inventoryEntity.getSkuId());
            }
        }
        skuIdList = skuIdList.stream().distinct().collect(Collectors.toList());
        Map<String, Boolean> skuIdCostlyMap = commodityCostlyService.queryCostlyBySkuIdListV2(skuIdList, tenantCode);

        boolean haveCostlyFlag = Boolean.FALSE;
        for (MoveInventoryDTO moveInventoryDTO : selectedList) {
            String[] splitInventoryNos = moveInventoryDTO.getInventoryNos().split(",");
            String newInventoryNos = "";
            for (String splitInventoryNo : splitInventoryNos) {
                if (skuIdCostlyMap.get(invNoSkuIdMap.get(splitInventoryNo))) {
                    haveCostlyFlag = Boolean.TRUE;
                    newInventoryNos += splitInventoryNo;
                    newInventoryNos += ",";
                }
            }
            // 移除最后一个库存号后面的,
            if (newInventoryNos.endsWith(",")) {
                newInventoryNos = newInventoryNos.substring(0, newInventoryNos.length() - 1);
            }
            moveInventoryDTO.setInventoryNos(newInventoryNos);
        }
        return haveCostlyFlag ? selectedList : null;
    }

    private MoveOderCreateRequest convert(MoveBillCreateRequest request) {
        if ( request == null ) {
            return null;
        }

        MoveOderCreateRequest moveOderCreateRequest = new MoveOderCreateRequest();

        moveOderCreateRequest.setWarehouseCode( request.getWarehouseCode() );
        moveOderCreateRequest.setRemark( request.getRemark() );
        moveOderCreateRequest.setSelectedList(convert(request.getSelectedList() ) );
        moveOderCreateRequest.setSelectAll( request.getSelectAll() );
        moveOderCreateRequest.setMoveInventoryPageQueryRequest(convert(request.getMoveInventoryPageQueryRequest() ) );

        return moveOderCreateRequest;
    }


    protected com.poizon.scm.saas.wms.inner.create.request.MoveInventoryPageQueryRequest  convert(MoveInventoryPageQueryRequest moveInventoryPageQueryRequest) {
        if ( moveInventoryPageQueryRequest == null ) {
            return null;
        }

        com.poizon.scm.saas.wms.inner.create.request.MoveInventoryPageQueryRequest moveInventoryPageQueryRequest1 = new com.poizon.scm.saas.wms.inner.create.request.MoveInventoryPageQueryRequest();

        moveInventoryPageQueryRequest1.setWarehouseCode( moveInventoryPageQueryRequest.getWarehouseCode() );
        List<Integer> list = moveInventoryPageQueryRequest.getBizTypes();
        if ( list != null ) {
            moveInventoryPageQueryRequest1.setBizTypes(new ArrayList<>(list) );
        }
        List<String> list1 = moveInventoryPageQueryRequest.getAreaCodes();
        if ( list1 != null ) {
            moveInventoryPageQueryRequest1.setAreaCodes(new ArrayList<>(list1) );
        }
        moveInventoryPageQueryRequest1.setFromPassNum( moveInventoryPageQueryRequest.getLpassNum() );
        moveInventoryPageQueryRequest1.setToPassNum( moveInventoryPageQueryRequest.getHpassNum() );
        moveInventoryPageQueryRequest1.setFromLevelNum( moveInventoryPageQueryRequest.getLlevelNum() );
        moveInventoryPageQueryRequest1.setToLevelNum( moveInventoryPageQueryRequest.getHlevelNum() );
        moveInventoryPageQueryRequest1.setFromColumnNum( moveInventoryPageQueryRequest.getLcolumnNum() );
        moveInventoryPageQueryRequest1.setToColumnNum( moveInventoryPageQueryRequest.getHcolumnNum() );
        moveInventoryPageQueryRequest1.setOwnerCode( moveInventoryPageQueryRequest.getOwnerCode() );
        List<String> list2 = moveInventoryPageQueryRequest.getQualityLevelList();
        if ( list2 != null ) {
            moveInventoryPageQueryRequest1.setQualityLevelList(new ArrayList<>(list2) );
        }
        moveInventoryPageQueryRequest1.setCommoditySelection( moveInventoryPageQueryRequest.getCommoditySelection() );
        moveInventoryPageQueryRequest1.setCommodityQuery( moveInventoryPageQueryRequest.getCommodityQuery() );
        moveInventoryPageQueryRequest1.setThirdCategoryIds( moveInventoryPageQueryRequest.getThirdCategoryIds() );
        moveInventoryPageQueryRequest1.setReceivedStartTime(DateUtils.parseDateTimeIgnoreEmpty(moveInventoryPageQueryRequest.getReceivedStartTime(), DateUtils.FORMAT_TIME));
        moveInventoryPageQueryRequest1.setReceivedEndTime(DateUtils.parseDateTimeIgnoreEmpty(moveInventoryPageQueryRequest.getReceivedEndTime(), DateUtils.FORMAT_TIME));
        moveInventoryPageQueryRequest1.setExpiredStartTime(DateUtils.parseDateTimeIgnoreEmpty(moveInventoryPageQueryRequest.getExpiredStartTime(), DateUtils.FORMAT_TIME));
        moveInventoryPageQueryRequest1.setExpiredEndTime(DateUtils.parseDateTimeIgnoreEmpty(moveInventoryPageQueryRequest.getExpiredEndTime(), DateUtils.FORMAT_TIME));
        moveInventoryPageQueryRequest1.setBrandIdList(moveInventoryPageQueryRequest.getBrandIdList());
        moveInventoryPageQueryRequest1.setAbcClassify(moveInventoryPageQueryRequest.getAbcClassify());

        return moveInventoryPageQueryRequest1;
    }

    protected List<MoveInventoryItemRequest> convert(List<MoveInventoryDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<MoveInventoryItemRequest> list1 = new ArrayList<MoveInventoryItemRequest>( list.size() );
        for ( MoveInventoryDTO moveInventoryDTO : list ) {
            list1.add(convert( moveInventoryDTO ) );
        }

        return list1;
    }

    private MoveInventoryDTO convert(MoveInventorySelectedDTO moveInventorySelectedDTO) {
        if ( moveInventorySelectedDTO == null ) {
            return null;
        }
        MoveInventoryDTO moveInventoryDTO= new MoveInventoryDTO();
        moveInventoryDTO.setOperationQty( moveInventorySelectedDTO.getOperationQty() );
        moveInventoryDTO.setInventoryNos( moveInventorySelectedDTO.getInventoryNos() );
        return moveInventoryDTO;
    }

    /**
     * 将moveInventoryDTO对象转为MoveInventorySelectedDTO对象
     * @param moveInventoryDTO
     * @return MoveInventorySelectedDTO
     */
    private MoveInventorySelectedDTO convertMoveInventorySelectedDTO(MoveInventoryDTO moveInventoryDTO) {
        if ( moveInventoryDTO == null ) {
            return null;
        }
        MoveInventorySelectedDTO moveInventorySelectedDTO = new MoveInventorySelectedDTO();
        moveInventorySelectedDTO.setOperationQty( moveInventoryDTO.getOperationQty() );
        moveInventorySelectedDTO.setInventoryNos( moveInventoryDTO.getInventoryNos() );
        return moveInventorySelectedDTO;
    }

    private MoveInventoryItemRequest convert(MoveInventoryDTO moveInventoryDTO) {
        if ( moveInventoryDTO == null ) {
            return null;
        }
        MoveInventoryItemRequest moveInventoryItemRequest = new MoveInventoryItemRequest();
        moveInventoryItemRequest.setOperationQty( moveInventoryDTO.getOperationQty() );
        moveInventoryItemRequest.setInventoryNos( moveInventoryDTO.getInventoryNos() );
        return moveInventoryItemRequest;
    }


    private void check(MoveBillCreateRequest request) {

        if (request.getWarehouseCode() == null || request.getWarehouseCode().isEmpty()) {
            throw new WmsOperationException("仓库必填");
        }
        if (request.getSelectAll() == 1) {
            MoveInventoryPageQueryRequest moveInventoryPageQueryRequest = request.getMoveInventoryPageQueryRequest();
            moveInventoryPageQueryRequest.verify();
            if ("".equals(moveInventoryPageQueryRequest.getBrandName())) {
                moveInventoryPageQueryRequest.setBrandName(null);
            }
            if ("".equals(moveInventoryPageQueryRequest.getCommodityQuery())) {
                moveInventoryPageQueryRequest.setCommodityQuery(null);
            }
            if ("".equals(moveInventoryPageQueryRequest.getOwnerCode())) {
                moveInventoryPageQueryRequest.setOwnerCode(null);
            }
            if ("".equals(moveInventoryPageQueryRequest.getThirdCategoryIds())) {
                moveInventoryPageQueryRequest.setThirdCategoryIds(null);
            }
            String brandName = moveInventoryPageQueryRequest.getBrandName();
            if (brandName != null ) {
                List<SpuBrandRspDo> list = scpBrandRepository.selectByBrandName(brandName);
                if (list == null) {
                    throw new WmsOperationException("品牌不存在");
                }
                moveInventoryPageQueryRequest.setBrandIdList(list.stream().map(SpuBrandRspDo::getBrandId).collect(Collectors.toList()));
            }

        }
    }


    @ApiModelProperty(value = "强制完成移库下架")
    @RequestMapping(value = "/v1/force-finish",method = RequestMethod.POST)
    public Result<Boolean> forceFinishMoveBill(@Valid @RequestBody MoveBillForceFinishRequest request) {
        OperationUserContext context = OperationUserContextHolder.get();
        if (context == null) {
            throw new WmsOperationException("请重新登录");
        }
        String moveOrderNo = request.getMoveBillNo();
        if (moveOrderNo == null) {
            throw new WmsOperationException("参数错误");
        }

        OffTaskDTO offTaskDTO = getOffTaskDTO(context.getTenantCode(), moveOrderNo);
        if (offTaskDTO.getTaskType().equals("G2D")) {
            throw new WmsOperationException("良转次的移库任务不可强制完成哦");
        }
        if (offTaskDTO.getStatus() == 0) {
            throw new WmsException("下架任务还未执行，建议删除或取消，而不是强制完成");
        }


        int pageSize = 500;
        OffTaskDetailQuery offTaskDetailQuery = new OffTaskDetailQuery();
        offTaskDetailQuery.setTenantCode(offTaskDTO.getTenantCode());
        offTaskDetailQuery.setTaskNo(offTaskDTO.getTaskNo());
        offTaskDetailQuery.setWarehouseCode(offTaskDTO.getWarehouseCode());
        offTaskDetailQuery.setStatusList(Arrays.asList(-10, 0, 10));
        offTaskDetailQuery.setPageNum(1);
        offTaskDetailQuery.setPageSize(pageSize);

        Result<PagingObject<OffTaskDetailDTO>>  queryDetailResult = offTaskDetailQueryApi.query(offTaskDetailQuery);

        if (!Result.isSuccess(queryDetailResult)) {
            log.warn("offTaskDetailQueryApi.query fail queryTaskResult = {}", JSON.toJSONString(offTaskDetailQuery));
            throw new WmsOperationException(queryDetailResult.getMsg());
        }

        PagingObject<OffTaskDetailDTO> queryDetailResultPage = queryDetailResult.getData();
        if (queryDetailResultPage == null) {
            log.warn("offTaskQueryApi.query null queryTaskResult = {}", JSON.toJSONString(queryDetailResult));
            throw new WmsException("未知异常");
        }
        List<OffTaskDetailDTO> dtoList = queryDetailResultPage.getContents();

        OffFinishRequest offFinishRequest = new OffFinishRequest();
        offFinishRequest.setTenantCode(offTaskDTO.getTenantCode());
        offFinishRequest.setWarehouseCode(offTaskDTO.getWarehouseCode());
        offFinishRequest.setOperationUserId(context.getUserId());
        offFinishRequest.setOperationRealName(context.getRealName());
        offFinishRequest.setOperationUserName(context.getUserName());
        for (OffTaskDetailDTO offTaskDetailDTO : dtoList) {
            offFinishRequest.setTaskDetailNo(offTaskDetailDTO.getDetailNo());
            Result<OffExecuteResponse> finishResult = offExecuteApi.finish(offFinishRequest);
            if (!Result.isSuccess(finishResult)) {
                log.warn("offExecuteApi.finish fail finishResult = {}", JSON.toJSONString(finishResult));
                throw new WmsOperationException(finishResult.getMsg());
            }
        }
        if (queryDetailResultPage.getTotal() > pageSize) {
            throw new WmsOperationException("已经为你强制完成了部分任务，由于该移库单下任务较多，请你再多重试几次直到全部任务完成");
        }

        return Result.ofSuccess(true);
    }

    @ApiModelProperty(value = "删除移库单")
    @RequestMapping(value = "/v1/delete",method = RequestMethod.POST)
    public Result<Boolean> deleteMoveBill(@Valid @RequestBody MoveBillDeleteRequest request) {
        OperationUserContext context = OperationUserContextHolder.get();
        if (context == null) {
            throw new WmsOperationException("请重新登录");
        }
        OffTaskDTO offTaskDTO = getOffTaskDTO(context.getTenantCode(), request.getMoveBillNo());
        if (offTaskDTO.getTaskType().equals("G2D")) {
            throw new WmsOperationException("良转次的移库任务不可强制完成哦");
        }
        MoveOderCancelRequest moveOderCancelRequest = new MoveOderCancelRequest();
        moveOderCancelRequest.setTenantCode(context.getTenantCode());
        moveOderCancelRequest.setMoveOrderNo(request.getMoveBillNo());

        Result<Void> result = moveOrderCancelApi.cancel(moveOderCancelRequest);
        if (!Result.isSuccess(result)) {
            log.warn("moveOrderCancelApi.cancel_fail = {}", JSON.toJSON(result));
            throw new WmsOperationException(result.getMsg());
        }
        return Result.ofSuccess(true);
    }

    private OffTaskDTO getOffTaskDTO(String tenantCode, String moveOrderNo) {
        OffTaskQuery offTaskQuery = new OffTaskQuery();
        offTaskQuery.setTenantCode(tenantCode);
        // offTaskQuery.setTaskType("MV");
        offTaskQuery.setTaskNo(moveOrderNo);
        offTaskQuery.setPageSize(1);
        offTaskQuery.setPageNum(1);
        Result<PagingObject<OffTaskDTO>> queryTaskResult = offTaskQueryApi.query(offTaskQuery);
        if (!Result.isSuccess(queryTaskResult)) {
            log.warn("offTaskQueryApi.query fail queryTaskResult = {}", JSON.toJSONString(queryTaskResult));
            throw new WmsOperationException(queryTaskResult.getMsg());
        }
        PagingObject<OffTaskDTO> pagingObject = queryTaskResult.getData();
        if (pagingObject == null) {
            log.warn("offTaskQueryApi.query null queryTaskResult = {}", JSON.toJSONString(queryTaskResult));
            throw new WmsException("未知异常");
        }
        List<OffTaskDTO> list = pagingObject.getContents();
        if (list.isEmpty()) {
            log.warn("offTaskQueryApi.query null queryTaskResult = {}", JSON.toJSONString(queryTaskResult));
            throw new WmsException("任务不存在，请稍后重试");
        }
        return list.get(0);
    }

    @ApiModelProperty(value = "导出移库单")
    @RequestMapping(value = "/v1/export",method = RequestMethod.POST)
    public Result<ReportCenterResponse> exportMoveBill(@Valid @RequestBody MoveBillExportRequest request) {
        Integer moveType = request.getMoveType();
        if (moveType != null && moveType == 3) {
            if(request.getPickCreateStartTime() == null || request.getPickCreateEndTime() == null){
                throw new WmsOperationException("时间范围不能为空");
            }
            long diffDays = com.poizon.scm.wms.util.util.DateUtils.betweenDaysByJdk8(DateUtils.getDateFromString(request.getPickCreateStartTime()),
                    DateUtils.getDateFromString(request.getPickCreateEndTime()));
            if(diffDays > maxExportDays){
                String errMsg = "时间范围不能超过"+maxExportDays+"天";
                throw new WmsOperationException(errMsg);
            }
            String s = JSONUtil.toJsonStr(request);
            ReportCreateRequest reportCreateRequest = reportDownloadService.buildReportDownloadRequest("移库单明细列表", "wms", s);
            ReportCenterResponse response = excelExportService.triggerAsyncDownload((() -> queryQualityExportData(request)),
                    "移库明细列表" + DateUtils.dateToString(new Date(), DateUtils.FROMAT_DATE),
                    "移库明细列表", REPORT_HEADER, reportCreateRequest);
            return Result.ofSuccess(response);

        }
        if (moveType != null && moveType == 4) {
            if(request.getPickCreateStartTime() == null || request.getPickCreateEndTime() == null){
                throw new WmsOperationException("时间范围不能为空");
            }
            long diffDays = com.poizon.scm.wms.util.util.DateUtils.betweenDaysByJdk8(DateUtils.getDateFromString(request.getPickCreateStartTime()),
                    DateUtils.getDateFromString(request.getPickCreateEndTime()));
            if(diffDays > maxExportDays){
                String errMsg = "时间范围不能超过"+maxExportDays+"天";
                throw new WmsOperationException(errMsg);
            }
            String s = JSONUtil.toJsonStr(request);
            ReportCreateRequest reportCreateRequest = reportDownloadService.buildReportDownloadRequest("移库单明细列表", "wms", s);
            ReportCenterResponse response = excelExportService.triggerAsyncDownload((() -> queryIdentifyExportData(request)),
                    "移库明细列表" + DateUtils.dateToString(new Date(), DateUtils.FROMAT_DATE),
                    "移库明细列表", REPORT_HEADER, reportCreateRequest);
            return Result.ofSuccess(response);

        }
        if (moveType != null && moveType == 5) {
            if (request.getPickCreateStartTime() == null || request.getPickCreateEndTime() == null) {
                throw new WmsOperationException("时间范围不能为空");
            }
            long diffDays = com.poizon.scm.wms.util.util.DateUtils.betweenDaysByJdk8(DateUtils.getDateFromString(request.getPickCreateStartTime()),
                    DateUtils.getDateFromString(request.getPickCreateEndTime()));
            if (diffDays > maxExportDays) {
                String errMsg = "时间范围不能超过" + maxExportDays + "天";
                throw new WmsOperationException(errMsg);
            }
            String s = JSONUtil.toJsonStr(request);
            ReportCreateRequest reportCreateRequest = reportDownloadService.buildReportDownloadRequest("移库单明细列表", "wms", s);
            ReportCenterResponse response = excelExportService.triggerAsyncDownload((() -> queryJmvExportData(request)),
                    "移库明细列表" + DateUtils.dateToString(new Date(), DateUtils.FROMAT_DATE),
                    "移库明细列表", REPORT_HEADER, reportCreateRequest);
            return Result.ofSuccess(response);
        }
        return commandBus.handle(request);
    }

    /**
     *  新导出 待coding
     */
    private List<Object[]> queryJmvExportData(MoveBillExportRequest request) {
        MoveOderAdminQueryExportRequest exportRequest = new MoveOderAdminQueryExportRequest();
        int pageNum = 1;
        exportRequest.setStartNum(pageNum);
        exportRequest.setLimitSize(pageSize);
        exportRequest.setCount(false);
        OperationUserContext userContext = OperationUserContextHolder.get();
        String tenantCode = userContext.getTenantCode();
        String warehouseCode = userContext.getWarehouseCode();
        exportRequest.setTenantCode(tenantCode);
        exportRequest.setWarehouseCode(warehouseCode);
        exportRequest.setMoveBillNo(request.getMoveBillNo());
        exportRequest.setMoveType(request.getMoveType().toString());
        exportRequest.setReferenceNo(request.getReferenceNo());
        exportRequest.setStatusList(request.getStatusList());
        exportRequest.setUniqueCode(request.getUniqueCode());
        exportRequest.setBarCode(request.getBarCode());
        exportRequest.setPickCreateStartTime(request.getPickCreateStartTime());
        exportRequest.setPickCreateEndTime(request.getPickCreateEndTime());
        exportRequest.setPickCompleteStartTime(request.getPickCompleteStartTime());
        exportRequest.setPickCompleteEndTime(request.getPickCompleteEndTime());

        List<MoveOrderAdminExportDTO> exportList = new ArrayList<>();
        List<MoveOrderAdminExportDTO> queryList;
        do {
            queryList = moveOrderAdminQueryApi.queryForExportData(exportRequest).getData();
            exportRequest.setStartNum(++pageNum);
            exportList.addAll(queryList);
        } while (CollUtil.isNotEmpty(queryList) && queryList.size() == pageSize);

        return exportList.stream().map(exportDTO -> {
            List<Object> row = new ArrayList<>();
            row.add(exportDTO.getWarehouseName());
            row.add(exportDTO.getMoveOrderNo());
            row.add(exportDTO.getReferenceNo());
            row.add(exportDTO.getStatusName());
            row.add(exportDTO.getCreatedUserName());
            row.add(exportDTO.getCreatedTime());
            row.add(exportDTO.getPickUserName());
            row.add(exportDTO.getPickReceiveTime());
            row.add(exportDTO.getPickCompleteTime());
            row.add(exportDTO.getPlanMoveOutQty());
            row.add(exportDTO.getLockedQty());
            row.add(exportDTO.getActualMoveOutQty());
            row.add(exportDTO.getUniqueCode());
            row.add(exportDTO.getStandardCode());
            row.add(exportDTO.getExtraCode());
            row.add(exportDTO.getOwnerCode());
            row.add(exportDTO.getOwnerName());
            row.add(exportDTO.getContainerCode());
            row.add(exportDTO.getOriginLocationCode());
            row.add(exportDTO.getPlanMoveOutQty());
            row.add(exportDTO.getLockedQty());
            row.add(exportDTO.getActualMoveOutQty());
            row.add(exportDTO.getUpperTaskNo());
            row.add(exportDTO.getUpperUserName());
            row.add(exportDTO.getActualUpperQty());
            row.add(exportDTO.getLocationCode());
            row.add(exportDTO.getUpperCompleteTime());
            row.add(exportDTO.getUpperStatusName());
            return row.toArray();
        }).collect(Collectors.toList());
    }

    private List<Object[]> queryIdentifyExportData(MoveBillExportRequest request){
        InnerIdentifyMovePageQueryRequest qualityControlOrderApiQuery = nearExpireOrderConverter.convertIdentifyExportRequest(request);
        qualityControlOrderApiQuery.setTenantCode(OperationUserContextHolder.getTenantCode());
        String barCode = request.getBarCode();
        if (!StringUtils.isBlank(barCode)) {
            ItemCode itemCode = skuRepository.parse(barCode);
            if (Objects.equals(itemCode.getType(), ItemCodeType.SKU_ID)) {
                qualityControlOrderApiQuery.setSkuIdList(itemCode.getSkuIds());
            } else {
                qualityControlOrderApiQuery.setUniqueCode(barCode);
            }
        }
        if (StringUtils.isNotBlank(request.getUniqueCode())) {
            qualityControlOrderApiQuery.setUniqueCode(request.getUniqueCode());
        }
        if (StringUtils.isBlank(request.getUniqueCode())) {
            qualityControlOrderApiQuery.setUniqueCode(null);
        }
        if (StringUtils.isBlank(request.getMoveBillNo())) {
            qualityControlOrderApiQuery.setMoveBillNo(null);
        }
        qualityControlOrderApiQuery.setOffCreateStartTime(DateUtils.getDateFromString(request.getPickCreateStartTime()));
        qualityControlOrderApiQuery.setOffCreateEndTime(DateUtils.getDateFromString(request.getPickCreateEndTime()));
        qualityControlOrderApiQuery.setOffCompleteStartTime(DateUtils.getDateFromString(request.getPickCompleteStartTime()));
        qualityControlOrderApiQuery.setOffCompleteEndTime(DateUtils.getDateFromString(request.getPickCompleteEndTime()));

        int pageNum = 1;
        List<InnerIdentifyMoveItemDTO> qualityControlOrderExportApiDTOList = new ArrayList<>();
        qualityControlOrderApiQuery.setPageSize(pageSize);
        while (true){
            qualityControlOrderApiQuery.setPageNum(pageNum);
            Result<List<InnerIdentifyMoveItemDTO>> result =  innerIdentifyMoveQueryApi.exportItem(qualityControlOrderApiQuery);
            if(!Result.isSuccess(result)){
                log.warn("queryQualityExportData result error,msg:{},param:{}",result.getMsg(),
                        JSON.toJSONString(qualityControlOrderApiQuery));
                break;
            }
            if(CollectionUtils.isEmpty(result.getData())){
                break;
            }
            qualityControlOrderExportApiDTOList.addAll(result.getData());
            pageNum++;
        }
        return buildIdentifyReports(qualityControlOrderExportApiDTOList);
    }


    private List<Object[]> buildIdentifyReports(List<InnerIdentifyMoveItemDTO> apiDTOList) {
        List<Object[]> resultList = new ArrayList<>();
        for (InnerIdentifyMoveItemDTO moveBillExportResponse : apiDTOList) {
            List<Object> row = new ArrayList<>();
            row.add(moveBillExportResponse.getWarehouseName());
            row.add(moveBillExportResponse.getMoveBillNo());
            row.add(moveBillExportResponse.getReferenceNo());
            row.add(moveBillExportResponse.getStatusName());
            row.add(moveBillExportResponse.getCreatedUserName());
            row.add(moveBillExportResponse.getCreatedTime());
            row.add(moveBillExportResponse.getPickUserName());
            row.add(moveBillExportResponse.getPickReceiveTime());
            row.add(moveBillExportResponse.getPickCompleteTime());
            row.add(moveBillExportResponse.getPlanMoveOutQty());
            row.add(moveBillExportResponse.getLockedQty());
            row.add(moveBillExportResponse.getActualMoveOutQty());
            row.add(moveBillExportResponse.getUniqueCode());
            row.add(moveBillExportResponse.getStandardCode());
            row.add(moveBillExportResponse.getExtraCode());
            row.add(moveBillExportResponse.getOwnerCode());
            row.add(moveBillExportResponse.getOwnerName());
            row.add(moveBillExportResponse.getContainerCode());
            row.add(moveBillExportResponse.getOriginLocationCode());
            row.add(moveBillExportResponse.getPlanMoveOutQty());
            row.add(moveBillExportResponse.getLockedQty());
            row.add(moveBillExportResponse.getActualMoveOutQty());
            row.add(moveBillExportResponse.getUpperTaskNo());
            row.add(moveBillExportResponse.getUpperUserName());
            row.add(moveBillExportResponse.getActualUpperQty());
            row.add(moveBillExportResponse.getLocationCode());
            row.add(moveBillExportResponse.getUpperCompleteTime());
            row.add(moveBillExportResponse.getUpperStatusName());
            resultList.add(row.toArray());
        }
        return resultList;
    }

    private List<Object[]> queryQualityExportData(MoveBillExportRequest request){
        QualityControlOrderExportApiRequest qualityControlOrderApiQuery = nearExpireOrderConverter.convertQualityExportRequest(request);
        qualityControlOrderApiQuery.setTenantCode(OperationUserContextHolder.getTenantCode());
        String barCode = request.getBarCode();
        if (!StringUtils.isBlank(barCode)) {
            ItemCode itemCode = skuRepository.parse(barCode);
            if (Objects.equals(itemCode.getType(), ItemCodeType.SKU_ID)) {
                qualityControlOrderApiQuery.setSkuIdList(itemCode.getSkuIds());
            } else {
                qualityControlOrderApiQuery.setUniqueCode(barCode);
            }
        }
        if (StringUtils.isNotBlank(request.getUniqueCode())) {
            qualityControlOrderApiQuery.setUniqueCode(request.getUniqueCode());
        }
        if (StringUtils.isBlank(request.getUniqueCode())) {
            qualityControlOrderApiQuery.setUniqueCode(null);
        }
        if (StringUtils.isBlank(request.getMoveBillNo())) {
            qualityControlOrderApiQuery.setMoveBillNo(null);
        }
        qualityControlOrderApiQuery.setOffCreateStartTime(DateUtils.getDateFromString(request.getPickCreateStartTime()));
        qualityControlOrderApiQuery.setOffCreateEndTime(DateUtils.getDateFromString(request.getPickCreateEndTime()));
        qualityControlOrderApiQuery.setOffCompleteStartTime(DateUtils.getDateFromString(request.getPickCompleteStartTime()));
        qualityControlOrderApiQuery.setOffCompleteEndTime(DateUtils.getDateFromString(request.getPickCompleteEndTime()));

        int pageNum = 0;
        List<QualityControlOrderExportApiDTO> qualityControlOrderExportApiDTOList = new ArrayList<>();
        qualityControlOrderApiQuery.setPageSize(pageSize);
        while (true){
            qualityControlOrderApiQuery.setPageNum(pageNum);
            Result<List<QualityControlOrderExportApiDTO>> result = qualityControlOrderDetailQueryApi.export(qualityControlOrderApiQuery);
            if(!Result.isSuccess(result)){
                log.warn("queryQualityExportData result error,msg:{},param:{}",result.getMsg(),
                        JSON.toJSONString(qualityControlOrderApiQuery));
                break;
            }
            if(CollectionUtils.isEmpty(result.getData())){
                break;
            }
            qualityControlOrderExportApiDTOList.addAll(result.getData());
            pageNum++;
        }
        return buildReports(qualityControlOrderExportApiDTOList);
    }

    private List<Object[]> buildReports(List<QualityControlOrderExportApiDTO> apiDTOList) {
        List<Object[]> resultList = new ArrayList<>();
        for (QualityControlOrderExportApiDTO moveBillExportResponse : apiDTOList) {
            List<Object> row = new ArrayList<>();
            row.add(moveBillExportResponse.getWarehouseName());
            row.add(moveBillExportResponse.getMoveBillNo());
            row.add(moveBillExportResponse.getReferenceNo());
            row.add(moveBillExportResponse.getMoveStatusName());
            row.add(moveBillExportResponse.getCreatedUserName());
            row.add(moveBillExportResponse.getMoveCreatedTime());
            row.add(moveBillExportResponse.getPickUserName());
            row.add(moveBillExportResponse.getPickReceiveTime());
            row.add(moveBillExportResponse.getPickCompleteTime());
            row.add(moveBillExportResponse.getTotalPlanMoveOutQty());
            row.add(moveBillExportResponse.getTotalLockedQty());
            row.add(moveBillExportResponse.getTotalActualMoveOutQty());
            row.add(moveBillExportResponse.getUniqueCode());
            row.add(moveBillExportResponse.getStandardCode());
            row.add(moveBillExportResponse.getExtraCode());
            row.add(moveBillExportResponse.getOwnerCode());
            row.add(moveBillExportResponse.getOwnerName());
            row.add(moveBillExportResponse.getContainerCode());
            row.add(moveBillExportResponse.getOriginLocationCode());
            row.add(moveBillExportResponse.getPlanMoveOutQty());
            row.add(moveBillExportResponse.getLockedQty());
            row.add(moveBillExportResponse.getActualMoveOutQty());
            row.add(moveBillExportResponse.getUpperTaskNo());
            row.add(moveBillExportResponse.getUpperUserName());
            row.add(moveBillExportResponse.getActualUpperQty());
            row.add(moveBillExportResponse.getLocationCode());
            row.add(moveBillExportResponse.getUpperCompleteTime());
            row.add(moveBillExportResponse.getUpperStatusName());
            resultList.add(row.toArray());
        }
        return resultList;
    }
}
