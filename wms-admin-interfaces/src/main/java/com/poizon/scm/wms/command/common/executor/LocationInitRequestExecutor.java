package com.poizon.scm.wms.command.common.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.command.common.request.LocationInitRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.base.BasLocationService;
import com.poizon.scm.wms.service.base.BasWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LocationInitRequestExecutor extends AbstractCommandExecutor<LocationInitRequest, String> {

    @Autowired
    private BasLocationService basLocationService;

    @Autowired
    private BasWarehouseService basWarehouseService;

    @Override
    protected String doExecute(LocationInitRequest locationInitRequest) {
        log.info("接受刷仓库的新增虚拟库位请求, 入参 -> [{}]", JSON.toJSONString(locationInitRequest));
        //查询出该租户下所有仓库
        if(StringUtils.isBlank(locationInitRequest.getTenantCode())){
            locationInitRequest.setTenantCode(OperationUserContextHolder.DEFAULT_TENANT_CODE);
        }
        OperationUserContextHolder.get().setTenantCode(locationInitRequest.getTenantCode());
        List<String> warehouseList = new ArrayList<>();
        if(StringUtils.isBlank(locationInitRequest.getWarehouseCode())){
            warehouseList = obtainWarehouseList();
        }else{
            warehouseList.add(locationInitRequest.getWarehouseCode());
        }
        warehouseList.stream().forEach(x -> {
            try {
                locationInitRequest.setWarehouseCode(x);
                basLocationService.initVirtualLocationCode(locationInitRequest);
            }catch (Exception e){
                log.warn("{}仓库初始化虚拟库位失败",x,e);
            }
        });
        log.info("结束刷仓库的新增虚拟库位请求");
        return Result.DEFAULT_SUCCESS_MESSAGE;
    }

    private List<String> obtainWarehouseList(){
        Map<String, String> warehouseMap = basWarehouseService.warehouseNameMap();
        if (warehouseMap == null || warehouseMap.size() == 0) {
            log.warn("查询出来的仓库集合为空");
            throw new WmsOperationException("查询出来的仓库集合为空");
        }
        return warehouseMap.entrySet().stream().map(x->x.getKey()).collect(Collectors.toList());
    }
}
