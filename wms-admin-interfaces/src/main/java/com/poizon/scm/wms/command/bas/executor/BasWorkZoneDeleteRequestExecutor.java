package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.command.bas.request.BasWorkZoneDeleteRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.bas.BasWorkZoneCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 作业库区删除执行器
 * @Author: LMF
 * @Date: 2021/6/1
 */
@Component
public class BasWorkZoneDeleteRequestExecutor  extends AbstractCommandExecutor<BasWorkZoneDeleteRequest, Void> {

    @Resource
    BasWorkZoneCommandService basWorkZoneCommandService;

    @Override
    protected Void doExecute(BasWorkZoneDeleteRequest request) {
        basWorkZoneCommandService.delete(request.getId());
        return null;
    }
}
