package com.poizon.scm.wms.command.bas.station.station.request;

import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date  30/04/2024 周二
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationBatchAddRequest extends Request implements Serializable {
    private static final long serialVersionUID = -6239996976273504845L;

    @Valid
    @NotEmpty(message = "新增列表不能为空", groups = ValidateGroups.Add.class)
    private List<BasStationRequest> requestList;

}
