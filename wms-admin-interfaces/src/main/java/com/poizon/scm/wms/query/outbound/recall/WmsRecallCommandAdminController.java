package com.poizon.scm.wms.query.outbound.recall;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.adapter.third.outbound.recall.WmsRecallRpcRepository;
import com.poizon.scm.wms.adapter.third.outbound.recall.params.RecallReInterceptParams;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.query.outbound.recall.request.RecallReInterceptRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author： LMF
 * @date： 2023/12/1 10:34 AM
 * @description： 后台-召回记录列表
 * @modifiedBy：
 * @version: 1.0
 */
@RestController
@Api(tags = {"后台-召回记录列表"})
@RequestMapping("/admin/wms/recall")
public class WmsRecallCommandAdminController {

    @Resource
    private WmsRecallRpcRepository recallRpcRepository;

    @PostMapping(value = "/v1/reIntercept")
    @ApiOperation(value = "重新拦截")
    public Result<Void> reIntercept(@RequestBody @Validated RecallReInterceptRequest request) {
        RecallReInterceptParams params = new RecallReInterceptParams();
        params.setRecallBillCode(request.getRecallBillCode());
        params.setTenantId(OperationUserContextHolder.getTenantCode());
        recallRpcRepository.reIntercept(params);
        return Result.ofSuccess(null);
    }

}
