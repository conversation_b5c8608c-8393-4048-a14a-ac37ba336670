package com.poizon.scm.wms.command.container;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.poizon.scm.wms.adapter.container.model.ContainerInstanceDo;
import com.poizon.scm.wms.common.WmsResult;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.domain.inner.container.instance.processor.ContainerAutoEmptyProcessor;
import com.poizon.scm.wms.domain.inner.container.swap.SwapContainerService;
import com.shizhuang.duapp.tenant.TenantContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @author: tywei
 * @create: 2021-07-10 9:47 下午
 **/
@RestController
@RequestMapping("/admin/container/")
@Api(tags = "容器异常处理类")
@Slf4j
public class ContainerExceptionController {

    @Value("${container.empty.limit:200}")
    private Integer limit;
    @Autowired
    private ContainerAutoEmptyProcessor containerAutoEmptyProcessor;
    @Autowired
    private SwapContainerService swapContainerService;

    @ApiOperation("容器异常job")
    @PostMapping(value = "/emptyJob", produces = "application/json")
    public WmsResult<String> emptyJob() {
        execute();
        return WmsResult.success("调度器启动成功！");
    }

    @ApiOperation("复制容器明细")
    @PostMapping(value = "/copyContainerDetail", produces = "application/json")
    public WmsResult<String> copyContainerDetail(
            @RequestParam(value = "instanceNo") String instanceNo,
            @RequestParam(value = "tenantCode", defaultValue = "1", required = false) String tenantCode) {
        if (StringUtils.isBlank(instanceNo)) {
            return WmsResult.error("实例号不可为空", -1L);
        }
        if (Objects.isNull(tenantCode)) {
            tenantCode = "1";
        }
        TenantContext.setContextId(tenantCode);
        try {
            swapContainerService.copyResultToContainerDetail(instanceNo);
        } finally {
            TenantContext.clear();
        }
        return WmsResult.success("复制容器明细成功");
    }

    private void execute() {
        Long firstId = 0L;
        OperationUserContextHolder.buildNoWarehouseDefaultContext();
        long beginTime = System.currentTimeMillis();
        log.info("开始执行容器自动释放job.");
        for (; ; ) {
            List<ContainerInstanceDo> instanceDos = containerAutoEmptyProcessor.queryEmptyContainerCodes(firstId, limit);
            if (CollectionUtils.isEmpty(instanceDos)) {
                break;
            }
            firstId = Objects.requireNonNull(Iterables.getLast(instanceDos)).getId();
            List<String> containerList = Lists.newArrayList();
            instanceDos.forEach(instanceDo -> {
                //实例下有未取消的订单，不做处理
                boolean isUnCancelOrder = containerAutoEmptyProcessor.isUnCancelOrder(instanceDo);
                if (isUnCancelOrder) {
                    return;
                }
                boolean result = containerAutoEmptyProcessor.executeEmptyContainer(instanceDo);
                if (result) {
                    containerList.add("[" + instanceDo.getContainerCode() + "," + instanceDo.getInstanceNo() + "]");
                }
            });
            if (CollectionUtils.isNotEmpty(containerList)) {
                log.info("完成释放的容器号-实例号列表:{}", containerList);
            }
            if (instanceDos.size() < limit) {
                break;
            }
        }
        log.info("容器自动释放job结束. cost time:{}ms", (System.currentTimeMillis() - beginTime));
    }
}
