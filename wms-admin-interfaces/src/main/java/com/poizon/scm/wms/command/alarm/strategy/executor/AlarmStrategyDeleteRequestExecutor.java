package com.poizon.scm.wms.command.alarm.strategy.executor;

import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyDeleteRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyCommandService;
import com.poizon.scm.wms.util.enums.IsActiveEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Component
public class AlarmStrategyDeleteRequestExecutor extends AbstractCommandExecutor<AlarmStrategyDeleteRequest,Void> {

    @Resource
    private AlarmStrategyCommandService alarmStrategyCommandService;

    @Resource
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyDeleteRequest request) {
        AlarmStrategyDo strategyDo = alarmStrategyQueryRepository.queryByStrategyNo(request.getStrategyNo());

        if (strategyDo == null) {
            throw new WmsOperationException("预警策略不存在");
        }
        if (strategyDo.getStatus().equals(IsActiveEnum.ENABLE.getStatus())) {
            throw new WmsOperationException("已停用的预警策略才可以删除");
        }
        alarmStrategyCommandService.deleteStrategy(buildDeleteStrategyParam(strategyDo));
        return null;
    }

    private AlarmStrategyDo buildDeleteStrategyParam(AlarmStrategyDo strategyDo) {
        AlarmStrategyDo alarmStrategyDo = new AlarmStrategyDo();
        alarmStrategyDo.setStrategyNo(strategyDo.getStrategyNo());
        alarmStrategyDo.setDeleted(1);
        alarmStrategyDo.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        return alarmStrategyDo;
    }
}
