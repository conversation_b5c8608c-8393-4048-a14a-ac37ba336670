package com.poizon.scm.wms.command.alarm.strategy.executor;

import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyEditRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyCommandService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
/**
 * <AUTHOR>
 * @date 2021/7/28
 */
@Component
public class AlarmStrategyEditRequestExecutor extends AbstractCommandExecutor<AlarmStrategyEditRequest, Void> {

    @Resource
    private AlarmStrategyCommandService alarmStrategyCommandService;

    @Resource
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyEditRequest request) {
        AlarmStrategyDo strategyDo = alarmStrategyQueryRepository.queryByStrategyNo(request.getStrategyCode());
        if (strategyDo == null) {
            throw new WmsOperationException("预警策略不存在");
        }
        //校验策略名称是否存在
        List<AlarmStrategyDo> list = alarmStrategyQueryRepository.queryStrategyByNameAndWarehouseCode(request.getStrategyName(),strategyDo.getWarehouseCode());
        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> !item.getStrategyNo().equals(strategyDo.getStrategyNo()))) {
            throw new WmsOperationException("预警策略名称重复，请修改");
        }
        alarmStrategyCommandService.editStrategy(buildStrategyUpdateParam(request));
        return null;
    }

    private AlarmStrategyDo buildStrategyUpdateParam(AlarmStrategyEditRequest request) {
        AlarmStrategyDo strategyDo = new AlarmStrategyDo();
        strategyDo.setStrategyNo(request.getStrategyCode());
        strategyDo.setStrategyName(request.getStrategyName().trim());
        strategyDo.setStrategyDesc(request.getStrategyDesc().trim());
        strategyDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        return strategyDo;
    }
}