package com.poizon.scm.wms.command.bas.station.area.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationAreaApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaRequest;
import com.poizon.scm.wms.bas.api.command.station.response.BaseStationAreaResponse;
import com.poizon.scm.wms.bas.api.common.response.PageResult;
import com.poizon.scm.wms.command.bas.station.area.request.BasStationAreaSelectRequest;
import com.poizon.scm.wms.command.bas.station.area.response.BasStationAreaSelectResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  23/04/2024 周二
 */
@Slf4j
@Component
public class BasStationAreaSelectRequestExecutor extends AbstractCommandExecutor<BasStationAreaSelectRequest, com.poizon.scm.wms.common.pagesupport.response.PageResult<BasStationAreaSelectResponse>> {

    @DubboReference(interfaceClass = BasStationAreaApi.class, check = false)
    private BasStationAreaApi basStationAreaApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected com.poizon.scm.wms.common.pagesupport.response.PageResult<BasStationAreaSelectResponse> doExecute(BasStationAreaSelectRequest request) {
        BasStationAreaRequest basStationAreaRequest = BeanCopyUtils.copyProperties(request, BasStationAreaRequest.class);
        basStationAreaRequest.setTenantId(OperationUserContextHolder.getTenantCode());

        Result<PageResult<BaseStationAreaResponse>> selectResult = LogUtils.logRemoteInvoke(log, basStationAreaRequest, basStationAreaApi::select);

        return Optional.ofNullable(selectResult).map(Result::getData).map(pr -> {
            com.poizon.scm.wms.common.pagesupport.response.PageResult<BasStationAreaSelectResponse> pageResult = new com.poizon.scm.wms.common.pagesupport.response.PageResult<>();
            pageResult.setPageSize(pr.getPageSize());
            pageResult.setPageNum(pr.getPageNum());
            pageResult.setPages(pr.getPages());
            pageResult.setTotal(pr.getTotal());
            List<BasStationAreaSelectResponse> dataList = pr.getDataList()
                    .stream()
                    .map(baseStationAreaResponse -> BeanCopyUtils.copyProperties(baseStationAreaResponse, BasStationAreaSelectResponse.class))
                    .collect(Collectors.toList());
            pageResult.setDataList(dataList);
            return pageResult;
        }).orElse(new com.poizon.scm.wms.common.pagesupport.response.PageResult<>());
    }
}
