package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ExceptionConfigModifyParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionConfigAdminRepository;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigStatusChangeRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author： LMF
 * @date： 2022/8/9 10:04 PM
 * @description： 异常提报原因配置变更状态执行器
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class ExceptionConfigStatusChangeRequestExecutor extends AbstractCommandExecutor<ExceptionConfigStatusChangeRequest, Void> {

    @Resource
    private WmsExceptionConfigAdminRepository wmsExceptionConfigAdminRepository;

    @Override
    protected Void doExecute(ExceptionConfigStatusChangeRequest request) {
        ExceptionConfigModifyParam modifyParam = new ExceptionConfigModifyParam();
        modifyParam.setId(request.getId());
        modifyParam.setStatus(request.getStatus());
        modifyParam.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        modifyParam.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        modifyParam.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        modifyParam.setTenantId(OperationUserContextHolder.getScmTenantCode());
        wmsExceptionConfigAdminRepository.modifyExceptionConfig(modifyParam);
        return null;
    }
}
