package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配置元数据修改")
public class WmsBasConfigMetaModifyRequest extends Request {

    /**
     * 配置元数据ID
     */
    @ApiModelProperty(value = "配置元数据ID", required = true)
    private Long id;

    /**
     * 配置项名称
     */
    @ApiModelProperty("配置元数据名称")
    private String name;

    /**
     * 配置项值类型：NUMBER、STRING、LIST、JSON
     */
    @ApiModelProperty("配置元数据的值类型")
    private String valueType;

    /**
     * 配置项的值
     */
    @ApiModelProperty("配置元数据的值")
    private String value;

    /**
     * 配置级别
     */
    @ApiModelProperty("配置级别")
    private Integer level;

    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer priority;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

}
