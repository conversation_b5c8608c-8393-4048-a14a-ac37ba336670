package com.poizon.scm.wms.command.alarm.rule.request;

import com.poizon.scm.wms.util.request.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AlarmStrategyRuleListQueryRequest extends BasePageRequest {
    @ApiModelProperty("策略编号")
    @NotBlank(message = "策略编号不能为空")
    private String strategyNo;
    @ApiModelProperty("规则名称")
    private String ruleName;
    @ApiModelProperty("状态")
    private Integer status;
}
