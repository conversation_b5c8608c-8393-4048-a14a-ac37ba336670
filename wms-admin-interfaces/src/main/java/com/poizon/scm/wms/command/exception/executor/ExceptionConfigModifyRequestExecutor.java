package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ExceptionConfigModifyParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionConfigAdminRepository;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigModifyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionConfigModifyRequestExecutor extends AbstractCommandExecutor<ExceptionConfigModifyRequest, Void> {

    @Autowired
    private WmsExceptionConfigAdminRepository wmsExceptionConfigAdminRepository;

    @Override
    protected void verify(ExceptionConfigModifyRequest request) {
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getWarehouseCode()), "适用仓库不能为空");
        if (StringUtils.isNotEmpty(request.getPreinstallRemark())) {
            Preconditions.checkBiz(request.getPreinstallRemark().length() <= 200, "预设备注长度不能超过200个字符！");
        }
    }

    @Override
    protected Void doExecute(ExceptionConfigModifyRequest request) {
        ExceptionConfigModifyParam modifyParam = new ExceptionConfigModifyParam();
        modifyParam.setId(request.getId());
        modifyParam.setDirectionCode(StringUtils.isNotBlank(request.getDirectionCode()) ? request.getDirectionCode() : null);
        modifyParam.setOperateCode(StringUtils.isNotBlank(request.getOperateCode()) ? request.getOperateCode() : null);
        modifyParam.setSort(request.getSort());
        modifyParam.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        modifyParam.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        modifyParam.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        modifyParam.setPreinstallRemark(request.getPreinstallRemark());
        modifyParam.setErrorLabelCode(request.getErrorLabelCode());
        modifyParam.setWarehouseCode(request.getWarehouseCode());
        modifyParam.setTenantId(OperationUserContextHolder.getScmTenantCode());

        wmsExceptionConfigAdminRepository.modifyExceptionConfig(modifyParam);
        return null;
    }
}
