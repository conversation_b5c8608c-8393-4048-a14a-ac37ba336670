package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RuleDetailItemRequest {
    //expressCode
    @ApiModelProperty("例如 expressCode")
    private String code;
    //快递商
    @ApiModelProperty("例如 name")
    private String name;
    //EQUAL
    @ApiModelProperty("例如 EQUAL")
    private String opt;
    //相等
    @ApiModelProperty("例如 相等")
    private String optName;
    //SF
    @ApiModelProperty("例如 SF")
    private String value;
    //顺丰
    @ApiModelProperty("例如 顺丰")
    private String valueName;
}
