package com.poizon.scm.wms.command.bas.station.station.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.station.BasStationRelationApi;
import com.poizon.scm.wms.command.bas.station.station.request.BindStationRelationRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: 平川
 * @createTime: 2024年07月04日 16:16:39
 * @version: 1.0
 * @Description: 绑定工作站executor
 */
@Slf4j
@Component
public class BindStationRelationRequestExecutor extends AbstractCommandExecutor<BindStationRelationRequest, Void> {

    @DubboReference(check = false)
    private BasStationRelationApi basStationRelationApi;

    @Override
    protected Void doExecute(BindStationRelationRequest request) {

        if (request == null || StringUtils.isAnyEmpty(request.getWarehouseCode(), request.getStationCode())){
            throw new WmsOperationException("入参为空");
        }

        com.poizon.scm.wms.bas.api.command.station.request.BindStationRelationRequest bindStationRelationRequest =
                new com.poizon.scm.wms.bas.api.command.station.request.BindStationRelationRequest();

        OperationUserContext userContext = OperationUserContextHolder.get();
        bindStationRelationRequest.setOperateUserId(userContext.getUserId());
        bindStationRelationRequest.setOperateUserName(userContext.getUserName());
        bindStationRelationRequest.setWarehouseCode(request.getWarehouseCode());
        bindStationRelationRequest.setStationCode(request.getStationCode());
        bindStationRelationRequest.setStationSide(request.getStationSide());

        Result<Boolean> booleanResult = basStationRelationApi.bindStationRelation(bindStationRelationRequest);
        if (!Result.isSuccess(booleanResult) || !booleanResult.getData()){
            throw new WmsOperationException(booleanResult.getMsg());
        }

        return null;
    }
}
