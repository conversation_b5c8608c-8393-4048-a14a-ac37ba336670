package com.poizon.scm.wms.command.outbound.launch.strategy.executor;

import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyDo;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyQueryRepository;
import com.poizon.scm.wms.command.outbound.launch.strategy.request.CreateStrategyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.NewNoGenUtil;
import com.poizon.scm.wms.common.utils.enums.NewNoGenEnum;
import com.poizon.scm.wms.domain.outbound.launch.StrategyCommandService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/28
 */
@Component
public class CreateStrategyRequestExecutor extends AbstractCommandExecutor<CreateStrategyRequest, Void> {

    @Resource
    private StrategyCommandService strategyCommandService;
    @Resource
    private BizOffShelvesStrategyQueryRepository strategyQueryRepository;

    @Override
    protected Void doExecute(CreateStrategyRequest request) {
        strategyCommandService.save(buildStrategyCreateParam(request));
        return null;
    }

    @Override
    protected void verify(CreateStrategyRequest request) {
        //校验相同仓库策略名称不能相同
        List<BizOffShelvesStrategyDo> list = strategyQueryRepository.queryByStrategyNameAndWarehouseCode(request.getWarehouseCode(), request.getStrategyName());
        if (CollectionUtils.isNotEmpty(list)) {
            throw new WmsOperationException("策略名称重复，请修改");
        }
    }

    private BizOffShelvesStrategyDo buildStrategyCreateParam(CreateStrategyRequest request) {
        BizOffShelvesStrategyDo bizOffShelvesStrategyDo = new BizOffShelvesStrategyDo();
        bizOffShelvesStrategyDo.setStrategyNo(NewNoGenUtil.generateNewNo(NewNoGenEnum.OFF_SHELVES_STRATEGY));
        bizOffShelvesStrategyDo.setStrategyName(request.getStrategyName().trim());
        bizOffShelvesStrategyDo.setStrategyDesc(request.getStrategyDesc().trim());
        bizOffShelvesStrategyDo.setType(request.getType());
        bizOffShelvesStrategyDo.setStatus(0);
        bizOffShelvesStrategyDo.setThreshold(100);
        bizOffShelvesStrategyDo.setFrequency(30);
        bizOffShelvesStrategyDo.setTailWaveFrequency(1);
        bizOffShelvesStrategyDo.setWarehouseCode(request.getWarehouseCode());
        bizOffShelvesStrategyDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        bizOffShelvesStrategyDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        bizOffShelvesStrategyDo.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        bizOffShelvesStrategyDo.setCreatedTime(new Date());
        bizOffShelvesStrategyDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        bizOffShelvesStrategyDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        bizOffShelvesStrategyDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        bizOffShelvesStrategyDo.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        bizOffShelvesStrategyDo.setVersion(1);
        bizOffShelvesStrategyDo.setDeleted(0);
        return bizOffShelvesStrategyDo;
    }
}
