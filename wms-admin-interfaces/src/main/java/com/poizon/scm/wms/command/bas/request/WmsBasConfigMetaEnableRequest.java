package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配置元数据启用")
public class WmsBasConfigMetaEnableRequest extends Request {

    /**
     * 配置元数据ID
     */
    @ApiModelProperty(value = "配置元数据ID", required = true)
    private Long id;

}
