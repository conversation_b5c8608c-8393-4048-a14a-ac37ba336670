package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BaseSortingRuleDetailSaveRequest {

    @ApiModelProperty("仓库")
    @NotBlank(message = "仓库不能为空")
    private String warehouseCode;

    @ApiModelProperty("场景")
    @NotBlank(message = "场景不能为空")
    private String sceneCode;

    @ApiModelProperty("规则编号")
    @NotBlank(message = "规则编号不能为空")
    private String ruleCode;

    @ApiModelProperty("格口编码")
    @NotBlank(message = "格口编码不能为空")
    private String chuteCode;

    @ApiModelProperty("工作台编码")
    @NotBlank(message = "工作台编码不能为空")
    private String stationCode;

    @ApiModelProperty("优先级")
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    @ApiModelProperty("满箱提示")
    @NotBlank(message = "满箱提示不能为空")
    private String opGuide;

    @ApiModelProperty("状态 1 启用 2 禁用")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty("因子")
    private List<RuleDetailItemRequest> itemApiRequestList;
}
