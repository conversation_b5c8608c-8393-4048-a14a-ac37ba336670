package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ExceptionInfoAcceptParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionInfoRpcRepository;
import com.poizon.scm.wms.api.dto.request.exception.info.ExceptionInfoAcceptRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import com.poizon.scm.wms.util.util.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExceptionInfoAcceptRequestExecutor extends AbstractCommandExecutor<ExceptionInfoAcceptRequest, Boolean> {

    @Autowired
    private WmsExceptionInfoRpcRepository wmsExceptionInfoRpcRepository;

    @Override
    protected Boolean doExecute(ExceptionInfoAcceptRequest request) {
        ExceptionInfoAcceptParam acceptParam = BeanUtil.copy(request, ExceptionInfoAcceptParam.class);
        return wmsExceptionInfoRpcRepository.accept(acceptParam);
    }

    @Override
    protected void verify(ExceptionInfoAcceptRequest request) {
        Preconditions.checkBiz(request != null && StringUtils.isNotBlank(request.getExceptionNo()), "异常No不能为空！");
    }

}
