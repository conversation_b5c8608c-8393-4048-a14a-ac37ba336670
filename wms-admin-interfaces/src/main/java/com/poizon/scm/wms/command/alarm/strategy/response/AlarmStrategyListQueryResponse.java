package com.poizon.scm.wms.command.alarm.strategy.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class AlarmStrategyListQueryResponse {
    /**
     * 策略编号
     */
    @ApiModelProperty("策略编号")
    private String strategyNo;

    /**
     * 策略名称
     */
    @ApiModelProperty("策略名称")
    private String strategyName;

    /**
     * 策略描述
     */
    @ApiModelProperty("策略描述")
    private String strategyDesc;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String type;

    /**
     * 策略状态(0-已停用,1-生效中)
     */
    @ApiModelProperty("策略状态")
    private Integer status;

    /**
     * 仓库编号
     */
    @ApiModelProperty("仓库编号")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @ApiModelProperty("仓库名称")
    private String warehouseName;


    /**
     * 创建人真实姓名
     */
    @ApiModelProperty("创建人真实姓名")
    private String createdRealName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createdTime;

    /**
     * 修改人真实姓名
     */
    @ApiModelProperty("修改人真实姓名")
    private String updatedRealName;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private String updatedTime;

}
