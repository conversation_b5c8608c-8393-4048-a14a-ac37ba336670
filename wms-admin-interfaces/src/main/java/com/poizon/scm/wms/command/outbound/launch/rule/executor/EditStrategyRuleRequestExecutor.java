package com.poizon.scm.wms.command.outbound.launch.rule.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyRuleDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.RuleListQueryParam;
import com.poizon.scm.wms.adapter.outbound.launch.model.result.RuleListQueryResult;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyRuleQueryRepository;
import com.poizon.scm.wms.command.outbound.launch.rule.request.EditStrategyRuleRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.outbound.launch.StrategyRuleCommandService;
import com.poizon.scm.wms.domain.outbound.launch.entity.RuleContent;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/24
 */
@Component
public class EditStrategyRuleRequestExecutor extends AbstractCommandExecutor<EditStrategyRuleRequest, Void> {

    @Resource
    private StrategyRuleCommandService strategyRuleCommandService;

    @Resource
    private BizOffShelvesStrategyRuleQueryRepository strategyRuleQueryRepository;

    @Value("${auto.launch.max.second.category:1000}")
    private Integer autoLaunchMaxSecondCategory;

    @Override
    protected Void doExecute(EditStrategyRuleRequest request) {
        strategyRuleCommandService.updateByRuleNoSelective(buildUpdateParam(request));
        return null;
    }

    private BizOffShelvesStrategyRuleDo buildUpdateParam(EditStrategyRuleRequest request) {
        BizOffShelvesStrategyRuleDo ruleDo = new BizOffShelvesStrategyRuleDo();
        ruleDo.setRuleNo(request.getRuleNo());
        ruleDo.setRuleName(request.getRuleName());
        ruleDo.setRuleDesc(request.getRuleDesc());
        ruleDo.setPriority(request.getPriority());
        ruleDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        ruleDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        ruleDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        ruleDo.setUpdatedTime(new Date());
        ruleDo.setRuleContent(JSON.toJSONString(request.getRuleContent()));
        ruleDo.setFrequency(request.getFrequency());
        return ruleDo;
    }

    @Override
    protected void verify(EditStrategyRuleRequest request) {
        BizOffShelvesStrategyRuleDo ruleDo = strategyRuleQueryRepository.queryByRuleNo(request.getRuleNo());
        if (ruleDo == null) {
            throw new WmsOperationException("规则不存在");
        }
        List<RuleListQueryResult> list = strategyRuleQueryRepository.queryList(new RuleListQueryParam(ruleDo.getStrategyNo()), OperationUserContextHolder.getScmTenantCode());
        list = list.stream().filter(item -> !item.getRuleNo().equals(request.getRuleNo())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getPriority().equals(request.getPriority()))) {
            throw new WmsOperationException("该优先级已存在");
        }

        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getRuleName().equals(request.getRuleName()))) {
            throw new WmsOperationException("该规则名称已存在");
        }

        RuleContent ruleContent = request.getRuleContent();
        if (null != ruleContent) {
            List<String> secondCategoryIds = ruleContent.getSecondCategoryIds();
            if (CollectionUtils.isNotEmpty(secondCategoryIds) && secondCategoryIds.size() > autoLaunchMaxSecondCategory) {
                throw new WmsOperationException("类目数量超长，最多只能选择" + autoLaunchMaxSecondCategory + "个类目");
            }
        }
    }
}
