package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.info.task.ExceptionTaskProcessParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionTaskAdminRepository;
import com.poizon.scm.wms.api.dto.request.exception.task.ExceptionTaskProcessRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionTaskProcessRequestExecutor extends AbstractCommandExecutor<ExceptionTaskProcessRequest, Void> {

    @Autowired
    private WmsExceptionTaskAdminRepository wmsExceptionTaskAdminRepository;

    @Override
    protected Void doExecute(ExceptionTaskProcessRequest request) {
        ExceptionTaskProcessParam param = new ExceptionTaskProcessParam();
        param.setId(request.getId());
        param.setExceptionNo(request.getExceptionNo());
        param.setResult(request.getResult());
        param.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        param.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        param.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionTaskAdminRepository.process(param);
        return null;
    }

}
