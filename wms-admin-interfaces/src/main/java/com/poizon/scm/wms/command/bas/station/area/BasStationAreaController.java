package com.poizon.scm.wms.command.bas.station.area;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.station.area.request.*;
import com.poizon.scm.wms.command.bas.station.area.response.BasStationAreaSelectResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.pagesupport.response.PageResult;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * date 22/04/2024 11:36
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("/admin/bas/station/area")
public class BasStationAreaController {

    @Resource
    private CommandBus commandBus;

    @PostMapping("/add")
    public Result<BasStationAreaSelectResponse> add(@RequestBody @Validated(value = ValidateGroups.Add.class) BasStationAreaAddRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @Validated(value = ValidateGroups.Delete.class) BasStationAreaDeleteRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Validated(value = ValidateGroups.Update.class) BasStationAreaUpdateRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/query")
    public Result<PageResult<BasStationAreaSelectResponse>> query(@RequestBody @Validated(value = ValidateGroups.Select.class) BasStationAreaSelectRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/enum/query")
    public Result<List<BasStationAreaSelectResponse>> enumQuery(@RequestBody @Validated(value = ValidateGroups.Select2.class) BasStationAreaEnumSelectRequest request) {
        return commandBus.handle(Objects.isNull(request) ? new BasStationAreaEnumSelectRequest() : request);
    }
}
