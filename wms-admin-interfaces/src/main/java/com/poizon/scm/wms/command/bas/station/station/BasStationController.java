package com.poizon.scm.wms.command.bas.station.station;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.station.station.request.*;
import com.poizon.scm.wms.command.bas.station.station.response.BasStationQueryScreenResponse;
import com.poizon.scm.wms.command.bas.station.station.response.BasStationResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.pagesupport.response.PageResult;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * date  28/04/2024 周日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("/admin/bas/station")
public class BasStationController {

    @Resource
    private CommandBus commandBus;

    /**
     * 通过stationName模糊查询工位
     *
     * @param request request
     * @return List<BasStationResponse>
     */
    @PostMapping("/query")
    public Result<List<BasStationResponse>> query(@RequestBody @Validated(value = ValidateGroups.Select.class) BasStationQueryRequest request) {
        return commandBus.handle(request);
    }

    /**
     * 通过stationCode查询工位
     *
     * @param request request
     * @return BasStationResponse
     */
    @PostMapping("/queryByCode")
    public Result<BasStationResponse> queryByCode(@RequestBody AdminStationQueryRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/queryPage")
    public Result<PageResult<BasStationResponse>> queryPage(@RequestBody @Validated(value = ValidateGroups.Select3.class) BasStationQueryPageRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/release")
    public Result<Boolean> release(@RequestBody @Validated(value = ValidateGroups.Update.class) BasStationReleaseRequest request) {
        return commandBus.handle(request);
    }

    /**
     * 工位图大屏幕 编辑前查询
     *
     * @param request request
     * @return List<BasStationResponse>
     */
    @PostMapping("/queryScreen")
    public Result<List<BasStationQueryScreenResponse>> queryScreen(@RequestBody @Validated(value = ValidateGroups.Select2.class) BasStationQueryScreenRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody @Validated(value = ValidateGroups.Add.class) BasStationBatchAddRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Validated(value = ValidateGroups.Update.class) BasStationUpdateRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @Validated(value = ValidateGroups.Delete.class) BasStationDeleteRequest request) {
        return commandBus.handle(request);
    }
}
