package com.poizon.scm.wms.command.handover.executor;

import com.poizon.scm.wms.api.command.handover.request.UpdateHandoverStatusRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.haandover.DeliveryHandoverHeaderService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: dwq
 * @Date: 2020/10/20 9:27 下午
 */
@Service
public class UpdateHandoverStatusRequestExecutor extends AbstractCommandExecutor<UpdateHandoverStatusRequest,Void> {
    @Autowired
    private DeliveryHandoverHeaderService deliveryHandoverHeaderService;
    @Override
    protected Void doExecute(UpdateHandoverStatusRequest command) {
        deliveryHandoverHeaderService.updateStatusByHandoverNo(command.getHandoverNo(), NumberUtils.INTEGER_ONE);
        return null;
    }
}
