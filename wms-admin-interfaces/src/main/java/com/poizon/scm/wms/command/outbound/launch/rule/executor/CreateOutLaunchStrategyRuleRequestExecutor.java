package com.poizon.scm.wms.command.outbound.launch.rule.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyRuleDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.RuleListQueryParam;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.auto.AutoLaunchRuleValidateParam;
import com.poizon.scm.wms.adapter.outbound.launch.model.result.RuleListQueryResult;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyQueryRepository;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyRuleQueryRepository;
import com.poizon.scm.wms.command.outbound.launch.rule.request.CreateOutLaunchStrategyRuleRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.NewNoGenUtil;
import com.poizon.scm.wms.common.utils.enums.NewNoGenEnum;
import com.poizon.scm.wms.domain.launch.auto.validation.AutoLaunchRuleValidation;
import com.poizon.scm.wms.domain.outbound.launch.StrategyRuleCommandService;
import com.poizon.scm.wms.domain.outbound.launch.entity.AutoLaunchRuleContent;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CreateOutLaunchStrategyRuleRequestExecutor  extends AbstractCommandExecutor<CreateOutLaunchStrategyRuleRequest, Void> {

    @Resource
    private StrategyRuleCommandService strategyRuleCommandService;

    @Resource
    private BizOffShelvesStrategyQueryRepository strategyQueryRepository;

    @Resource
    private BizOffShelvesStrategyRuleQueryRepository strategyRuleQueryRepository;
    @Resource
    private AutoLaunchRuleValidation autoLaunchRuleValidation;

    @Override
    protected Void doExecute(CreateOutLaunchStrategyRuleRequest request) {
        validateAutoLaunchRule(request.getAutoLaunchRuleContent());
        strategyRuleCommandService.save(buildCreateStrategyRuleParam(request));
        return null;
    }

    public void validateAutoLaunchRule(AutoLaunchRuleContent autoLaunchRuleContent) {
        AutoLaunchRuleValidateParam param = new AutoLaunchRuleValidateParam();
        //转化bizTypes为list
        List<String> bizTypeList = Arrays.asList(StringUtils.split(autoLaunchRuleContent.getBizTypes(), ","));
        List<Integer> bizTypeIntList = new ArrayList<>();
        CollectionUtils.collect(bizTypeList, Integer::valueOf,bizTypeIntList);
        param.setBizTypeParams(bizTypeIntList);
        param.setDeliveryType(autoLaunchRuleContent.getType());
        param.setTagParams(autoLaunchRuleContent.getTagList());
        param.setGoodsFlow(autoLaunchRuleContent.getGoodsFlow());
        autoLaunchRuleValidation.validate(param);
    }


    private BizOffShelvesStrategyRuleDo buildCreateStrategyRuleParam(CreateOutLaunchStrategyRuleRequest request) {
        BizOffShelvesStrategyRuleDo strategyRuleDo = new BizOffShelvesStrategyRuleDo();
        strategyRuleDo.setStrategyNo(request.getStrategyNo());
        strategyRuleDo.setRuleNo(NewNoGenUtil.generateNewNo(NewNoGenEnum.OFF_SHELVES_STRATEGY_RULE));
        strategyRuleDo.setRuleName(request.getRuleName());
        strategyRuleDo.setRuleDesc(request.getRuleDesc());
        strategyRuleDo.setPriority(request.getPriority());
        strategyRuleDo.setStatus(0);
        strategyRuleDo.setRuleContent(JSON.toJSONString(request.getAutoLaunchRuleContent()));
        strategyRuleDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        strategyRuleDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        strategyRuleDo.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        strategyRuleDo.setCreatedTime(new Date());
        strategyRuleDo.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        strategyRuleDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        strategyRuleDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        strategyRuleDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        strategyRuleDo.setFrequency(request.getFrequency());
        strategyRuleDo.setVersion(1);
        strategyRuleDo.setDeleted(0);
        return strategyRuleDo;
    }


    @Override
    protected void verify(CreateOutLaunchStrategyRuleRequest request) {
        BizOffShelvesStrategyDo strategyDo = strategyQueryRepository.queryByStrategyNo(request.getStrategyNo());
        if (strategyDo == null) {
            throw new WmsOperationException("策略不存在");
        }
        List<RuleListQueryResult> list = strategyRuleQueryRepository.queryList(new RuleListQueryParam(request.getStrategyNo()), OperationUserContextHolder.getScmTenantCode());
        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getPriority().equals(request.getPriority()))) {
            throw new WmsOperationException("该优先级已存在");
        }
    }
}