package com.poizon.scm.wms.query.outbound.outbill.executor;

import com.google.common.collect.Lists;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.SkuRepository;
import com.poizon.scm.wms.adapter.inventory.model.InventoryDo;
import com.poizon.scm.wms.api.dto.common.SelectStringValueOption;
import com.poizon.scm.wms.api.enums.DictTypeEnum;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.commodity.GoodsQueryService;
import com.poizon.scm.wms.domain.commodity.request.GoodsQueryParam;
import com.poizon.scm.wms.domain.commodity.request.QueryCommodityIdReqDomain;
import com.poizon.scm.wms.domain.commodity.response.CommodityIdResponseDomain;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.query.outbound.outbill.request.WmsSkuQueryRequest;
import com.poizon.scm.wms.query.outbound.outbill.response.WmsSkuQueryResponse;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.service.sys.SysDictV2Service;
import com.poizon.scm.wms.util.common.ItemCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class WmsSkuQueryRequestExecutor extends AbstractCommandExecutor<WmsSkuQueryRequest, PagingObject<WmsSkuQueryResponse>> {


    @Autowired
    private ICommodityQueryV2Service commodityQueryV2Service;

    @Autowired
    private GoodsQueryService goodsQueryService;

    @Autowired
    private SkuRepository skuRepository;

    @Autowired
    private SysDictV2Service sysDictV2Service;

    @Override
    protected PagingObject<WmsSkuQueryResponse> doExecute(WmsSkuQueryRequest request) {
        GoodsQueryParam queryParam = parseFrom(request);
        if (Objects.isNull(queryParam)) {
            /*查询条件冲突,不用走查询了*/
            PagingObject<WmsSkuQueryResponse> pagingObject = new PagingObject<>();
            pagingObject.setPageNum(request.getPageNum());
            pagingObject.setPages(0);
            pagingObject.setPageSize(request.getPageSize());
            pagingObject.setTotal(0);
            return pagingObject;
        }
        PagingObject<InventoryDo> inventoryDoPagingObject = goodsQueryService.queryGoods(queryParam);
        /*构建返回值*/
        return buildResponse(inventoryDoPagingObject);
    }

    /**
     * 构建返回值
     *
     * @param pagingInventory
     * @return
     */
    private PagingObject<WmsSkuQueryResponse> buildResponse(PagingObject<InventoryDo> pagingInventory) {
        PagingObject<WmsSkuQueryResponse> pagingResponse = new PagingObject<>();
        pagingResponse.setPages(pagingInventory.getPages());
        pagingResponse.setTotal(pagingInventory.getTotal());
        pagingResponse.setPageNum(pagingInventory.getPageNum());
        pagingResponse.setPageSize(pagingInventory.getPageSize());
        List<WmsSkuQueryResponse> skuQueryResponses = build(pagingInventory.getContents());
        pagingResponse.setContents(skuQueryResponses);
        return pagingResponse;
    }

    /**
     * 将库存转换成响应对象
     *
     * @param inventoryDos
     * @return
     */
    private List<WmsSkuQueryResponse> build(List<InventoryDo> inventoryDos) {
        List<WmsSkuQueryResponse> responseList = new ArrayList<>();
        if (CollectionUtils.isEmpty(inventoryDos)){
            return responseList;
        }
        Set<String> skuSet = inventoryDos.stream().map(InventoryDo::getSkuId).collect(Collectors.toSet());
        Map<String, SkuCommonRspDomain> skuCommonMap = commodityQueryV2Service.querySkuCommonMapBySkuIds(OperationUserContextHolder.getScmTenantCode(), skuSet);
        Map<String, String> qualityLevelMap = getQualityLevelMap(inventoryDos.get(0).getWarehouseCode());
        for (InventoryDo inventoryDo : inventoryDos) {
            WmsSkuQueryResponse response = new WmsSkuQueryResponse();
            SkuCommonRspDomain skuCommonRspDomain = skuCommonMap.get(inventoryDo.getSkuId());
            response.setInventoryNo(inventoryDo.getInventoryNo());
            response.setUniqueCode(inventoryDo.getUniqueCode());
            response.setSkuId(inventoryDo.getSkuId());
            response.setGoodsArticleNumber(skuCommonRspDomain.getArtNoMain());
            response.setGoodsName(skuCommonRspDomain.getSpuName());
            response.setGoodsCategory(skuCommonRspDomain.getCategoryNameLevel3());
            response.setProperty(skuCommonRspDomain.getPropertyText());
            response.setColor(skuCommonRspDomain.getColor());
            response.setBrandName(skuCommonRspDomain.getBrandName());
            response.setSeriesName(skuCommonRspDomain.getSeriesName());
            response.setQualityLevel(inventoryDo.getQualityLevel());
            response.setQualityLevelName(qualityLevelMap.get(inventoryDo.getQualityLevel()));
            response.setOwnerCode(inventoryDo.getOwnerCode());
            response.setBizType(inventoryDo.getBizType());
            response.setQty(inventoryDo.getOnHandQty());
            responseList.add(response);
        }
        return responseList;
    }

    private Map<String, String> getQualityLevelMap(String warehouseCode) {
        return sysDictV2Service.listByStringValueNoException(
                warehouseCode, DictTypeEnum.QUALITY_LEVEL).stream().collect(Collectors.toMap(SelectStringValueOption::getValue, SelectStringValueOption::getText));
    }

    /**
     * 将请求转换成参数
     *
     * @param request
     * @return
     */
    private GoodsQueryParam parseFrom(WmsSkuQueryRequest request) {
        GoodsQueryParam queryParam = new GoodsQueryParam();
        List<String> skuIds = new ArrayList<>();
        boolean needFindSku = false;
        if (CollectionUtils.isNotEmpty(request.getSkuIdList())) {
            needFindSku = findIntersection(false, skuIds, request.getSkuIdList());
        }
        /*解析barCode*/
        if (StringUtils.isNotBlank(request.getBarcode())) {
            ItemCode parse = skuRepository.parse(request.getBarcode());
            if (parse.isUniqueCode()) {
                queryParam.setUniqueCode(parse.getUniqueCode());
            } else {
                needFindSku = findIntersection(needFindSku, skuIds, parse.getSkuIds());
            }
        }
        /*解析商品名称和货号*/
        if (StringUtils.isNotBlank(request.getGoodsArticleNumber()) || StringUtils.isNotBlank(request.getGoodsName())) {
            QueryCommodityIdReqDomain queryDomain = new QueryCommodityIdReqDomain();
            queryDomain.setArticleNumber(request.getGoodsArticleNumber());
            queryDomain.setGoodsTitle(request.getGoodsName());
            queryDomain.setTenantCode(OperationUserContextHolder.getScmTenantCode());
            List<CommodityIdResponseDomain> commodityIdResponseDomains = commodityQueryV2Service.queryCommodityIdInfoByTitle(queryDomain);
            if (CollectionUtils.isNotEmpty(commodityIdResponseDomains)) {
                Set<String> skuIdSet = new HashSet<>();
                List<String> skuIdString = commodityIdResponseDomains.stream().map(CommodityIdResponseDomain::getSkuIds).filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
                for (String skuString : skuIdString) {
                    skuIdSet.addAll(Arrays.asList(skuString.split(",")));
                }
                needFindSku = findIntersection(needFindSku, skuIds, Lists.newArrayList(skuIdSet));
            }
        }
        if (needFindSku && CollectionUtils.isEmpty(skuIds)) {
            /*说明商品货号、商品名称、sku这几个条件冲突了*/
            return null;
        }
        queryParam.setSkuIds(skuIds);
        queryParam.setQualityLevel(request.getQualityLevel());
        queryParam.setWarehouseCode(request.getWarehouseCode());
        return queryParam;
    }

    /**
     * 求两个集合的交集
     *
     * @param needFindSku 是否已经取过交集
     * @param skuIds
     * @param other
     */
    private boolean findIntersection(boolean needFindSku, List<String> skuIds, List<String> other) {
        if (needFindSku && CollectionUtils.isEmpty(skuIds)) {
            /*如果已经取过交集并且交集是空，说明已经冲突了，就不用在取交接了*/
            return true;
        }
        if (CollectionUtils.isEmpty(skuIds)) {
            skuIds.addAll(other);
        } else {
            skuIds.retainAll(other);
        }
        return true;
    }
}
