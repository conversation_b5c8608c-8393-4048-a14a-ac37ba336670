package com.poizon.scm.wms.command.alarm.rule.request;

import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.domain.alarm.AlarmRuleContent;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AlarmStrategyRuleEditRequest extends Request {

    @NotBlank(message = "规则编号不能为空")
    @ApiModelProperty("规则编号")
    private String ruleNo;

    @NotBlank(message = "规则名称不能为空")
    @Length(max = 50, message = "规则名称过长")
    @ApiModelProperty("规则名称")
    private String ruleName;

    @NotBlank(message = "规则描述不能为空")
    @Length(max = 100, message = "规则描述过长")
    @ApiModelProperty("规则描述")
    private String ruleDesc;


    @NotNull(message = "规则维度内容不能为空")
    @ApiModelProperty("规则维度内容")
    private List<AlarmRuleContent> ruleContent;


    @ApiModelProperty("调度类型 0-间隔,1-时间点")
    private Integer scheduleType;


    @ApiModelProperty("指定频率时间间隔（分钟）")
    private Integer duration;


    @ApiModelProperty("指定时间点（12:00）")
    private List<String> timePoints;


    @Override
    public void verify() throws RequestException {
        if (CollectionUtils.isEmpty(ruleContent)) {
            throw new WmsOperationException("规则维度内容不能为空");
        }
    }
}
