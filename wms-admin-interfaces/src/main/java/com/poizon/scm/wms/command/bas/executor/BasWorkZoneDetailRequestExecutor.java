package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.command.bas.request.BasWorkZoneDetailRequest;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.bas.BasWorkZoneCommandService;
import com.poizon.scm.wms.domain.bas.entity.param.update.BasWorkZoneDetailUpdateParam;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: wuyusen
 * @Date: 2021/6/1
 * @Description:
 */
@Component
public class BasWorkZoneDetailRequestExecutor extends AbstractCommandExecutor<BasWorkZoneDetailRequest, Void> {

    @Resource
    BasWorkZoneCommandService basWorkZoneCommandService;

    @Override
    protected Void doExecute(BasWorkZoneDetailRequest request) {
        BasWorkZoneDetailUpdateParam basWorkZoneDetailUpdateParam = new BasWorkZoneDetailUpdateParam();
        BeanUtils.copyProperties(request, basWorkZoneDetailUpdateParam);
        // 判断作业库区编号是否为数字和英文字母的组合
        String regex = "^[a-z0-9A-Z]+$";
        if (!request.getWorkZoneCode().matches(regex)) {
            throw new WmsException(WmsExceptionCode.WORK_ZONE_PARAMS_ERROR);
        }
        if (basWorkZoneDetailUpdateParam.getId() == null) {
            basWorkZoneCommandService.insert(basWorkZoneDetailUpdateParam);
        } else {
            basWorkZoneCommandService.update(basWorkZoneDetailUpdateParam);
        }
        return null;
    }
}
