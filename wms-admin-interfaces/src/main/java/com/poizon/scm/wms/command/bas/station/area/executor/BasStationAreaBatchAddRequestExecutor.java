package com.poizon.scm.wms.command.bas.station.area.executor;

import cn.hutool.core.collection.CollUtil;
import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationAreaApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaRequest;
import com.poizon.scm.wms.bas.api.command.station.response.BaseStationAreaResponse;
import com.poizon.scm.wms.command.bas.station.area.request.BasStationAreaBatchAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 22/04/2024 1:57
 */
@Slf4j
@Component
public class BasStationAreaBatchAddRequestExecutor extends AbstractCommandExecutor<BasStationAreaBatchAddRequest, Boolean> {

    @DubboReference(interfaceClass = BasStationAreaApi.class, check = false)
    private BasStationAreaApi basStationAreaApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected Boolean doExecute(BasStationAreaBatchAddRequest request) {
        List<BasStationAreaRequest> basStationAreaRequestList = request.getAddList().stream()
                .map(basStationAreaRequest -> BeanCopyUtils.copyProperties(basStationAreaRequest, BasStationAreaRequest.class))
                .peek(basStationAreaRequest -> basStationAreaRequest.setTenantId(OperationUserContextHolder.getTenantCode()))
                .collect(Collectors.toList());

        com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaBatchAddRequest batchAddRequest = new com.poizon.scm.wms.bas.api.command.station.request.BasStationAreaBatchAddRequest();
        batchAddRequest.setRequestList(basStationAreaRequestList);
        Result<List<BaseStationAreaResponse>> addResult = LogUtils.logRemoteInvoke(log, batchAddRequest, basStationAreaApi::add);

        return Optional.ofNullable(addResult).map(Result::getData)
                .map(CollUtil::isNotEmpty)
                .orElse(Boolean.FALSE);
    }

}
