package com.poizon.scm.wms.command.alarm.strategy.executor;

import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyUpdateStatusRequest;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Component
public class AlarmStrategyUpdateStatusRequestExecutor extends AbstractCommandExecutor<AlarmStrategyUpdateStatusRequest, Void> {

    @Resource
    private AlarmStrategyCommandService alarmStrategyCommandService;

    @Resource
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyUpdateStatusRequest request) {
        AlarmStrategyDo strategyDo = alarmStrategyQueryRepository.queryByStrategyNo(request.getStrategyNo());
        if (strategyDo == null) {
            throw new WmsOperationException("策略不存在");
        }
        alarmStrategyCommandService.updateStrategyStatus(request.getStrategyNo(),request.getStatus());
        return null;
    }

}
