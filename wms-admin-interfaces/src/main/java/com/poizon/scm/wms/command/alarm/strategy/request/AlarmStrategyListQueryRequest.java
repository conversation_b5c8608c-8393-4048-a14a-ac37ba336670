package com.poizon.scm.wms.command.alarm.strategy.request;

import com.poizon.scm.wms.util.request.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AlarmStrategyListQueryRequest extends BasePageRequest {
    @ApiModelProperty("策略名称")
    private String strategyName;
    @ApiModelProperty("类型")
    private String strategyType;
    @ApiModelProperty("仓库")
    private List<String> warehouseCodesList;
    @ApiModelProperty("状态")
    private Integer status;
}
