package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.operate.ExceptionSuggestOperateAddParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionSuggestOperateRepository;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class ExceptionSuggestOperateAddRequestExecutor extends AbstractCommandExecutor<ExceptionSuggestOperateAddRequest, Void> {

    @Autowired
    private WmsExceptionSuggestOperateRepository wmsExceptionSuggestOperateRepository;

    @Override
    protected void verify(ExceptionSuggestOperateAddRequest request) {
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getOperateCode()), "异常建议操作编码不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getOperateName()), "建议操作按钮文案不能为空！");
        Preconditions.checkBiz(request.getOperateCode().length() <= 50, "异常建议操作编码长度不能超过50个字符！");
        Preconditions.checkBiz(request.getOperateName().length() <= 50, "建议操作按钮文案长度不能超过50个字符！");
        Preconditions.checkBiz(request.getSysCode().length() <= 50, "对应系统模块编码长度不能超过50个字符！");
        Preconditions.checkBiz(request.getSysName().length() <= 100, "对应系统模块名称长度不能超过100个字符！");
    }

    @Override
    protected Void doExecute(ExceptionSuggestOperateAddRequest request) {
        ExceptionSuggestOperateAddParam addParam = new ExceptionSuggestOperateAddParam();
        addParam.setTenantId(OperationUserContextHolder.getTenantCode());
        addParam.setOperateCode(request.getOperateCode());
        addParam.setOperateName(request.getOperateName());
        addParam.setSysCode(request.getSysCode());
        addParam.setSysName(request.getSysName());
        addParam.setStatus(Optional.ofNullable(request.getStatus()).orElse(1));
        addParam.setSort(request.getSort());
        addParam.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        addParam.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        addParam.setCreatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionSuggestOperateRepository.add(addParam);
        return null;
    }

}
