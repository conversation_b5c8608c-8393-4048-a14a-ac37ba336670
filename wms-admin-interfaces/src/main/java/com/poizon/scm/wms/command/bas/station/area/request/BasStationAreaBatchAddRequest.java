package com.poizon.scm.wms.command.bas.station.area.request;

import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationAreaBatchAddRequest extends Request implements Serializable {
    private static final long serialVersionUID = 6077294425061845707L;

    @Valid
    @NotEmpty(groups = ValidateGroups.Add.class, message = "新增列表不能为空")
    private List<BasStationAreaRequest> addList;
}
