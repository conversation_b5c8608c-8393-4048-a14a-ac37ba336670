package com.poizon.scm.wms.command.container.request;

import com.poizon.scm.wms.util.Preconditions;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestException;
import com.poizon.scm.wms.util.framework.RequestExceptionCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class ContainerReleaseAdminRequest extends Request {

    @ApiModelProperty(value = "仓库编码", required = true)
    private String warehouseCode;
    @ApiModelProperty(value = "容器号", required = true)
    private String containerCode;
    @Override
    public void verify() throws RequestException {
        Preconditions.checkStringNotBlank(warehouseCode, RequestExceptionCode.WAREHOUSE_CODE_NULL);
        Preconditions.checkStringNotBlank(containerCode, RequestExceptionCode.CONTAINER_CODE_NULL);
    }
}
