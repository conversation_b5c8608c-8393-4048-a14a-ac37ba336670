package com.poizon.scm.wms.command.alarm.rule.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyRuleQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyRuleDo;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleEditRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.alarm.AlarmRuleTimeContent;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyRuleCommandService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Component
public class AlarmStrategyRuleEditRequestExecutor extends AbstractCommandExecutor<AlarmStrategyRuleEditRequest, Void> {

    @Resource
    private AlarmStrategyRuleCommandService alarmStrategyRuleCommandService;

    @Resource
    private AlarmStrategyRuleQueryRepository alarmStrategyRuleQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyRuleEditRequest request) {
        alarmStrategyRuleCommandService.editRule(buildUpdateParam(request));
        return null;
    }

    private AlarmStrategyRuleDo buildUpdateParam(AlarmStrategyRuleEditRequest request) {
        AlarmStrategyRuleDo ruleDo = new AlarmStrategyRuleDo();
        ruleDo.setRuleNo(request.getRuleNo());
        ruleDo.setRuleName(request.getRuleName());
        ruleDo.setRuleDesc(request.getRuleDesc());
        ruleDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        ruleDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        ruleDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        ruleDo.setUpdatedTime(new Date());
        ruleDo.setScheduleType(request.getScheduleType());
        ruleDo.setScheduleTimes(AlarmRuleTimeContent.convertJsonToSave(request.getDuration(),request.getTimePoints()));
        ruleDo.setRuleContent(JSON.toJSONString(request.getRuleContent()));
        ruleDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        return ruleDo;
    }

    @Override
    protected void verify(AlarmStrategyRuleEditRequest request) {
        AlarmStrategyRuleDo ruleDo = alarmStrategyRuleQueryRepository.queryRuleByRuleNo(request.getRuleNo());
        if (ruleDo == null) {
            throw new WmsOperationException("预警规则不存在");
        }
        List<AlarmStrategyRuleDo> list = alarmStrategyRuleQueryRepository.queryListByStrategyNo(ruleDo.getStrategyNo());
        list = list.stream().filter(item -> !item.getRuleNo().equals(request.getRuleNo())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getRuleName().equals(request.getRuleName()))) {
            throw new WmsOperationException("该预警规则名称已存在");
        }

    }
}