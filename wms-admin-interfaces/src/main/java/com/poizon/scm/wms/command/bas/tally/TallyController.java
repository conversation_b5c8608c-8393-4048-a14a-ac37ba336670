package com.poizon.scm.wms.command.bas.tally;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.saas.wms.inner.query.response.MoveOderAdminDTO;
import com.poizon.scm.wms.service.base.tally.TallyService;
import com.poizon.scm.wms.service.base.tally.request.TallyTaskCancelRequest;
import com.poizon.scm.wms.service.base.tally.request.TallyTaskCreateRequest;
import com.poizon.scm.wms.service.base.tally.request.TallyTaskDetailQueryRequest;
import com.poizon.scm.wms.service.base.tally.request.TallyTaskQueryRequest;
import com.poizon.scm.wms.service.base.tally.response.TallyTaskDetailQueryResponse;
import com.poizon.scm.wms.service.base.tally.response.TallyTaskQueryResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: 平川
 * @createTime: 2024年07月09日 20:32:18
 * @version: 1.0
 * @Description: 理货controller
 */
@RestController
@RequestMapping("admin/bas/tally")
@Api(tags = {"scm后台-理货"})
public class TallyController {

    @Autowired
    private TallyService tallyService;

    /**
     * 理货任务查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/task/query", method = RequestMethod.POST)
    @ApiOperation(value = "理货任务查询")
    public Result<TallyTaskQueryResponse> taskQuery(@RequestBody @Valid TallyTaskQueryRequest request) {
        return new Result<>(tallyService.taskQuery(request));
    }


    /**
     * 理货任务明细查询
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/task/detail/query", method = RequestMethod.POST)
    @ApiOperation(value = "理货任务查询")
    public Result<TallyTaskDetailQueryResponse> taskDetailQuery(@RequestBody @Valid TallyTaskDetailQueryRequest request) {
        return new Result<>(tallyService.taskDetailQuery(request));
    }

    /**
     * 理货任务创建
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/task/create", method = RequestMethod.POST)
    @ApiOperation(value = "理货任务创建")
    public Result<Void> taskCreate(@RequestBody @Valid TallyTaskCreateRequest request) {
        return new Result<>(tallyService.createTask(request));
    }

    /**
     * 理货任务取消
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/task/cancel", method = RequestMethod.POST)
    @ApiOperation(value = "理货任务创建")
    public Result<Void> taskCancel(@RequestBody @Valid TallyTaskCancelRequest request) {
        return new Result<>(tallyService.cancelTask(request));
    }


}
