package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigAddRequest;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigImportRequest;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigModifyRequest;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigStatusChangeRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/admin/exce/config")
@Api(tags = "H5-库内异常-异常配置")
public class WmsExceptionConfigCommandController {

    @Autowired
    private CommandBus bus;


    @ApiOperation("修改异常配置")
    @PostMapping("/v1/modify")
    public Result<Void> modifyExceptionConfig(@RequestBody @Valid ExceptionConfigModifyRequest request) {
        return bus.handle(request);
    }

    @ApiOperation("新增异常原因配置")
    @PostMapping("/v1/add")
    public Result<Void> addExceptionConfig(@RequestBody @Valid ExceptionConfigAddRequest request){
        return bus.handle(request);
    }

    @ApiOperation("异常配置状态变更(生效/失效)")
    @PostMapping("/v1/statusChange")
    public Result<Void> statusChange(@RequestBody @Valid ExceptionConfigStatusChangeRequest request) {
        return bus.handle(request);
    }

    @ApiOperation("导入异常配置")
    @PostMapping("/v1/import")
    public Result<Boolean> importExceptionConfig(@RequestBody @Valid ExceptionConfigImportRequest request){
        return bus.handle(request);
    }
}
