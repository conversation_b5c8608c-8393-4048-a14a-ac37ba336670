package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配置信息停用")
public class WmsBasConfigDisableRequest extends Request {

    /**
     * 配置ID
     */
    @ApiModelProperty(value = "配置ID", required = true)
    private Long id;

}
