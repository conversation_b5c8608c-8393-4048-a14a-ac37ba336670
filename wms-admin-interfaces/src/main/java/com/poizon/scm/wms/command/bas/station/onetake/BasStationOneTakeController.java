package com.poizon.scm.wms.command.bas.station.onetake;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.station.onetake.request.BasStationOneTakeRequest;
import com.poizon.scm.wms.command.bas.station.onetake.response.BasStationOneTakeResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * date  25/04/2024 周四
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("/admin/bas/station/oneTake")
public class BasStationOneTakeController {

    @Resource
    private CommandBus commandBus;

    @PostMapping("/query")
    public Result<BasStationOneTakeResponse> query(@RequestBody @Validated(value = ValidateGroups.Select.class) BasStationOneTakeRequest request) {
        return commandBus.handle(request);
    }


}
