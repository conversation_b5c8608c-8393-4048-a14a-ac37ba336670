package com.poizon.scm.wms.command.bas.station.map.request;

import com.poizon.scm.wms.bas.api.enums.BasStationAreaTypeEnum;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationMapUnbindRequest extends Request implements Serializable {
    private static final long serialVersionUID = -2898040293636179259L;

    @NotNull(message = "id不能为空", groups = ValidateGroups.Update.class)
    private Long id;

    /**
     * 分区类型
     *
     * @see BasStationAreaTypeEnum
     */
    private String areaType;

}
