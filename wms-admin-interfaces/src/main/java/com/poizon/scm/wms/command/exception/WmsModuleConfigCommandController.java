package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.exception.request.ModuleConfigAddRequest;
import com.poizon.scm.wms.command.exception.request.ModuleConfigModifyActivityRequest;
import com.poizon.scm.wms.command.exception.request.ModuleConfigModifyRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2021/8/31 20:30
 * @description 异常模块表维护
 */
@RestController
@RequestMapping("/admin/module/config")
@Api(tags = "H5-库内异常-模块")
public class WmsModuleConfigCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 异常模块配置页面新增异常模块
     *
     * @param request
     * <AUTHOR>
     * @since 2021/8/31 20:30
     */
    @ApiOperation("新增异常模块")
    @PostMapping("/v1/add")
    public Result<Void> addModule(@RequestBody @Valid ModuleConfigAddRequest request) {
        return bus.handle(request);
    }

    /**
     * 异常模块配置页面修改异常模块
     *
     * @param request
     * <AUTHOR>
     * @since 2021/8/31 20:30
     */
    @ApiOperation("修改异常模块名称")
    @PostMapping("/v1/modify")
    public Result<Void> modifyModule(@RequestBody @Valid ModuleConfigModifyRequest request) {
        return bus.handle(request);
    }

    /**
     * 修改异常模块启用/禁用
     *
     * @param request
     * <AUTHOR>
     * @since 2021/8/31 20:30
     */
    @ApiOperation("修改异常模块启用禁用")
    @PostMapping("/v1/modify/activity")
    public Result<Void> modifyModule(@RequestBody @Valid ModuleConfigModifyActivityRequest request) {
        return bus.handle(request);
    }
}
