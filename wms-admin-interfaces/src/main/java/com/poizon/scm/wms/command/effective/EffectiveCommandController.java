package com.poizon.scm.wms.command.effective;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.command.effective.request.AddRequest;
import com.poizon.scm.wms.api.command.effective.request.AuditedRequest;
import com.poizon.scm.wms.api.command.effective.request.ModifyRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 效期管理
 *
 * @author: tywei
 * @create: 2021-03-08 7:07 下午
 **/

@Slf4j
@Api(tags = {"效期审核"})
@RestController
@RequestMapping("/admin/effective/v1")
public class EffectiveCommandController {

    @Autowired
    private CommandBus commandBus;

    @ApiOperation("新增效期")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody AddRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("修改效期")
    @PostMapping("/modify")
    public Result<Void> modify(@RequestBody ModifyRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("效期审核")
    @PostMapping("/audited")
    public Result<Void> audited(@RequestBody AuditedRequest request) {
        return commandBus.handle(request);
    }
}
