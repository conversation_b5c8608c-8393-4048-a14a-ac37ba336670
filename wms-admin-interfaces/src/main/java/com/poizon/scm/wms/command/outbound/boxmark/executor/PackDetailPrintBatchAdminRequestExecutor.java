package com.poizon.scm.wms.command.outbound.boxmark.executor;

import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDetailDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackDetailRepository;
import com.poizon.scm.wms.command.outbound.boxmark.request.PackDetailPrintBatchAdminRequest;
import com.poizon.scm.wms.command.outbound.boxmark.response.PackDetailPrintBatchAdminResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.domain.outbound.returngoods.service.WmsPackService;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.util.enums.WmsPrintStatusEnum;
import com.poizon.scm.wms.util.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 装箱明细打印执行器
 * <AUTHOR>
 * @date 2022/04/26 2:33 下午
 */
@Service
public class PackDetailPrintBatchAdminRequestExecutor extends AbstractCommandExecutor<PackDetailPrintBatchAdminRequest, List<PackDetailPrintBatchAdminResponse>> {

    @Resource
    private WmsPackService wmsPackService;
    @Resource
    private WmsPackDetailRepository wmsPackDetailRepository;
    @Resource
    private ICommodityQueryV2Service commodityQueryV2Service;

    @Override
    protected List<PackDetailPrintBatchAdminResponse> doExecute(PackDetailPrintBatchAdminRequest request) {

        Map<String, WmsPackDo> packDoMap =
                wmsPackService.updatePackPrintStatus(request.getPackNos(), WmsPrintStatusEnum.PRINT_COMPLETED.getStatus());

        List<WmsPackDetailDo> wmsPackDetailDos = wmsPackDetailRepository.listByPackNoList(packDoMap.keySet());
        Map<String, List<WmsPackDetailDo>> packDetailMap =
                wmsPackDetailDos.stream().collect(Collectors.groupingBy(WmsPackDetailDo::getPackNo));

        Map<String, SkuCommonRspDomain> skuCommonRspDomainMap = getStringSkuCommonRspDomainMap(wmsPackDetailDos);

        List<PackDetailPrintBatchAdminResponse> responseList = new ArrayList<>();
        packDoMap.forEach((k,v) -> {
            PackDetailPrintBatchAdminResponse response = new PackDetailPrintBatchAdminResponse();
            response.setBoxMark(k);
            response.setSealTime(DateUtils.formatTimeForAdmin(v.getSealTime()));
            response.setReceiver(v.getLimitationCode());
            response.setQty(0);
            List<PackDetailPrintBatchAdminResponse.ItemInfo> itemInfoList = new ArrayList<>();
            response.setItemInfoList(itemInfoList);
            List<WmsPackDetailDo> packDetailDoList = packDetailMap.get(k);
            if (CollectionUtils.isEmpty(packDetailDoList)){
                responseList.add(response);
                return;
            }
            response.setQty(packDetailDoList.stream().mapToInt(WmsPackDetailDo::getQty).sum());
            packDetailDoList.forEach(wmsPackDetailDo -> {
                PackDetailPrintBatchAdminResponse.ItemInfo itemInfo = new PackDetailPrintBatchAdminResponse.ItemInfo();
                itemInfo.setBarcode(wmsPackDetailDo.getUniqueCode());
                itemInfo.setGoodsTitle(wmsPackDetailDo.getGoodsTitle());
                SkuCommonRspDomain skuCommonRspDomain = skuCommonRspDomainMap.get(wmsPackDetailDo.getSkuId());
                itemInfo.setFirstCategoryName(Objects.nonNull(skuCommonRspDomain) ?
                        skuCommonRspDomain.getCategoryNameLevel1() : StringUtils.EMPTY);
                itemInfoList.add(itemInfo);
            });
            responseList.add(response);
        });
        return responseList;
    }

    private Map<String, SkuCommonRspDomain> getStringSkuCommonRspDomainMap(List<WmsPackDetailDo> wmsPackDetailDos) {
        Set<String> skuSets = wmsPackDetailDos.stream().map(WmsPackDetailDo::getSkuId).collect(Collectors.toSet());
        Map<String, SkuCommonRspDomain> skuCommonRspDomainMap = commodityQueryV2Service.querySkuCommonMapBySkuIds(OperationUserContextHolder.getScmTenantCode(), skuSets);
        return skuCommonRspDomainMap;
    }

}
