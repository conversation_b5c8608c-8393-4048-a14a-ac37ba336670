package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class BaseSortingEntitySyncRequest implements Serializable {

    @ApiModelProperty("仓库")
    @NotBlank(message = "仓库不能为空")
    private String warehouseCode;

    @ApiModelProperty("被采纳工作台编码")
    @NotBlank(message = "被采纳的工作台编码不能为空")
    private String acceptedStationCode;

    @ApiModelProperty("被同步工作台编码")
    @NotEmpty(message = "被同步的工作台编码不能为空")
    private List<String> syncedStationCodeList;
}
