package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigModifyParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigAdminRepository;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigModifyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WmsBasConfigModifyRequestExecutor extends AbstractCommandExecutor<WmsBasConfigModifyRequest, Void> {

    @Autowired
    private WmsConfigAdminRepository wmsConfigAdminRepository;

    @Override
    protected void verify(WmsBasConfigModifyRequest request) {
        super.verify(request);
        Preconditions.checkBiz(null != request.getId(), "配置ID不能为空！");
    }

    @Override
    protected Void doExecute(WmsBasConfigModifyRequest request) {

        wmsConfigAdminRepository.modify(WmsConfigModifyParam.builder()
                .id(request.getId())
                .name(request.getName())
                .valueType(request.getValueType())
                .value(request.getValue())
                .level(request.getLevel())
                .priority(request.getPriority())
                .sort(request.getSort())
                .remark(request.getRemark())
                .updatedUserId(OperationUserContextHolder.get().getUserId())
                .updatedRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
