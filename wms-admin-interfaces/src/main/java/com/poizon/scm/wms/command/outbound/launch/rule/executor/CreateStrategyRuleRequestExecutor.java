package com.poizon.scm.wms.command.outbound.launch.rule.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.BizOffShelvesStrategyRuleDo;
import com.poizon.scm.wms.adapter.outbound.launch.model.param.RuleListQueryParam;
import com.poizon.scm.wms.adapter.outbound.launch.model.result.RuleListQueryResult;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyQueryRepository;
import com.poizon.scm.wms.adapter.outbound.launch.repository.query.BizOffShelvesStrategyRuleQueryRepository;
import com.poizon.scm.wms.command.outbound.launch.rule.request.CreateStrategyRuleRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.NewNoGenUtil;
import com.poizon.scm.wms.common.utils.enums.NewNoGenEnum;
import com.poizon.scm.wms.domain.outbound.launch.StrategyRuleCommandService;
import com.poizon.scm.wms.domain.outbound.launch.entity.RuleContent;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/27
 */
@Component
public class CreateStrategyRuleRequestExecutor extends AbstractCommandExecutor<CreateStrategyRuleRequest, Void> {

    @Resource
    private StrategyRuleCommandService strategyRuleCommandService;

    @Resource
    private BizOffShelvesStrategyQueryRepository strategyQueryRepository;

    @Resource
    private BizOffShelvesStrategyRuleQueryRepository strategyRuleQueryRepository;

    @Value("${auto.launch.max.second.category:1000}")
    private Integer autoLaunchMaxSecondCategory;


    @Override
    protected Void doExecute(CreateStrategyRuleRequest request) {
        strategyRuleCommandService.save(buildCreateStrategyRuleParam(request));
        return null;
    }

    private BizOffShelvesStrategyRuleDo buildCreateStrategyRuleParam(CreateStrategyRuleRequest request) {
        BizOffShelvesStrategyRuleDo strategyRuleDo = new BizOffShelvesStrategyRuleDo();
        strategyRuleDo.setStrategyNo(request.getStrategyNo());
        strategyRuleDo.setRuleNo(NewNoGenUtil.generateNewNo(NewNoGenEnum.OFF_SHELVES_STRATEGY_RULE));
        strategyRuleDo.setRuleName(request.getRuleName());
        strategyRuleDo.setRuleDesc(request.getRuleDesc());
        strategyRuleDo.setPriority(request.getPriority());
        strategyRuleDo.setStatus(0);
        strategyRuleDo.setRuleContent(JSON.toJSONString(request.getRuleContent()));
        strategyRuleDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        strategyRuleDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        strategyRuleDo.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        strategyRuleDo.setCreatedTime(new Date());
        strategyRuleDo.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        strategyRuleDo.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        strategyRuleDo.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        strategyRuleDo.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        strategyRuleDo.setFrequency(request.getFrequency());
        strategyRuleDo.setVersion(1);
        strategyRuleDo.setDeleted(0);
        return strategyRuleDo;
    }

    @Override
    protected void verify(CreateStrategyRuleRequest request) {
        BizOffShelvesStrategyDo strategyDo = strategyQueryRepository.queryByStrategyNo(request.getStrategyNo());
        if (strategyDo == null) {
            throw new WmsOperationException("策略不存在");
        }
        List<RuleListQueryResult> list = strategyRuleQueryRepository.queryList(new RuleListQueryParam(request.getStrategyNo()), OperationUserContextHolder.getScmTenantCode());
        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getPriority().equals(request.getPriority()))) {
            throw new WmsOperationException("该优先级已存在");
        }
        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getRuleName().equals(request.getRuleName()))) {
            throw new WmsOperationException("该规则名称已存在");
        }
        if (request.getRuleContent().getMinOrderNum() > request.getRuleContent().getMaxOrderNum()) {
            throw new WmsOperationException("最小任务数不能大于最大任务数");
        }

        RuleContent ruleContent = request.getRuleContent();
        if (null != ruleContent) {
            List<String> secondCategoryIds = ruleContent.getSecondCategoryIds();
            if (CollectionUtils.isNotEmpty(secondCategoryIds) && secondCategoryIds.size() > autoLaunchMaxSecondCategory) {
                throw new WmsOperationException("类目数量超长，最多只能选择" + autoLaunchMaxSecondCategory + "个类目");
            }
        }
    }

}
