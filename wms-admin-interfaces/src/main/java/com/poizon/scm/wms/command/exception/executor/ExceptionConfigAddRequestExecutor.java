
package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigAddRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.exception.ExceptionConfigAddParams;
import com.poizon.scm.wms.service.exception.IWmsExceptionProcessConfigService;
import com.poizon.scm.wms.util.Preconditions;
import com.poizon.scm.wms.util.common.StringUtils;
import com.poizon.scm.wms.util.util.BeanUtil;
import com.poizon.scm.wms.util.util.NumberUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 8/9/22 2:51 PM
 */
@Component
public class ExceptionConfigAddRequestExecutor extends AbstractCommandExecutor<ExceptionConfigAddRequest,Void> {
    @Autowired
    private IWmsExceptionProcessConfigService wmsExceptionProcessConfigService;

    @Override
    protected void verify(ExceptionConfigAddRequest request) {
        if (!NumberUtils.INTEGER_ONE.equals(request.getAllWarehouseFlag())) {
            Preconditions.checkBiz(StringUtils.isNotBlank(request.getWarehouseCode()) || CollectionUtils.isNotEmpty(request.getWarehouseCodes()), "适用仓库不能为空");
        }
    }

    @Override
    protected Void doExecute(ExceptionConfigAddRequest request) {
        ExceptionConfigAddParams params = BeanUtil.copy(request, ExceptionConfigAddParams.class);
        return wmsExceptionProcessConfigService.addExceptionConfig(params);
    }

}
