package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class BaseSortingEntityCopyRequest {

    @ApiModelProperty("仓库")
    @NotBlank(message = "仓库不能为空")
    private String warehouseCode;

    @ApiModelProperty("场景code")
    @NotBlank(message = "场景code不能为空")
    private String sceneCode;

    @ApiModelProperty("实体code")
    @NotBlank(message = "实体code不能为空")
    private String code;

    @ApiModelProperty("原有实体code")
    private String sourceCode;

    private Integer copyRuleFlag;

    @ApiModelProperty("实体名字")
    @NotBlank(message = "实体名字不能为空")
    private String name;

    @ApiModelProperty("状态")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
