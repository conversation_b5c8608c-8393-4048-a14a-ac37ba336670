package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SaveCacheLocationRequest extends Request {
    @ApiModelProperty(value = "缓存key", required = true, notes = "查询key对应的缓存分页数据")
    @NotBlank(message = "key不能为空")
    private String key;
}
