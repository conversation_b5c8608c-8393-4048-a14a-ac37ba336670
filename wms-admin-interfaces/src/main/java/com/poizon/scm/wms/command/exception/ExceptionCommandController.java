
package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.info.ExceptionAuditRequest;
import com.poizon.scm.wms.api.dto.request.exception.info.ExceptionInfoAcceptRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description 异常记录列表
 */
@RestController
@RequestMapping("/admin/exce")
@Api(tags = "H5-库内异常-异常记录列表")
public class ExceptionCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 已确认
     */
    @ApiOperation("已确认")
    @PostMapping("/v1/info/accept")
    public Result<Boolean> accept(@RequestBody ExceptionInfoAcceptRequest request) {
        return bus.handle(request);
    }

    @ApiOperation("免赔付审核")
    @PostMapping(value = "/v1/audit")
    public Result<Void> audit(@RequestBody ExceptionAuditRequest request) {
        return bus.handle(request);
    }

}
