package com.poizon.scm.wms.command.outbound.parcel.executor.delivery;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.response.logistics.LogisticsInfoResponse;
import com.poizon.scm.wms.command.outbound.parcel.request.delivery.QueryParcelDeliveryInfoRequest;
import com.poizon.scm.wms.command.outbound.parcel.response.delivery.QueryDeliveryDetailInfoResponse;
import com.poizon.scm.wms.command.outbound.parcel.response.delivery.QueryDeliveryInfoReceiveResponse;
import com.poizon.scm.wms.command.outbound.parcel.response.delivery.QueryDeliveryInfoResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.outbound.returngoods.service.logistic.LogisticConfig;
import com.poizon.scm.wms.outbound.api.parcel.DeliveryParcelOrderApi;
import com.poizon.scm.wms.outbound.api.parcel.request.delivery.QueryDeliveryInfoRequest;
import com.poizon.scm.wms.util.util.BeanUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author:chaoyuan
 * @createTime:2023/7/9 15:46
 */
@Component
public class QueryParcelDeliveryInfoRequestExecutor extends AbstractCommandExecutor<QueryParcelDeliveryInfoRequest, QueryDeliveryInfoResponse> {

    @DubboReference(interfaceClass = DeliveryParcelOrderApi.class, timeout = 5000, check = false)
    private DeliveryParcelOrderApi deliveryParcelOrderApi;

    @Resource
    private LogisticConfig config;

    @Override
    protected QueryDeliveryInfoResponse doExecute(QueryParcelDeliveryInfoRequest request) {
        return getResult(request);
    }

    private QueryDeliveryInfoResponse getResult(QueryParcelDeliveryInfoRequest request) {

        QueryDeliveryInfoRequest param = new QueryDeliveryInfoRequest();
        BeanUtil.copy(request, param);
        param.setTenantCode(OperationUserContextHolder.getScmTenantCode());

        Result<com.poizon.scm.wms.outbound.api.parcel.response.delivery.QueryDeliveryInfoResponse> rpcResult = deliveryParcelOrderApi.queryDeliveryInfo(param);
        if (!Result.isSuccess(rpcResult)) {
            //直接报错
            throw new WmsOperationException(rpcResult.getMsg());
        }


        if (Objects.isNull(rpcResult.getData())) {
            return null;
        }

        com.poizon.scm.wms.outbound.api.parcel.response.delivery.QueryDeliveryInfoResponse data = rpcResult.getData();
        QueryDeliveryInfoResponse result = new QueryDeliveryInfoResponse();

        BeanUtil.copy(data, result);

        if (Objects.nonNull(data.getDeliveryDetailReceiveResponse())) {
            QueryDeliveryInfoReceiveResponse queryDeliveryInfoReceiveResponse = new QueryDeliveryInfoReceiveResponse();
            BeanUtil.copy(data.getDeliveryDetailReceiveResponse(), queryDeliveryInfoReceiveResponse);
            result.setDeliveryDetailReceiveResponse(queryDeliveryInfoReceiveResponse);
        }

        if (CollectionUtils.isNotEmpty(data.getQueryDeliveryDetailInfoResponseList())) {
            List<com.poizon.scm.wms.outbound.api.parcel.response.delivery.QueryDeliveryDetailInfoResponse> deliveryDetailInfoResponseList = data.getQueryDeliveryDetailInfoResponseList();

            // 非DHL不填充url
            if(interceptCannotPrint(deliveryDetailInfoResponseList.get(0).getLogisticCode(), data.getWarehouseCode())){
                deliveryDetailInfoResponseList.stream().forEach(QueryDeliveryDetailInfoResponse -> QueryDeliveryDetailInfoResponse.setLogisticsBillUrl(""));
            }
            result.setQueryDeliveryDetailInfoResponse(BeanUtil.deepCopyByList(deliveryDetailInfoResponseList, QueryDeliveryDetailInfoResponse.class));


        }

        return result;

    }

    /**
     * 判断H5是否允许打印，目前只有DHL
     *
     */
    private boolean interceptCannotPrint(String logisticsCode, String warehouseCode) {
        /*针对DHL下单，只允许在flutter端下单，不允许打印，在返回的时候抛个错误*/
        if (config.getOnlyPlaceOrder() != null && config.getOnlyPlaceOrder().containsKey(warehouseCode)) {
            List<String> logisticsCodeList = config.getOnlyPlaceOrder().get(warehouseCode);
            if (logisticsCodeList != null && logisticsCodeList.contains(logisticsCode)) {
                return false;
            }
        }
        return true;
    }
}
