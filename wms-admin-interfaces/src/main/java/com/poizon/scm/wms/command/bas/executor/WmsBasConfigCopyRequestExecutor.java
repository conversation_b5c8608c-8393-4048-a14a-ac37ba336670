package com.poizon.scm.wms.command.bas.executor;

import com.google.common.collect.Maps;
import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigCopyParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigAdminRepository;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigConstRepository;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigCopyRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class WmsBasConfigCopyRequestExecutor extends AbstractCommandExecutor<WmsBasConfigCopyRequest, Void> {

    @Autowired
    private WmsConfigAdminRepository wmsConfigAdminRepository;

    @Autowired
    private WmsConfigConstRepository wmsConfigConstRepository;

    @Override
    protected void verify(WmsBasConfigCopyRequest request) {
        super.verify(request);
        Preconditions.checkBiz(CollectionUtils.isNotEmpty(request.getTargetIdList()), "配置作用域不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getTargetType()), "配置作用域类型不能为空！");
        Preconditions.checkBiz(null != request.getConfigId(), "配置ID不能为空！");
    }

    @Override
    protected Void doExecute(WmsBasConfigCopyRequest request) {

        Map<String, String> targetNameMap = Maps.newHashMapWithExpectedSize(request.getTargetIdList().size());
        Map<String, String> map = wmsConfigConstRepository.targetIdMap(request.getTargetType());
        request.getTargetIdList().forEach(e -> targetNameMap.put(e, map.getOrDefault(e, "")));

        wmsConfigAdminRepository.copy(WmsConfigCopyParam.builder()
                .targetIdList(request.getTargetIdList())
                .targetNameMap(targetNameMap)
                .targetType(request.getTargetType())
                .configId(request.getConfigId())
                .updatedUserId(OperationUserContextHolder.get().getUserId())
                .updatedRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
