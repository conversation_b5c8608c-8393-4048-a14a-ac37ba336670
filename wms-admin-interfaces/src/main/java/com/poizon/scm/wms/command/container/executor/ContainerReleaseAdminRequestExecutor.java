package com.poizon.scm.wms.command.container.executor;

import com.poizon.scm.wms.adapter.container.model.BasContainerDo;
import com.poizon.scm.wms.adapter.container.model.param.rpc.LpnContainerReleaseParam;
import com.poizon.scm.wms.adapter.container.model.result.rpc.ContainerInstanceResult;
import com.poizon.scm.wms.adapter.container.repository.BasContainerRepository;
import com.poizon.scm.wms.adapter.container.repository.rpc.ContainerLpnQueryRepository;
import com.poizon.scm.wms.command.container.request.ContainerReleaseAdminRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.Preconditions;
import com.poizon.scm.wms.domain.inner.container.instance.ContainerOperateService;
import com.poizon.scm.wms.domain.inner.container.instance.enums.BasContainerStatusEnum;
import com.poizon.scm.wms.domain.lpn.container.config.LpnContainerGrayConfig;
import com.poizon.scm.wms.lpn.api.container.enums.LpnContainerStepEnum;
import com.poizon.scm.wms.util.enums.IsActiveEnum;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ContainerReleaseAdminRequestExecutor extends AbstractCommandExecutor<ContainerReleaseAdminRequest, Void> {


    @Resource
    private BasContainerRepository basContainerRepository;
    @Resource
    private ContainerOperateService containerOperateService;
    @Resource
    private LpnContainerGrayConfig lpnContainerGrayConfig;
    @Resource
    private ContainerLpnQueryRepository containerLpnQueryRepository;

    @Override
    protected void verify(ContainerReleaseAdminRequest request) {
        if (!lpnContainerGrayConfig.getLpnContainerGrayWarehouseCode().contains(request.getWarehouseCode())) {
            throw new WmsOperationException("该仓库不支持在当前页面操作容器释放");
        }
    }

    @Override
    protected Void doExecute(ContainerReleaseAdminRequest request) {
        // 查询容器，判断是否存在，判断是否被占用
        BasContainerDo basContainerDo = basContainerRepository.findByContainer(request.getContainerCode());
        Preconditions.checkBizV2(Objects.nonNull(basContainerDo), "容器不存在");
        Preconditions.checkBizV2(IsActiveEnum.ENABLE.getCode().equals(basContainerDo.getIsActive()), "容器已停用");
        if (BasContainerStatusEnum.AVAILABLE.getStatus().equals(basContainerDo.getContainerStatus())) {
            //重复释放
            return null;
        }
        checkStep(request);
        containerOperateService.releaseContainerV2(LpnContainerReleaseParam.builder()
                .containerCode(request.getContainerCode())
                .warehouseCode(request.getWarehouseCode())
                .version(basContainerDo.getVersion())
                .build());
        return null;
    }

    private void checkStep(ContainerReleaseAdminRequest request) {
        ContainerInstanceResult containerInstanceResult = containerLpnQueryRepository
                .queryByContainerCode(OperationUserContextHolder.getTenantCode(), request.getWarehouseCode(), request.getContainerCode());
        if (Objects.isNull(containerInstanceResult)) {
            return;
        }
        List<String> canReleaseSteps = LpnContainerStepEnum.CAN_RELEASE_STEP;
        if (CollectionUtils.isEmpty(canReleaseSteps) || !canReleaseSteps.contains(containerInstanceResult.getStep())) {
            throw new WmsOperationException("当前环节：" +
                    LpnContainerStepEnum.getEnumByStep(containerInstanceResult.getStep()) + "不支持后台释放!");
        }
        if (Objects.equals(getReceiveUpperDirection(), containerInstanceResult.getDirection())) {
            throw new WmsOperationException(WmsExceptionCode.RECEIVE_UPPER_DIRECTION_NOT_SUPPORT_CONTAINER_RELEASE);
        }
    }

    private String getReceiveUpperDirection() {
        return LpnContainerStepEnum.RECEIVE.getStep() + "_" + LpnContainerStepEnum.UPPER.getStep();
    }
}
