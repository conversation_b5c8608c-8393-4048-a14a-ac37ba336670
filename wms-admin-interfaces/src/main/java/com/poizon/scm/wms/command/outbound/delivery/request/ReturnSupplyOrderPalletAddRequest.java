package com.poizon.scm.wms.command.outbound.delivery.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Set;


/**
 * 退供单托盘数新增
 *
 * <AUTHOR>
 * @date 2023/09/03
 */
@Data
@ApiModel
public class ReturnSupplyOrderPalletAddRequest extends Request {
    @ApiModelProperty(value = "托盘数", notes = "单位为托")
    @NotNull(message = "托盘数不能为空")
    @Min(value = 1, message = "托盘数最小为1托")
    @Max(value = 10000000, message = "托盘数最大为1000000托")
    private Integer palletQty;
    @ApiModelProperty(value = "箱数量", notes = "单位为箱")
    @NotNull(message = "箱数量不能为空")
    @Min(value = 1, message = "箱数最小为1箱")
    @Max(value = 10000000, message = "箱数最大为1000000箱")
    private Integer numberOfBoxes;
    @ApiModelProperty(value = "重量", notes = "单位千克（kg）")
    @NotNull(message = "重量不能为空")
    @DecimalMin(value = "0.01", message = "重量最小为0.01Kg")
    @DecimalMax(value = "10000000", message = "重量最大为1000000Kg")
    private BigDecimal weight;
    @NotNull(message = "体积不能为空")
    @DecimalMin(value = "0.001", message = "体积最小为0.001立方米")
    @DecimalMax(value = "1000000", message = "体积最大为1000000立方米")
    private BigDecimal volume;
    @Size(min = 1, message = "退供单数量最少1个")
    private Set<String> returnSupplyOrders;
}
