package com.poizon.scm.wms.command.bas.station.station.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.station.BasStationApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationQueryRequest;
import com.poizon.scm.wms.command.bas.station.station.request.AdminStationQueryRequest;
import com.poizon.scm.wms.command.bas.station.station.response.BasStationResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 * date  13/07/2024
 */
@Slf4j
@Component
public class AdminStationQueryRequestExecutor extends AbstractCommandExecutor<AdminStationQueryRequest, BasStationResponse> {

    @DubboReference(check = false)
    private BasStationApi basStationApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected BasStationResponse doExecute(AdminStationQueryRequest request) {
        BasStationQueryRequest basStationQueryRequest = new BasStationQueryRequest();
        basStationQueryRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        basStationQueryRequest.setWarehouseCode(request.getWarehouseCode());
        basStationQueryRequest.setStationCode(request.getStationCode());
        Result<com.poizon.scm.wms.bas.api.command.station.response.BasStationResponse> responseResult = basStationApi.queryStationInfo(basStationQueryRequest);

        if (!Result.isSuccess(responseResult)){
            throw new WmsOperationException(responseResult.getMsg());
        }

        if (responseResult.getData() == null){
            log.warn("工作站查询为空, 入参:{}", JSON.toJSONString(basStationQueryRequest));
            throw new WmsOperationException("工作站查询为空,请扫描正确的站点编码");
        }

        BasStationResponse stationResponse = new BasStationResponse();
        stationResponse.setStationCode(responseResult.getData().getStationCode());
        stationResponse.setStationSide(responseResult.getData().getStationSide());
        stationResponse.setStationPropertyDesc(responseResult.getData().getStationPropertyDesc());
        stationResponse.setStationType(responseResult.getData().getStationType());

        return stationResponse;
    }

}
