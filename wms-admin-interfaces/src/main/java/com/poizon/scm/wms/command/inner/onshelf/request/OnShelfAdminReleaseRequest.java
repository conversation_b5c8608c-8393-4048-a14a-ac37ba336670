package com.poizon.scm.wms.command.inner.onshelf.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/8/24 8:48 下午
 * @description
 */
@Data
@ApiModel
public class OnShelfAdminReleaseRequest extends Request {

    @ApiModelProperty(value = "上架任务单号")
    @NotBlank(message = "上架任务释放，任务单号不允许为空")
    private String taskNo;

}
