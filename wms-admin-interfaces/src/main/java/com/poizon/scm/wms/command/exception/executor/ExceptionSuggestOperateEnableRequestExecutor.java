package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.operate.ExceptionSuggestOperateStatusParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionSuggestOperateRepository;
import com.poizon.scm.wms.api.dto.request.exception.operate.ExceptionSuggestOperateEnableRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionSuggestOperateEnableRequestExecutor extends AbstractCommandExecutor<ExceptionSuggestOperateEnableRequest, Void> {

    @Autowired
    private WmsExceptionSuggestOperateRepository wmsExceptionSuggestOperateRepository;

    @Override
    protected Void doExecute(ExceptionSuggestOperateEnableRequest request) {
        ExceptionSuggestOperateStatusParam param = new ExceptionSuggestOperateStatusParam();
        param.setId(request.getId());
        param.setOperateCode(request.getOperateCode());
        param.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        param.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        param.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionSuggestOperateRepository.enable(param);
        return null;
    }

}
