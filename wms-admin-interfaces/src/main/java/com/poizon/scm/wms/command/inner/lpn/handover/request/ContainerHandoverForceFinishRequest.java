package com.poizon.scm.wms.command.inner.lpn.handover.request;

import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.util.common.StringUtils;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2022/6/2 4:47 PM
 **/
@Data
public class ContainerHandoverForceFinishRequest extends Request {

    @ApiModelProperty(value = "仓库编码",required = true)
    private String warehouseCode;

    @ApiModelProperty(value = "交接编号",required = true)
    private String handoverNo;

    @Override
    public void verify() throws RequestException {
        if (StringUtils.isBlank(warehouseCode)) {
            throw new WmsOperationException("仓库编码不能为空");
        }
        if (StringUtils.isBlank(handoverNo)) {
            throw new WmsOperationException("交接单号不能为空");
        }
    }
}
