package com.poizon.scm.wms.command.bas.station.onetake.request;

import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * date  25/04/2024 周四
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationOneTakeRequest extends Request implements Serializable {
    private static final long serialVersionUID = 2485797535196303089L;

    @NotEmpty(message = "仓库编码不能为空", groups = ValidateGroups.Select.class)
    private String warehouseCode;

    /**
     * 工位分区编码
     */
    @NotEmpty(message = "工位分区编码不能为空", groups = ValidateGroups.Select.class)
    private String stationAreaCode;

}
