package com.poizon.scm.wms.command.goodsshelf.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.enums.BasAreaPropertiesEnum;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaApi;
import com.poizon.scm.wms.bas.api.command.area.response.BasAreaResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.GoodsShelfAdminApi;
import com.poizon.scm.wms.bas.api.command.goodsshelf.request.BindLocationQueryApiRequest;
import com.poizon.scm.wms.bas.api.command.goodsshelf.response.BindLocationInfoResponse;
import com.poizon.scm.wms.command.goodsshelf.request.BindLocationQueryRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
@Slf4j
@Component
public class BindLocationQueryRequestExecutor extends AbstractCommandExecutor<BindLocationQueryRequest,BindLocationInfoResponse> {
    @DubboReference(check = false)
    private GoodsShelfAdminApi goodsShelfAdminApi;
    @DubboReference(check = false)
    private BaseAreaApi baseAreaApi;

    @Override
    protected BindLocationInfoResponse doExecute(BindLocationQueryRequest request) {
        checkParam(request);
        Result<BasAreaResponse> basAreaResponseResult = baseAreaApi.selectByWarehouseAreaCode(OperationUserContextHolder.getTenantCode(), request.getWarehouseCode(), request.getAreaCode());
        if(!Result.isSuccess(basAreaResponseResult)){
            throw new WmsOperationException(basAreaResponseResult.getMsg());
        }
        if(ObjectUtils.isEmpty(basAreaResponseResult.getData())){
            throw new WmsOperationException("库区不存在");
        }
        String areaProperties = basAreaResponseResult.getData().getAreaProperties();
        if(!BasAreaPropertiesEnum.AGV.getCode().equals(areaProperties)){
            throw new WmsOperationException("选择的库区非AGV库区");
        }
        BindLocationQueryApiRequest apiRequest = new BindLocationQueryApiRequest();
        BeanUtils.copyProperties(request,apiRequest);
        Result<BindLocationInfoResponse> apiResult = goodsShelfAdminApi.queryBindLocationList(apiRequest);
        if(!Result.isSuccess(apiResult)){
            throw new WmsOperationException(apiResult.getMsg());
        }
        return apiResult.getData();
    }

    private void checkParam(BindLocationQueryRequest request){
        if(StringUtils.isEmpty(request.getWarehouseCode())){
            throw new WmsOperationException("仓库不能为空");
        }
        if(StringUtils.isEmpty(request.getAreaCode())){
            throw new WmsOperationException("库区不能为空");
        }
        if(StringUtils.isEmpty(request.getGoodsShelfCode())){
            throw new WmsOperationException("货架不能为空");
        }
        if(StringUtils.isEmpty(request.getGoodsShelfSide())){
            throw new WmsOperationException("货架面不能为空");
        }
    }
}
