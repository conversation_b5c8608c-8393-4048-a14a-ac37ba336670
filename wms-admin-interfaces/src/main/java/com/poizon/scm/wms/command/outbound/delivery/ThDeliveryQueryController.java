package com.poizon.scm.wms.command.outbound.delivery;

import com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.response.logistics.LogisticsInfoResponse;
import com.poizon.scm.wms.api.dto.response.report.ReportCenterResponse;
import com.poizon.scm.wms.command.outbound.delivery.request.*;
import com.poizon.scm.wms.command.outbound.delivery.response.*;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.outbound.launch.api.delivery.ThDeliveryQueryApi;
import com.poizon.scm.wms.util.common.ReadOnly;
import com.poizon.scm.wms.util.util.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 退货出库专属查询
 * @Author: dwq
 * @Date: 2021/11/9 8:46 下午
 */
@Slf4j
@Api(tags = "退货出库单")
@RestController
@RequestMapping("admin/thDelivery")
public class ThDeliveryQueryController {

    @Autowired
    private CommandBus commandBus;

    @Value("${th.delivery.list.open:true}")
    private Boolean isOpen;


    @DubboReference(check = false, timeout = 5000)
    private ThDeliveryQueryApi thDeliveryQueryApi;


    @ReadOnly
    @ApiOperation("列表")
    @PostMapping("list")
    public Result<PagingObject<ThDeliveryListQueryResponse>> list(@Valid@RequestBody ThDeliveryListQueryRequest request){
        if(isOpen) {
            com.poizon.scm.wms.outbound.launch.api.delivery.request.ThDeliveryListQueryRequest req = BeanUtil.deepCopy(request, com.poizon.scm.wms.outbound.launch.api.delivery.request.ThDeliveryListQueryRequest.class);
            req.setWarehouseCodes(request.getWarehouseCode());
            req.setUserId(OperationUserContextHolder.get().getUserId());
            req.setTenantCode(OperationUserContextHolder.getScmTenantCode());
            req.setWarehouseCode(null);
            log.info("请求列表入参:{}", JSON.toJSONString(req));
            Result<PagingObject<com.poizon.scm.wms.outbound.launch.api.delivery.response.ThDeliveryListQueryResponse>> result = thDeliveryQueryApi.list(req);
            log.info("请求列表结果:{}", JSON.toJSONString(result));
            if (Result.isSuccess(result)) {
                PagingObject data = BeanUtil.deepCopy(result.getData(), PagingObject.class);
                return Result.ofSuccess(data);
            } else {
                throw new WmsException(result.getCode(), result.getMsg());
            }
        }else {
            return commandBus.handle(request);
        }

    }

    @ApiOperation("列表导出")
    @PostMapping("listExport")
    public Result<ReportCenterResponse> export(@Valid@RequestBody ThDeliveryListExportRequest request){
        if(isOpen) {
            com.poizon.scm.wms.outbound.launch.api.delivery.request.ThDeliveryListExportRequest req = com.poizon.scm.wms.util.util.BeanUtil.deepCopy(request, com.poizon.scm.wms.outbound.launch.api.delivery.request.ThDeliveryListExportRequest.class);
            req.setWarehouseCodes(request.getWarehouseCode());
            req.setTenantCode(OperationUserContextHolder.getScmTenantCode());
            req.setUserId(OperationUserContextHolder.get().getUserId());
            req.setRealName(OperationUserContextHolder.get().getRealName());
            req.setWarehouseCode(null);
            log.info("请求导出入参:{}", JSON.toJSONString(req));
            Result<com.poizon.scm.wms.outbound.launch.api.report.response.ReportCenterResponse> result = thDeliveryQueryApi.export(req);
            log.info("请求导出结果:{}", JSON.toJSONString(result));
            //return commandBus.handle(request);
            if (Result.isSuccess(result)) {
                return Result.ofSuccess(BeanUtil.deepCopy(result.getData(), ReportCenterResponse.class));
            } else {
                throw new WmsException(result.getCode(), result.getMsg());
            }
        }else {
            return commandBus.handle(request);
        }
    }

    @ApiOperation("详情")
    @PostMapping("detail")
    public Result<ThDeliveryDetailQueryResponse> detail(@Valid@RequestBody ThDeliveryDetailQueryRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("手机号解密")
    @PostMapping("decryptMobile")
    public Result<DecryptMobileResponse> decryptMobile(@Valid@RequestBody DecryptMobileRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("修改备注")
    @PostMapping("updateRemark")
    public Result<Void> updateRemark(@Valid@RequestBody ThDeliveryUpdateRemarkRequest request){
        return commandBus.handle(request);
    }
    @ApiOperation("备注list")
    @PostMapping("remarkList")
    public Result<List<ThDeliveryRemarkListResponse>> remarkList(@Valid@RequestBody ThDeliveryRemarkListRequest request){
        return commandBus.handle(request);
    }

    @ApiOperation("打印自提交接单")
    @PostMapping("/print/self/handover/bill")
    public Result<SelfHandoverBillPrintResponse> printSelfHandoverBill(@RequestBody SelfHandoverBillPrintRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("出库单一键发货完成")
    @PostMapping("/shipping/v1/oneClickShip")
    public Result<LogisticsInfoResponse> oneClickShip(@RequestBody DeliveryOrderOneClickShipRequest deliveryOrderOneClickShipRequest) {
        return commandBus.handle(deliveryOrderOneClickShipRequest);
    }
}
