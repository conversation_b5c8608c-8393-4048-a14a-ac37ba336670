package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.request.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class LocationCachePageRequest  extends BasePageRequest {

    @ApiModelProperty(value = "缓存key", required = true, notes = "查询key对应的缓存分页数据")
    @NotBlank(message = "key不能为空")
    private  String key;

    @ApiModelProperty(value = "通道号", required = true, notes = "查询key对应的缓存分页数据")
    @NotNull(message = "通道号不能为空")
    private  Integer passNum;


}
