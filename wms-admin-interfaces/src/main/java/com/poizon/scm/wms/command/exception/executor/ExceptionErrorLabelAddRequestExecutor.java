package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.errorlabel.ExceptionErrorLabelAddParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionErrorLabelRepository;
import com.poizon.scm.wms.api.dto.request.exception.errorlabel.ExceptionErrorLabelAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.util.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author： LMF
 * @date： 2022/8/10 11:13 AM
 * @description： 新增失误标签执行器
 * @modifiedBy：
 * @version: 1.0
 */
@Component
public class ExceptionErrorLabelAddRequestExecutor  extends AbstractCommandExecutor<ExceptionErrorLabelAddRequest, String> {

    @Resource
    private WmsExceptionErrorLabelRepository wmsExceptionErrorLabelRepository;

    @Override
    protected String doExecute(ExceptionErrorLabelAddRequest request) {
        ExceptionErrorLabelAddParam addParam = new ExceptionErrorLabelAddParam();
        addParam.setTenantId(OperationUserContextHolder.getTenantCode());
        addParam.setErrorLabelName(request.getErrorLabel());
        addParam.setStatus(NumberUtils.INTEGER_ONE);
        addParam.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        addParam.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        addParam.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        return wmsExceptionErrorLabelRepository.add(addParam);
    }
}
