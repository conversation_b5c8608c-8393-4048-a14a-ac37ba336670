package com.poizon.scm.wms.command.alarm.strategy.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Data
public class AlarmStrategyCreateRequest extends Request {
    @ApiModelProperty(value = "策略名称")
    @NotBlank(message = "策略名称不能为空")
    @Length(max = 50, message = "策略名称过长")
    private String strategyName;

    @ApiModelProperty(value = "策略描述")
    @NotBlank(message = "策略描述不能为空")
    @Length(max = 100, message = "策略描述过长")
    private String strategyDesc;

    @ApiModelProperty(value = "仓库")
    @NotBlank(message = "仓库不能为空")
    private String warehouseCode;

    @ApiModelProperty(value = "类型")
    @NotBlank(message = "类型不能为空")
    private String type;

    @ApiModelProperty(value = "状态")
    @NotNull(message = "状态不能为空")
    private Integer status;

}

