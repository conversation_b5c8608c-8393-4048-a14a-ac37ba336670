package com.poizon.scm.wms.command.common.executor;

import com.poizon.scm.wms.api.command.common.request.OutBoundTypeQueryRequest;
import com.poizon.scm.wms.api.command.common.response.OutBoundTypeQueryResponse;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.BeanUtil;
import com.poizon.scm.wms.service.common.IWarehouseDeliveryTypeConfigService;
import com.poizon.scm.wms.service.common.entity.WarehouseDeliveryTypeConfigParam;
import com.poizon.scm.wms.util.framework.RequestException;
import com.poizon.scm.wms.util.framework.RequestExceptionCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 出库单类型查询
 * @Date 2020/11/12 2:41 下午
 */
@Component
public class OutBoundTypeQueryRequestExecutor extends AbstractCommandExecutor<OutBoundTypeQueryRequest, OutBoundTypeQueryResponse> {

    @Autowired
    private IWarehouseDeliveryTypeConfigService warehouseDeliveryTypeConfigService;

    @Override
    protected OutBoundTypeQueryResponse doExecute(OutBoundTypeQueryRequest request) {
        OutBoundTypeQueryResponse outBoundTypeQueryResponse = new OutBoundTypeQueryResponse();
        List<WarehouseDeliveryTypeConfigParam> deliveryTypeConfigParams = warehouseDeliveryTypeConfigService.
                queryDeliveryTypeList(request.getWarehouseCode(), request.getQueryType());
        outBoundTypeQueryResponse.setOutBoundTypeList(BeanUtil.copyListProperties(deliveryTypeConfigParams,
                OutBoundTypeQueryResponse.SingleOutBoundType.class));
        return outBoundTypeQueryResponse;
    }

    @Override
    protected void verify(OutBoundTypeQueryRequest request) {
        if (StringUtils.isBlank(request.getWarehouseCode())) {
            throw new RequestException(RequestExceptionCode.DELIVERY_TYPE_QUERY_EMPTY_ERROR);
        }
    }
}
