package com.poizon.scm.wms.command.alarm.strategy;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyCreateRequest;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyDeleteRequest;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyEditRequest;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyUpdateStatusRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Api(tags = {"PC-预警策略-command"})
@RequestMapping("/admin/alarm/strategy")
@RestController
public class AlarmStrategyCommandController {

    @Resource
    private CommandBus commandBus;

    @ApiOperation("创建策略")
    @PostMapping("/create")
    public Result<Void> create(@RequestBody @Valid AlarmStrategyCreateRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("策略启用/禁用")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@RequestBody @Valid AlarmStrategyUpdateStatusRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("删除策略")
    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody @Valid AlarmStrategyDeleteRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("策略更新")
    @PostMapping("/edit")
    public Result<Void> update(@RequestBody @Valid AlarmStrategyEditRequest request) {
        return commandBus.handle(request);
    }


}
