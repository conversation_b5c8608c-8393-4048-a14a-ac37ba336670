package com.poizon.scm.wms.command.centerreport.delivery.request;

import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestException;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 截单出库及时率请求参数
 * @Author: dwq
 * @Date: 2021/1/20 11:09 上午
 */
@Data
public class DeliveryTimelinessRateExportRequest extends Request {
    /**
     * 仓库列表
     */
    private List<String> warehouseCodes;
    /**
     * 出库单创建开始时间
     */
    private Date startDate;
    /**
     * 出库单创建截止时间
     */
    private Date endDate;

    @Override
    public void verify() throws RequestException {
        super.verify();
        if(!startDate.before(endDate)){
            throw new WmsOperationException(WmsExceptionCode.PARAMS_ERROR,"开始时间不能大于等于结束时间");
        }
    }
}
