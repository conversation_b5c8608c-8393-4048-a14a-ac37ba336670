package com.poizon.scm.wms.command.antifake.response;

import com.poizon.scm.wms.adapter.goods.PinkAiQuality;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/09/25
 */
@Data
public class PinkAiQualityResponse {
    /**
     * 正常数量
     * */
    private Integer normalNum;
    /**
     * 异常数量
     */
    private Integer exceptionNum;
    /**
     * 检查中数量
     */
    private Integer checkingNum;
    /**
     * 模型质检结果
     */
    private List<PinkAiQuality.AiQcModel> modelList;

    /**
     * 模型质检结果
     */
    @Data
    public static class AiQcModel implements Serializable {
        /**
         * 模型名称
         */
        private String modelName;

        /**
         * 质检结果code
         */
        private Integer aiResult;

        /**
         * 质检结果
         */
        private String aiResultDesc;

        /**
         * 匹配的结果
         */
        private String matchValue;

        /**
         * 封面图
         */
        private String imageUrl;

        /**
         * 部位列表
         */
        private List<com.poizon.scm.wms.outbound.api.antifake.response.PinkAiQualityResponse.AiQcModel.Part> partList;

        /**
         * 部位质检结果
         */
        @Data
        public static class Part {
            /**
             * 部位名称
             */
            private String imagePartName;

            /**
             * 质检匹配到的内容
             */
            private String matchValue;
            /**
             * 图片列表
             */
            private List<String> discernList;
        }
    }
}
