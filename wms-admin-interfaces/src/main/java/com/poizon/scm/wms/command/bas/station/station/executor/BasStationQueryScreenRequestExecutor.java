package com.poizon.scm.wms.command.bas.station.station.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationRequest;
import com.poizon.scm.wms.bas.api.command.station.response.BasStationScreenResponse;
import com.poizon.scm.wms.command.bas.station.station.request.BasStationQueryScreenRequest;
import com.poizon.scm.wms.command.bas.station.station.response.BasStationQueryScreenResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  29/04/2024 周一
 */
@Slf4j
@Component
public class BasStationQueryScreenRequestExecutor extends AbstractCommandExecutor<BasStationQueryScreenRequest, List<BasStationQueryScreenResponse>> {

    @DubboReference(check = false)
    private BasStationApi basStationApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected List<BasStationQueryScreenResponse> doExecute(BasStationQueryScreenRequest request) {
        BasStationRequest basStationRequest = BeanCopyUtils.copyProperties(request, BasStationRequest.class);
        basStationRequest.setTenantId(OperationUserContextHolder.getTenantCode());

        Result<List<BasStationScreenResponse>> result = LogUtils.logRemoteInvoke(log, basStationRequest, basStationApi::selectScreen);

        return Optional.ofNullable(result).map(Result::getData)
                .map(basStationScreenResponses -> basStationScreenResponses.stream().map(response -> {
                            BasStationQueryScreenResponse queryScreenResponse = new BasStationQueryScreenResponse();
                            queryScreenResponse.setId(response.getId());
                            queryScreenResponse.setTenantId(response.getTenantId());
                            queryScreenResponse.setWarehouseCode(response.getWarehouseCode());
                            queryScreenResponse.setStationAreaCode(response.getStationAreaCode());
                            queryScreenResponse.setStationGroupCode(response.getStationGroupCode());
                            queryScreenResponse.setRow(response.getRow());
                            queryScreenResponse.setColumn(response.getColumn());
                            List<BasStationQueryScreenResponse.StationDto> stationDtoList = response.getStationDtoList().stream()
                                    .map(stationDto -> BeanCopyUtils.copyProperties(stationDto, BasStationQueryScreenResponse.StationDto.class))
                                    .collect(Collectors.toList());
                            queryScreenResponse.setStationDtoList(stationDtoList);
                            return queryScreenResponse;
                        })
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }
}
