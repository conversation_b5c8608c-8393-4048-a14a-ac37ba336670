package com.poizon.scm.wms.command.alarm.strategy;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleDetailQueryRequest;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleListQueryRequest;
import com.poizon.scm.wms.command.alarm.rule.response.AlarmStrategyRuleDetailQueryResponse;
import com.poizon.scm.wms.command.alarm.rule.response.AlarmStrategyRuleListQueryResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Api(tags = {"PC-预警策略-query"})
@RequestMapping("/admin/alarm/strategy/rule")
@RestController
public class AlarmStrategyRuleQueryController {
    @Resource
    private CommandBus commandBus;

    @ApiOperation("规则列表查询")
    @PostMapping("/list")
    public Result<PagingObject<AlarmStrategyRuleListQueryResponse>> list(@RequestBody @Valid AlarmStrategyRuleListQueryRequest request) {
        return commandBus.handle(request);
    }

    @ApiOperation("规则详情查询")
    @PostMapping("/detail")
    public Result<AlarmStrategyRuleDetailQueryResponse> detail(@RequestBody @Valid AlarmStrategyRuleDetailQueryRequest request) {
        return commandBus.handle(request);
    }

}
