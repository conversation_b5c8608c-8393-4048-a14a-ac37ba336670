package com.poizon.scm.wms.command.inner.onshelf.executor;

import com.poizon.scm.wms.command.inner.onshelf.request.OnShelfAdminForceFinishRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.upper.UpperCommandService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/8/24 8:53 下午
 * @description
 */
@Component
public class OnShelfAdminForceFinishRequestExecutor
        extends AbstractCommandExecutor<OnShelfAdminForceFinishRequest, Void> {

    @Resource
    UpperCommandService upperCommandService;

    @Override
    protected Void doExecute(OnShelfAdminForceFinishRequest request) {
        // 强制完成上架任务
        upperCommandService.forceFinishUpperTask(request.getTaskNo());
        return null;
    }
}
