package com.poizon.scm.wms.command.bas.sorting;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.adapter.bas.config.model.WmsConfigDto;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigRepository;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.bas.api.enums.ConfigCategory;
import com.poizon.scm.wms.bas.api.enums.ConfigTargetType;
import com.poizon.scm.wms.bas.api.enums.ConfigType;
import com.poizon.scm.wms.bas.api.sorting.api.BaseSortingSceneApi;
import com.poizon.scm.wms.bas.api.sorting.request.BaseSortingSceneListApiRequest;
import com.poizon.scm.wms.bas.api.sorting.request.BaseSortingScenePageApiRequest;
import com.poizon.scm.wms.bas.api.sorting.request.BaseSortingSceneSaveApiRequest;
import com.poizon.scm.wms.bas.api.sorting.request.BaseSortingSceneUpdateApiRequest;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingSceneApiResponse;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingScenePageApiResponse;
import com.poizon.scm.wms.command.bas.sorting.convert.BasSortingConvert;
import com.poizon.scm.wms.command.bas.sorting.request.*;
import com.poizon.scm.wms.command.bas.sorting.response.BaseSortingSceneFactorListResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wuyusen
 * @Date: 2021/5/31
 * @Description:
 */
@RestController
@RequestMapping("admin/bas")
@Api(tags = {"后台-基础资料-拣货库区-command"})
public class BaseSortingSceneController {

    private BasSortingConvert basSortingConvert = BasSortingConvert.INSTANCE;

    @DubboReference(interfaceClass = BaseSortingSceneApi.class, timeout = 5000, check = false)
    private BaseSortingSceneApi baseSortingSceneApi;

    @Resource
    private ScpWarehouseRepository scpWarehouseRepository;

    @Resource
    private WmsConfigRepository wmsConfigRepository;

    @PostMapping(value = "/sorting/scene/save", produces = "application/json")
    @ApiOperation(value = "新建场景")
    public Result<Void> save(@RequestBody @Valid BaseSortingSceneSaveRequest request) {
        BaseSortingSceneSaveApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert(request);
        baseSortingSceneSaveApiRequest.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setWarehouseName(scpWarehouseRepository.queryNameByCodeAndTenant(OperationUserContextHolder.get().getTenantCode(),
                request.getWarehouseCode()));
        baseSortingSceneSaveApiRequest.setAutoFlag(1);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingSceneApi.saveBaseSortingScene(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/scene/update", produces = "application/json")
    @ApiOperation(value = "修改和删除场景")
    public Result<Void> update(@RequestBody @Valid BaseSortingSceneUpdateRequest request) {
        BaseSortingSceneUpdateApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert1(request);
        baseSortingSceneSaveApiRequest.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingSceneApi.updateBaseSortingScene(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/scene/page", produces = "application/json")
    @ApiOperation(value = "场景分页查询")
    public Result<PagingObject<BaseSortingScenePageApiResponse>> page(@RequestBody @Valid BaseSortingScenePageRequest request) {
        BaseSortingScenePageApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert2(request);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingSceneApi.pageList(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/scene/list", produces = "application/json")
    @ApiOperation(value = "场景下拉查询")
    public Result<List<BaseSortingSceneApiResponse>> list(@RequestBody @Valid BaseSortingSceneListRequest request) {
        BaseSortingSceneListApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert7(request);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingSceneApi.list(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/scene/factor/list", produces = "application/json")
    @ApiOperation(value = "查看因子")
    public Result<BaseSortingSceneFactorListResponse> queryFactorList(@RequestBody @Valid BaseSortingSceneFactorListRequest request) {
        List<WmsConfigDto> configDtoList = wmsConfigRepository.queryByTargetAndCategory(request.getCode(),
                ConfigTargetType.SORT_SCENE.code(), ConfigType.BIZ.code(), ConfigCategory.SORT_RULE.code());
        BaseSortingSceneFactorListResponse response = new BaseSortingSceneFactorListResponse();
        if(CollectionUtils.isEmpty(configDtoList)){
            return Result.ofSuccess(response);
        }
        response.setMsg(configDtoList.stream().map(WmsConfigDto::getName).collect(Collectors.joining(",")));
        return Result.ofSuccess(response);
    }

}
