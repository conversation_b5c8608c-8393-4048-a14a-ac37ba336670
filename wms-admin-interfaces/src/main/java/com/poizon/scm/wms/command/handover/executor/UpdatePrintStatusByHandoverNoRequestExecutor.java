package com.poizon.scm.wms.command.handover.executor;

import com.poizon.scm.wms.api.command.handover.request.UpdatePrintStatusByHandoverNoRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.haandover.DeliveryHandoverHeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: dwq
 * @Date: 2020/10/19 5:18 下午
 */
@Service
public class UpdatePrintStatusByHandoverNoRequestExecutor extends AbstractCommandExecutor<UpdatePrintStatusByHandoverNoRequest,Void> {
    @Autowired
    private DeliveryHandoverHeaderService deliveryHandoverHeaderService;
    @Override
    protected Void doExecute(UpdatePrintStatusByHandoverNoRequest command) {
        deliveryHandoverHeaderService.updatePrintStatusByHandoverNo(command.getHandoverNo(),1);
        return null;
    }
}
