package com.poizon.scm.wms.command.container;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.container.request.ContainerParkConfigModifyRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/7 5:48 PM
 * @description 容器停靠配置请求
 */
@RestController
@RequestMapping("admin/container/park/config")
public class ContainerParkConfigCommandController {

    @Resource
    private CommandBus commandBus;

    @PostMapping("/modify")
    public Result<Void> modifyContainerParkConfig(@RequestBody ContainerParkConfigModifyRequest request) {
        return commandBus.handle(request);
    }
}
