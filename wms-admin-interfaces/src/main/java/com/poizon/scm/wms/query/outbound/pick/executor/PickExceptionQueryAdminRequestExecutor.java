package com.poizon.scm.wms.query.outbound.pick.executor;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.poizon.fusion.common.model.PagingObject;
import com.poizon.scm.wms.adapter.common.SkuRepository;
import com.poizon.scm.wms.adapter.outbound.pick.model.PickTaskExceptionEntity;
import com.poizon.scm.wms.adapter.outbound.pick.query.PickExceptionPageQuery;
import com.poizon.scm.wms.adapter.outbound.pick.repository.db.query.PickExceptionQueryRepository;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.domain.commodity.response.SkuCommonRspDomain;
import com.poizon.scm.wms.query.outbound.pick.request.PickExceptionQueryAdminRequest;
import com.poizon.scm.wms.query.outbound.pick.response.PickExceptionResponse;
import com.poizon.scm.wms.service.commodity.service.ICommodityQueryV2Service;
import com.poizon.scm.wms.util.common.ItemCode;
import com.poizon.scm.wms.util.enums.*;
import com.poizon.scm.wms.util.util.BeanUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/29 10:01 上午
 **/
@Component
public class PickExceptionQueryAdminRequestExecutor extends AbstractCommandExecutor<PickExceptionQueryAdminRequest,
        PagingObject<PickExceptionResponse>> {

    @Resource
    private PickExceptionQueryRepository pickExceptionQueryRepository;
    @Resource
    private ICommodityQueryV2Service iCommodityQueryV2Service;
    @Resource
    private SkuRepository skuRepository;


    @Override
    protected PagingObject<PickExceptionResponse> doExecute(PickExceptionQueryAdminRequest request) {
        PickExceptionPageQuery query = BeanUtil.copy(request,PickExceptionPageQuery.class);
        //barcode参数解析
        convertBarcodeQuery(query);
        PagingObject<PickTaskExceptionEntity> pagingObject = pickExceptionQueryRepository.queryPageList(query,
                request.getPageNum(), request.getPageSize());
        PagingObject<PickExceptionResponse> result = JSONUtil.toBean(JSONUtil.toJsonStr(pagingObject), new TypeReference<PagingObject<PickExceptionResponse>>() {
            @Override
            public Type getType() {
                return super.getType();
            }
        },true);
        if (CollectionUtils.isEmpty(result.getContents())) {
            return result;
        }

        result.getContents().forEach(item -> {
//            DeliveryHeaderDo deliveryHeaderDo = deliveryHeaderMap.get(item.getDeliveryOrderCode());
            //出库单类型
            item.setDeliveryTypeName(WmsOutBoundTypeEnum.getDescByType(item.getDeliveryType()));
            //出库单状态
            WmsOutBoundStatusEnum deliveryStatus = WmsOutBoundStatusEnum.getEnums(item.getDeliveryStatus());
            item.setDeliveryStatusName(deliveryStatus == null? "":deliveryStatus.getDesc());
            //业务类型
            WmsBizTypeEnum bizType = WmsBizTypeEnum.getBizType(item.getBizType());
            item.setBizTypeName(bizType == null?"": bizType.getBizName());
            //标签
//            Integer orderTags = deliveryHeaderDo.getOrderTags() == null?0:deliveryHeaderDo.getOrderTags();
            item.setTagNames(DeliveryTagV2Enum.getDescByBitVal(item.getOrderTags()));
            //国标码/附加码
            SkuCommonRspDomain skuCommonRspDomain = iCommodityQueryV2Service.querySkuCommonInfoBySkuId(
                    OperationUserContextHolder.getScmTenantCode(),item.getSkuId());
            if (skuCommonRspDomain != null) {
                item.setStandardCode(skuCommonRspDomain.getStandCode());
                item.setExtraCode(skuCommonRspDomain.getExtraCode());
            }
            //出库单创建时间
            item.setDeliveryCreateTime(item.getDeliveryCreateTime());

        });

        return result;
    }

    private void convertBarcodeQuery(PickExceptionPageQuery query) {
        if (StringUtils.isEmpty(query.getBarCode())) {
            return;
        }
        ItemCode itemCode = skuRepository.parse(query.getBarCode());
        if (itemCode == null) {
            return;
        }
        if (ItemCodeType.UNIQUE_CODE.equals(itemCode.getType())) {
            // 唯一码模式查询
            String uniqueCode = itemCode.getCode().get(0);
            query.setUniqueCode(uniqueCode);
        } else {
            // 商品编码模式查询
            query.setSkuIds(itemCode.getCode());
        }
    }



}
