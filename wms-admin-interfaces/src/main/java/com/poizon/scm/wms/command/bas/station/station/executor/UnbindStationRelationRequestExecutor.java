package com.poizon.scm.wms.command.bas.station.station.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.api.command.station.BasStationRelationApi;
import com.poizon.scm.wms.command.bas.station.station.request.UnbindStationRelationRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: 平川
 * @createTime: 2024年07月04日 16:17:13
 * @version: 1.0
 * @Description: 解绑工位关系executor
 */
@Slf4j
@Component
public class UnbindStationRelationRequestExecutor extends AbstractCommandExecutor<UnbindStationRelationRequest, Void> {

    @DubboReference(check = false)
    private BasStationRelationApi basStationRelationApi;

    @Override
    protected Void doExecute(UnbindStationRelationRequest request) {

        if (request == null || StringUtils.isAnyEmpty(request.getWarehouseCode(), request.getStationCode())){
            throw new WmsOperationException("入参为空");
        }

        com.poizon.scm.wms.bas.api.command.station.request.UnbindStationRelationRequest unbindStationRelationRequest =
                new com.poizon.scm.wms.bas.api.command.station.request.UnbindStationRelationRequest();
        unbindStationRelationRequest.setWarehouseCode(request.getWarehouseCode());
        unbindStationRelationRequest.setStationCode(request.getStationCode());
        unbindStationRelationRequest.setStationSide(request.getStationSide());
        OperationUserContext userContext = OperationUserContextHolder.get();
        unbindStationRelationRequest.setOperateUserId(userContext.getUserId());
        unbindStationRelationRequest.setOperateUserName(userContext.getUserName());

        Result<Boolean> booleanResult = basStationRelationApi.unbindStationRelation(unbindStationRelationRequest);

        if (!Result.isSuccess(booleanResult) || !booleanResult.getData()){
            throw new WmsOperationException("工作站解绑失败");
        }

        return null;
    }
}
