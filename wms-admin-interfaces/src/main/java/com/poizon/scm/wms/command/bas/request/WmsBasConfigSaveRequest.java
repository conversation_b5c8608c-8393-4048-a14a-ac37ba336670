package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.util.framework.Request;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "配置信息新增")
public class WmsBasConfigSaveRequest extends Request {

    /**
     * 配置作用域对象
     */
    @ApiModelProperty("配置作用域对象")
    private String targetId;

    @ApiModelProperty("配置作用域名称")
    private String targetName;

    /**
     * 配置作用域
     */
    @ApiModelProperty("配置作用域类型")
    private String targetType;

    /**
     * 配置元数据ID
     */
    @ApiModelProperty("配置元数据ID")
    private Long configItemId;

    /**
     * 类型：SYSTEM:系统、BIZ:业务、DEVICE:设备
     */
    @ApiModelProperty(value = "配置类型", required = true)
    private String type;

    /**
     * 分类：SWITCH:开关、CONFIG:配置、SORT_RULE:分拣规则
     */
    @ApiModelProperty(value = "配置分类", required = true)
    private String category;

    /**
     * 配置编码
     */
    @ApiModelProperty(value = "配置编码", required = true)
    private String code;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称", required = true)
    private String name;

    /**
     * 值类型：NUMBER、STRING、LIST、JSON
     */
    @ApiModelProperty(value = "配置的值类型", required = true)
    private String valueType;

    /**
     * 配置的值
     */
    @ApiModelProperty("配置的值")
    private String value;

    /**
     * 配置级别
     */
    @ApiModelProperty("配置级别")
    private Integer level;

    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    private Integer priority;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

}
