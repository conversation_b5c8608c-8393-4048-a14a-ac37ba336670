package com.poizon.scm.wms.command.bas.station.area.response;

import com.poizon.scm.wms.bas.api.enums.BasStationAreaTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date  23/04/2024 周二
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationAreaSelectResponse implements Serializable {
    private static final long serialVersionUID = 87594024156655898L;

    private Long id;

    @ApiModelProperty("仓库编码")
    private String warehouseCode;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("分区编码")
    private String stationAreaCode;

    @ApiModelProperty("分区名称")
    private String stationAreaName;

    /**
     * 分区类型
     *
     * @see BasStationAreaTypeEnum
     */
    private String areaType;

}
