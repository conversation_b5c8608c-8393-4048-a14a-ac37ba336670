package com.poizon.scm.wms.command.exception.executor;


import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wehs.api.definition.exception.config.DbCompensationRuleAdminApi;
import com.poizon.scm.wehs.api.request.exception.config.DbCompensationRuleAddRequest;

import com.poizon.scm.wms.api.dto.request.exception.config.CompensationRuleAddRequest;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;

import com.poizon.scm.wms.util.util.BeanUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class CompensationRuleAddRequestExecutor extends AbstractCommandExecutor<CompensationRuleAddRequest,Void> {

    @DubboReference(check = false)
    private DbCompensationRuleAdminApi dbCompensationRuleAdminApi;
    @Override
    protected Void doExecute(CompensationRuleAddRequest request) {
        DbCompensationRuleAddRequest dbCompensationRuleAddRequest = BeanUtil.copy(request,DbCompensationRuleAddRequest.class);

        Result<Void> result = dbCompensationRuleAdminApi.add(dbCompensationRuleAddRequest);
        if(!Result.isSuccess(result)){
            throw  new WmsOperationException(result.getCode(),result.getMsg());
        }
        return null;
    }
}
