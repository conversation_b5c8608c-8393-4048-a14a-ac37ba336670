package com.poizon.scm.wms.command.bas.sorting;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.adapter.scp.repository.ScpWarehouseRepository;
import com.poizon.scm.wms.bas.api.enums.BaseSortingEntityCategoryEnum;
import com.poizon.scm.wms.bas.api.enums.BaseSortingEntityTypeEnum;
import com.poizon.scm.wms.bas.api.sorting.api.BaseSortingEntityApi;
import com.poizon.scm.wms.bas.api.sorting.request.*;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingEntityApiResponse;
import com.poizon.scm.wms.bas.api.sorting.response.BaseSortingEntityPageApiResponse;
import com.poizon.scm.wms.command.bas.sorting.convert.BasSortingConvert;
import com.poizon.scm.wms.command.bas.sorting.request.*;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("admin/bas")
@Api(tags = {"后台-基础资料-拣货库区-command"})
public class BaseSortingEntityController {

    private BasSortingConvert basSortingConvert = BasSortingConvert.INSTANCE;

    @DubboReference(interfaceClass = BaseSortingEntityApi.class, timeout = 5000, check = false)
    private BaseSortingEntityApi baseSortingEntityApi;

    @Resource
    private ScpWarehouseRepository scpWarehouseRepository;

    @PostMapping(value = "/sorting/entity/save", produces = "application/json")
    @ApiOperation(value = "新建设备实体")
    public Result<Void> save(@RequestBody @Valid BaseSortingEntitySaveRequest request) {
        BaseSortingEntitySaveApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.conver3(request);
        baseSortingSceneSaveApiRequest.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setWarehouseName(scpWarehouseRepository.queryNameByCodeAndTenant(OperationUserContextHolder.get().getTenantCode(),
                request.getWarehouseCode()));
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        baseSortingSceneSaveApiRequest.setType(BaseSortingEntityTypeEnum.HARDWARE.name());
        baseSortingSceneSaveApiRequest.setCategory(BaseSortingEntityCategoryEnum.STATION.name());
        return baseSortingEntityApi.saveBaseSortingEntity(baseSortingSceneSaveApiRequest);
    }


    @PostMapping(value = "/sorting/entity/copy", produces = "application/json")
    @ApiOperation(value = "复制设备实体")
    public Result<Void> copy(@RequestBody @Valid BaseSortingEntityCopyRequest request) {
        BaseSortingEntityCopyApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.conver6(request);
        baseSortingSceneSaveApiRequest.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setWarehouseName(scpWarehouseRepository.queryNameByCodeAndTenant(OperationUserContextHolder.get().getTenantCode(),
                request.getWarehouseCode()));
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        baseSortingSceneSaveApiRequest.setType(BaseSortingEntityTypeEnum.HARDWARE.name());
        baseSortingSceneSaveApiRequest.setCategory(BaseSortingEntityCategoryEnum.STATION.name());
        return baseSortingEntityApi.copyBaseSortingEntity(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/entity/sync", produces = "application/json")
    @ApiOperation(value = "同步工作台规则")
    public Result<Void> sync(@RequestBody @Valid BaseSortingEntitySyncRequest request) {
        BaseSortingEntitySyncApiRequest remoteBaseSortingEntitySyncApiRequest = basSortingConvert.convert17(request);
        remoteBaseSortingEntitySyncApiRequest.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        remoteBaseSortingEntitySyncApiRequest.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        remoteBaseSortingEntitySyncApiRequest.setWarehouseName(scpWarehouseRepository.queryNameByCodeAndTenant(OperationUserContextHolder.get().getTenantCode(),
                request.getWarehouseCode()));
        remoteBaseSortingEntitySyncApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        remoteBaseSortingEntitySyncApiRequest.setType(BaseSortingEntityTypeEnum.HARDWARE.name());
        remoteBaseSortingEntitySyncApiRequest.setCategory(BaseSortingEntityCategoryEnum.STATION.name());
        return baseSortingEntityApi.syncBaseSortingEntity(remoteBaseSortingEntitySyncApiRequest);
    }

    @PostMapping(value = "/sorting/entity/update", produces = "application/json")
    @ApiOperation(value = "修改和删除设备实体")
    public Result<Void> update(@RequestBody @Valid BaseSortingEntityUpdateRequest request) {
        BaseSortingEntityUpdateApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert4(request);
        baseSortingSceneSaveApiRequest.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        baseSortingSceneSaveApiRequest.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingEntityApi.updateBaseSortingEntity(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/entity/page", produces = "application/json")
    @ApiOperation(value = "设备实体分页查询")
    public Result<PagingObject<BaseSortingEntityPageApiResponse>> page(@RequestBody @Valid BaseSortingEntityPageRequest request) {
        BaseSortingEntityPageApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert5(request);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        return baseSortingEntityApi.pageList(baseSortingSceneSaveApiRequest);
    }

    @PostMapping(value = "/sorting/entity/station/list", produces = "application/json")
    @ApiOperation(value = "设备实体分页查询")
    public Result<List<BaseSortingEntityApiResponse>> stationList(@RequestBody @Valid BaseSortingEntityListRequest request) {
        BaseSortingEntityQueryApiRequest baseSortingSceneSaveApiRequest = basSortingConvert.convert16(request);
        baseSortingSceneSaveApiRequest.setTenantCode(OperationUserContextHolder.get().getTenantCode());
        baseSortingSceneSaveApiRequest.setType(BaseSortingEntityTypeEnum.HARDWARE.name());
        baseSortingSceneSaveApiRequest.setCategory(BaseSortingEntityCategoryEnum.STATION.name());
        return baseSortingEntityApi.list(baseSortingSceneSaveApiRequest);
    }
}
