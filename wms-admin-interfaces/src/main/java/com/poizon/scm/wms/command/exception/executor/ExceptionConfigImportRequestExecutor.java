package com.poizon.scm.wms.command.exception.executor;

import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.poizon.scm.wms.api.dto.request.apply.ExceptionConfigImportVO;
import com.poizon.scm.wms.api.dto.request.exception.config.ExceptionConfigImportRequest;
import com.poizon.scm.wms.common.auth.OperationUserContext;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.apply.excel.ExceptionConfigInfoExcelListener;
import com.poizon.scm.wms.service.apply.excel.ExceptionConfigInfoExcelService;
import com.poizon.scm.wms.util.Preconditions;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @Description
 * @Date 8/10/22 9:27 PM
 */
@Component
@Slf4j
public class ExceptionConfigImportRequestExecutor extends AbstractCommandExecutor<ExceptionConfigImportRequest,Boolean> {

    @Autowired
    private ExceptionConfigInfoExcelService exceptionConfigInfoExcelService;
    @Autowired
    private ThreadPoolTaskExecutor adminThreadPoolExecutor;

    @Autowired
    private DistributeLockUtil distributeLockUtil;

    private static final int MAX_LINE = 500;

    @Override
    protected void verify(ExceptionConfigImportRequest request) {
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getFileUrl()), "文件地址不能为空！");
    }

    /**
     * 去除地址最后的反斜杠
     */
    private String getFileName(String url) {
        String[] str = url.split("/");
        return str[str.length - 1];
    }

    @Override
    protected Boolean doExecute(ExceptionConfigImportRequest request) {
        boolean lockFlag = false;
        String fileName = getFileName(request.getFileUrl());

        try {
            lockFlag = distributeLockUtil.tryLockForBiz(LockEnum.EXCEPTION_CONFIG_IMPORT_LOCK, fileName);
            if (!lockFlag) {
                throw new WmsException("请不要重复提交");
            }

            log.info("异常配置导入文件地址 url = {}", request.getFileUrl());
            InputStream inputStream = HttpUtil.createGet(request.getFileUrl()).execute().bodyStream();
            if (null == inputStream) {
                throw new WmsException(request.getFileUrl() + " 读取Excel文件流失败");
            }

            OperationUserContext userContext = OperationUserContextHolder.get();
            EasyExcelFactory.read(inputStream)
                    .head(ExceptionConfigImportVO.class)
                    .registerReadListener(ExceptionConfigInfoExcelListener.builder()
                            .exceptionConfigInfoExcelService(exceptionConfigInfoExcelService)
                            .adminThreadPoolExecutor(adminThreadPoolExecutor)
                            .request(request)
                            .maxLine(MAX_LINE)
                            .userId(userContext.getUserId())
                            .userName(userContext.getRealName())
                            .build())
                    .sheet()
                    .doRead();

            return Boolean.TRUE;
        } catch (WmsException e) {
            log.error("异常配置导入失败 url = {}", request.getFileUrl(), e);
            throw e;
        } catch (InterruptedException e) {
            log.error("interrupt, 异常配置导入异常 url = {}", request.getFileUrl(), e);
            Thread.currentThread().interrupt();
            throw new WmsException(e.getMessage());
        } catch (Exception e) {
            log.error("异常配置导入异常 url = {}", request.getFileUrl(), e);
            throw new WmsException(e.getMessage());
        }finally{
            if (lockFlag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.EXCEPTION_CONFIG_IMPORT_LOCK, fileName);
            }
        }
    }
}
