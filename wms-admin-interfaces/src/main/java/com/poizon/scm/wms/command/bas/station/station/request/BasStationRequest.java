package com.poizon.scm.wms.command.bas.station.station.request;

import com.poizon.scm.wms.bas.api.enums.BasStationAreaTypeEnum;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import com.poizon.scm.wms.util.framework.Request;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * date  28/04/2024 周日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationRequest extends Request implements Serializable {
    private static final long serialVersionUID = -2940994253510512465L;

    /**
     * 自增ID
     */
    @NotNull(message = "id不能为空", groups = {ValidateGroups.Update.class, ValidateGroups.Delete.class})
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 仓库编码
     */
    @NotBlank(message = "仓库编码不能为空", groups = {ValidateGroups.Select.class, ValidateGroups.Select2.class, ValidateGroups.Select3.class, ValidateGroups.Add.class})
    private String warehouseCode;

    /**
     * 区域类型
     *
     * @see BasStationAreaTypeEnum
     */
//    @NotBlank(message = "区域类型不能为空", groups = {ValidateGroups.Add.class})
    private String areaType;

    /**
     * 工位分区编码
     * , ValidateGroups.Add.class
     */
    @NotBlank(message = "工位分区编码不能为空", groups = {ValidateGroups.Select2.class})
    private String stationAreaCode;

    /**
     * 工位组编码
     */
    private String stationGroupCode;

    /**
     * 工位编码
     */
    @NotBlank(message = "工位码不能为空", groups = ValidateGroups.Add.class)
    private String stationCode;

    /**
     * 工位名称
     * , ValidateGroups.Add.class
     */
//    @NotBlank(message = "工位名称不能为空", groups = {ValidateGroups.Select.class})
    @Length(max = 100, message = "工位名称长度不能超过100", groups = ValidateGroups.Add.class)
    private String stationName;

    /**
     * 工位横坐标
     */
    private Integer row;

    /**
     * 工位纵坐标
     */
    private Integer column;

    /**
     * 作业属性
     *
     * @see com.poizon.scm.wms .api.enums.StationPropertyEnum
     */
//    @NotNull(message = "工位作业属性不能为空", groups = {ValidateGroups.Add.class})
    private String stationProperty;

    /**
     * 设备类型 目前只允许填“P80”或者为空
     *
     * @see com.poizon.scm.wms.bas.api.enums.BasStationDeviceTypeEnum
     */
    private String deviceType;

}
