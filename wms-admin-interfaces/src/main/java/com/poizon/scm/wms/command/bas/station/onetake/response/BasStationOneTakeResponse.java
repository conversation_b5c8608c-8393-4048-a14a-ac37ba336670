package com.poizon.scm.wms.command.bas.station.onetake.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * date  28/04/2024 周日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationOneTakeResponse implements Serializable {
    private static final long serialVersionUID = 1120916483150950309L;

    /**
     * 待流转数
     */
    private Integer toBeCirculatedCount;

    /**
     * 待质检数
     */
    private Integer toBeInspectedCount;

    /**
     * 待鉴别数
     */
    private Integer toBeIdentifiedCount;

    /**
     * 待打包数
     */
    private Integer toBePackedCount;

    /**
     * 工位分区名称
     */
    private String stationAreaName;

    /**
     * 工位组列表
     */
    private List<MapDto> mapDtoList;

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class MapDto implements Serializable {

        private static final long serialVersionUID = -2815989376238531692L;

        /**
         * 仓库编码
         */
        private String warehouseCode;

        /**
         * 仓库名称
         */
        private String warehouseName;

        /**
         * 工位分区编码
         */
        private String stationAreaCode;

        /**
         * 工位组编码
         */
        private String stationGroupCode;

        /**
         * 工位横坐标
         */
        private Integer row;

        /**
         * 工位纵坐标
         */
        private Integer column;

        /**
         * 工位列表
         */
        private List<StationDto> stationDtoList;


    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class StationDto implements Serializable {
        private static final long serialVersionUID = -9077437303903780596L;

        /**
         * 自增ID
         */
        private Long id;

        /**
         * 仓库编码
         */
        private String warehouseCode;

        /**
         * 工位分区编码
         */
        private String stationAreaCode;

        /**
         * 工位组编码
         */
        private String stationGroupCode;

        /**
         * 工位编码
         */
        private String stationCode;

        /**
         * 工位名称
         */
        private String stationName;

        /**
         * 工位横坐标
         */
        private Integer row;

        /**
         * 工位纵坐标
         */
        private Integer column;

        /**
         * 工位是否已绑定 true-已绑定
         */
        private Boolean bound;

        /**
         * 作业属性
         *
         * @see com.poizon.scm.wms .api.enums.StationPropertyEnum
         */
        private String stationProperty;

        /**
         * 唯一码
         */
        private String uniqueCode;

        /**
         * 唯一码流向
         */
        private String uniqueDirection;

        /**
         * 唯一码流向描述
         */
        private String uniqueDirectionDesc;

        /**
         * 流向备注
         */
        private String directionRemark;

        /**
         * 流向操作时间
         */
        private Date directionOperateTime;

    }

}
