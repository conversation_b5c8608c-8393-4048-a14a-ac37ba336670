package com.poizon.scm.wms.command.bas.station.area.request;

import com.poizon.scm.wms.common.pagesupport.request.PageRequestSupported;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date  23/04/2024 周二
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationAreaSelectRequest extends BasStationAreaRequest implements PageRequestSupported, Serializable {
    private static final long serialVersionUID = 8651686394987792074L;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页面大小
     */
    private Integer pageSize = 20;

    /**
     * 是否count
     */
    private boolean count = true;

    /**
     * 分区编码列表
     */
    private List<String> stationAreaCodeList;

}
