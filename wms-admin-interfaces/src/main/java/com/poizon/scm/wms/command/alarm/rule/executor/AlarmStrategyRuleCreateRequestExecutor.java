package com.poizon.scm.wms.command.alarm.rule.executor;

import com.alibaba.fastjson.JSON;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyRuleQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyRuleDo;
import com.poizon.scm.wms.adapter.alarm.model.param.AlarmStrategyRuleListQueryParam;
import com.poizon.scm.wms.command.alarm.rule.request.AlarmStrategyRuleCreateRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.NewNoGenUtil;
import com.poizon.scm.wms.common.utils.enums.NewNoGenEnum;
import com.poizon.scm.wms.domain.alarm.AlarmRuleTimeContent;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyRuleCommandService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 * @date 2023/1/5
 */
@Component
public class AlarmStrategyRuleCreateRequestExecutor extends AbstractCommandExecutor<AlarmStrategyRuleCreateRequest, Void> {

    @Resource
    private AlarmStrategyRuleCommandService alarmStrategyRuleCommandService;

    @Resource
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Resource
    private AlarmStrategyRuleQueryRepository alarmStrategyRuleQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyRuleCreateRequest request) {
        alarmStrategyRuleCommandService.save(buildCreateStrategyRuleParam(request));
        return null;
    }

    private AlarmStrategyRuleDo buildCreateStrategyRuleParam(AlarmStrategyRuleCreateRequest request) {
        AlarmStrategyRuleDo strategyRuleDo = new AlarmStrategyRuleDo();
        strategyRuleDo.setStrategyNo(request.getStrategyNo());
        strategyRuleDo.setRuleNo(NewNoGenUtil.generateNewNo(NewNoGenEnum.ALARM_STRATEGY_RULE));
        strategyRuleDo.setRuleName(request.getRuleName());
        strategyRuleDo.setRuleDesc(request.getRuleDesc());
        strategyRuleDo.setWarehouseCode(request.getWarehouseCode());
        strategyRuleDo.setStatus(0);
        strategyRuleDo.setRuleContent(JSON.toJSONString(request.getRuleContent()));
        strategyRuleDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        strategyRuleDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        strategyRuleDo.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        strategyRuleDo.setCreatedTime(new Date());
        strategyRuleDo.setTenantCode(OperationUserContextHolder.getScmTenantCode());
        strategyRuleDo.setScheduleType(request.getScheduleType());
        strategyRuleDo.setScheduleTimes(AlarmRuleTimeContent.convertJsonToSave(request.getDuration(),request.getTimePoints()));
        strategyRuleDo.setVersion(1);
        strategyRuleDo.setDeleted(0);
        return strategyRuleDo;
    }

    @Override
    protected void verify(AlarmStrategyRuleCreateRequest request) {
        AlarmStrategyDo strategyDo = alarmStrategyQueryRepository.queryByStrategyNo(request.getStrategyNo());
        if (strategyDo == null) {
            throw new WmsOperationException("预警策略不存在");
        }
        AlarmStrategyRuleListQueryParam queryParam = new AlarmStrategyRuleListQueryParam();
        queryParam.setStrategyNo(request.getStrategyNo());

        List<AlarmStrategyRuleDo> list = alarmStrategyRuleQueryRepository.queryListByStrategyNo(request.getStrategyNo());

        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(item -> item.getRuleName().equals(request.getRuleName()))) {
            throw new WmsOperationException("该预警规则名称已存在");
        }

    }
}
