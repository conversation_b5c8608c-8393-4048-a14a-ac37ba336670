package com.poizon.scm.wms.command.goodsshelf.response;

import com.poizon.scm.wms.bas.api.command.goodsshelf.response.GoodsShelfDetailResponse;
import lombok.Data;

import java.util.List;

@Data
public class GoodsShelfCommodityInfoResponse {
    /**
     * 自增id
     */
    private Long id;
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 库区编码
     */
    private String areaCode;
    /**
     * 货架编码
     */
    private String goodsShelfCode;

    /**
     * 库位信息
     */
    private List<GoodsShelfCommodityInfoResponse.GoodsShelfCommodityDetail> list;

    @Data
    public static class GoodsShelfCommodityDetail{
        /**
         * 货架朝向
         */
        private String goodsShelfSide;
        /**
         * 库位编码
         */
        private String locationCode;
        /**
         * 唯一码（商品编码）
         */
        private String uniqueCode;
        /**
         * skuId
         */
        private String skuId;
        /**
         * 商品名
         */
        private String spuName;
        /**
         * 国标码
         */
        private String standCode;
        /**
         * 附加码
         */
        private String extraCode;
        /**
         * spuId
         */
        private String spuId;
        /**
         * 货号
         */
        private String artNoMain;
        /**
         * 一级分类
         */
        private String categoryNameLevel1;
        /**
         *二级分类
         */
        private String categoryNameLevel2;
        /**
         *三级分类
         */
        private String categoryNameLevel3;
        /**
         *一级分类编码
         */
        private String categoryIdLevel1;
        /**
         *二级分类编码
         */
        private String categoryIdLevel2;
        /**
         *三级分类编码
         */
        private String categoryIdLevel3;
    }
}
