package com.poizon.scm.wms.command.config.convert;

import com.poizon.scm.wms.command.config.request.AddConfigRequest;
import com.poizon.scm.wms.command.config.request.ModifyConfigRequest;
import com.poizon.scm.wms.pojo.sysconfig.AddConfigDetailParam;
import com.poizon.scm.wms.pojo.sysconfig.ModifyConfigDetailParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: tywei
 * @create: 2021-06-03 5:06 下午
 **/
@Mapper
public interface ConvertConfig {
    ConvertConfig INSTANCE = Mappers.getMapper(ConvertConfig.class);

    ModifyConfigDetailParam toParam(ModifyConfigRequest request);

    AddConfigDetailParam toParam(AddConfigRequest request);


}
