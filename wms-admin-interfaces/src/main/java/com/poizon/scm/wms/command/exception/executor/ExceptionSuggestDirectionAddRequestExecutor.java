package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.direction.ExceptionSuggestDirectionAddParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionSuggestDirectionRepository;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionAddRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class ExceptionSuggestDirectionAddRequestExecutor extends AbstractCommandExecutor<ExceptionSuggestDirectionAddRequest, Void> {

    @Autowired
    private WmsExceptionSuggestDirectionRepository wmsExceptionSuggestDirectionRepository;

    @Override
    protected void verify(ExceptionSuggestDirectionAddRequest request) {
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getDirectionCode()), "建议异常处理方向编码不能为空！");
        Preconditions.checkBiz(StringUtils.isNotBlank(request.getDirectionName()), "建议处理方向文案不能为空！");
        Preconditions.checkBiz(request.getDirectionCode().length() <= 50, "建议异常处理方向编码长度不能超过50个字符！");
        Preconditions.checkBiz(request.getDirectionName().length() <= 150, "建议处理方向文案长度不能超过150个字符！");
    }

    @Override
    protected Void doExecute(ExceptionSuggestDirectionAddRequest request) {
        ExceptionSuggestDirectionAddParam addParam = new ExceptionSuggestDirectionAddParam();
        addParam.setTenantId(OperationUserContextHolder.getTenantCode());
        addParam.setDirectionCode(request.getDirectionCode());
        addParam.setDirectionName(request.getDirectionName());
        addParam.setStatus(Optional.ofNullable(request.getStatus()).orElse(1));
        addParam.setSort(request.getSort());
        addParam.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        addParam.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        addParam.setCreatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionSuggestDirectionRepository.add(addParam);
        return null;
    }

}
