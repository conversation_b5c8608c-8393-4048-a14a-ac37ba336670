package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionAddRequest;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionDisableRequest;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionEnableRequest;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionModifyRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description 建议异常处理方式表维护
 */
@RestController
@RequestMapping("/admin/exce/sug/direction")
@Api(tags = "H5-库内异常-建议异常处理方式")
public class ExceptionSuggestDirectionCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 新增
     */
    @ApiOperation("新增建议异常处理方式")
    @PostMapping("/v1/add")
    public Result<Void> addSuggestDirection(@RequestBody ExceptionSuggestDirectionAddRequest request) {
        return bus.handle(request);
    }

    /**
     * 修改
     */
    @ApiOperation("修改建议异常处理方式")
    @PostMapping("/v1/modify")
    public Result<Void> modifySuggestDirection(@RequestBody ExceptionSuggestDirectionModifyRequest request) {
        return bus.handle(request);
    }

    /**
     * 停用
     */
    @ApiOperation("停用建议异常处理方式")
    @PostMapping("/v1/disable")
    public Result<Void> disableSuggestDirection(@RequestBody ExceptionSuggestDirectionDisableRequest request) {
        return bus.handle(request);
    }

    /**
     * 启用
     */
    @ApiOperation("启用建议异常处理方式")
    @PostMapping("/v1/enable")
    public Result<Void> enableSuggestDirection(@RequestBody ExceptionSuggestDirectionEnableRequest request) {
        return bus.handle(request);
    }

}
