package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.task.ExceptionTaskConfirmRequest;
import com.poizon.scm.wms.api.dto.request.exception.task.ExceptionTaskProcessRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description 异常任务单
 */
@RestController
@RequestMapping("/admin/exce/task")
@Api(tags = "H5-库内异常-异常任务单")
public class ExceptionTaskCommandController {

    @Autowired
    private CommandBus bus;

    /**
     * 确认赔付
     */
    @ApiOperation("确认赔付")
    @PostMapping("/v1/confirm")
    public Result<Void> confirm(@RequestBody ExceptionTaskConfirmRequest request) {
        return bus.handle(request);
    }

    /**
     * 处理异常
     */
    @ApiOperation("处理异常")
    @PostMapping("/v1/process")
    public Result<Void> process(@RequestBody ExceptionTaskProcessRequest request) {
        return bus.handle(request);
    }

}
