package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.config.CompensationRuleAddRequest;
import com.poizon.scm.wms.api.dto.request.exception.config.CompensationRuleEditRequest;
import com.poizon.scm.wms.api.dto.request.exception.config.CompensationRulePageRequest;
import com.poizon.scm.wms.api.dto.response.exception.CompensationRulePageResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/exce/compensation/rule")
@Api(tags = "H5-异常管理-DB服务商赔付规则配置")
public class DbCompensationRuleCommandController {

    @Autowired
    private CommandBus bus;

    @ApiOperation("新增配置")
    @PostMapping("/v1/add")
    public Result<Void> add(@RequestBody CompensationRuleAddRequest request) {
        return bus.handle(request);
    }

    @ApiOperation("修改配置 or 状态 ")
    @PostMapping("/v1/edit")
    public Result<Void> edit(@RequestBody CompensationRuleEditRequest request) {
        return bus.handle(request);
    }
}
