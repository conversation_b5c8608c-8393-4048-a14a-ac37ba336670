package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.direction.ExceptionSuggestDirectionStatusParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionSuggestDirectionRepository;
import com.poizon.scm.wms.api.dto.request.exception.direction.ExceptionSuggestDirectionDisableRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExceptionSuggestDirectionDisableRequestExecutor extends AbstractCommandExecutor<ExceptionSuggestDirectionDisableRequest, Void> {

    @Autowired
    private WmsExceptionSuggestDirectionRepository wmsExceptionSuggestDirectionRepository;

    @Override
    protected Void doExecute(ExceptionSuggestDirectionDisableRequest request) {
        ExceptionSuggestDirectionStatusParam param = new ExceptionSuggestDirectionStatusParam();
        param.setId(request.getId());
        param.setDirectionCode(request.getDirectionCode());
        param.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        param.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        param.setUpdatedRealName(OperationUserContextHolder.get().getRealName());

        wmsExceptionSuggestDirectionRepository.disable(param);
        return null;
    }

}
