package com.poizon.scm.wms.command.config;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.config.request.AddConfigRequest;
import com.poizon.scm.wms.command.config.request.ModifyConfigRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: tywei
 * @create: 2021-06-03 4:48 下午
 **/
@Api("配置管理")
@RequestMapping("/admin/config")
@RestController
public class ConfigController {
    @Resource
    private CommandBus commandBus;

    @ApiOperation("更新配置明细")
    @PostMapping("/modify")
    public Result modify(@RequestBody ModifyConfigRequest request) {
        commandBus.handle(request);
        return Result.ofSuccess(null);
    }

    @ApiOperation("新增配置")
    @PostMapping("/add")
    public Result add(@RequestBody AddConfigRequest request) {
        commandBus.handle(request);
        return Result.ofSuccess(null);
    }

}
