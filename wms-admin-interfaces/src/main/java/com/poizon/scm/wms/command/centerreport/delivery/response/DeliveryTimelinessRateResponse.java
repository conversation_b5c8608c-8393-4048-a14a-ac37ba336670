package com.poizon.scm.wms.command.centerreport.delivery.response;

import lombok.Data;

/**
 * 截单时间出库及时率报表
 * @Author: dwq
 * @Date: 2021/1/20 10:28 上午
 */
@Data
public class DeliveryTimelinessRateResponse {

    /**
     * 仓库code
     */
    private String warehouseCode;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 日期
     */
    private String date;
    /**
     * 总单数，开始时间的19点至第二天的19点的总单数
     */
    private Integer total;
    /**
     * 24点出库单数量，总单数中，交接时间在第二天23：59：59秒内的出库单数量
     */
    private Integer num_24;
    /**
     * 24出库及时率，出库单数量/总单数
     *
     */
    private String rate;
}
