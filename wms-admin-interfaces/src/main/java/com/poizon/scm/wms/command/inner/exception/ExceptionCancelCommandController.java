package com.poizon.scm.wms.command.inner.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.consumer.outbound.staiton.request.WorkStationOrderGoodsConsumerRequest;
import com.poizon.scm.wms.domain.inner.exception.param.WorkStationOrderGoodsConsumerParam;
import com.poizon.scm.wms.service.outbound.cancel.WorkStationOrderGoodsService;
import com.poizon.scm.wms.util.json.JsonUtils;
import com.shizhuang.duapp.tenant.TenantContext;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 取消件测试接口类
 * <AUTHOR>
 */
@RestController
@RequestMapping("/exception")
@Slf4j
public class ExceptionCancelCommandController {
    @Autowired
    private CommandBus commandBus;

    @Resource
    private WorkStationOrderGoodsService workStationOrderGoodsService;

    @ApiOperation("消费pink取消件入异常箱mq")
    @PostMapping(value = "/v1/inputWorkStation", produces = "application/json")
    public Result entryOrderCreate(@RequestBody WorkStationOrderGoodsConsumerRequest request){
        return commandBus.handle(request);
    }

    /**
     * curl 'http://127.0.0.1:8888/exception/v1/inputWorkStation2' --data '{"stationNo":"ST0008","operateUserId":102962,"tenantId":"1","warehouseCode":"SH12","deliveryOrderCode":"058319336402747892","tradeOrderNo":"058319336402747892","qty":1,"bizType":6}'
     *
     * @param request
     * @return
     */
    @ApiOperation("消费pink取消件入工位箱")
    @PostMapping(value = "/v2/inputWorkStation2", produces = "application/json")
    public Result inputWorkStation2(@RequestBody WorkStationOrderGoodsConsumerParam request) {
        try {
            TenantContext.setContextId(request.getTenantId());
            OperationUserContextHolder.buildOperationUserContext(1L, "system", "system", request.getWarehouseCode());
            log.info("开始处理pink入工作台容器, request :{}", JsonUtils.serialize(request));
            workStationOrderGoodsService.inputContainer0(request);
        } finally {
            TenantContext.clear();
            OperationUserContextHolder.clear();
        }
        return new Result();
    }
}
