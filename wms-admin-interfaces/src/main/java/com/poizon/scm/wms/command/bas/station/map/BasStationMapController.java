package com.poizon.scm.wms.command.bas.station.map;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.bas.station.map.request.BasStationMapAddRequest;
import com.poizon.scm.wms.command.bas.station.map.request.BasStationMapBindRequest;
import com.poizon.scm.wms.command.bas.station.map.request.BasStationMapDeleteRequest;
import com.poizon.scm.wms.command.bas.station.map.request.BasStationMapUnbindRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import com.poizon.scm.wms.common.validate.ValidateGroups;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * date  24/04/2024 周三
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("/admin/bas/station/map")
public class BasStationMapController {

    @Resource
    private CommandBus commandBus;

    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody @Validated(value = ValidateGroups.Add.class) BasStationMapAddRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody @Validated(value = ValidateGroups.Delete.class) BasStationMapDeleteRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/bind")
    public Result<Boolean> bind(@RequestBody @Validated(value = ValidateGroups.Update.class) BasStationMapBindRequest request) {
        return commandBus.handle(request);
    }

    @PostMapping("/unbind")
    public Result<Boolean> unbind(@RequestBody @Validated(value = ValidateGroups.Update.class) BasStationMapUnbindRequest request) {
        return commandBus.handle(request);
    }


}
