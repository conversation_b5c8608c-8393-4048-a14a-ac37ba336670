package com.poizon.scm.wms.command.bas.executor;

import com.poizon.scm.wms.adapter.bas.config.param.WmsConfigMetaModifyParam;
import com.poizon.scm.wms.adapter.bas.config.repository.WmsConfigMetaAdminRepository;
import com.poizon.scm.wms.bas.api.enums.EnableStatusEnum;
import com.poizon.scm.wms.command.bas.request.WmsBasConfigMetaDisableRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.config.WcsConfig;
import com.poizon.scm.wms.util.Preconditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WmsBasConfigMetaDisableRequestExecutor extends AbstractCommandExecutor<WmsBasConfigMetaDisableRequest, Void> {

    @Autowired
    private WmsConfigMetaAdminRepository wmsConfigMetaAdminRepository;

    @Autowired
    private WcsConfig wcsConfig;

    @Override
    protected void verify(WmsBasConfigMetaDisableRequest request) {
        super.verify(request);
        Preconditions.checkBiz(null != request.getId(), "配置元数据ID不能为空！");

        //元数据先只有开发能操作
        Preconditions.checkBiz(wcsConfig.allowOperateBaseInfoUserId(OperationUserContextHolder.get().getUserId()), "无操作权限！");
    }

    @Override
    protected Void doExecute(WmsBasConfigMetaDisableRequest request) {

        wmsConfigMetaAdminRepository.modify(WmsConfigMetaModifyParam.builder()
                .id(request.getId())
                .mode(EnableStatusEnum.DISABLE.code())
                .updatedUserId(OperationUserContextHolder.get().getUserId())
                .updatedRealName(OperationUserContextHolder.get().getRealName())
                .build());

        return null;
    }

}
