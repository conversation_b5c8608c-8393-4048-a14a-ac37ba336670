package com.poizon.scm.wms.command.bas.station.station.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationApi;
import com.poizon.scm.wms.bas.api.command.station.request.BasStationRequest;
import com.poizon.scm.wms.command.bas.station.station.request.BasStationDeleteRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * date  2024/5/16 周四
 */
@Slf4j
@Component
public class BasStationDeleteRequestExecutor extends AbstractCommandExecutor<BasStationDeleteRequest, Boolean> {

    @DubboReference(check = false)
    private BasStationApi basStationApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected Boolean doExecute(BasStationDeleteRequest request) {
        BasStationRequest basStationRequest = BeanCopyUtils.copyProperties(request, BasStationRequest.class);
        basStationRequest.setTenantId(OperationUserContextHolder.getTenantCode());

        Result<Boolean> result = LogUtils.logRemoteInvoke(log, basStationRequest, basStationApi::delete);

        return Optional.ofNullable(result)
                .map(Result::getData)
                .orElse(Boolean.FALSE);
    }
}
