package com.poizon.scm.wms.command.bas.sorting.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BaseSortingSceneSaveRequest {

    @ApiModelProperty("仓库")
    @NotBlank(message = "仓库不能为空")
    private String warehouseCode;

    @ApiModelProperty("环节")
    @NotBlank(message = "环节不能为空")
    private String scene;

    @ApiModelProperty("编码")
    @NotBlank(message = "编码不能为空")
    private String code;

    @ApiModelProperty("名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty("作业模式")
    private Integer opMode;

    @ApiModelProperty("扩展属性")
    private String feature;
}
