package com.poizon.scm.wms.command.exception;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.dto.request.exception.errorlabel.ExceptionErrorLabelAddRequest;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author： LMF
 * @date： 2022/8/10 11:09 AM
 * @description： 失误标签
 * @modifiedBy：
 * @version: 1.0
 */
@RestController
@RequestMapping("/admin/exce/label")
@Api(tags = "H5-库内异常-失误标签配置")
public class ExceptionErrorLabelCommandController {

    @Resource
    private CommandBus bus;

    @ApiOperation("新增失误标签")
    @PostMapping("/v1/add")
    public Result<String> add(@RequestBody @Valid ExceptionErrorLabelAddRequest request){
        return bus.handle(request);
    }

}
