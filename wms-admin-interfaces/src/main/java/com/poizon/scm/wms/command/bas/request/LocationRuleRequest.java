package com.poizon.scm.wms.command.bas.request;

import com.poizon.scm.wms.api.enums.BasLocationSpecsEnum;
import com.poizon.scm.wms.util.enums.LocationIsMultiItemEnum;
import com.poizon.scm.wms.util.enums.LocationIsMultiSkuEnum;
import com.poizon.scm.wms.util.enums.LocationPropertyEnum;
import com.poizon.scm.wms.util.framework.Request;
import com.poizon.scm.wms.util.framework.RequestException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("库位生成器规则模型")
public class LocationRuleRequest extends Request implements Serializable {

    /**
     * 仓库code
     */
    @ApiModelProperty(value = "仓库编码", required = true, notes = "查询仓库列表来源")
    @NotBlank(message = "仓库编码不能为空")
    private String warehouseCode;


    /**
     * 仓库code
     */
    @ApiModelProperty(value = "仓库编码", required = true, notes = "查询仓库列表来源")
    @NotBlank(message = "库区编码不能为空")
    private String areaCode;


    /**
     * 库位类型，无瑕疵/轻度瑕疵/中度瑕疵/严重瑕疵/不可售卖/系统暂存
     */
    @ApiModelProperty(value = "库位类型", required = true)
    @NotBlank(message = "库位类型不能为空")
    private String locationType;


    @ApiModelProperty(value = "库位类型", required = true)
    @NotNull(message = "库位属性不能为空")
    private Byte locationProperty;

    @ApiModelProperty(value = "库位规格", required = true)
    @NotBlank(message = "库位规格不能为空")
    private String locationSpecs;

    @ApiModelProperty(value = "头部通道是否单列 true:是 false 否", required = true)
    @NotNull(message = "头部通道面数不能为空")
    private Boolean firstPassSingleRow;


    @ApiModelProperty(value = "尾部通道面数", required = true)
    @NotNull(message = "尾部通道面数不能为空")
    private Boolean lastPassSingleRow;


    @ApiModelProperty(value = "通道开始", required = true)
    @NotNull(message = "通道号不能为空")
    @Min(value = 1L, message = "通道号开始不能小于1")
    @Max(value = 50L, message = "通道号开始不能超过100L")
    private Integer startPassNum;


    @ApiModelProperty(value = "通道结束", required = true)
    @NotNull(message = "通道号不能为空")
    @Min(value = 1L, message = "通道号结束不能小于1")
    @Max(value = 50L, message = "通道号结束不能超过100L")
    private Integer endPassNum;


    @ApiModelProperty(value = "排范围开始值", required = true)
    @NotNull(message = "排范围开始值不能为空")
    @Min(value = 1L, message = "排开始不能小于1")
    @Max(value = 100L, message = "排开始不能超过100L")
    private Integer startRowNum;


    @ApiModelProperty(value = "排范围结束值", required = true)
    @NotNull(message = "排范围结束值不能为空")
    @Min(value = 1L, message = "排结束不能小于1")
    @Max(value = 100L, message = "排结束不能超过100L")
    private Integer endRowNum;


    @ApiModelProperty(value = "列范围开始值", required = true)
    @NotNull(message = "列范围开始值不能为空")
    @Min(value = 1L, message = "列开始不能小于1")
    @Max(value = 99L, message = "列开始不能超过100L")
    private Integer startColumnNum;


    @ApiModelProperty(value = "列范围结束值", required = true)
    @NotNull(message = "列范围结束值不能为空")
    @Min(value = 1L, message = "列结束不能小于1")
    @Max(value = 99L, message = "列结束不能超过100L")
    private Integer endColumnNum;


    @ApiModelProperty(value = "层范围开始值", required = true)
    @NotNull(message = "层范围开始值不能为空")
    @Min(value = 1L, message = "层开始不能小于1")
    @Max(value = 10L, message = "层开始不能超过10L")
    private Integer startLevelNum;


    @ApiModelProperty(value = "层范围结束值", required = true)
    @NotNull(message = "层范围结束值不能为空")
    @Min(value = 1L, message = "层开始不能小于1")
    @Max(value = 10L, message = "层开始不能超过10L")
    private Integer endLevelNum;


    @ApiModelProperty(value = "长", required = true)
    @NotBlank(message = "长不能为空")
    private String length;


    @ApiModelProperty(value = "宽", required = true)
    @NotBlank(message = "宽不能为空")
    private String width;

    @ApiModelProperty(value = "高", required = true)
    @NotBlank(message = "高不能为空")
    private String height;


    @ApiModelProperty(value = "是否启用", required = true)
    @NotNull(message = "是否启用不能为空")
    private Integer isActive;


    @ApiModelProperty(value = "最大商品件数", required = true)
    @NotNull(message = "最大商品件数不能为空")
    private Integer maxCommodityNum;


    /**
     * 作业区编码
     */
    @ApiModelProperty(value = "作业区编码", required = true, notes = "")
    @NotBlank(message = "作业区编码不能为空")
    private String workZoneCode;


    /**
     * 是否允许sku混放，0允许，1不允许
     */
    @ApiModelProperty(value = "是否允许商品混放，0允许，1不允许", required = true)
    @Min(value = 0, message = "是否允许商品混放传参不规范，请联系开发解决")
    @Max(value = 1, message = "是否允许商品混放传参不规范，请联系开发解决")
    @NotNull(message = "是否允许商品混放不可为空")
    private Integer isMultiSku;

    /**
     * 是否允许效期混放，0允许，1不允许
     */
    private Integer isMultiEffective;

    @ApiModelProperty(value = "库位连接符号")
    private String connectionSymbol = StringUtils.EMPTY;

    @ApiModelProperty(value = "是否包含连接符号")
    @NotNull(message = "是否包含连接符号不能为空")
    private Boolean hasConnectionSymbol;

    private String warehouseName;

    private String areaName;

    private String workZoneName;

    /**
     * 指定业务类型，多选逗号隔开
     */
    private List<Integer> bizTypeList;

    /**
     * 是否允许混货主 0允许，1不允许
     */
    private Integer isMultiOwner;

    /**
     *  是否允许混质量等级 0允许，1不允许
     */
    private Integer isMultiQualityLevel;

    /**
     * 最大SKU数
     */
    private Integer maxSkuNum;

    /**
     * ABC分类(A品,B品,C品)
     */
    @ApiModelProperty(value="ABC分类(A品,B品,C品)")
    private String abcType;

    private Integer isMultiBatch;

    @Override
    public void verify() throws RequestException {
        //这边就不同判空了 基本上在Valid组件判过了。 这边判断值合不合理

        //目前只支持 0/1
        if (this.getIsActive() != 0 && this.getIsActive() != 1) {
            throw new RequestException("是否启用值越界");
        }

        if (this.getLocationProperty() == null || LocationPropertyEnum.getMessage(this.getLocationProperty()) == null) {
            throw new RequestException("库位属性越界");
        }

        if (this.getIsMultiEffective() != null && LocationIsMultiItemEnum.getEnum(this.getIsMultiEffective()) == null) {
            throw new RequestException("是否允许批次混放越界");
        }

        if (this.getIsMultiSku() != null && LocationIsMultiSkuEnum.getEnum(this.getIsMultiSku()) == null) {
            throw new RequestException("是否允许商品混放越界");
        }
        if (null == BasLocationSpecsEnum.getDescByCode(this.locationSpecs)) {
            throw new RequestException("库位规格越界");
        }

        if (startPassNum > endPassNum) {
            throw new RequestException("通道范围开始大于结束范围");
        }

        if (startRowNum > endRowNum) {
            throw new RequestException("排道范围开始大于结束范围");
        }

        if (startLevelNum > endLevelNum) {
            throw new RequestException("层范围开始大于结束范围");
        }

        if (startColumnNum > endColumnNum) {
            throw new RequestException("列范围开始大于结束范围");
        }

    }

}
