package com.poizon.scm.wms.command.outbound.pack.executor;

import com.poizon.scm.wms.adapter.outbound.returngoods.model.WmsPackDetailAndSnInfo;
import com.poizon.scm.wms.adapter.outbound.returngoods.repository.WmsPackDetailRepository;
import com.poizon.scm.wms.command.outbound.pack.request.PackDetailQueryRequest;
import com.poizon.scm.wms.command.outbound.pack.response.PackDetailQueryResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.DateUtils;
import com.poizon.scm.wms.domain.commodity.response.SkuBarcodeRspDomain;
import com.poizon.scm.wms.service.commodity.mdmv2.IScpSkuBarCodeServiceV2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: dwq
 * @Date: 2021/11/11 11:21 上午
 */
@Service
public class PackDetailQueryRequestExecutor extends AbstractCommandExecutor<PackDetailQueryRequest, List<PackDetailQueryResponse>> {

    @Autowired
    private WmsPackDetailRepository wmsPackDetailRepository;
    @Autowired
    private IScpSkuBarCodeServiceV2 scpSkuBarCodeServiceV2;
    @Override
    protected List<PackDetailQueryResponse> doExecute(PackDetailQueryRequest request) {
        List<WmsPackDetailAndSnInfo> packDetailInfoList = wmsPackDetailRepository.querySnNoByPackNo(request.getPackNo());
        return buildResp(packDetailInfoList);
    }
    private List<PackDetailQueryResponse> buildResp(List<WmsPackDetailAndSnInfo> packDetailDos){
        List<PackDetailQueryResponse> responses = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(packDetailDos)){
            packDetailDos.forEach(e->{
                PackDetailQueryResponse response= new PackDetailQueryResponse();
                if(StringUtils.isNotBlank(e.getUniqueCode())){
                    response.setCode(e.getUniqueCode());
                    response.setQty(e.getQty());
                }else{
                    if(StringUtils.isNotBlank(e.getSnNo())) {
                        response.setCode(e.getSnNo());
                        response.setQty(1);
                    }else {
                        SkuBarcodeRspDomain barcode = scpSkuBarCodeServiceV2.querySkuBarcodeBySkuId(OperationUserContextHolder.getScmTenantCode(), e.getSkuId());
                        if (barcode != null) {
                            response.setCode(barcode.getBarCode());
                        }
                        response.setQty(e.getQty());
                    }
                }
                response.setCreatedDate(DateUtils.dateToString(e.getCreatedTime()));
                response.setGoodsTitle(e.getGoodsTitle());

                responses.add(response);
            });
        }
        return responses;

    }
}
