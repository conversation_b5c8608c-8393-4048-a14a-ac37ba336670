package com.poizon.scm.wms.command.common;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.bas.sdk.bizlog.BizLogApiDTO;
import com.poizon.scm.wms.bas.sdk.bizlog.BizLogApiQuery;
import com.poizon.scm.wms.bas.sdk.bizlog.BizLogClient;
import com.poizon.scm.wms.command.common.response.WmsBizLogQueryResponse;
import com.poizon.scm.wms.common.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("admin/bizLog")
@RestController
@Slf4j
@Api(tags = {"后台-WMS业务日志查询"})
public class WmsBizLogQueryController {

    @Resource
    private BizLogClient bizLogClient;


    /**
     * admin/bizLog/queryLog
     * @param request
     * @return
     */
    @ApiOperation(value = "库内业务日志查询", produces = "application/json")
    @PostMapping(value = "queryLog", produces = "application/json")
    public Result<PagingObject<WmsBizLogQueryResponse>> queryLog(@RequestBody BizLogApiQuery request) {
        Result<PagingObject<BizLogApiDTO>> result1 = bizLogClient.query(request);
        PagingObject<BizLogApiDTO> pagingObject1 = result1.getData();

        Result<PagingObject<WmsBizLogQueryResponse>> result2 = new Result<>();
        if (pagingObject1 != null) {
            PagingObject<WmsBizLogQueryResponse> pagingObject2 = new PagingObject<>();
            List<BizLogApiDTO> list1 = pagingObject1.getContents();
            pagingObject2.setContents(list1.stream().map(this::convert).collect(Collectors.toList()));
            pagingObject2.setTotal(pagingObject1.getTotal());
            pagingObject2.setPageSize(pagingObject1.getPageSize());
            pagingObject2.setPageNum(pagingObject1.getPageNum());
            pagingObject2.setPages(pagingObject1.getPages());
            result2.setData(pagingObject2);
        }

        result2.setCode(result1.getCode());
        result2.setMsg(result1.getMsg());
        return result2;
    }

    private WmsBizLogQueryResponse convert(BizLogApiDTO bizLogApiDTO) {
        WmsBizLogQueryResponse wmsOperateLogResponse = new WmsBizLogQueryResponse();
        wmsOperateLogResponse.setOperationContent(bizLogApiDTO.getOperationContent());
        wmsOperateLogResponse.setOperationTime(bizLogApiDTO.getOperationTime());
        wmsOperateLogResponse.setOperationTimeStr(DateUtils.dateToString(bizLogApiDTO.getOperationTime(), DateUtils.FORMAT_TIME));
        wmsOperateLogResponse.setOperationType(bizLogApiDTO.getOperationType());
        wmsOperateLogResponse.setOperationTypeDesc(bizLogApiDTO.getOperationTypeDesc());
        wmsOperateLogResponse.setOperationUserRealName(bizLogApiDTO.getOperationUserRealName());
        return wmsOperateLogResponse;
    }

}
