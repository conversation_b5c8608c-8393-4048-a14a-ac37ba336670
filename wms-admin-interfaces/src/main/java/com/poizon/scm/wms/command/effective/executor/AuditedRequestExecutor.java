package com.poizon.scm.wms.command.effective.executor;

import com.poizon.scm.wms.adapter.effective.model.SpuEffectiveRecordDo;
import com.poizon.scm.wms.adapter.effective.repository.SpuEffectiveRecordRepository;
import com.poizon.scm.wms.api.command.effective.request.AuditedRequest;
import com.poizon.scm.wms.api.enums.SpuEffectiveStatusEnum;
import com.poizon.scm.wms.common.enums.LockEnum;
import com.poizon.scm.wms.common.exceptions.WmsException;
import com.poizon.scm.wms.common.exceptions.WmsExceptionCode;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.service.effective.EffectiveService;
import com.poizon.scp.framwork.cache.util.DistributeLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 效期审核
 *
 * @author: tywei
 * @create: 2021-03-11 10:19 上午
 **/
@Slf4j
@Component
public class AuditedRequestExecutor
        extends AbstractCommandExecutor<AuditedRequest, Void> {
    @Autowired
    private EffectiveService effectiveService;
    @Autowired
    private SpuEffectiveRecordRepository spuEffectiveRecordRepository;
    @Autowired
    private DistributeLockUtil distributeLockUtil;

    @Override
    protected Void doExecute(AuditedRequest request) {
        List<SpuEffectiveRecordDo> records = spuEffectiveRecordRepository.findBySkuIds(
                request.getSkuIds(),
                SpuEffectiveStatusEnum.INIT.getStatus());
        if (CollectionUtils.isEmpty(records)) {
            throw new WmsOperationException(WmsExceptionCode.SPU_EFFECTIVE_AUDITED_ERROR,
                    String.format("skuId:%s无待审核记录", StringUtils.join(request.getSkuIds(), ",")));
        }
        records.forEach(this::running);
        return null;
    }

    private void running(SpuEffectiveRecordDo recordDo) {
        boolean flag = false;
        try {
            if (!distributeLockUtil.tryLockForBiz(LockEnum.SPU_EFFECTIVE_AUDITED_LOCK, recordDo.getSpuId())) {
                throw new WmsOperationException(WmsExceptionCode.GET_LOCK_FAILED);
            }
            flag = true;
            running0(recordDo);
        } catch (WmsException | WmsOperationException e) {
            throw e;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("失败", e);
            throw new WmsException(WmsExceptionCode.UNKNOWN_ERROR, e);
        } catch (Exception e) {
            log.error("失败", e);
            throw new WmsException(WmsExceptionCode.UNKNOWN_ERROR, e);
        } finally {
            if (flag) {
                distributeLockUtil.releaseLockForBiz(LockEnum.SPU_EFFECTIVE_AUDITED_LOCK, recordDo.getSpuId());
            }
        }
    }

    private void running0(SpuEffectiveRecordDo effectiveRecordDo) {
        if (Objects.isNull(effectiveRecordDo.getExpiryDate()) || effectiveRecordDo.getExpiryDate() <=0) {
            throw new WmsOperationException(
                    WmsExceptionCode.SPU_EFFECTIVE_AUDITED_ERROR,
                    String.format("商品有效期必需不为空且大于0. skuId:[%s], 有效期:[%d] 请重新修改",
                            effectiveRecordDo.getSkuId(), effectiveRecordDo.getExpiryDate())
            );
        }
        effectiveService.audited(effectiveRecordDo);
    }
}
