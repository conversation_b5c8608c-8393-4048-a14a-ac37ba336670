package com.poizon.scm.wms.command.exception.executor;

import com.poizon.scm.wms.adapter.exception.param.rpc.ModuleConfigModifyParam;
import com.poizon.scm.wms.adapter.exception.repository.rpc.WmsExceptionConfigAdminRepository;
import com.poizon.scm.wms.command.exception.request.ModuleConfigModifyActivityRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ModuleConfigModifyActivityRequestExecutor extends AbstractCommandExecutor<ModuleConfigModifyActivityRequest, Void> {

    @Autowired
    private WmsExceptionConfigAdminRepository wmsExceptionConfigAdminRepository;

    @Override
    protected Void doExecute(ModuleConfigModifyActivityRequest request) {

        ModuleConfigModifyParam param = new ModuleConfigModifyParam();
        param.setId(request.getId());
        param.setUpdatedUserId(OperationUserContextHolder.get().getUserId());
        param.setUpdatedUserName(OperationUserContextHolder.get().getUserName());
        param.setUpdatedRealName(OperationUserContextHolder.get().getRealName());
        param.setActivity(request.getActivity());
        wmsExceptionConfigAdminRepository.modifyModuleActivity(param);
        return null;
    }

}
