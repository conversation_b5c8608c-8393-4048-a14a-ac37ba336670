package com.poizon.scm.wms.command.goodsshelf.executor;

import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.api.enums.BasAreaPropertiesEnum;
import com.poizon.scm.wms.bas.api.command.area.BaseAreaApi;
import com.poizon.scm.wms.bas.api.command.area.response.BasAreaResponse;
import com.poizon.scm.wms.bas.api.command.goodsshelf.GoodsShelfAdminApi;
import com.poizon.scm.wms.bas.api.command.goodsshelf.request.GoodsShelfSideUnbindApiRequest;
import com.poizon.scm.wms.bas.api.enums.GoodsShelfSideEnum;
import com.poizon.scm.wms.command.goodsshelf.request.GoodsShelfSideUnbindRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.util.util.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GoodsShelfSideUnbindRequestExecutor extends AbstractCommandExecutor<GoodsShelfSideUnbindRequest,Boolean> {
    @DubboReference(check = false)
    private GoodsShelfAdminApi goodsShelfAdminApi;
    @DubboReference(check = false)
    private BaseAreaApi baseAreaApi;
    @Override
    protected Boolean doExecute(GoodsShelfSideUnbindRequest request) {
        checkParam(request);
        Result<BasAreaResponse> basAreaResponseResult = baseAreaApi.selectByWarehouseAreaCode(OperationUserContextHolder.getTenantCode(), request.getWarehouseCode(), request.getAreaCode());
        if(!Result.isSuccess(basAreaResponseResult)){
            throw new WmsOperationException(basAreaResponseResult.getMsg());
        }
        if(ObjectUtils.isEmpty(basAreaResponseResult.getData())){
            throw new WmsOperationException("库区不存在");
        }
        String areaProperties = basAreaResponseResult.getData().getAreaProperties();
        if(!BasAreaPropertiesEnum.AGV.getCode().equals(areaProperties)){
            throw new WmsOperationException("选择的库区非AGV库区");
        }
        GoodsShelfSideUnbindApiRequest apiRequest = new GoodsShelfSideUnbindApiRequest();
        BeanUtils.copyProperties(request,apiRequest);
        apiRequest.setTenantCode(OperationUserContextHolder.getTenantCode());
        Result<Boolean> apiResult = goodsShelfAdminApi.unbindBasLocation(apiRequest);
        if(!Result.isSuccess(apiResult)){
            throw new WmsOperationException(apiResult.getMsg());
        }
        return apiResult.getData();
    }
    private void checkParam(GoodsShelfSideUnbindRequest request){
        if(StringUtils.isBlank(request.getWarehouseCode())){
            throw new WmsOperationException("请选择仓库");
        }
        if(StringUtils.isBlank(request.getAreaCode())){
            throw new WmsOperationException("请选择库区");
        }
        if(StringUtils.isBlank(request.getGoodsShelfCode())){
            throw new WmsOperationException("请输入货架");
        }
        if(StringUtils.isBlank(request.getGoodsShelfSide())|| !GoodsShelfSideEnum.containsSide(request.getGoodsShelfSide())){
            throw new WmsOperationException("货架面为空或不存在");
        }
        if(ObjectUtils.isEmpty(request.getGoodsShelfColumn())||ObjectUtils.isEmpty(request.getGoodsShelfLevel())||
                request.getGoodsShelfColumn()<= NumberUtils.INTEGER_ZERO||request.getGoodsShelfLevel()<=NumberUtils.INTEGER_ZERO){
            throw new WmsOperationException("要绑定的位置错误");
        }
        if(StringUtils.isBlank(request.getLocationCode())){
            throw new WmsOperationException("请输入要解绑的库位");
        }
    }
}
