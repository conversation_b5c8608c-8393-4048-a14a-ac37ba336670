package com.poizon.scm.wms.command.bas.station.station.executor;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.poizon.fusion.common.model.Result;
import com.poizon.fusion.utils.BeanCopyUtils;
import com.poizon.scm.wms.bas.api.command.station.BasStationApi;
import com.poizon.scm.wms.command.bas.station.station.request.BasStationQueryPageRequest;
import com.poizon.scm.wms.command.bas.station.station.response.BasStationResponse;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.pagesupport.response.PageResult;
import com.poizon.scm.wms.common.utils.LogUtils;
import com.poizon.scm.wms.util.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date  2024/5/16 周四
 */
@Slf4j
@Component
public class BasStationQueryPageRequestExecutor extends AbstractCommandExecutor<BasStationQueryPageRequest, PageResult<BasStationResponse>> {

    @DubboReference(check = false)
    private BasStationApi basStationApi;

    /**
     * 执行
     *
     * @param request 命令，输入数据
     * @return R 泛型输出数据
     */
    @Override
    protected PageResult<BasStationResponse> doExecute(BasStationQueryPageRequest request) {
        com.poizon.scm.wms.bas.api.command.station.request.BasStationQueryPageRequest basStationQueryPageRequest = BeanCopyUtils.copyProperties(request, com.poizon.scm.wms.bas.api.command.station.request.BasStationQueryPageRequest.class);
        basStationQueryPageRequest.setTenantId(OperationUserContextHolder.getTenantCode());
        basStationQueryPageRequest.setDeviceType(request.getDeviceType());

        log.info("basStationQueryPageRequest:{}", JSON.toJSONString(basStationQueryPageRequest));

        if (StringUtils.isBlank(request.getStationCode())) {
            basStationQueryPageRequest.setStationCode(null);
        }

        Result<com.poizon.scm.wms.bas.api.common.response.PageResult<com.poizon.scm.wms.bas.api.command.station.response.BasStationResponse>>
                result = LogUtils.logRemoteInvoke(log, basStationQueryPageRequest, basStationApi::queryPage);

        return Optional.ofNullable(result)
                .map(Result::getData)
                .map(basStationResponsePageResult -> {
                    PageResult<BasStationResponse> pageResult = new PageResult<>();
                    pageResult.buildPageResult(
                            basStationResponsePageResult.getDataList().stream().map(basStationResponse -> BeanCopyUtils.copyProperties(basStationResponse, BasStationResponse.class)).collect(Collectors.toList()),
                            basStationResponsePageResult.getPageNum(),
                            basStationResponsePageResult.getPageSize(),
                            basStationResponsePageResult.getPages(),
                            basStationResponsePageResult.getTotal());
                    return pageResult;
                }).orElse(new PageResult<>());
    }
}
