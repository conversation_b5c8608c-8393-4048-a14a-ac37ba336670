package com.poizon.scm.wms.command.antifake.response;

import com.poizon.scm.wms.adapter.third.outbound.pack.result.PinkClaspResult;
import lombok.Data;

import java.util.List;

/**
 * pink防伪证扣复用信息
 *
 * <AUTHOR>
 * @date 2024/10/09
 */
@Data
public class PinkAntifakeResponse {
    /**
     * 可复用证书数量
     */
    private int avaReuseCert;

    /**
     * 可复用防伪扣数量
     */
    private int avaReuseClasp;

    /**
     * 证书
     */
    private PinkClaspResult certVo;

    /**
     * 扣列表
     */
    private List<PinkClaspResult> claspVoList;

}
