package com.poizon.scm.wms.command.bas.station.station.request;

import com.poizon.scm.wms.common.pagesupport.request.PageRequestSupported;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date  2024/5/16 周四
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BasStationQueryPageRequest extends BasStationQueryRequest implements PageRequestSupported, Serializable {
    private static final long serialVersionUID = -2537492759219269535L;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页面大小
     */
    private Integer pageSize = 20;

    /**
     * 是否count
     */
    private boolean count = true;

}
