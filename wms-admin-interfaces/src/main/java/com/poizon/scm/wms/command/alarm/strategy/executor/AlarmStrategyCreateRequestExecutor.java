package com.poizon.scm.wms.command.alarm.strategy.executor;

import com.poizon.scm.wms.adapter.alarm.Repository.AlarmStrategyQueryRepository;
import com.poizon.scm.wms.adapter.alarm.model.AlarmStrategyDo;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyCreateRequest;
import com.poizon.scm.wms.common.auth.OperationUserContextHolder;
import com.poizon.scm.wms.common.exceptions.WmsOperationException;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import com.poizon.scm.wms.common.utils.NewNoGenUtil;
import com.poizon.scm.wms.common.utils.enums.NewNoGenEnum;
import com.poizon.scm.wms.domain.alarm.AlarmStrategyCommandService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Component
public class AlarmStrategyCreateRequestExecutor extends AbstractCommandExecutor<AlarmStrategyCreateRequest, Void> {

    @Resource
    private AlarmStrategyCommandService alarmStrategyCommandService;
    @Resource
    private AlarmStrategyQueryRepository alarmStrategyQueryRepository;

    @Override
    protected Void doExecute(AlarmStrategyCreateRequest request) {
        alarmStrategyCommandService.save(buildStrategyCreateParam(request));
        return null;
    }

    @Override
    protected void verify(AlarmStrategyCreateRequest request) {
        //校验相同仓库策略名称不能相同
        List<AlarmStrategyDo> list = alarmStrategyQueryRepository.queryStrategyByNameAndWarehouseCode(request.getStrategyName(),
                request.getWarehouseCode());
        if (CollectionUtils.isNotEmpty(list)) {
            throw new WmsOperationException("预警策略名称重复，请修改");
        }
    }

    private AlarmStrategyDo buildStrategyCreateParam(AlarmStrategyCreateRequest request) {
        AlarmStrategyDo saveStrategyDo = new AlarmStrategyDo();
        saveStrategyDo.setStrategyNo(NewNoGenUtil.generateNewNo(NewNoGenEnum.ALARM_STRATEGY));
        saveStrategyDo.setStrategyName(request.getStrategyName().trim());
        saveStrategyDo.setStrategyDesc(request.getStrategyDesc().trim());
        saveStrategyDo.setType(request.getType());
        saveStrategyDo.setStatus(0);
        saveStrategyDo.setWarehouseCode(request.getWarehouseCode());
        saveStrategyDo.setCreatedUserId(OperationUserContextHolder.get().getUserId());
        saveStrategyDo.setCreatedUserName(OperationUserContextHolder.get().getUserName());
        saveStrategyDo.setCreatedRealName(OperationUserContextHolder.get().getRealName());
        saveStrategyDo.setCreatedTime(new Date());
        saveStrategyDo.setTenantCode(OperationUserContextHolder.getTenantCode());
        saveStrategyDo.setVersion(1);
        saveStrategyDo.setDeleted(0);
        return saveStrategyDo;
    }
}