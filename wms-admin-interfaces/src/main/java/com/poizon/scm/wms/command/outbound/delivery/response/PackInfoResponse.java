package com.poizon.scm.wms.command.outbound.delivery.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: dwq
 * @Date: 2021/11/12 2:23 下午
 */
@Data
@ApiModel("包裹信息")
public class PackInfoResponse {

    @ApiModelProperty(value = "包裹号")
    private String packNo;
    @ApiModelProperty(value = "MARK码")
    private String packSeqNum;

    @ApiModelProperty(value = "原运单号")
    private String subExpressNo;
}
