package com.poizon.scm.wms.command.alarm.strategy;

import com.poizon.fusion.common.model.PagingObject;
import com.poizon.fusion.common.model.Result;
import com.poizon.scm.wms.command.alarm.strategy.request.AlarmStrategyListQueryRequest;
import com.poizon.scm.wms.command.alarm.strategy.response.AlarmStrategyListQueryResponse;
import com.poizon.scm.wms.common.framework.CommandBus;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
@Api(tags = {"PC-预警策略-query"})
@RequestMapping("/admin/alarm/strategy")
@RestController
public class AlarmStrategyQueryController {
    @Resource
    private CommandBus commandBus;

    @ApiOperation("策略列表查询")
    @PostMapping("/list")
    public Result<PagingObject<AlarmStrategyListQueryResponse>> list(@RequestBody @Valid AlarmStrategyListQueryRequest request) {
        return commandBus.handle(request);
    }



}
