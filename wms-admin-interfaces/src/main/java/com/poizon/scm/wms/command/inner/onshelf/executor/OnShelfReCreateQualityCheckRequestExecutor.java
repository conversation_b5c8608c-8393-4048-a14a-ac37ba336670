package com.poizon.scm.wms.command.inner.onshelf.executor;

import com.poizon.scm.wms.adapter.upper.model.param.UpperReCreateQualityCheckParam;
import com.poizon.scm.wms.adapter.upper.repository.rpc.UpperRpcRepository;
import com.poizon.scm.wms.command.inner.onshelf.request.OnShelfReCreateQualityCheckRequest;
import com.poizon.scm.wms.common.framework.AbstractCommandExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/5/9 3:49 PM
 * @description
 */
@Component
public class OnShelfReCreateQualityCheckRequestExecutor
        extends AbstractCommandExecutor<OnShelfReCreateQualityCheckRequest, Void> {

    @Resource
    private UpperRpcRepository upperRpcRepository;

    @Override
    protected Void doExecute(OnShelfReCreateQualityCheckRequest request) {
        UpperReCreateQualityCheckParam param = new UpperReCreateQualityCheckParam();
        param.setTaskNo(request.getTaskNo());
        upperRpcRepository.reCreateQualityCheck(param);
        return null;
    }
}
