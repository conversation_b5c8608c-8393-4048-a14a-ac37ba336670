package com.poizon.fusion.alicloud.rocketmq.configuration;

import com.poizon.fusion.dmq.consumer.DmqProcessor;
import com.poizon.fusion.dmq.consumer.holder.RocketMqConsumerHolder;
import com.poizon.fusion.dmq.consumer.holder.RocketMqOrderConsumerHolder;
import com.poizon.fusion.dmq.shutdown.ShutDownAbleMqBean;
import com.shizhuang.duapp.shutdown.ShutdownAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * MQ优雅下线
 */
@Slf4j
@Component
public class MQShutdownManager extends ShutdownAbstract implements DisposableBean {

    private boolean SHUTDOWN_STATE = false;

    public Collection<ShutDownAbleMqBean> getShutDownAbleMqBeans() {
        return ApplicationContentUtil.getBeansOfType(ShutDownAbleMqBean.class).values();
    }

    public MQShutdownManager() {
        log.info("MQShutdownManager init success");
    }

    @Override
    public int order() {
        return 0;
    }

    @Override
    public void shutdownGracefully() {
        Collection<ShutDownAbleMqBean> shutDownAbleMqBeans = getShutDownAbleMqBeans();
        if (!CollectionUtils.isEmpty(shutDownAbleMqBeans)) {
            shutDownAbleMqBeans.stream().forEach(v -> {
                log.info("shutdownGracefully,MQShutdownManager getShutDownAbleMqBeans:{}", v.getClass().getName());
            });
            shutDownAbleMqBeans.stream().filter(this::isConsumer)
                    .forEach(v -> {
                        log.info("MQShutdownManager shutdownGracefully for component:{}", v.getClass().getName());
                        v.shutdownGracefully();
                    });
            SHUTDOWN_STATE = true;
        }
    }

    private boolean isConsumer(Object bean) {
        return bean instanceof RocketMqConsumerHolder || bean instanceof RocketMqOrderConsumerHolder || bean instanceof DmqProcessor;
    }

    @Override
    public void destroy() {
        log.info("进入 MQShutdownManager#destroy方法");
        Collection<ShutDownAbleMqBean> shutDownAbleMqBeans = getShutDownAbleMqBeans();
        if (!CollectionUtils.isEmpty(shutDownAbleMqBeans)) {
            if (!SHUTDOWN_STATE) {
                shutDownAbleMqBeans.stream().filter(this::isConsumer)
                        .forEach(v -> {
                            log.info("MQShutdownManager destroy for component:{}", v.getClass().getName());
                            v.shutdownGracefully();
                        });
            }
            shutDownAbleMqBeans.stream().filter(v -> !isConsumer(v)).forEach(v -> {
                log.info("MQShutdownManager destroy for component:{}", v.getClass().getName());
                v.shutdownGracefully();
            });
        }
    }
}
