package com.poizon.scm.wms.util.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @category <AUTHOR>
 * @since 2020/4/15 17:14
 */
public enum TaskTypeEnum implements ICodeEnum<String, String> {
    /**
     * 收货任务
     */
    RECEIVED("received", "收货任务", "收货"),

    /**
     * 质检任务
     */
    QUALITY("quality", "质检任务", "质检"),

    /**
     * 上架任务
     */
    UPPER("upper", "上架任务", "上架"),

    /**
     * 拣货任务
     */
    PICK("pick", "拣货任务", "拣货"),

    /**
     * 拣货任务
     */
    LAUNCH_PICK("launch_pick", "波次拣货任务", "波次拣货"),

    /**
     * 发货任务
     */
    SHIP("ship", "发货任务", "发货"),

    /**
     * 盘点任务
     */
    STOCK_TAKING("stock_taking", "盘点任务", "盘点"),

    /**
     * 移位任务
     */
    MOVE("move", "移位任务", "移位"),

    //取自TransLogTaskTypeEnum 本来没有这2个任务 库存交易日志用的这枚举
    // 产品要区分出拣货和上架 没新字段区分 在这里加一下
    MOVE_UPPER("move_upper", "移库上架","移库上架"),
    MOVE_PICK("move_pick", "移库下架","移库下架"),

    /**
     * 属性调整
     */
    MODIFY_ATTR("modify_attr", "属性调整任务", "属性调整"),

    ALLOCATE("allocated", "库存分配", "库存分配"),

    RETURN_INVENTORY("return_inventory", "返回库存", "取消分配"),

    ANTI_FAKE("anti_fake", "出库防伪", ""),

    RECEIVED_QUALITY("received_quality", "收货质检", ""),

    RETURN_SHELF("return_shelf", "返架任务", "返架"),

    RETURN_SHELF_INNER("RS", "库内返架任务", "返架"),

    QUALITY_CHECK("quality_check", "商品质检任务", "商品质检"),

    IDENTIFY("identify", "商品鉴别任务", "商品鉴别"),

    EXCEPTION_SHELF("exception_shelf", "返架任务(新)", "返架(新)"),

    REPLENISH_OFF("replenish_off", "补货下架任务", "补货下架"),

    REPLENISH_UPPER("replenish_upper", "补货上架任务", "补货上架"),

    //该枚举用于打印波次
    LAUNCH_PRINT("launch_print","波次打印任务","波次打印"),

    UC_IDENTIFY("uc_identify","唯一码鉴别任务","唯一码鉴别"),

    JCJJ_OUTBOUDN("jcjj_outbound","寄存交接出库","寄存交接出库"),

    JCJJ_INBOUDN("jcjj_inbound","寄存交接入库","寄存交接入库"),

    MODIFY_ATTR_4_WORK("modifyAttr_4work","作业流程属性变更","属性调整"),

    FREEZE("freeze","冻结任务","属性调整"),

    UNFREEZE("unFreeze","解冻任务","属性调整"),

    INNER_UP("INNER_UP","库内上架","库内上架"),

    INNER_DOWN("INNER_DOWN","库内下架","库内下架"),

    /**
     * 补货超量下架
     */
    REPLENISH_OFF_MORE("rep_off_more", "补货超量下架","补货超量下架"),
    /**
     * 盘点矫正
     */
    PHASE_CORRECT("PHASE_CORRECT", "盘点矫正","盘点矫正"),

    /**
     * 订单取消
     */
    TRADE_CANCEL("TRADE_CANCEL", "订单取消", "订单取消"),

    /**
     * 取消件入箱
     */
    RETURN_FIND("RETURN_FIND", "取消件入箱", "取消件入箱"),

    /**
     * 修改库存属性修改扣减
     */
    MODIFY_SUB("modify_sub", "修改库存属性修改扣减", "修改库存属性修改扣减"),

    /**
     * 修改库存属性修改增加
     */
    MODIFY_ADD("modify_add", "修改库存属性修改增加", "修改库存属性修改增加"),
    ;

    /**
     * 可以查询明细的任务类型集合
     */
    public static final List<TaskTypeEnum> TASK_DETAIL_SCAN_LIST = Lists.newArrayList(UPPER,PICK,MOVE,MODIFY_ATTR,RECEIVED,SHIP,RETURN_SHELF,RECEIVED_QUALITY);

    /**
     * 可以打印明细的任务类型集合
     */
    public static final List<TaskTypeEnum> TASK_DETAIL_PRINT_LIST = Lists.newArrayList(UPPER,PICK,MOVE,MODIFY_ATTR,RECEIVED,SHIP,RETURN_SHELF,RECEIVED_QUALITY);

    /**
     * 补货任务类型
     */
    public static final List<TaskTypeEnum> REPLENISH_TASK_LIST = Lists.newArrayList(REPLENISH_OFF, REPLENISH_UPPER);

    public static final List<String> REPLACE_WAREHOUSE_LIST = Lists.newArrayList(SHIP.getTaskType(), RECEIVED_QUALITY.getTaskType());

    /**
     * 真实操作的任务类型
     */
    public static final List<String> REAL_OPERATE_TASK_LIST =  Lists.newArrayList(
            RECEIVED.getTaskType(),
            QUALITY.getTaskType(),
            UPPER.getTaskType(),
            PICK.getTaskType(),
            SHIP.getTaskType(),
            MOVE.getTaskType(),
            MODIFY_ATTR.getTaskType(),
            RECEIVED_QUALITY.getTaskType()
    );
    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务名称
     */
    private String taskName;

    private String operateName;


    TaskTypeEnum(String taskType, String taskName, String operateName) {
        this.taskType = taskType;
        this.taskName = taskName;
        this.operateName = operateName;
    }


    public String getTaskType() {
        return taskType;
    }



    public String getTaskName() {
        return taskName;
    }

    public String getOperateName() {
        return operateName;
    }

    public static TaskTypeEnum getTaskType(String taskType) {
        for (TaskTypeEnum taskTypeEnum : values()) {
            if (taskTypeEnum.getTaskType().equals(taskType)) {
                return taskTypeEnum;
            }
        }
        return null;
    }
    @Override
    public String getCode() {
        return this.taskType;
    }

    @Override
    public String getName() {
        return this.taskName;
    }

    @Override
    public String getValue() {
        return this.taskType;
    }

    public static String convertPhaseType(TaskTypeEnum taskTypeEnum){
        switch (taskTypeEnum){
            case RECEIVED:
                return "RECEIVED";
            case UPPER:
                return "UPPER";
        }
        return null;
    }

    public static TaskTypeEnum convertType(String type){
        switch (type){
            case "RECEIVED":
                return RECEIVED;
            case "UPPER":
                return UPPER;
        }
        return null;
    }
}
