package com.poizon.scm.wms.util.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/2
 */
public enum PickingOrderStatusEnum implements ICodeEnum<String, String> {


    /**
     * 11-完全分配
     */
    WHOLE_ALLOCATE(11, "完全分配"),
    /**
     * 34-拣货完成
     */
    PICKED(34, "拣货完成"),
    /**
     * 90-取消
     */
    CANCEL(90, "取消"),
    ;

    private Integer status;

    private String desc;

    public static List<Integer> finishedStatus = Lists.newArrayList(PICKED.status, CANCEL.status);

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    PickingOrderStatusEnum(Integer status, String desc) {
        this.desc = desc;
        this.status = status;
    }

    @Override
    public String getCode() {
        return String.valueOf(getStatus());
    }

    @Override
    public String getName() {
        return getDesc();
    }

    @Override
    public String getValue() {
        return String.valueOf(getStatus());
    }

}
